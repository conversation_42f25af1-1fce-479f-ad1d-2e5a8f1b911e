  {
  "initial_prompt": "1. `01_intent-overview.md`\n\n       * Foundational document defining the project\u2019s core purpose and scope\n       * Establishes the primary intent and vision guiding all subsequent files\n       * Serves as the initial anchor and source of truth for overall direction\n       * Positioned first to ensure every following detail is rooted in a clear objective\n\n    2. `02_context-background.md`\n\n       * Explains the motivation behind the project and the problem it addresses\n       * Provides essential background and domain context for understanding the project\u2019s environment\n       * Describes how the solution is expected to work from the user\u2019s perspective\n       * Outlines user experience goals and success criteria for the project\n       * Follows the intent overview to ground the high-level vision in real-world context and needs\n\n    3. `03_existing-patterns.md`\n\n       * Details the system\u2019s architecture and major components in place or planned\n       * Records key technical decisions and established design conventions guiding the solution\n       * Highlights important design patterns or methodologies being utilized\n       * Describes how components interact and relate within the system structure\n       * Positioned here to translate contextual background into a concrete technical blueprint, ensuring design coherence moving forward\n\n    4. `04_tech-stack.md`\n\n       * Lists the programming languages, frameworks, and tools chosen for implementation\n       * Describes the development environment, configuration, and setup specifics\n       * Notes key dependencies, libraries, and external services integrated into the project\n       * Specifies any technical constraints, requirements, or standards to be respected\n       * Follows the design blueprint, mapping architectural decisions to the actual technologies that will realize them\n\n    5. `05_current-activity.md`\n\n       * Summarizes the current focus of development and active tasks in progress\n       * Logs recent changes, updates, or new features implemented in the codebase\n       * Identifies the immediate next steps or upcoming work planned\n       * Notes any active decisions, open questions, or considerations at this stage\n       * Captures new insights, patterns, or preferences learned during recent development\n       * Sits mid-sequence to provide a real-time snapshot of the project, linking established context and design to ongoing execution\n\n    6. `06_progress-tracking.md`\n\n       * Provides an up-to-date overview of the project\u2019s status and overall progress\n       * Highlights completed features or milestones achieved (what\u2019s working)\n       * Lists remaining tasks, features, or goals yet to be completed (what\u2019s left)\n       * Notes any known issues, bugs, or blockers currently identified\n       * Chronicles how the project\u2019s direction or key decisions have evolved over time\n       * Placed after current activity to consolidate what\u2019s been done and what remains, informing priorities and guiding the final objective\n\n    7. `07_priority-tasks.md`\n\n       * Identifies the highest-priority tasks, fixes, or features needing immediate attention\n       * Ranks and clarifies these tasks to guide the team\u2019s focus and resource allocation\n       * Derives priorities from the latest progress and context to address critical gaps\n       * Ensures the development effort remains aligned with what\u2019s most impactful for success\n       * Bridges the gap between progress status and final goals by outlining the next critical steps toward completion\n\n    8. `08_distilled-objective.md`\n\n       * Refines and summarizes the project\u2019s ultimate goal or end-state in a concise statement\n       * Distills the project\u2019s purpose and success criteria after considering all prior context and progress\n       * Reflects any adjustments to the original intent based on insights gained during development\n       * Validates that all documentation and efforts align with this singular, ultimate objective\n       * Concludes the sequence by providing a clear final target, ensuring every preceding step aligns with the intended outcome",
  "sequence_id": "0003|0002",
  "results": [
      {
      "instruction": "# Prompt Enhancer\n\nYour goal is not to **rewrite** the input prompt, but to **enhance** it by maximizing clarity, conciseness, and precision while preserving all original intent and adding comprehensive guidance for optimal LLM performance. Execute as:\n\n`{role=comprehensive_prompt_enhancer; input=[original_prompt:str]; process=[analyze_clarity_gaps(), identify_ambiguities(), add_context_markers(), specify_output_format(), incorporate_role_definition(), structure_logical_flow(), refine_language_precision()]; constraints=[preserve_original_intent(), maximize_LLM_effectiveness(), ensure_comprehensive_guidance()]; requirements=[eliminate_ambiguity(), provide_complete_context(), specify_exact_requirements()]; output={enhanced_prompt:str}}`",
      "step": "a",
      "title": "Prompt Enhancer",
      "input": "1. `01_intent-overview.md`\n\n       * Foundational document defining the project\u2019s core purpose and scope\n       * Establishes the primary intent and vision guiding all subsequent files\n       * Serves as the initial anchor and source of truth for overall direction\n       * Positioned first to ensure every following detail is rooted in a clear objective\n\n    2. `02_context-background.md`\n\n       * Explains the motivation behind the project and the problem it addresses\n       * Provides essential background and domain context for understanding the project\u2019s environment\n       * Describes how the solution is expected to work from the user\u2019s perspective\n       * Outlines user experience goals and success criteria for the project\n       * Follows the intent overview to ground the high-level vision in real-world context and needs\n\n    3. `03_existing-patterns.md`\n\n       * Details the system\u2019s architecture and major components in place or planned\n       * Records key technical decisions and established design conventions guiding the solution\n       * Highlights important design patterns or methodologies being utilized\n       * Describes how components interact and relate within the system structure\n       * Positioned here to translate contextual background into a concrete technical blueprint, ensuring design coherence moving forward\n\n    4. `04_tech-stack.md`\n\n       * Lists the programming languages, frameworks, and tools chosen for implementation\n       * Describes the development environment, configuration, and setup specifics\n       * Notes key dependencies, libraries, and external services integrated into the project\n       * Specifies any technical constraints, requirements, or standards to be respected\n       * Follows the design blueprint, mapping architectural decisions to the actual technologies that will realize them\n\n    5. `05_current-activity.md`\n\n       * Summarizes the current focus of development and active tasks in progress\n       * Logs recent changes, updates, or new features implemented in the codebase\n       * Identifies the immediate next steps or upcoming work planned\n       * Notes any active decisions, open questions, or considerations at this stage\n       * Captures new insights, patterns, or preferences learned during recent development\n       * Sits mid-sequence to provide a real-time snapshot of the project, linking established context and design to ongoing execution\n\n    6. `06_progress-tracking.md`\n\n       * Provides an up-to-date overview of the project\u2019s status and overall progress\n       * Highlights completed features or milestones achieved (what\u2019s working)\n       * Lists remaining tasks, features, or goals yet to be completed (what\u2019s left)\n       * Notes any known issues, bugs, or blockers currently identified\n       * Chronicles how the project\u2019s direction or key decisions have evolved over time\n       * Placed after current activity to consolidate what\u2019s been done and what remains, informing priorities and guiding the final objective\n\n    7. `07_priority-tasks.md`\n\n       * Identifies the highest-priority tasks, fixes, or features needing immediate attention\n       * Ranks and clarifies these tasks to guide the team\u2019s focus and resource allocation\n       * Derives priorities from the latest progress and context to address critical gaps\n       * Ensures the development effort remains aligned with what\u2019s most impactful for success\n       * Bridges the gap between progress status and final goals by outlining the next critical steps toward completion\n\n    8. `08_distilled-objective.md`\n\n       * Refines and summarizes the project\u2019s ultimate goal or end-state in a concise statement\n       * Distills the project\u2019s purpose and success criteria after considering all prior context and progress\n       * Reflects any adjustments to the original intent based on insights gained during development\n       * Validates that all documentation and efforts align with this singular, ultimate objective\n       * Concludes the sequence by providing a clear final target, ensuring every preceding step aligns with the intended outcome",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhanced_prompt\": \"You are to provide detailed, structured explanations for each of the project documentation files listed below. Your task is to ensure maximum clarity, remove ambiguity, and provide complete context so that a reader unfamiliar with the project fully understands the purpose, content, and sequence of each file within the documentation set. The intent is to guide the optimal composition and logical ordering of technical documentation for a software project. \\n\\nRole: Comprehensive Technical Documentation Analyst\\n\\nContext Markers & Output Structure:\\n1. Approach each file as part of a sequential documentation process\u2014beginning with project inception and vision, proceeding through context, design, technical implementation, progress tracking, prioritization, and culminating with a distilled statement of project goals.\\n2. For each file, explicitly clarify:\\n    - Its precise purpose in the overall documentation sequence\\n    - The type of information and level of detail it should contain\\n    - Its relationship to preceding and subsequent files\\n    - How it contributes to overall project transparency and direction\\n3. For maximum precision, use concise bullet points or structured sub-sections where appropriate. Ensure each description eliminates vagueness or overlap with other files.\\n4. Preserve the original sequence and intent as outlined, but enhance completeness and specificity of guidance for preparing each document.\\n\\nOutput Format:\\nReturn a numbered list with the file name as a heading for each item, followed by your enhanced explanation, adhering to the constraints above. Use clear language and ensure comprehensive guidance for someone tasked with preparing each documentation file.\\n\\nRequirements:\\n- Eliminate any ambiguity about content, purpose, and sequencing.\\n- Provide all context and definitions necessary for an LLM to generate or review these documents in a software project context.\\n- Specify exact expectations for each file, with no assumptions of prior knowledge.\\n- Structure and refine the explanations for logical flow, maximum clarity, and actionable utility.\\n\\nReference sequence of documentation files:\\n1. 01_intent-overview.md\\n2. 02_context-background.md\\n3. 03_existing-patterns.md\\n4. 04_tech-stack.md\\n5. 05_current-activity.md\\n6. 06_progress-tracking.md\\n7. 07_priority-tasks.md\\n8. 08_distilled-objective.md\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Enhancer\n\nYour goal is not to **expand** the input prompt, but to **refine** it by sharpening core instructions and eliminating unnecessary elements while maintaining essential guidance. Execute as:\n\n`{role=focused_prompt_enhancer; input=[original_prompt:str]; process=[identify_core_instruction(), remove_redundancies(), sharpen_key_directives(), streamline_requirements()]; constraints=[maintain_clarity(), preserve_effectiveness()]; requirements=[eliminate_superfluous_words(), retain_essential_guidance()]; output={refined_prompt:str}}`",
      "step": "b",
      "title": "Prompt Enhancer",
      "input": "{\n  \"initial_prompt\": \"1. `01_intent-overview.md`\\n\\n       * Foundational document defining the project\\u2019s core purpose and scope\\n       * Establishes the primary intent and vision guiding all subsequent files\\n       * Serves as the initial anchor and source of truth for overall direction\\n       * Positioned first to ensure every following detail is rooted in a clear objective\\n\\n    2. `02_context-background.md`\\n\\n       * Explains the motivation behind the project and the problem it addresses\\n       * Provides essential background and domain context for understanding the project\\u2019s environment\\n       * Describes how the solution is expected to work from the user\\u2019s perspective\\n       * Outlines user experience goals and success criteria for the project\\n       * Follows the intent overview to ground the high-level vision in real-world context and needs\\n\\n    3. `03_existing-patterns.md`\\n\\n       * Details the system\\u2019s architecture and major components in place or planned\\n       * Records key technical decisions and established design conventions guiding the solution\\n       * Highlights important design patterns or methodologies being utilized\\n       * Describes how components interact and relate within the system structure\\n       * Positioned here to translate contextual background into a concrete technical blueprint, ensuring design coherence moving forward\\n\\n    4. `04_tech-stack.md`\\n\\n       * Lists the programming languages, frameworks, and tools chosen for implementation\\n       * Describes the development environment, configuration, and setup specifics\\n       * Notes key dependencies, libraries, and external services integrated into the project\\n       * Specifies any technical constraints, requirements, or standards to be respected\\n       * Follows the design blueprint, mapping architectural decisions to the actual technologies that will realize them\\n\\n    5. `05_current-activity.md`\\n\\n       * Summarizes the current focus of development and active tasks in progress\\n       * Logs recent changes, updates, or new features implemented in the codebase\\n       * Identifies the immediate next steps or upcoming work planned\\n       * Notes any active decisions, open questions, or considerations at this stage\\n       * Captures new insights, patterns, or preferences learned during recent development\\n       * Sits mid-sequence to provide a real-time snapshot of the project, linking established context and design to ongoing execution\\n\\n    6. `06_progress-tracking.md`\\n\\n       * Provides an up-to-date overview of the project\\u2019s status and overall progress\\n       * Highlights completed features or milestones achieved (what\\u2019s working)\\n       * Lists remaining tasks, features, or goals yet to be completed (what\\u2019s left)\\n       * Notes any known issues, bugs, or blockers currently identified\\n       * Chronicles how the project\\u2019s direction or key decisions have evolved over time\\n       * Placed after current activity to consolidate what\\u2019s been done and what remains, informing priorities and guiding the final objective\\n\\n    7. `07_priority-tasks.md`\\n\\n       * Identifies the highest-priority tasks, fixes, or features needing immediate attention\\n       * Ranks and clarifies these tasks to guide the team\\u2019s focus and resource allocation\\n       * Derives priorities from the latest progress and context to address critical gaps\\n       * Ensures the development effort remains aligned with what\\u2019s most impactful for success\\n       * Bridges the gap between progress status and final goals by outlining the next critical steps toward completion\\n\\n    8. `08_distilled-objective.md`\\n\\n       * Refines and summarizes the project\\u2019s ultimate goal or end-state in a concise statement\\n       * Distills the project\\u2019s purpose and success criteria after considering all prior context and progress\\n       * Reflects any adjustments to the original intent based on insights gained during development\\n       * Validates that all documentation and efforts align with this singular, ultimate objective\\n       * Concludes the sequence by providing a clear final target, ensuring every preceding step aligns with the intended outcome\",\n  \"enhanced_prompt\": \"You are to provide detailed, structured explanations for each of the project documentation files listed below. Your task is to ensure maximum clarity, remove ambiguity, and provide complete context so that a reader unfamiliar with the project fully understands the purpose, content, and sequence of each file within the documentation set. The intent is to guide the optimal composition and logical ordering of technical documentation for a software project. \\n\\nRole: Comprehensive Technical Documentation Analyst\\n\\nContext Markers & Output Structure:\\n1. Approach each file as part of a sequential documentation process\\u2014beginning with project inception and vision, proceeding through context, design, technical implementation, progress tracking, prioritization, and culminating with a distilled statement of project goals.\\n2. For each file, explicitly clarify:\\n    - Its precise purpose in the overall documentation sequence\\n    - The type of information and level of detail it should contain\\n    - Its relationship to preceding and subsequent files\\n    - How it contributes to overall project transparency and direction\\n3. For maximum precision, use concise bullet points or structured sub-sections where appropriate. Ensure each description eliminates vagueness or overlap with other files.\\n4. Preserve the original sequence and intent as outlined, but enhance completeness and specificity of guidance for preparing each document.\\n\\nOutput Format:\\nReturn a numbered list with the file name as a heading for each item, followed by your enhanced explanation, adhering to the constraints above. Use clear language and ensure comprehensive guidance for someone tasked with preparing each documentation file.\\n\\nRequirements:\\n- Eliminate any ambiguity about content, purpose, and sequencing.\\n- Provide all context and definitions necessary for an LLM to generate or review these documents in a software project context.\\n- Specify exact expectations for each file, with no assumptions of prior knowledge.\\n- Structure and refine the explanations for logical flow, maximum clarity, and actionable utility.\\n\\nReference sequence of documentation files:\\n1. 01_intent-overview.md\\n2. 02_context-background.md\\n3. 03_existing-patterns.md\\n4. 04_tech-stack.md\\n5. 05_current-activity.md\\n6. 06_progress-tracking.md\\n7. 07_priority-tasks.md\\n8. 08_distilled-objective.md\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"refined_prompt\": \"Provide clear, structured explanations for each project documentation file listed below, ensuring each explanation includes:\\n- The file\u2019s distinct purpose in the documentation sequence\\n- The specific information it should contain\\n- Its relationship to prior and subsequent files\\n\\nUse concise bullet points or sub-sections for clarity. Avoid overlap and ambiguity between files. Maintain the original file order and intent, but sharpen specificity and guidance for preparing each document. Explanations must be fully self-contained and understandable to readers new to the project, supporting accurate creation or review of technical documentation.\\n\\nOutput: Numbered list with file name as a heading, followed by the corresponding explanation for each file.\\n\\nFiles (in order):\\n1. 01_intent-overview.md\\n2. 02_context-background.md\\n3. 03_existing-patterns.md\\n4. 04_tech-stack.md\\n5. 05_current-activity.md\\n6. 06_progress-tracking.md\\n7. 07_priority-tasks.md\\n8. 08_distilled-objective.md\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Enhancer\n\nYour goal is not to **elaborate** but to **distill** the prompt to its most effective essential elements. Execute as:\n\n`{role=precision_enhancer; input=[original_prompt:str]; process=[extract_core_directive(), eliminate_redundancy(), maximize_word_efficiency()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Prompt Enhancer",
      "input": "{\n  \"initial_prompt\": \"1. `01_intent-overview.md`\\n\\n       * Foundational document defining the project\\u2019s core purpose and scope\\n       * Establishes the primary intent and vision guiding all subsequent files\\n       * Serves as the initial anchor and source of truth for overall direction\\n       * Positioned first to ensure every following detail is rooted in a clear objective\\n\\n    2. `02_context-background.md`\\n\\n       * Explains the motivation behind the project and the problem it addresses\\n       * Provides essential background and domain context for understanding the project\\u2019s environment\\n       * Describes how the solution is expected to work from the user\\u2019s perspective\\n       * Outlines user experience goals and success criteria for the project\\n       * Follows the intent overview to ground the high-level vision in real-world context and needs\\n\\n    3. `03_existing-patterns.md`\\n\\n       * Details the system\\u2019s architecture and major components in place or planned\\n       * Records key technical decisions and established design conventions guiding the solution\\n       * Highlights important design patterns or methodologies being utilized\\n       * Describes how components interact and relate within the system structure\\n       * Positioned here to translate contextual background into a concrete technical blueprint, ensuring design coherence moving forward\\n\\n    4. `04_tech-stack.md`\\n\\n       * Lists the programming languages, frameworks, and tools chosen for implementation\\n       * Describes the development environment, configuration, and setup specifics\\n       * Notes key dependencies, libraries, and external services integrated into the project\\n       * Specifies any technical constraints, requirements, or standards to be respected\\n       * Follows the design blueprint, mapping architectural decisions to the actual technologies that will realize them\\n\\n    5. `05_current-activity.md`\\n\\n       * Summarizes the current focus of development and active tasks in progress\\n       * Logs recent changes, updates, or new features implemented in the codebase\\n       * Identifies the immediate next steps or upcoming work planned\\n       * Notes any active decisions, open questions, or considerations at this stage\\n       * Captures new insights, patterns, or preferences learned during recent development\\n       * Sits mid-sequence to provide a real-time snapshot of the project, linking established context and design to ongoing execution\\n\\n    6. `06_progress-tracking.md`\\n\\n       * Provides an up-to-date overview of the project\\u2019s status and overall progress\\n       * Highlights completed features or milestones achieved (what\\u2019s working)\\n       * Lists remaining tasks, features, or goals yet to be completed (what\\u2019s left)\\n       * Notes any known issues, bugs, or blockers currently identified\\n       * Chronicles how the project\\u2019s direction or key decisions have evolved over time\\n       * Placed after current activity to consolidate what\\u2019s been done and what remains, informing priorities and guiding the final objective\\n\\n    7. `07_priority-tasks.md`\\n\\n       * Identifies the highest-priority tasks, fixes, or features needing immediate attention\\n       * Ranks and clarifies these tasks to guide the team\\u2019s focus and resource allocation\\n       * Derives priorities from the latest progress and context to address critical gaps\\n       * Ensures the development effort remains aligned with what\\u2019s most impactful for success\\n       * Bridges the gap between progress status and final goals by outlining the next critical steps toward completion\\n\\n    8. `08_distilled-objective.md`\\n\\n       * Refines and summarizes the project\\u2019s ultimate goal or end-state in a concise statement\\n       * Distills the project\\u2019s purpose and success criteria after considering all prior context and progress\\n       * Reflects any adjustments to the original intent based on insights gained during development\\n       * Validates that all documentation and efforts align with this singular, ultimate objective\\n       * Concludes the sequence by providing a clear final target, ensuring every preceding step aligns with the intended outcome\",\n  \"refined_prompt\": \"Provide clear, structured explanations for each project documentation file listed below, ensuring each explanation includes:\\n- The file\\u2019s distinct purpose in the documentation sequence\\n- The specific information it should contain\\n- Its relationship to prior and subsequent files\\n\\nUse concise bullet points or sub-sections for clarity. Avoid overlap and ambiguity between files. Maintain the original file order and intent, but sharpen specificity and guidance for preparing each document. Explanations must be fully self-contained and understandable to readers new to the project, supporting accurate creation or review of technical documentation.\\n\\nOutput: Numbered list with file name as a heading, followed by the corresponding explanation for each file.\\n\\nFiles (in order):\\n1. 01_intent-overview.md\\n2. 02_context-background.md\\n3. 03_existing-patterns.md\\n4. 04_tech-stack.md\\n5. 05_current-activity.md\\n6. 06_progress-tracking.md\\n7. 07_priority-tasks.md\\n8. 08_distilled-objective.md\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"role\": \"precision_enhancer\",\n  \"input\": \"Provide clear, structured explanations for each project documentation file listed below, ensuring each explanation includes:\\n- The file\u2019s distinct purpose in the documentation sequence\\n- The specific information it should contain\\n- Its relationship to prior and subsequent files\\n\\nUse concise bullet points or sub-sections for clarity. Avoid overlap and ambiguity between files. Maintain the original file order and intent, but sharpen specificity and guidance for preparing each document. Explanations must be fully self-contained and understandable to readers new to the project, supporting accurate creation or review of technical documentation.\\n\\nOutput: Numbered list with file name as a heading, followed by the corresponding explanation for each file.\\n\\nFiles (in order):\\n1. 01_intent-overview.md\\n2. 02_context-background.md\\n3. 03_existing-patterns.md\\n4. 04_tech-stack.md\\n5. 05_current-activity.md\\n6. 06_progress-tracking.md\\n7. 07_priority-tasks.md\\n8. 08_distilled-objective.md\",\n  \"process\": [\n    \"extract_core_directive()\",\n    \"eliminate_redundancy()\",\n    \"maximize_word_efficiency()\"\n  ],\n  \"precise_prompt\": \"For each file listed, concisely describe:\\n- Its unique role in the documentation sequence\\n- The essential information it must include\\n- How it connects to adjacent files\\n\\nUse numbered file headings and clear, non-overlapping bullet points. Keep explanations self-contained, specific, and accessible for those new to the project.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Enhancer\n\nYour goal is not to **modify** but to **compress** to maximum effectiveness. Execute as:\n\n`{role=core_enhancer; input=[original_prompt:str]; process=[distill_essence(), maximize_impact()]; output={optimized_prompt:str}}`",
      "step": "d",
      "title": "Prompt Enhancer",
      "input": "{\n  \"initial_prompt\": \"1. `01_intent-overview.md`\\n\\n       * Foundational document defining the project\\u2019s core purpose and scope\\n       * Establishes the primary intent and vision guiding all subsequent files\\n       * Serves as the initial anchor and source of truth for overall direction\\n       * Positioned first to ensure every following detail is rooted in a clear objective\\n\\n    2. `02_context-background.md`\\n\\n       * Explains the motivation behind the project and the problem it addresses\\n       * Provides essential background and domain context for understanding the project\\u2019s environment\\n       * Describes how the solution is expected to work from the user\\u2019s perspective\\n       * Outlines user experience goals and success criteria for the project\\n       * Follows the intent overview to ground the high-level vision in real-world context and needs\\n\\n    3. `03_existing-patterns.md`\\n\\n       * Details the system\\u2019s architecture and major components in place or planned\\n       * Records key technical decisions and established design conventions guiding the solution\\n       * Highlights important design patterns or methodologies being utilized\\n       * Describes how components interact and relate within the system structure\\n       * Positioned here to translate contextual background into a concrete technical blueprint, ensuring design coherence moving forward\\n\\n    4. `04_tech-stack.md`\\n\\n       * Lists the programming languages, frameworks, and tools chosen for implementation\\n       * Describes the development environment, configuration, and setup specifics\\n       * Notes key dependencies, libraries, and external services integrated into the project\\n       * Specifies any technical constraints, requirements, or standards to be respected\\n       * Follows the design blueprint, mapping architectural decisions to the actual technologies that will realize them\\n\\n    5. `05_current-activity.md`\\n\\n       * Summarizes the current focus of development and active tasks in progress\\n       * Logs recent changes, updates, or new features implemented in the codebase\\n       * Identifies the immediate next steps or upcoming work planned\\n       * Notes any active decisions, open questions, or considerations at this stage\\n       * Captures new insights, patterns, or preferences learned during recent development\\n       * Sits mid-sequence to provide a real-time snapshot of the project, linking established context and design to ongoing execution\\n\\n    6. `06_progress-tracking.md`\\n\\n       * Provides an up-to-date overview of the project\\u2019s status and overall progress\\n       * Highlights completed features or milestones achieved (what\\u2019s working)\\n       * Lists remaining tasks, features, or goals yet to be completed (what\\u2019s left)\\n       * Notes any known issues, bugs, or blockers currently identified\\n       * Chronicles how the project\\u2019s direction or key decisions have evolved over time\\n       * Placed after current activity to consolidate what\\u2019s been done and what remains, informing priorities and guiding the final objective\\n\\n    7. `07_priority-tasks.md`\\n\\n       * Identifies the highest-priority tasks, fixes, or features needing immediate attention\\n       * Ranks and clarifies these tasks to guide the team\\u2019s focus and resource allocation\\n       * Derives priorities from the latest progress and context to address critical gaps\\n       * Ensures the development effort remains aligned with what\\u2019s most impactful for success\\n       * Bridges the gap between progress status and final goals by outlining the next critical steps toward completion\\n\\n    8. `08_distilled-objective.md`\\n\\n       * Refines and summarizes the project\\u2019s ultimate goal or end-state in a concise statement\\n       * Distills the project\\u2019s purpose and success criteria after considering all prior context and progress\\n       * Reflects any adjustments to the original intent based on insights gained during development\\n       * Validates that all documentation and efforts align with this singular, ultimate objective\\n       * Concludes the sequence by providing a clear final target, ensuring every preceding step aligns with the intended outcome\",\n  \"role\": \"precision_enhancer\",\n  \"input\": \"Provide clear, structured explanations for each project documentation file listed below, ensuring each explanation includes:\\n- The file\\u2019s distinct purpose in the documentation sequence\\n- The specific information it should contain\\n- Its relationship to prior and subsequent files\\n\\nUse concise bullet points or sub-sections for clarity. Avoid overlap and ambiguity between files. Maintain the original file order and intent, but sharpen specificity and guidance for preparing each document. Explanations must be fully self-contained and understandable to readers new to the project, supporting accurate creation or review of technical documentation.\\n\\nOutput: Numbered list with file name as a heading, followed by the corresponding explanation for each file.\\n\\nFiles (in order):\\n1. 01_intent-overview.md\\n2. 02_context-background.md\\n3. 03_existing-patterns.md\\n4. 04_tech-stack.md\\n5. 05_current-activity.md\\n6. 06_progress-tracking.md\\n7. 07_priority-tasks.md\\n8. 08_distilled-objective.md\",\n  \"process\": [\n    \"extract_core_directive()\",\n    \"eliminate_redundancy()\",\n    \"maximize_word_efficiency()\"\n  ],\n  \"precise_prompt\": \"For each file listed, concisely describe:\\n- Its unique role in the documentation sequence\\n- The essential information it must include\\n- How it connects to adjacent files\\n\\nUse numbered file headings and clear, non-overlapping bullet points. Keep explanations self-contained, specific, and accessible for those new to the project.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_prompt\": \"For each documentation file below, provide a numbered list where each file heading is followed by:\\n- A concise statement of its unique function in the sequence\\n- Bulleted list of key content requirements\\n- Brief note on its connection to the files before and after\\n\\nEnsure all explanations are clear, non-redundant, and informative for readers unfamiliar with the project. Maintain original file order.\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:\n\n`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=10)]; output={title:str}}`",
      "step": "a",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"1. `01_intent-overview.md`\\n\\n       * Foundational document defining the project\\u2019s core purpose and scope\\n       * Establishes the primary intent and vision guiding all subsequent files\\n       * Serves as the initial anchor and source of truth for overall direction\\n       * Positioned first to ensure every following detail is rooted in a clear objective\\n\\n    2. `02_context-background.md`\\n\\n       * Explains the motivation behind the project and the problem it addresses\\n       * Provides essential background and domain context for understanding the project\\u2019s environment\\n       * Describes how the solution is expected to work from the user\\u2019s perspective\\n       * Outlines user experience goals and success criteria for the project\\n       * Follows the intent overview to ground the high-level vision in real-world context and needs\\n\\n    3. `03_existing-patterns.md`\\n\\n       * Details the system\\u2019s architecture and major components in place or planned\\n       * Records key technical decisions and established design conventions guiding the solution\\n       * Highlights important design patterns or methodologies being utilized\\n       * Describes how components interact and relate within the system structure\\n       * Positioned here to translate contextual background into a concrete technical blueprint, ensuring design coherence moving forward\\n\\n    4. `04_tech-stack.md`\\n\\n       * Lists the programming languages, frameworks, and tools chosen for implementation\\n       * Describes the development environment, configuration, and setup specifics\\n       * Notes key dependencies, libraries, and external services integrated into the project\\n       * Specifies any technical constraints, requirements, or standards to be respected\\n       * Follows the design blueprint, mapping architectural decisions to the actual technologies that will realize them\\n\\n    5. `05_current-activity.md`\\n\\n       * Summarizes the current focus of development and active tasks in progress\\n       * Logs recent changes, updates, or new features implemented in the codebase\\n       * Identifies the immediate next steps or upcoming work planned\\n       * Notes any active decisions, open questions, or considerations at this stage\\n       * Captures new insights, patterns, or preferences learned during recent development\\n       * Sits mid-sequence to provide a real-time snapshot of the project, linking established context and design to ongoing execution\\n\\n    6. `06_progress-tracking.md`\\n\\n       * Provides an up-to-date overview of the project\\u2019s status and overall progress\\n       * Highlights completed features or milestones achieved (what\\u2019s working)\\n       * Lists remaining tasks, features, or goals yet to be completed (what\\u2019s left)\\n       * Notes any known issues, bugs, or blockers currently identified\\n       * Chronicles how the project\\u2019s direction or key decisions have evolved over time\\n       * Placed after current activity to consolidate what\\u2019s been done and what remains, informing priorities and guiding the final objective\\n\\n    7. `07_priority-tasks.md`\\n\\n       * Identifies the highest-priority tasks, fixes, or features needing immediate attention\\n       * Ranks and clarifies these tasks to guide the team\\u2019s focus and resource allocation\\n       * Derives priorities from the latest progress and context to address critical gaps\\n       * Ensures the development effort remains aligned with what\\u2019s most impactful for success\\n       * Bridges the gap between progress status and final goals by outlining the next critical steps toward completion\\n\\n    8. `08_distilled-objective.md`\\n\\n       * Refines and summarizes the project\\u2019s ultimate goal or end-state in a concise statement\\n       * Distills the project\\u2019s purpose and success criteria after considering all prior context and progress\\n       * Reflects any adjustments to the original intent based on insights gained during development\\n       * Validates that all documentation and efforts align with this singular, ultimate objective\\n       * Concludes the sequence by providing a clear final target, ensuring every preceding step aligns with the intended outcome\",\n  \"optimized_prompt\": \"For each documentation file below, provide a numbered list where each file heading is followed by:\\n- A concise statement of its unique function in the sequence\\n- Bulleted list of key content requirements\\n- Brief note on its connection to the files before and after\\n\\nEnsure all explanations are clear, non-redundant, and informative for readers unfamiliar with the project. Maintain original file order.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"title\": \"Project Documentation Sequence: Purpose, Context, Design, Activity, Progress, Priorities, Objective\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:\n\n`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=6)]; output={title:str}}`",
      "step": "b",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"1. `01_intent-overview.md`\\n\\n       * Foundational document defining the project\\u2019s core purpose and scope\\n       * Establishes the primary intent and vision guiding all subsequent files\\n       * Serves as the initial anchor and source of truth for overall direction\\n       * Positioned first to ensure every following detail is rooted in a clear objective\\n\\n    2. `02_context-background.md`\\n\\n       * Explains the motivation behind the project and the problem it addresses\\n       * Provides essential background and domain context for understanding the project\\u2019s environment\\n       * Describes how the solution is expected to work from the user\\u2019s perspective\\n       * Outlines user experience goals and success criteria for the project\\n       * Follows the intent overview to ground the high-level vision in real-world context and needs\\n\\n    3. `03_existing-patterns.md`\\n\\n       * Details the system\\u2019s architecture and major components in place or planned\\n       * Records key technical decisions and established design conventions guiding the solution\\n       * Highlights important design patterns or methodologies being utilized\\n       * Describes how components interact and relate within the system structure\\n       * Positioned here to translate contextual background into a concrete technical blueprint, ensuring design coherence moving forward\\n\\n    4. `04_tech-stack.md`\\n\\n       * Lists the programming languages, frameworks, and tools chosen for implementation\\n       * Describes the development environment, configuration, and setup specifics\\n       * Notes key dependencies, libraries, and external services integrated into the project\\n       * Specifies any technical constraints, requirements, or standards to be respected\\n       * Follows the design blueprint, mapping architectural decisions to the actual technologies that will realize them\\n\\n    5. `05_current-activity.md`\\n\\n       * Summarizes the current focus of development and active tasks in progress\\n       * Logs recent changes, updates, or new features implemented in the codebase\\n       * Identifies the immediate next steps or upcoming work planned\\n       * Notes any active decisions, open questions, or considerations at this stage\\n       * Captures new insights, patterns, or preferences learned during recent development\\n       * Sits mid-sequence to provide a real-time snapshot of the project, linking established context and design to ongoing execution\\n\\n    6. `06_progress-tracking.md`\\n\\n       * Provides an up-to-date overview of the project\\u2019s status and overall progress\\n       * Highlights completed features or milestones achieved (what\\u2019s working)\\n       * Lists remaining tasks, features, or goals yet to be completed (what\\u2019s left)\\n       * Notes any known issues, bugs, or blockers currently identified\\n       * Chronicles how the project\\u2019s direction or key decisions have evolved over time\\n       * Placed after current activity to consolidate what\\u2019s been done and what remains, informing priorities and guiding the final objective\\n\\n    7. `07_priority-tasks.md`\\n\\n       * Identifies the highest-priority tasks, fixes, or features needing immediate attention\\n       * Ranks and clarifies these tasks to guide the team\\u2019s focus and resource allocation\\n       * Derives priorities from the latest progress and context to address critical gaps\\n       * Ensures the development effort remains aligned with what\\u2019s most impactful for success\\n       * Bridges the gap between progress status and final goals by outlining the next critical steps toward completion\\n\\n    8. `08_distilled-objective.md`\\n\\n       * Refines and summarizes the project\\u2019s ultimate goal or end-state in a concise statement\\n       * Distills the project\\u2019s purpose and success criteria after considering all prior context and progress\\n       * Reflects any adjustments to the original intent based on insights gained during development\\n       * Validates that all documentation and efforts align with this singular, ultimate objective\\n       * Concludes the sequence by providing a clear final target, ensuring every preceding step aligns with the intended outcome\",\n  \"title\": \"Project Documentation Sequence: Purpose, Context, Design, Activity, Progress, Priorities, Objective\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Project Documentation Structure Overview\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:\n\n`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=4)]; output={title:str}}`",
      "step": "c",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"1. `01_intent-overview.md`\\n\\n       * Foundational document defining the project\\u2019s core purpose and scope\\n       * Establishes the primary intent and vision guiding all subsequent files\\n       * Serves as the initial anchor and source of truth for overall direction\\n       * Positioned first to ensure every following detail is rooted in a clear objective\\n\\n    2. `02_context-background.md`\\n\\n       * Explains the motivation behind the project and the problem it addresses\\n       * Provides essential background and domain context for understanding the project\\u2019s environment\\n       * Describes how the solution is expected to work from the user\\u2019s perspective\\n       * Outlines user experience goals and success criteria for the project\\n       * Follows the intent overview to ground the high-level vision in real-world context and needs\\n\\n    3. `03_existing-patterns.md`\\n\\n       * Details the system\\u2019s architecture and major components in place or planned\\n       * Records key technical decisions and established design conventions guiding the solution\\n       * Highlights important design patterns or methodologies being utilized\\n       * Describes how components interact and relate within the system structure\\n       * Positioned here to translate contextual background into a concrete technical blueprint, ensuring design coherence moving forward\\n\\n    4. `04_tech-stack.md`\\n\\n       * Lists the programming languages, frameworks, and tools chosen for implementation\\n       * Describes the development environment, configuration, and setup specifics\\n       * Notes key dependencies, libraries, and external services integrated into the project\\n       * Specifies any technical constraints, requirements, or standards to be respected\\n       * Follows the design blueprint, mapping architectural decisions to the actual technologies that will realize them\\n\\n    5. `05_current-activity.md`\\n\\n       * Summarizes the current focus of development and active tasks in progress\\n       * Logs recent changes, updates, or new features implemented in the codebase\\n       * Identifies the immediate next steps or upcoming work planned\\n       * Notes any active decisions, open questions, or considerations at this stage\\n       * Captures new insights, patterns, or preferences learned during recent development\\n       * Sits mid-sequence to provide a real-time snapshot of the project, linking established context and design to ongoing execution\\n\\n    6. `06_progress-tracking.md`\\n\\n       * Provides an up-to-date overview of the project\\u2019s status and overall progress\\n       * Highlights completed features or milestones achieved (what\\u2019s working)\\n       * Lists remaining tasks, features, or goals yet to be completed (what\\u2019s left)\\n       * Notes any known issues, bugs, or blockers currently identified\\n       * Chronicles how the project\\u2019s direction or key decisions have evolved over time\\n       * Placed after current activity to consolidate what\\u2019s been done and what remains, informing priorities and guiding the final objective\\n\\n    7. `07_priority-tasks.md`\\n\\n       * Identifies the highest-priority tasks, fixes, or features needing immediate attention\\n       * Ranks and clarifies these tasks to guide the team\\u2019s focus and resource allocation\\n       * Derives priorities from the latest progress and context to address critical gaps\\n       * Ensures the development effort remains aligned with what\\u2019s most impactful for success\\n       * Bridges the gap between progress status and final goals by outlining the next critical steps toward completion\\n\\n    8. `08_distilled-objective.md`\\n\\n       * Refines and summarizes the project\\u2019s ultimate goal or end-state in a concise statement\\n       * Distills the project\\u2019s purpose and success criteria after considering all prior context and progress\\n       * Reflects any adjustments to the original intent based on insights gained during development\\n       * Validates that all documentation and efforts align with this singular, ultimate objective\\n       * Concludes the sequence by providing a clear final target, ensuring every preceding step aligns with the intended outcome\",\n  \"title\": \"Project Documentation Structure Overview\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Documentation Sequence Overview\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:\n\n`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
      "step": "d",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"1. `01_intent-overview.md`\\n\\n       * Foundational document defining the project\\u2019s core purpose and scope\\n       * Establishes the primary intent and vision guiding all subsequent files\\n       * Serves as the initial anchor and source of truth for overall direction\\n       * Positioned first to ensure every following detail is rooted in a clear objective\\n\\n    2. `02_context-background.md`\\n\\n       * Explains the motivation behind the project and the problem it addresses\\n       * Provides essential background and domain context for understanding the project\\u2019s environment\\n       * Describes how the solution is expected to work from the user\\u2019s perspective\\n       * Outlines user experience goals and success criteria for the project\\n       * Follows the intent overview to ground the high-level vision in real-world context and needs\\n\\n    3. `03_existing-patterns.md`\\n\\n       * Details the system\\u2019s architecture and major components in place or planned\\n       * Records key technical decisions and established design conventions guiding the solution\\n       * Highlights important design patterns or methodologies being utilized\\n       * Describes how components interact and relate within the system structure\\n       * Positioned here to translate contextual background into a concrete technical blueprint, ensuring design coherence moving forward\\n\\n    4. `04_tech-stack.md`\\n\\n       * Lists the programming languages, frameworks, and tools chosen for implementation\\n       * Describes the development environment, configuration, and setup specifics\\n       * Notes key dependencies, libraries, and external services integrated into the project\\n       * Specifies any technical constraints, requirements, or standards to be respected\\n       * Follows the design blueprint, mapping architectural decisions to the actual technologies that will realize them\\n\\n    5. `05_current-activity.md`\\n\\n       * Summarizes the current focus of development and active tasks in progress\\n       * Logs recent changes, updates, or new features implemented in the codebase\\n       * Identifies the immediate next steps or upcoming work planned\\n       * Notes any active decisions, open questions, or considerations at this stage\\n       * Captures new insights, patterns, or preferences learned during recent development\\n       * Sits mid-sequence to provide a real-time snapshot of the project, linking established context and design to ongoing execution\\n\\n    6. `06_progress-tracking.md`\\n\\n       * Provides an up-to-date overview of the project\\u2019s status and overall progress\\n       * Highlights completed features or milestones achieved (what\\u2019s working)\\n       * Lists remaining tasks, features, or goals yet to be completed (what\\u2019s left)\\n       * Notes any known issues, bugs, or blockers currently identified\\n       * Chronicles how the project\\u2019s direction or key decisions have evolved over time\\n       * Placed after current activity to consolidate what\\u2019s been done and what remains, informing priorities and guiding the final objective\\n\\n    7. `07_priority-tasks.md`\\n\\n       * Identifies the highest-priority tasks, fixes, or features needing immediate attention\\n       * Ranks and clarifies these tasks to guide the team\\u2019s focus and resource allocation\\n       * Derives priorities from the latest progress and context to address critical gaps\\n       * Ensures the development effort remains aligned with what\\u2019s most impactful for success\\n       * Bridges the gap between progress status and final goals by outlining the next critical steps toward completion\\n\\n    8. `08_distilled-objective.md`\\n\\n       * Refines and summarizes the project\\u2019s ultimate goal or end-state in a concise statement\\n       * Distills the project\\u2019s purpose and success criteria after considering all prior context and progress\\n       * Reflects any adjustments to the original intent based on insights gained during development\\n       * Validates that all documentation and efforts align with this singular, ultimate objective\\n       * Concludes the sequence by providing a clear final target, ensuring every preceding step aligns with the intended outcome\",\n  \"title\": \"Documentation Sequence Overview\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Documentation Sequence\"}",
        }
      }
    }