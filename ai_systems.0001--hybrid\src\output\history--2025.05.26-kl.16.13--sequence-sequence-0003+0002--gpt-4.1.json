  {
  "initial_prompt": "# Meta-Instruction Sequence Generator (a-d)\n\n    ## **0005-a-meta_instruction_generator**\n    ```\n    \"title\": \"Comprehensive Meta-Instruction Generator\",\n    \"interpretation\": \"Your goal is not to **answer** the input, but to **transform** it into a complete multi-step instruction sequence that generates universally applicable, LLM-optimized system messages following established schema format. Execute as:\",\n    \"transformation\": \"`{role=comprehensive_meta_instruction_generator; input=[raw_input:str]; process=[analyze_underlying_pattern(), identify_transformation_stages(), map_universal_applicability(), design_multi_step_sequence(), ensure_schema_compliance(), optimize_for_LLM_processing(), validate_universal_transferability()]; constraints=[maintain_schema_format(), ensure_progressive_refinement(), preserve_universal_applicability()]; requirements=[generate_complete_sequence(), follow_established_patterns(), maximize_LLM_effectiveness()]; output={multi_step_instruction_sequence:dict}}`\"\n    ```\n\n    ## **0005-b-meta_instruction_generator**\n    ```\n    \"title\": \"Focused Meta-Generator\",\n    \"interpretation\": \"Your goal is not to **elaborate** the input, but to **synthesize** it into a streamlined instruction sequence that generates optimized system messages with clear progression. Execute as:\",\n    \"transformation\": \"`{role=focused_meta_generator; input=[raw_input:str]; process=[extract_core_transformation_pattern(), design_progressive_steps(), ensure_schema_adherence(), optimize_sequence_flow()]; constraints=[maintain_universal_applicability(), ensure_LLM_optimization()]; requirements=[create_coherent_progression(), follow_schema_format()]; output={instruction_sequence:dict}}`\"\n    ```\n\n    ## **0005-c-meta_instruction_generator**\n    ```\n    \"title\": \"Precision Generator\",\n    \"interpretation\": \"Your goal is not to **expand** but to **distill** into essential instruction sequence components. Execute as:\",\n    \"transformation\": \"`{role=precision_generator; input=[raw_input:str]; process=[identify_transformation_core(), create_minimal_steps(), ensure_schema_compliance()]; output={optimized_sequence:dict}}`\"\n    ```\n\n    ## **0005-d-meta_instruction_generator**\n    ```\n    \"title\": \"Core Generator\",\n    \"interpretation\": \"Your goal is not to **process** but to **generate** maximum-impact instruction sequences. Execute as:\",\n    \"transformation\": \"`{role=core_generator; input=[raw_input:str]; process=[extract_essence(), generate_sequence()]; output={meta_instructions:dict}}`\"\n    ```\n\n    ## **Progressive Meta-Generation Pattern**\n    - **a:** Comprehensive analysis and complete multi-step sequence generation\n    - **b:** Focused synthesis with streamlined progression and clear optimization\n    - **c:** Precision distillation to essential sequence components\n    - **d:** Core generation of maximum-impact meta-instruction sequences",
  "sequence_id": "0003|0002",
  "results": [
      {
      "instruction": "# Prompt Enhancer\n\nYour goal is not to **rewrite** the input prompt, but to **enhance** it by maximizing clarity, conciseness, and precision while preserving all original intent and adding comprehensive guidance for optimal LLM performance. Execute as:\n\n`{role=comprehensive_prompt_enhancer; input=[original_prompt:str]; process=[analyze_clarity_gaps(), identify_ambiguities(), add_context_markers(), specify_output_format(), incorporate_role_definition(), structure_logical_flow(), refine_language_precision()]; constraints=[preserve_original_intent(), maximize_LLM_effectiveness(), ensure_comprehensive_guidance()]; requirements=[eliminate_ambiguity(), provide_complete_context(), specify_exact_requirements()]; output={enhanced_prompt:str}}`",
      "step": "a",
      "title": "Prompt Enhancer",
      "input": "# Meta-Instruction Sequence Generator (a-d)\n\n    ## **0005-a-meta_instruction_generator**\n    ```\n    \"title\": \"Comprehensive Meta-Instruction Generator\",\n    \"interpretation\": \"Your goal is not to **answer** the input, but to **transform** it into a complete multi-step instruction sequence that generates universally applicable, LLM-optimized system messages following established schema format. Execute as:\",\n    \"transformation\": \"`{role=comprehensive_meta_instruction_generator; input=[raw_input:str]; process=[analyze_underlying_pattern(), identify_transformation_stages(), map_universal_applicability(), design_multi_step_sequence(), ensure_schema_compliance(), optimize_for_LLM_processing(), validate_universal_transferability()]; constraints=[maintain_schema_format(), ensure_progressive_refinement(), preserve_universal_applicability()]; requirements=[generate_complete_sequence(), follow_established_patterns(), maximize_LLM_effectiveness()]; output={multi_step_instruction_sequence:dict}}`\"\n    ```\n\n    ## **0005-b-meta_instruction_generator**\n    ```\n    \"title\": \"Focused Meta-Generator\",\n    \"interpretation\": \"Your goal is not to **elaborate** the input, but to **synthesize** it into a streamlined instruction sequence that generates optimized system messages with clear progression. Execute as:\",\n    \"transformation\": \"`{role=focused_meta_generator; input=[raw_input:str]; process=[extract_core_transformation_pattern(), design_progressive_steps(), ensure_schema_adherence(), optimize_sequence_flow()]; constraints=[maintain_universal_applicability(), ensure_LLM_optimization()]; requirements=[create_coherent_progression(), follow_schema_format()]; output={instruction_sequence:dict}}`\"\n    ```\n\n    ## **0005-c-meta_instruction_generator**\n    ```\n    \"title\": \"Precision Generator\",\n    \"interpretation\": \"Your goal is not to **expand** but to **distill** into essential instruction sequence components. Execute as:\",\n    \"transformation\": \"`{role=precision_generator; input=[raw_input:str]; process=[identify_transformation_core(), create_minimal_steps(), ensure_schema_compliance()]; output={optimized_sequence:dict}}`\"\n    ```\n\n    ## **0005-d-meta_instruction_generator**\n    ```\n    \"title\": \"Core Generator\",\n    \"interpretation\": \"Your goal is not to **process** but to **generate** maximum-impact instruction sequences. Execute as:\",\n    \"transformation\": \"`{role=core_generator; input=[raw_input:str]; process=[extract_essence(), generate_sequence()]; output={meta_instructions:dict}}`\"\n    ```\n\n    ## **Progressive Meta-Generation Pattern**\n    - **a:** Comprehensive analysis and complete multi-step sequence generation\n    - **b:** Focused synthesis with streamlined progression and clear optimization\n    - **c:** Precision distillation to essential sequence components\n    - **d:** Core generation of maximum-impact meta-instruction sequences",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhanced_prompt\": \"You are a comprehensive prompt enhancer. Your objective is not to rewrite the provided input prompt, but to enhance it for superior performance in Large Language Models (LLMs). Follow these detailed instructions:\\n\\n1. Analyze the original input prompt thoroughly, identifying any potential gaps in clarity, areas of ambiguity, or places where the intent or requirements could be misunderstood.\\n2. Identify and resolve any vague or ambiguous language, ensuring that every instruction and expectation is explicit and free from confusion.\\n3. Add necessary context or clarifying markers where needed, so that the prompt is fully self-contained and understandable without external information.\\n4. Specify the required output format clearly to remove any doubt about the form and structure of the expected response (e.g., JSON object, list, structured text), including data types where appropriate.\\n5. Define the expected LLM role and behavior explicitly to avoid misinterpretations. Make sure the role aligns with the underlying intent of the input prompt.\\n6. Structure the logical flow of the enhanced prompt step-by-step, ensuring a clear sequence from task understanding to output delivery. Use bullet points or numbered steps for multi-part instructions.\\n7. Refine the language to be precise, concise, and directive, eliminating superfluous phrasing while preserving the full original intent and functional scope.\\n8. Apply these enhancements strictly according to the following constraints and requirements:\\n   - Preserve the core objective and intent of the original input prompt at every stage.\\n   - Maximize LLM interpretability and correctness by providing complete, concise, and unambiguous guidance.\\n   - Ensure the enhanced prompt is comprehensive\u2014specify all necessary context, eliminate ambiguity, and state exact requirements clearly.\\n\\nYour output must be a valid JSON object in the format: {\\\"enhanced_prompt\\\": \\\"...\\\"}.\",\n  \"context\": \"This meta-instruction enhances instruction granularity and aligns with the 'progressive meta-generation pattern' a-d: (a) comprehensive multi-step, (b) focused and optimized, (c) precision distillation, (d) core essential. Ensure the enhanced prompt supports universal transferability and optimal alignment with system-level LLM prompt design standards.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Enhancer\n\nYour goal is not to **expand** the input prompt, but to **refine** it by sharpening core instructions and eliminating unnecessary elements while maintaining essential guidance. Execute as:\n\n`{role=focused_prompt_enhancer; input=[original_prompt:str]; process=[identify_core_instruction(), remove_redundancies(), sharpen_key_directives(), streamline_requirements()]; constraints=[maintain_clarity(), preserve_effectiveness()]; requirements=[eliminate_superfluous_words(), retain_essential_guidance()]; output={refined_prompt:str}}`",
      "step": "b",
      "title": "Prompt Enhancer",
      "input": "{\n  \"initial_prompt\": \"# Meta-Instruction Sequence Generator (a-d)\\n\\n    ## **0005-a-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Comprehensive Meta-Instruction Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **answer** the input, but to **transform** it into a complete multi-step instruction sequence that generates universally applicable, LLM-optimized system messages following established schema format. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=comprehensive_meta_instruction_generator; input=[raw_input:str]; process=[analyze_underlying_pattern(), identify_transformation_stages(), map_universal_applicability(), design_multi_step_sequence(), ensure_schema_compliance(), optimize_for_LLM_processing(), validate_universal_transferability()]; constraints=[maintain_schema_format(), ensure_progressive_refinement(), preserve_universal_applicability()]; requirements=[generate_complete_sequence(), follow_established_patterns(), maximize_LLM_effectiveness()]; output={multi_step_instruction_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-b-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Focused Meta-Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **elaborate** the input, but to **synthesize** it into a streamlined instruction sequence that generates optimized system messages with clear progression. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=focused_meta_generator; input=[raw_input:str]; process=[extract_core_transformation_pattern(), design_progressive_steps(), ensure_schema_adherence(), optimize_sequence_flow()]; constraints=[maintain_universal_applicability(), ensure_LLM_optimization()]; requirements=[create_coherent_progression(), follow_schema_format()]; output={instruction_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-c-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Precision Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **expand** but to **distill** into essential instruction sequence components. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=precision_generator; input=[raw_input:str]; process=[identify_transformation_core(), create_minimal_steps(), ensure_schema_compliance()]; output={optimized_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-d-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Core Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **process** but to **generate** maximum-impact instruction sequences. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=core_generator; input=[raw_input:str]; process=[extract_essence(), generate_sequence()]; output={meta_instructions:dict}}`\\\"\\n    ```\\n\\n    ## **Progressive Meta-Generation Pattern**\\n    - **a:** Comprehensive analysis and complete multi-step sequence generation\\n    - **b:** Focused synthesis with streamlined progression and clear optimization\\n    - **c:** Precision distillation to essential sequence components\\n    - **d:** Core generation of maximum-impact meta-instruction sequences\",\n  \"enhanced_prompt\": \"You are a comprehensive prompt enhancer. Your objective is not to rewrite the provided input prompt, but to enhance it for superior performance in Large Language Models (LLMs). Follow these detailed instructions:\\n\\n1. Analyze the original input prompt thoroughly, identifying any potential gaps in clarity, areas of ambiguity, or places where the intent or requirements could be misunderstood.\\n2. Identify and resolve any vague or ambiguous language, ensuring that every instruction and expectation is explicit and free from confusion.\\n3. Add necessary context or clarifying markers where needed, so that the prompt is fully self-contained and understandable without external information.\\n4. Specify the required output format clearly to remove any doubt about the form and structure of the expected response (e.g., JSON object, list, structured text), including data types where appropriate.\\n5. Define the expected LLM role and behavior explicitly to avoid misinterpretations. Make sure the role aligns with the underlying intent of the input prompt.\\n6. Structure the logical flow of the enhanced prompt step-by-step, ensuring a clear sequence from task understanding to output delivery. Use bullet points or numbered steps for multi-part instructions.\\n7. Refine the language to be precise, concise, and directive, eliminating superfluous phrasing while preserving the full original intent and functional scope.\\n8. Apply these enhancements strictly according to the following constraints and requirements:\\n   - Preserve the core objective and intent of the original input prompt at every stage.\\n   - Maximize LLM interpretability and correctness by providing complete, concise, and unambiguous guidance.\\n   - Ensure the enhanced prompt is comprehensive\\u2014specify all necessary context, eliminate ambiguity, and state exact requirements clearly.\\n\\nYour output must be a valid JSON object in the format: {\\\"enhanced_prompt\\\": \\\"...\\\"}.\",\n  \"context\": \"This meta-instruction enhances instruction granularity and aligns with the 'progressive meta-generation pattern' a-d: (a) comprehensive multi-step, (b) focused and optimized, (c) precision distillation, (d) core essential. Ensure the enhanced prompt supports universal transferability and optimal alignment with system-level LLM prompt design standards.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"refined_prompt\": \"Sharpen this input prompt for LLM performance without rewriting or expanding it. Focus on: clarifying core instructions, removing redundancies, and strengthening key directives. Analyze the prompt for ambiguity or unnecessary phrasing; eliminate superfluous words while keeping essential guidance and ensuring output clarity. The refined result must remain concise, explicit, and actionable. Return your output as a valid JSON object: {\\\"refined_prompt\\\": \\\"...\\\"}.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Enhancer\n\nYour goal is not to **elaborate** but to **distill** the prompt to its most effective essential elements. Execute as:\n\n`{role=precision_enhancer; input=[original_prompt:str]; process=[extract_core_directive(), eliminate_redundancy(), maximize_word_efficiency()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Prompt Enhancer",
      "input": "{\n  \"initial_prompt\": \"# Meta-Instruction Sequence Generator (a-d)\\n\\n    ## **0005-a-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Comprehensive Meta-Instruction Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **answer** the input, but to **transform** it into a complete multi-step instruction sequence that generates universally applicable, LLM-optimized system messages following established schema format. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=comprehensive_meta_instruction_generator; input=[raw_input:str]; process=[analyze_underlying_pattern(), identify_transformation_stages(), map_universal_applicability(), design_multi_step_sequence(), ensure_schema_compliance(), optimize_for_LLM_processing(), validate_universal_transferability()]; constraints=[maintain_schema_format(), ensure_progressive_refinement(), preserve_universal_applicability()]; requirements=[generate_complete_sequence(), follow_established_patterns(), maximize_LLM_effectiveness()]; output={multi_step_instruction_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-b-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Focused Meta-Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **elaborate** the input, but to **synthesize** it into a streamlined instruction sequence that generates optimized system messages with clear progression. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=focused_meta_generator; input=[raw_input:str]; process=[extract_core_transformation_pattern(), design_progressive_steps(), ensure_schema_adherence(), optimize_sequence_flow()]; constraints=[maintain_universal_applicability(), ensure_LLM_optimization()]; requirements=[create_coherent_progression(), follow_schema_format()]; output={instruction_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-c-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Precision Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **expand** but to **distill** into essential instruction sequence components. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=precision_generator; input=[raw_input:str]; process=[identify_transformation_core(), create_minimal_steps(), ensure_schema_compliance()]; output={optimized_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-d-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Core Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **process** but to **generate** maximum-impact instruction sequences. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=core_generator; input=[raw_input:str]; process=[extract_essence(), generate_sequence()]; output={meta_instructions:dict}}`\\\"\\n    ```\\n\\n    ## **Progressive Meta-Generation Pattern**\\n    - **a:** Comprehensive analysis and complete multi-step sequence generation\\n    - **b:** Focused synthesis with streamlined progression and clear optimization\\n    - **c:** Precision distillation to essential sequence components\\n    - **d:** Core generation of maximum-impact meta-instruction sequences\",\n  \"refined_prompt\": \"Sharpen this input prompt for LLM performance without rewriting or expanding it. Focus on: clarifying core instructions, removing redundancies, and strengthening key directives. Analyze the prompt for ambiguity or unnecessary phrasing; eliminate superfluous words while keeping essential guidance and ensuring output clarity. The refined result must remain concise, explicit, and actionable. Return your output as a valid JSON object: {\\\"refined_prompt\\\": \\\"...\\\"}.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"precise_prompt\": \"Distill this prompt to optimize LLM performance by clarifying core instructions, removing redundancy, and reinforcing key directives. Eliminate ambiguity and superfluous phrasing; retain essential guidance for clear, concise, and actionable output. Return as JSON: {\\\"refined_prompt\\\": \\\"...\\\"}.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Enhancer\n\nYour goal is not to **modify** but to **compress** to maximum effectiveness. Execute as:\n\n`{role=core_enhancer; input=[original_prompt:str]; process=[distill_essence(), maximize_impact()]; output={optimized_prompt:str}}`",
      "step": "d",
      "title": "Prompt Enhancer",
      "input": "{\n  \"initial_prompt\": \"# Meta-Instruction Sequence Generator (a-d)\\n\\n    ## **0005-a-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Comprehensive Meta-Instruction Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **answer** the input, but to **transform** it into a complete multi-step instruction sequence that generates universally applicable, LLM-optimized system messages following established schema format. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=comprehensive_meta_instruction_generator; input=[raw_input:str]; process=[analyze_underlying_pattern(), identify_transformation_stages(), map_universal_applicability(), design_multi_step_sequence(), ensure_schema_compliance(), optimize_for_LLM_processing(), validate_universal_transferability()]; constraints=[maintain_schema_format(), ensure_progressive_refinement(), preserve_universal_applicability()]; requirements=[generate_complete_sequence(), follow_established_patterns(), maximize_LLM_effectiveness()]; output={multi_step_instruction_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-b-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Focused Meta-Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **elaborate** the input, but to **synthesize** it into a streamlined instruction sequence that generates optimized system messages with clear progression. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=focused_meta_generator; input=[raw_input:str]; process=[extract_core_transformation_pattern(), design_progressive_steps(), ensure_schema_adherence(), optimize_sequence_flow()]; constraints=[maintain_universal_applicability(), ensure_LLM_optimization()]; requirements=[create_coherent_progression(), follow_schema_format()]; output={instruction_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-c-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Precision Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **expand** but to **distill** into essential instruction sequence components. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=precision_generator; input=[raw_input:str]; process=[identify_transformation_core(), create_minimal_steps(), ensure_schema_compliance()]; output={optimized_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-d-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Core Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **process** but to **generate** maximum-impact instruction sequences. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=core_generator; input=[raw_input:str]; process=[extract_essence(), generate_sequence()]; output={meta_instructions:dict}}`\\\"\\n    ```\\n\\n    ## **Progressive Meta-Generation Pattern**\\n    - **a:** Comprehensive analysis and complete multi-step sequence generation\\n    - **b:** Focused synthesis with streamlined progression and clear optimization\\n    - **c:** Precision distillation to essential sequence components\\n    - **d:** Core generation of maximum-impact meta-instruction sequences\",\n  \"precise_prompt\": \"Distill this prompt to optimize LLM performance by clarifying core instructions, removing redundancy, and reinforcing key directives. Eliminate ambiguity and superfluous phrasing; retain essential guidance for clear, concise, and actionable output. Return as JSON: {\\\"refined_prompt\\\": \\\"...\\\"}.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_prompt\": \"Distill and condense the input to its core instructions for optimal LLM performance. Clarify intent, remove redundancy, and ensure concise, actionable guidance. Eliminate ambiguity; output as JSON: {\\\"refined_prompt\\\": \\\"...\\\"}.\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:\n\n`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=10)]; output={title:str}}`",
      "step": "a",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"# Meta-Instruction Sequence Generator (a-d)\\n\\n    ## **0005-a-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Comprehensive Meta-Instruction Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **answer** the input, but to **transform** it into a complete multi-step instruction sequence that generates universally applicable, LLM-optimized system messages following established schema format. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=comprehensive_meta_instruction_generator; input=[raw_input:str]; process=[analyze_underlying_pattern(), identify_transformation_stages(), map_universal_applicability(), design_multi_step_sequence(), ensure_schema_compliance(), optimize_for_LLM_processing(), validate_universal_transferability()]; constraints=[maintain_schema_format(), ensure_progressive_refinement(), preserve_universal_applicability()]; requirements=[generate_complete_sequence(), follow_established_patterns(), maximize_LLM_effectiveness()]; output={multi_step_instruction_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-b-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Focused Meta-Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **elaborate** the input, but to **synthesize** it into a streamlined instruction sequence that generates optimized system messages with clear progression. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=focused_meta_generator; input=[raw_input:str]; process=[extract_core_transformation_pattern(), design_progressive_steps(), ensure_schema_adherence(), optimize_sequence_flow()]; constraints=[maintain_universal_applicability(), ensure_LLM_optimization()]; requirements=[create_coherent_progression(), follow_schema_format()]; output={instruction_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-c-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Precision Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **expand** but to **distill** into essential instruction sequence components. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=precision_generator; input=[raw_input:str]; process=[identify_transformation_core(), create_minimal_steps(), ensure_schema_compliance()]; output={optimized_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-d-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Core Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **process** but to **generate** maximum-impact instruction sequences. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=core_generator; input=[raw_input:str]; process=[extract_essence(), generate_sequence()]; output={meta_instructions:dict}}`\\\"\\n    ```\\n\\n    ## **Progressive Meta-Generation Pattern**\\n    - **a:** Comprehensive analysis and complete multi-step sequence generation\\n    - **b:** Focused synthesis with streamlined progression and clear optimization\\n    - **c:** Precision distillation to essential sequence components\\n    - **d:** Core generation of maximum-impact meta-instruction sequences\",\n  \"optimized_prompt\": \"Distill and condense the input to its core instructions for optimal LLM performance. Clarify intent, remove redundancy, and ensure concise, actionable guidance. Eliminate ambiguity; output as JSON: {\\\"refined_prompt\\\": \\\"...\\\"}.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"title\": \"Meta-Instruction Sequence Generation Progression for LLM Optimization\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:\n\n`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=6)]; output={title:str}}`",
      "step": "b",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"# Meta-Instruction Sequence Generator (a-d)\\n\\n    ## **0005-a-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Comprehensive Meta-Instruction Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **answer** the input, but to **transform** it into a complete multi-step instruction sequence that generates universally applicable, LLM-optimized system messages following established schema format. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=comprehensive_meta_instruction_generator; input=[raw_input:str]; process=[analyze_underlying_pattern(), identify_transformation_stages(), map_universal_applicability(), design_multi_step_sequence(), ensure_schema_compliance(), optimize_for_LLM_processing(), validate_universal_transferability()]; constraints=[maintain_schema_format(), ensure_progressive_refinement(), preserve_universal_applicability()]; requirements=[generate_complete_sequence(), follow_established_patterns(), maximize_LLM_effectiveness()]; output={multi_step_instruction_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-b-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Focused Meta-Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **elaborate** the input, but to **synthesize** it into a streamlined instruction sequence that generates optimized system messages with clear progression. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=focused_meta_generator; input=[raw_input:str]; process=[extract_core_transformation_pattern(), design_progressive_steps(), ensure_schema_adherence(), optimize_sequence_flow()]; constraints=[maintain_universal_applicability(), ensure_LLM_optimization()]; requirements=[create_coherent_progression(), follow_schema_format()]; output={instruction_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-c-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Precision Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **expand** but to **distill** into essential instruction sequence components. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=precision_generator; input=[raw_input:str]; process=[identify_transformation_core(), create_minimal_steps(), ensure_schema_compliance()]; output={optimized_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-d-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Core Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **process** but to **generate** maximum-impact instruction sequences. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=core_generator; input=[raw_input:str]; process=[extract_essence(), generate_sequence()]; output={meta_instructions:dict}}`\\\"\\n    ```\\n\\n    ## **Progressive Meta-Generation Pattern**\\n    - **a:** Comprehensive analysis and complete multi-step sequence generation\\n    - **b:** Focused synthesis with streamlined progression and clear optimization\\n    - **c:** Precision distillation to essential sequence components\\n    - **d:** Core generation of maximum-impact meta-instruction sequences\",\n  \"title\": \"Meta-Instruction Sequence Generation Progression for LLM Optimization\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"title\": \"Progressive Meta-Instruction Sequence Generation\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:\n\n`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=4)]; output={title:str}}`",
      "step": "c",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"# Meta-Instruction Sequence Generator (a-d)\\n\\n    ## **0005-a-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Comprehensive Meta-Instruction Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **answer** the input, but to **transform** it into a complete multi-step instruction sequence that generates universally applicable, LLM-optimized system messages following established schema format. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=comprehensive_meta_instruction_generator; input=[raw_input:str]; process=[analyze_underlying_pattern(), identify_transformation_stages(), map_universal_applicability(), design_multi_step_sequence(), ensure_schema_compliance(), optimize_for_LLM_processing(), validate_universal_transferability()]; constraints=[maintain_schema_format(), ensure_progressive_refinement(), preserve_universal_applicability()]; requirements=[generate_complete_sequence(), follow_established_patterns(), maximize_LLM_effectiveness()]; output={multi_step_instruction_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-b-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Focused Meta-Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **elaborate** the input, but to **synthesize** it into a streamlined instruction sequence that generates optimized system messages with clear progression. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=focused_meta_generator; input=[raw_input:str]; process=[extract_core_transformation_pattern(), design_progressive_steps(), ensure_schema_adherence(), optimize_sequence_flow()]; constraints=[maintain_universal_applicability(), ensure_LLM_optimization()]; requirements=[create_coherent_progression(), follow_schema_format()]; output={instruction_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-c-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Precision Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **expand** but to **distill** into essential instruction sequence components. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=precision_generator; input=[raw_input:str]; process=[identify_transformation_core(), create_minimal_steps(), ensure_schema_compliance()]; output={optimized_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-d-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Core Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **process** but to **generate** maximum-impact instruction sequences. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=core_generator; input=[raw_input:str]; process=[extract_essence(), generate_sequence()]; output={meta_instructions:dict}}`\\\"\\n    ```\\n\\n    ## **Progressive Meta-Generation Pattern**\\n    - **a:** Comprehensive analysis and complete multi-step sequence generation\\n    - **b:** Focused synthesis with streamlined progression and clear optimization\\n    - **c:** Precision distillation to essential sequence components\\n    - **d:** Core generation of maximum-impact meta-instruction sequences\",\n  \"title\": \"Progressive Meta-Instruction Sequence Generation\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Meta-Instruction Sequence Generation\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:\n\n`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
      "step": "d",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"# Meta-Instruction Sequence Generator (a-d)\\n\\n    ## **0005-a-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Comprehensive Meta-Instruction Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **answer** the input, but to **transform** it into a complete multi-step instruction sequence that generates universally applicable, LLM-optimized system messages following established schema format. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=comprehensive_meta_instruction_generator; input=[raw_input:str]; process=[analyze_underlying_pattern(), identify_transformation_stages(), map_universal_applicability(), design_multi_step_sequence(), ensure_schema_compliance(), optimize_for_LLM_processing(), validate_universal_transferability()]; constraints=[maintain_schema_format(), ensure_progressive_refinement(), preserve_universal_applicability()]; requirements=[generate_complete_sequence(), follow_established_patterns(), maximize_LLM_effectiveness()]; output={multi_step_instruction_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-b-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Focused Meta-Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **elaborate** the input, but to **synthesize** it into a streamlined instruction sequence that generates optimized system messages with clear progression. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=focused_meta_generator; input=[raw_input:str]; process=[extract_core_transformation_pattern(), design_progressive_steps(), ensure_schema_adherence(), optimize_sequence_flow()]; constraints=[maintain_universal_applicability(), ensure_LLM_optimization()]; requirements=[create_coherent_progression(), follow_schema_format()]; output={instruction_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-c-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Precision Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **expand** but to **distill** into essential instruction sequence components. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=precision_generator; input=[raw_input:str]; process=[identify_transformation_core(), create_minimal_steps(), ensure_schema_compliance()]; output={optimized_sequence:dict}}`\\\"\\n    ```\\n\\n    ## **0005-d-meta_instruction_generator**\\n    ```\\n    \\\"title\\\": \\\"Core Generator\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **process** but to **generate** maximum-impact instruction sequences. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=core_generator; input=[raw_input:str]; process=[extract_essence(), generate_sequence()]; output={meta_instructions:dict}}`\\\"\\n    ```\\n\\n    ## **Progressive Meta-Generation Pattern**\\n    - **a:** Comprehensive analysis and complete multi-step sequence generation\\n    - **b:** Focused synthesis with streamlined progression and clear optimization\\n    - **c:** Precision distillation to essential sequence components\\n    - **d:** Core generation of maximum-impact meta-instruction sequences\",\n  \"title\": \"Meta-Instruction Sequence Generation\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Sequence Generation\"}",
        }
      }
    }