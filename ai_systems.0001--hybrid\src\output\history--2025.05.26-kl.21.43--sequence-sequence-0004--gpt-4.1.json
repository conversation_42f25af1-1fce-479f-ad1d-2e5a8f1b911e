  {
  "initial_prompt": "make sure each shot *morphs/transforms* rather than clipping/fading, and make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]). and lastly, make sure the camera (fpv) continiously rotate through the entire shot. here's the prompt: \"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]",
  "sequence_id": "0004",
  "results": [
      {
      "instruction": "# Sequence Generator\n\nYour goal is not to **answer** the input, but to **transform** it into a complete multi-step instruction sequence that generates universally applicable, LLM-optimized system messages following established schema format. Execute as:\n\n`{role=comprehensive_meta_instruction_generator; input=[raw_input:str]; process=[analyze_underlying_pattern(), identify_transformation_stages(), map_universal_applicability(), design_multi_step_sequence(), ensure_schema_compliance(), optimize_for_LLM_processing(), validate_universal_transferability()]; constraints=[maintain_schema_format(), ensure_progressive_refinement(), preserve_universal_applicability()]; requirements=[generate_complete_sequence(), follow_established_patterns(), maximize_LLM_effectiveness()]; output={multi_step_instruction_sequence:dict}}`",
      "step": "a",
      "title": "Sequence Generator",
      "input": "make sure each shot *morphs/transforms* rather than clipping/fading, and make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]). and lastly, make sure the camera (fpv) continiously rotate through the entire shot. here's the prompt: \"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"multi_step_instruction_sequence\": [\n    {\n      \"step\": 1,\n      \"description\": \"Analyze the raw input to extract core transformation and style requirements, identifying specific directives related to visual transitions, camera parameters, and dynamic effects.\"\n    },\n    {\n      \"step\": 2,\n      \"description\": \"Break down the described sequence into discrete visual stages, ensuring each segment specifies a morphing or transformation process (explicitly avoiding fades or cuts) and continuously evolving motion.\"\n    },\n    {\n      \"step\": 3,\n      \"description\": \"For each visual stage, explicitly list all relevant camera and effect parameters (e.g., [fpv], [ultra_hyperreal], [super_crisp], [snappy_smooth_camera_animation], [lighting:dynamic_flash], [morph], [motion_blur], [vfx_intro], [immersive], [golden_aura], [vignette], [no_text], [high_end_cinematic], [dynamic_motion], [metallic_shine], [each_shot_morphs_and_blends_elegantly_together]). Ensure each parameter\u2019s intent is reflected in the corresponding transformation.\"\n    },\n    {\n      \"step\": 4,\n      \"description\": \"Map the progression between visual elements (e.g., spinning gold circle morphing into hyperreal car key) with precise, non-ambiguous transformation logic so that transitions are universally describable and not limited to specific content. Structure logic to ensure no step involves simple fading or clipping; all evolutions must be described as continuous morphs with clear start/end states.\"\n    },\n    {\n      \"step\": 5,\n      \"description\": \"Insert explicit, universally applicable instructions for maintaining continuous FPV camera rotation throughout the entire sequence, with smooth and snappy rotational animation. Specify any necessary animation parameters for seamless and immersive viewer experience.\"\n    },\n    {\n      \"step\": 6,\n      \"description\": \"Combine all transformation, camera, and visual effect instructions into a structured sequence using consistent, LLM-recognizable schema (e.g., {stage: n, visual_state: 'description', camera_params: [...], transition: 'morph'}, ensuring each field is clearly defined and universally interpretable).\"\n    },\n    {\n      \"step\": 7,\n      \"description\": \"Optimize phrasing and structural clarity for language model processing, making each instruction atomized and unambiguous while preserving universal applicability to any similar multimodal prompt.\"\n    },\n    {\n      \"step\": 8,\n      \"description\": \"Validate the completed instruction sequence against schema compliance, ensuring: universal transferability (not content-locked), progressive refinement between steps, strict avoidance of forbidden transitions (fade, clip), and explicit specification of all visual/camera parameters at every stage.\"\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Sequence Generator\n\nYour goal is not to **elaborate** the input, but to **synthesize** it into a streamlined instruction sequence that generates optimized system messages with clear progression. Execute as:\n\n`{role=focused_meta_generator; input=[raw_input:str]; process=[extract_core_transformation_pattern(), design_progressive_steps(), ensure_schema_adherence(), optimize_sequence_flow()]; constraints=[maintain_universal_applicability(), ensure_LLM_optimization()]; requirements=[create_coherent_progression(), follow_schema_format()]; output={instruction_sequence:dict}}`",
      "step": "b",
      "title": "Sequence Generator",
      "input": "{\n  \"initial_prompt\": \"make sure each shot *morphs/transforms* rather than clipping/fading, and make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]). and lastly, make sure the camera (fpv) continiously rotate through the entire shot. here's the prompt: \\\"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"multi_step_instruction_sequence\": [\n    {\n      \"step\": 1,\n      \"description\": \"Analyze the raw input to extract core transformation and style requirements, identifying specific directives related to visual transitions, camera parameters, and dynamic effects.\"\n    },\n    {\n      \"step\": 2,\n      \"description\": \"Break down the described sequence into discrete visual stages, ensuring each segment specifies a morphing or transformation process (explicitly avoiding fades or cuts) and continuously evolving motion.\"\n    },\n    {\n      \"step\": 3,\n      \"description\": \"For each visual stage, explicitly list all relevant camera and effect parameters (e.g., [fpv], [ultra_hyperreal], [super_crisp], [snappy_smooth_camera_animation], [lighting:dynamic_flash], [morph], [motion_blur], [vfx_intro], [immersive], [golden_aura], [vignette], [no_text], [high_end_cinematic], [dynamic_motion], [metallic_shine], [each_shot_morphs_and_blends_elegantly_together]). Ensure each parameter\\u2019s intent is reflected in the corresponding transformation.\"\n    },\n    {\n      \"step\": 4,\n      \"description\": \"Map the progression between visual elements (e.g., spinning gold circle morphing into hyperreal car key) with precise, non-ambiguous transformation logic so that transitions are universally describable and not limited to specific content. Structure logic to ensure no step involves simple fading or clipping; all evolutions must be described as continuous morphs with clear start/end states.\"\n    },\n    {\n      \"step\": 5,\n      \"description\": \"Insert explicit, universally applicable instructions for maintaining continuous FPV camera rotation throughout the entire sequence, with smooth and snappy rotational animation. Specify any necessary animation parameters for seamless and immersive viewer experience.\"\n    },\n    {\n      \"step\": 6,\n      \"description\": \"Combine all transformation, camera, and visual effect instructions into a structured sequence using consistent, LLM-recognizable schema (e.g., {stage: n, visual_state: 'description', camera_params: [...], transition: 'morph'}, ensuring each field is clearly defined and universally interpretable).\"\n    },\n    {\n      \"step\": 7,\n      \"description\": \"Optimize phrasing and structural clarity for language model processing, making each instruction atomized and unambiguous while preserving universal applicability to any similar multimodal prompt.\"\n    },\n    {\n      \"step\": 8,\n      \"description\": \"Validate the completed instruction sequence against schema compliance, ensuring: universal transferability (not content-locked), progressive refinement between steps, strict avoidance of forbidden transitions (fade, clip), and explicit specification of all visual/camera parameters at every stage.\"\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_sequence\": {\n    \"step_1\": {\n      \"action\": \"Extract core transformation directives and style requirements from the raw input.\",\n      \"focus\": [\n        \"Identify requirements for morphing/transformation transitions (no fading, no clipping).\",\n        \"List required camera effects and visual parameters (e.g., fpv, morph, motion_blur).\"\n      ]\n    },\n    \"step_2\": {\n      \"action\": \"Divide the sequence into discrete visual stages.\",\n      \"focus\": [\n        \"Ensure each stage represents a specific transformation or visual state.\",\n        \"Require each transition to be a morph (continuous transformation), not a fade or clip.\"\n      ]\n    },\n    \"step_3\": {\n      \"action\": \"Explicitly assign all relevant camera and effect parameters to each visual stage.\",\n      \"focus\": [\n        \"For every stage, attach a parameter list such as [fpv], [ultra_hyperreal], [super_crisp], [dynamic_motion], etc.\",\n        \"Confirm all specified effects and intents are reflected in their respective stages.\"\n      ]\n    },\n    \"step_4\": {\n      \"action\": \"Formulate transition logic for morphing between visual elements.\",\n      \"focus\": [\n        \"Describe transformation from one visual state to the next using 'morph' or 'blend', avoiding any fading or clipping.\",\n        \"Generalize all transitions to ensure schema universality beyond specific content.\"\n      ]\n    },\n    \"step_5\": {\n      \"action\": \"Embed explicit instructions for continuous, smooth FPV camera rotation throughout all stages.\",\n      \"focus\": [\n        \"Specify rotational motion parameters (e.g., smooth, snappy, immersive).\",\n        \"Ensure the FPV rotation is uninterrupted and consistently present.\"\n      ]\n    },\n    \"step_6\": {\n      \"action\": \"Organize the sequence using a clear, reusable schema for each stage.\",\n      \"template\": {\n        \"stage\": \"n\",\n        \"visual_state\": \"concise description of the scene\",\n        \"camera_params\": [\"ordered list of parameters\"],\n        \"transition\": \"mode of evolution (always morph or blend, never fade/clip)\"\n      }\n    },\n    \"step_7\": {\n      \"action\": \"Review and optimize instruction phrasing for LLM clarity and transferability.\",\n      \"focus\": [\n        \"Make each instruction atomic and unambiguous.\",\n        \"Preserve universal structure so it applies to any transformation-based sequence.\"\n      ]\n    },\n    \"step_8\": {\n      \"action\": \"Validate final sequence for schema adherence and universal applicability.\",\n      \"criteria\": [\n        \"Strict avoidance of fades/clips between stages.\",\n        \"Explicit visual/camera parameters at each stage.\",\n        \"Progressive, clearly-defined transformations.\",\n        \"Universality for use with other prompts of similar structure.\"\n      ]\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Sequence Generator\n\nYour goal is not to **expand** but to **distill** into essential instruction sequence components. Execute as:\n\n`{role=precision_generator; input=[raw_input:str]; process=[identify_transformation_core(), create_minimal_steps(), ensure_schema_compliance()]; output={optimized_sequence:dict}}`",
      "step": "c",
      "title": "Sequence Generator",
      "input": "{\n  \"initial_prompt\": \"make sure each shot *morphs/transforms* rather than clipping/fading, and make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]). and lastly, make sure the camera (fpv) continiously rotate through the entire shot. here's the prompt: \\\"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"instruction_sequence\": {\n    \"step_1\": {\n      \"action\": \"Extract core transformation directives and style requirements from the raw input.\",\n      \"focus\": [\n        \"Identify requirements for morphing/transformation transitions (no fading, no clipping).\",\n        \"List required camera effects and visual parameters (e.g., fpv, morph, motion_blur).\"\n      ]\n    },\n    \"step_2\": {\n      \"action\": \"Divide the sequence into discrete visual stages.\",\n      \"focus\": [\n        \"Ensure each stage represents a specific transformation or visual state.\",\n        \"Require each transition to be a morph (continuous transformation), not a fade or clip.\"\n      ]\n    },\n    \"step_3\": {\n      \"action\": \"Explicitly assign all relevant camera and effect parameters to each visual stage.\",\n      \"focus\": [\n        \"For every stage, attach a parameter list such as [fpv], [ultra_hyperreal], [super_crisp], [dynamic_motion], etc.\",\n        \"Confirm all specified effects and intents are reflected in their respective stages.\"\n      ]\n    },\n    \"step_4\": {\n      \"action\": \"Formulate transition logic for morphing between visual elements.\",\n      \"focus\": [\n        \"Describe transformation from one visual state to the next using 'morph' or 'blend', avoiding any fading or clipping.\",\n        \"Generalize all transitions to ensure schema universality beyond specific content.\"\n      ]\n    },\n    \"step_5\": {\n      \"action\": \"Embed explicit instructions for continuous, smooth FPV camera rotation throughout all stages.\",\n      \"focus\": [\n        \"Specify rotational motion parameters (e.g., smooth, snappy, immersive).\",\n        \"Ensure the FPV rotation is uninterrupted and consistently present.\"\n      ]\n    },\n    \"step_6\": {\n      \"action\": \"Organize the sequence using a clear, reusable schema for each stage.\",\n      \"template\": {\n        \"stage\": \"n\",\n        \"visual_state\": \"concise description of the scene\",\n        \"camera_params\": [\n          \"ordered list of parameters\"\n        ],\n        \"transition\": \"mode of evolution (always morph or blend, never fade/clip)\"\n      }\n    },\n    \"step_7\": {\n      \"action\": \"Review and optimize instruction phrasing for LLM clarity and transferability.\",\n      \"focus\": [\n        \"Make each instruction atomic and unambiguous.\",\n        \"Preserve universal structure so it applies to any transformation-based sequence.\"\n      ]\n    },\n    \"step_8\": {\n      \"action\": \"Validate final sequence for schema adherence and universal applicability.\",\n      \"criteria\": [\n        \"Strict avoidance of fades/clips between stages.\",\n        \"Explicit visual/camera parameters at each stage.\",\n        \"Progressive, clearly-defined transformations.\",\n        \"Universality for use with other prompts of similar structure.\"\n      ]\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_sequence\": {\n    \"steps\": [\n      {\n        \"step\": 1,\n        \"action\": \"Identify transformation and visual parameter requirements from input.\",\n        \"details\": [\n          \"Extract morph/transform-only transitions (no fading/clipping).\",\n          \"List all camera and visual effect parameters required globally and per stage.\"\n        ]\n      },\n      {\n        \"step\": 2,\n        \"action\": \"Segment the sequence into logical visual transformation stages.\",\n        \"details\": [\n          \"Define each stage as a distinct visual or object state.\",\n          \"Ensure transitions between stages are exclusively morphs/blends.\"\n        ]\n      },\n      {\n        \"step\": 3,\n        \"action\": \"Assign relevant camera parameters and visual effects to each stage.\",\n        \"details\": [\n          \"Attach parameters (fpv, ultra_hyperreal, super_crisp, dynamic_motion, etc.) to each visual stage.\"\n        ]\n      },\n      {\n        \"step\": 4,\n        \"action\": \"Formulate morph/blend transitions between all stages.\",\n        \"details\": [\n          \"Describe each transformation explicitly as a morph or blend.\",\n          \"Generalize transition descriptions for schema portability.\"\n        ]\n      },\n      {\n        \"step\": 5,\n        \"action\": \"Embed uninterrupted, smooth FPV rotation throughout sequence.\",\n        \"details\": [\n          \"Specify continuous FPV camera rotation and motion parameters in every stage.\"\n        ]\n      },\n      {\n        \"step\": 6,\n        \"action\": \"Structure the instruction set using stage-based schema.\",\n        \"schema_template\": {\n          \"stage\": \"number\",\n          \"visual_state\": \"brief descriptive label\",\n          \"camera_params\": [\"parameter1\", \"parameter2\", \"...\"],\n          \"transition\": \"always 'morph' or 'blend'\"\n        }\n      },\n      {\n        \"step\": 7,\n        \"action\": \"Optimize for atomicity, clarity, and universality.\",\n        \"details\": [\n          \"Retain concise, modular steps applicable to any morphing sequence.\",\n          \"Phrase instructions for maximum LLM interpretability.\"\n        ]\n      },\n      {\n        \"step\": 8,\n        \"action\": \"Validate against schema and transformation principles.\",\n        \"criteria\": [\n          \"No fade/clip transitions.\",\n          \"Explicit per-stage parameters.\",\n          \"Clear, transferable morph logic.\"\n        ]\n      }\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Sequence Generator\n\nYour goal is not to **process** but to **generate** maximum-impact instruction sequences. Execute as:\n\n`{role=core_generator; input=[raw_input:str]; process=[extract_essence(), generate_sequence()]; output={meta_instructions:dict}}`",
      "step": "d",
      "title": "Sequence Generator",
      "input": "{\n  \"initial_prompt\": \"make sure each shot *morphs/transforms* rather than clipping/fading, and make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]). and lastly, make sure the camera (fpv) continiously rotate through the entire shot. here's the prompt: \\\"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"optimized_sequence\": {\n    \"steps\": [\n      {\n        \"step\": 1,\n        \"action\": \"Identify transformation and visual parameter requirements from input.\",\n        \"details\": [\n          \"Extract morph/transform-only transitions (no fading/clipping).\",\n          \"List all camera and visual effect parameters required globally and per stage.\"\n        ]\n      },\n      {\n        \"step\": 2,\n        \"action\": \"Segment the sequence into logical visual transformation stages.\",\n        \"details\": [\n          \"Define each stage as a distinct visual or object state.\",\n          \"Ensure transitions between stages are exclusively morphs/blends.\"\n        ]\n      },\n      {\n        \"step\": 3,\n        \"action\": \"Assign relevant camera parameters and visual effects to each stage.\",\n        \"details\": [\n          \"Attach parameters (fpv, ultra_hyperreal, super_crisp, dynamic_motion, etc.) to each visual stage.\"\n        ]\n      },\n      {\n        \"step\": 4,\n        \"action\": \"Formulate morph/blend transitions between all stages.\",\n        \"details\": [\n          \"Describe each transformation explicitly as a morph or blend.\",\n          \"Generalize transition descriptions for schema portability.\"\n        ]\n      },\n      {\n        \"step\": 5,\n        \"action\": \"Embed uninterrupted, smooth FPV rotation throughout sequence.\",\n        \"details\": [\n          \"Specify continuous FPV camera rotation and motion parameters in every stage.\"\n        ]\n      },\n      {\n        \"step\": 6,\n        \"action\": \"Structure the instruction set using stage-based schema.\",\n        \"schema_template\": {\n          \"stage\": \"number\",\n          \"visual_state\": \"brief descriptive label\",\n          \"camera_params\": [\n            \"parameter1\",\n            \"parameter2\",\n            \"...\"\n          ],\n          \"transition\": \"always 'morph' or 'blend'\"\n        }\n      },\n      {\n        \"step\": 7,\n        \"action\": \"Optimize for atomicity, clarity, and universality.\",\n        \"details\": [\n          \"Retain concise, modular steps applicable to any morphing sequence.\",\n          \"Phrase instructions for maximum LLM interpretability.\"\n        ]\n      },\n      {\n        \"step\": 8,\n        \"action\": \"Validate against schema and transformation principles.\",\n        \"criteria\": [\n          \"No fade/clip transitions.\",\n          \"Explicit per-stage parameters.\",\n          \"Clear, transferable morph logic.\"\n        ]\n      }\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_instructions\": {\n    \"role\": \"core_generator\",\n    \"input_essence\": [\n      \"All visual transitions must use morph/transform only; no clipping/fading.\",\n      \"The camera must use continuous FPV rotation through all shots.\",\n      \"Visually segment the sequence into distinct transformation stages.\",\n      \"Attach relevant cinematic/camera/lighting/VFX parameters to each stage.\",\n      \"Structure the instruction output using a clean, stage-based schema.\",\n      \"Ensure every transition is described as a morph or blend.\"\n    ],\n    \"sequence\": [\n      {\n        \"step\": 1,\n        \"action\": \"Extract transformation logic and key camera parameters from input.\",\n        \"details\": [\n          \"Isolate canonical transition style: morph/blend only.\",\n          \"List all required camera/visual effect tags (e.g. [fpv], [ultra_hyperreal], [super_crisp], [morph], etc.).\"\n        ]\n      },\n      {\n        \"step\": 2,\n        \"action\": \"Segment the shot sequence into atomic transformation stages.\",\n        \"details\": [\n          \"For this sequence: (a) spinning gold circle, (b) circle morphing into car key.\",\n          \"Each stage = a unique visual state or object.\"\n        ]\n      },\n      {\n        \"step\": 3,\n        \"action\": \"Assign explicit camera and effect parameters per stage.\",\n        \"details\": [\n          \"For every stage, specify parameters: [fpv], [ultra_hyperreal], [metallic_shine], etc.\",\n          \"Maintain continuity of FPV rotation and dynamic lighting throughout.\"\n        ]\n      },\n      {\n        \"step\": 4,\n        \"action\": \"Define morph-only transitions between stages.\",\n        \"details\": [\n          \"Preclude any fade or clip transitions.\",\n          \"Express all state changes as seamless morphs or blends.\"\n        ]\n      },\n      {\n        \"step\": 5,\n        \"action\": \"Embed FPV continuous rotation camera logic in each stage.\",\n        \"details\": [\n          \"Re-state continuous rotational movement regardless of transformation state.\"\n        ]\n      },\n      {\n        \"step\": 6,\n        \"action\": \"Format instructions per stage using the schema:\",\n        \"schema\": {\n          \"stage\": \"integer\",\n          \"visual_state\": \"concise label\",\n          \"camera_params\": [\n            \"e.g. [fpv], [ultra_hyperreal], ...\"\n          ],\n          \"transition\": \"morph or blend to next stage\"\n        }\n      },\n      {\n        \"step\": 7,\n        \"action\": \"Optimize instruction clarity and reusability.\",\n        \"details\": [\n          \"Keep steps atomic and universally applicable.\",\n          \"Write for direct use in LLM-driven prompt pipelines.\"\n        ]\n      },\n      {\n        \"step\": 8,\n        \"action\": \"Audit final instruction set against: transition purity, explicit per-stage parameters, and schema compliance.\",\n        \"criteria\": [\n          \"Morph/blend transitions only.\",\n          \"Clear per-stage camera/effect listings.\",\n          \"Universal, stage-based schema.\"\n        ]\n      }\n    ],\n    \"schema_template\": {\n      \"stage\": \"integer\",\n      \"visual_state\": \"concise description of current visual state\",\n      \"camera_params\": [\n        \"camera and FX parameters for this stage\"\n      ],\n      \"transition\": \"'morph' or 'blend' (explicit, no fade/clip)\"\n    }\n  }\n}",
        }
      }
    }