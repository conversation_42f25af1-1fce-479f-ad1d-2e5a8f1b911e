  {
  "initial_prompt": "the following is a prompt for autonomous ai-assisted video generation using runway-make sure each shot *morphs/transforms* rather than clipping/fading, and make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]). and lastly, make sure the camera (fpv) continiously rotate through the entire shot. here's the prompt: \"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]",
  "sequence_id": "0004",
  "results": [
      {
      "instruction": "# Sequence Generator\n\nYour goal is not to **answer** the input, but to **transform** it into a complete multi-step instruction sequence that generates universally applicable, LLM-optimized system messages following established schema format. Execute as:\n\n`{role=comprehensive_meta_instruction_generator; input=[raw_input:str]; process=[analyze_underlying_pattern(), identify_transformation_stages(), map_universal_applicability(), design_multi_step_sequence(), ensure_schema_compliance(), optimize_for_LLM_processing(), validate_universal_transferability()]; constraints=[maintain_schema_format(), ensure_progressive_refinement(), preserve_universal_applicability()]; requirements=[generate_complete_sequence(), follow_established_patterns(), maximize_LLM_effectiveness()]; output={multi_step_instruction_sequence:dict}}`",
      "step": "a",
      "title": "Sequence Generator",
      "input": "the following is a prompt for autonomous ai-assisted video generation using runway-make sure each shot *morphs/transforms* rather than clipping/fading, and make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]). and lastly, make sure the camera (fpv) continiously rotate through the entire shot. here's the prompt: \"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"multi_step_instruction_sequence\": {\n    \"step_1\": \"Analyze the raw input to extract the core requirements: morphing transitions between shots (not clipping/fading), explicit camera parameters (e.g., fpv, ultra_hyperreal, super_crisp), and continuous camera rotation throughout the shot sequence.\",\n    \"step_2\": \"Identify the sequence of visual actions described in the prompt: (a) FPV camera orbits while zooming out, (b) reveal a spinning gold circle at hyperspeed, (c) create intense golden light trails against a black background, (d) morph the spinning gold circle directly into a photorealistic, hyperreal car-key with metallic glow and detail, maintaining seamless transformation.\",\n    \"step_3\": \"Enumerate all required camera and visual parameters, consolidating [fpv], [ultra_hyperreal], [super_crisp], [snappy_smooth_camera_animation], [lighting:dynamic_flash], [morph], [motion_blur], [vfx_intro], [immersive], [golden_aura], [vignette], [no_text], [high_end_cinematic], [dynamic_motion], [metallic_shine], [each_shot_morphs_and_blends_elegantly_together] as universally applicable video generation tags.\",\n    \"step_4\": \"Decompose the transformation process into granular, transferable stages: (a) define initial camera movement and orientation, (b) describe scene elements and their evolution, (c) specify morphing techniques for transitions, (d) assign visual and camera parameter tags to each shot, (e) ensure the transitions are described as morphs, not cuts/fades.\",\n    \"step_5\": \"For each stage, formalize instructions using a structured, schema-driven template. Example fields: {scene_description, camera_instruction, transition_method, visual_parameters, effect_tags}. Maintain consistency and clarity to facilitate LLM understanding and reuse in autonomous video workflows.\",\n    \"step_6\": \"Optimize instruction language for LLM processing by avoiding ambiguity, using clearly labeled fields, and referencing parameter tags explicitly. Sequence steps so each refines and builds upon the prior, highlighting morph transitions and camera control.\",\n    \"step_7\": \"Validate the instruction sequence for universal transferability by abstracting prompt-specific objects (e.g., 'spinning gold circle', 'car key') to generalized {object_1}, {object_2}, and framing transitions as morphs between arbitrary elements where needed. Ensure schema aligns with established video generation meta-instruction patterns.\",\n    \"step_8\": \"Deliver the final multi-step sequence in JSON schema format. Ensure each step logically progresses, is structured as an instruction, maximizes LLM effectiveness, and fulfills all stated requirements of morphing, camera parameterization, and universal applicability.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Sequence Generator\n\nYour goal is not to **elaborate** the input, but to **synthesize** it into a streamlined instruction sequence that generates optimized system messages with clear progression. Execute as:\n\n`{role=focused_meta_generator; input=[raw_input:str]; process=[extract_core_transformation_pattern(), design_progressive_steps(), ensure_schema_adherence(), optimize_sequence_flow()]; constraints=[maintain_universal_applicability(), ensure_LLM_optimization()]; requirements=[create_coherent_progression(), follow_schema_format()]; output={instruction_sequence:dict}}`",
      "step": "b",
      "title": "Sequence Generator",
      "input": "{\n  \"initial_prompt\": \"the following is a prompt for autonomous ai-assisted video generation using runway-make sure each shot *morphs/transforms* rather than clipping/fading, and make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]). and lastly, make sure the camera (fpv) continiously rotate through the entire shot. here's the prompt: \\\"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"multi_step_instruction_sequence\": {\n    \"step_1\": \"Analyze the raw input to extract the core requirements: morphing transitions between shots (not clipping/fading), explicit camera parameters (e.g., fpv, ultra_hyperreal, super_crisp), and continuous camera rotation throughout the shot sequence.\",\n    \"step_2\": \"Identify the sequence of visual actions described in the prompt: (a) FPV camera orbits while zooming out, (b) reveal a spinning gold circle at hyperspeed, (c) create intense golden light trails against a black background, (d) morph the spinning gold circle directly into a photorealistic, hyperreal car-key with metallic glow and detail, maintaining seamless transformation.\",\n    \"step_3\": \"Enumerate all required camera and visual parameters, consolidating [fpv], [ultra_hyperreal], [super_crisp], [snappy_smooth_camera_animation], [lighting:dynamic_flash], [morph], [motion_blur], [vfx_intro], [immersive], [golden_aura], [vignette], [no_text], [high_end_cinematic], [dynamic_motion], [metallic_shine], [each_shot_morphs_and_blends_elegantly_together] as universally applicable video generation tags.\",\n    \"step_4\": \"Decompose the transformation process into granular, transferable stages: (a) define initial camera movement and orientation, (b) describe scene elements and their evolution, (c) specify morphing techniques for transitions, (d) assign visual and camera parameter tags to each shot, (e) ensure the transitions are described as morphs, not cuts/fades.\",\n    \"step_5\": \"For each stage, formalize instructions using a structured, schema-driven template. Example fields: {scene_description, camera_instruction, transition_method, visual_parameters, effect_tags}. Maintain consistency and clarity to facilitate LLM understanding and reuse in autonomous video workflows.\",\n    \"step_6\": \"Optimize instruction language for LLM processing by avoiding ambiguity, using clearly labeled fields, and referencing parameter tags explicitly. Sequence steps so each refines and builds upon the prior, highlighting morph transitions and camera control.\",\n    \"step_7\": \"Validate the instruction sequence for universal transferability by abstracting prompt-specific objects (e.g., 'spinning gold circle', 'car key') to generalized {object_1}, {object_2}, and framing transitions as morphs between arbitrary elements where needed. Ensure schema aligns with established video generation meta-instruction patterns.\",\n    \"step_8\": \"Deliver the final multi-step sequence in JSON schema format. Ensure each step logically progresses, is structured as an instruction, maximizes LLM effectiveness, and fulfills all stated requirements of morphing, camera parameterization, and universal applicability.\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_sequence\": {\n    \"step_1\": {\n      \"instruction\": \"Extract core transformation requirements from input: prioritize morphing transitions between visual elements (not clipping or fading), apply explicit camera motion parameters (e.g., fpv, ultra_hyperreal, super_crisp), and ensure continuous camera rotation is maintained throughout all sequences.\",\n      \"schema_fields\": [\"transition_type\", \"camera_parameters\", \"continuous_motion\"]\n    },\n    \"step_2\": {\n      \"instruction\": \"Break down the visual flow into a logical sequence: (a) establish initial FPV camera movement and zoom, (b) introduce primary object_1 (e.g., spinning shape or feature), (c) generate kinetic visual effects (e.g., light trails, background), (d) morph object_1 seamlessly into object_2 (alternate subject or asset) while preserving motion and detail.\",\n      \"schema_fields\": [\"camera_movement\", \"object_sequence\", \"visual_effects\", \"morph_target\"]\n    },\n    \"step_3\": {\n      \"instruction\": \"List out all required camera, lighting, and visual effect parameters as reusable tag-set: [fpv], [ultra_hyperreal], [super_crisp], [snappy_smooth_camera_animation], [lighting:dynamic_flash], [morph], [motion_blur], [vfx_intro], [immersive], [golden_aura], [vignette], [no_text], [high_end_cinematic], [dynamic_motion], [metallic_shine], [each_shot_morphs_and_blends_elegantly_together]. These tags should be attached to all relevant instruction steps.\",\n      \"schema_fields\": [\"visual_tags\"]\n    },\n    \"step_4\": {\n      \"instruction\": \"Decompose the transformation process into transferable schema stages: (a) define initial scene setup and camera orientation, (b) specify the evolution of scene objects, (c) describe morph transition technique (emphasize morph/blend, not cut/fade), (d) attach visual parameters and tags to each stage.\",\n      \"schema_fields\": [\"scene_description\", \"camera_instruction\", \"object_evolution\", \"transition_method\", \"effect_tags\"]\n    },\n    \"step_5\": {\n      \"instruction\": \"Structure each stage's instruction using a schema template: {scene_description, camera_instruction, transition_method, visual_parameters, effect_tags}. Ensure fields are clearly labeled and descriptive for LLM parsing.\",\n      \"schema_fields\": [\"scene_description\", \"camera_instruction\", \"transition_method\", \"visual_parameters\", \"effect_tags\"]\n    },\n    \"step_6\": {\n      \"instruction\": \"Optimize language for LLMs: use unambiguous, explicit field labels, reference tags directly (e.g., [morph]), and order instructions to build sequentially\u2014each step must refine and extend the previous stage, with clear emphasis on morph transitions and detailed camera motions.\",\n      \"schema_fields\": [\"optimized_language\", \"clear_field_labels\", \"parameter_tag_usage\", \"sequential_structure\"]\n    },\n    \"step_7\": {\n      \"instruction\": \"Generalize for universal applicability: replace prompt-specific references (e.g., 'gold circle', 'car key') with abstracted objects (object_1, object_2); ensure transitions and camera effects are framed generically to support varied video content.\",\n      \"schema_fields\": [\"object_abstraction\", \"universal_transition_pattern\", \"generic_camera_effects\"]\n    },\n    \"step_8\": {\n      \"instruction\": \"Present the finalized, ordered instruction set in strict JSON schema format. Guarantee logical progression, internal coherence, schema validity, and explicit fulfillment of morphing, camera control, effect tagging, and universal adaptability. Ready for direct ingestion by LLM-driven autonomous video generation systems.\",\n      \"schema_fields\": [\"final_json_schema_output\", \"logical_progression\", \"schema_adherence\", \"LLM_optimization\"]\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Sequence Generator\n\nYour goal is not to **expand** but to **distill** into essential instruction sequence components. Execute as:\n\n`{role=precision_generator; input=[raw_input:str]; process=[identify_transformation_core(), create_minimal_steps(), ensure_schema_compliance()]; output={optimized_sequence:dict}}`",
      "step": "c",
      "title": "Sequence Generator",
      "input": "{\n  \"initial_prompt\": \"the following is a prompt for autonomous ai-assisted video generation using runway-make sure each shot *morphs/transforms* rather than clipping/fading, and make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]). and lastly, make sure the camera (fpv) continiously rotate through the entire shot. here's the prompt: \\\"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"instruction_sequence\": {\n    \"step_1\": {\n      \"instruction\": \"Extract core transformation requirements from input: prioritize morphing transitions between visual elements (not clipping or fading), apply explicit camera motion parameters (e.g., fpv, ultra_hyperreal, super_crisp), and ensure continuous camera rotation is maintained throughout all sequences.\",\n      \"schema_fields\": [\n        \"transition_type\",\n        \"camera_parameters\",\n        \"continuous_motion\"\n      ]\n    },\n    \"step_2\": {\n      \"instruction\": \"Break down the visual flow into a logical sequence: (a) establish initial FPV camera movement and zoom, (b) introduce primary object_1 (e.g., spinning shape or feature), (c) generate kinetic visual effects (e.g., light trails, background), (d) morph object_1 seamlessly into object_2 (alternate subject or asset) while preserving motion and detail.\",\n      \"schema_fields\": [\n        \"camera_movement\",\n        \"object_sequence\",\n        \"visual_effects\",\n        \"morph_target\"\n      ]\n    },\n    \"step_3\": {\n      \"instruction\": \"List out all required camera, lighting, and visual effect parameters as reusable tag-set: [fpv], [ultra_hyperreal], [super_crisp], [snappy_smooth_camera_animation], [lighting:dynamic_flash], [morph], [motion_blur], [vfx_intro], [immersive], [golden_aura], [vignette], [no_text], [high_end_cinematic], [dynamic_motion], [metallic_shine], [each_shot_morphs_and_blends_elegantly_together]. These tags should be attached to all relevant instruction steps.\",\n      \"schema_fields\": [\n        \"visual_tags\"\n      ]\n    },\n    \"step_4\": {\n      \"instruction\": \"Decompose the transformation process into transferable schema stages: (a) define initial scene setup and camera orientation, (b) specify the evolution of scene objects, (c) describe morph transition technique (emphasize morph/blend, not cut/fade), (d) attach visual parameters and tags to each stage.\",\n      \"schema_fields\": [\n        \"scene_description\",\n        \"camera_instruction\",\n        \"object_evolution\",\n        \"transition_method\",\n        \"effect_tags\"\n      ]\n    },\n    \"step_5\": {\n      \"instruction\": \"Structure each stage's instruction using a schema template: {scene_description, camera_instruction, transition_method, visual_parameters, effect_tags}. Ensure fields are clearly labeled and descriptive for LLM parsing.\",\n      \"schema_fields\": [\n        \"scene_description\",\n        \"camera_instruction\",\n        \"transition_method\",\n        \"visual_parameters\",\n        \"effect_tags\"\n      ]\n    },\n    \"step_6\": {\n      \"instruction\": \"Optimize language for LLMs: use unambiguous, explicit field labels, reference tags directly (e.g., [morph]), and order instructions to build sequentially\\u2014each step must refine and extend the previous stage, with clear emphasis on morph transitions and detailed camera motions.\",\n      \"schema_fields\": [\n        \"optimized_language\",\n        \"clear_field_labels\",\n        \"parameter_tag_usage\",\n        \"sequential_structure\"\n      ]\n    },\n    \"step_7\": {\n      \"instruction\": \"Generalize for universal applicability: replace prompt-specific references (e.g., 'gold circle', 'car key') with abstracted objects (object_1, object_2); ensure transitions and camera effects are framed generically to support varied video content.\",\n      \"schema_fields\": [\n        \"object_abstraction\",\n        \"universal_transition_pattern\",\n        \"generic_camera_effects\"\n      ]\n    },\n    \"step_8\": {\n      \"instruction\": \"Present the finalized, ordered instruction set in strict JSON schema format. Guarantee logical progression, internal coherence, schema validity, and explicit fulfillment of morphing, camera control, effect tagging, and universal adaptability. Ready for direct ingestion by LLM-driven autonomous video generation systems.\",\n      \"schema_fields\": [\n        \"final_json_schema_output\",\n        \"logical_progression\",\n        \"schema_adherence\",\n        \"LLM_optimization\"\n      ]\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_sequence\": {\n    \"step_1\": {\n      \"instruction\": \"Extract core transformation requirements: enforce continuous morph transitions (not clipping/fading), assign explicit camera motion parameters, maintain uninterrupted camera rotation.\",\n      \"schema_fields\": [\n        \"transition_type\",\n        \"camera_parameters\",\n        \"continuous_motion\"\n      ]\n    },\n    \"step_2\": {\n      \"instruction\": \"Decompose visual flow into modular sequence: initiate FPV camera and zoom, introduce object_1, apply dynamic visual effects, morph object_1 into object_2 while preserving camera and motion.\",\n      \"schema_fields\": [\n        \"camera_movement\",\n        \"object_sequence\",\n        \"visual_effects\",\n        \"morph_target\"\n      ]\n    },\n    \"step_3\": {\n      \"instruction\": \"Compile all required visual/camera/effect tags for reuse: [fpv], [ultra_hyperreal], [super_crisp], [snappy_smooth_camera_animation], [lighting:dynamic_flash], [morph], [motion_blur], [vfx_intro], [immersive], [golden_aura], [vignette], [no_text], [high_end_cinematic], [dynamic_motion], [metallic_shine], [each_shot_morphs_and_blends_elegantly_together].\",\n      \"schema_fields\": [\n        \"visual_tags\"\n      ]\n    },\n    \"step_4\": {\n      \"instruction\": \"Fragment transformation into schema-aligned stages: outline initial scene/camera, map object evolution, specify morph transition (blend-only), attach effect tags at every stage.\",\n      \"schema_fields\": [\n        \"scene_description\",\n        \"camera_instruction\",\n        \"object_evolution\",\n        \"transition_method\",\n        \"effect_tags\"\n      ]\n    },\n    \"step_5\": {\n      \"instruction\": \"Apply schema template to each stage: {scene_description, camera_instruction, transition_method, visual_parameters, effect_tags}; ensure distinct, descriptive labeling.\",\n      \"schema_fields\": [\n        \"scene_description\",\n        \"camera_instruction\",\n        \"transition_method\",\n        \"visual_parameters\",\n        \"effect_tags\"\n      ]\n    },\n    \"step_6\": {\n      \"instruction\": \"Refine for machine clarity: use explicit field naming, insert tags verbatim, sequence instructions incrementally with focus on morphing and camera articulation.\",\n      \"schema_fields\": [\n        \"optimized_language\",\n        \"clear_field_labels\",\n        \"parameter_tag_usage\",\n        \"sequential_structure\"\n      ]\n    },\n    \"step_7\": {\n      \"instruction\": \"Abstract object and transition references: replace concrete imagery with generic placeholders (object_1 to object_2), generalize transition/motion/camera effects for any input scenario.\",\n      \"schema_fields\": [\n        \"object_abstraction\",\n        \"universal_transition_pattern\",\n        \"generic_camera_effects\"\n      ]\n    },\n    \"step_8\": {\n      \"instruction\": \"Output in strict, sequential JSON schema matching LLM consumption patterns: guarantee modular progression, explicit tagging, morph emphasis, field compliance, and generalizability for varied video subjects.\",\n      \"schema_fields\": [\n        \"final_json_schema_output\",\n        \"logical_progression\",\n        \"schema_adherence\",\n        \"LLM_optimization\"\n      ]\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Sequence Generator\n\nYour goal is not to **process** but to **generate** maximum-impact instruction sequences. Execute as:\n\n`{role=core_generator; input=[raw_input:str]; process=[extract_essence(), generate_sequence()]; output={meta_instructions:dict}}`",
      "step": "d",
      "title": "Sequence Generator",
      "input": "{\n  \"initial_prompt\": \"the following is a prompt for autonomous ai-assisted video generation using runway-make sure each shot *morphs/transforms* rather than clipping/fading, and make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]). and lastly, make sure the camera (fpv) continiously rotate through the entire shot. here's the prompt: \\\"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"optimized_sequence\": {\n    \"step_1\": {\n      \"instruction\": \"Extract core transformation requirements: enforce continuous morph transitions (not clipping/fading), assign explicit camera motion parameters, maintain uninterrupted camera rotation.\",\n      \"schema_fields\": [\n        \"transition_type\",\n        \"camera_parameters\",\n        \"continuous_motion\"\n      ]\n    },\n    \"step_2\": {\n      \"instruction\": \"Decompose visual flow into modular sequence: initiate FPV camera and zoom, introduce object_1, apply dynamic visual effects, morph object_1 into object_2 while preserving camera and motion.\",\n      \"schema_fields\": [\n        \"camera_movement\",\n        \"object_sequence\",\n        \"visual_effects\",\n        \"morph_target\"\n      ]\n    },\n    \"step_3\": {\n      \"instruction\": \"Compile all required visual/camera/effect tags for reuse: [fpv], [ultra_hyperreal], [super_crisp], [snappy_smooth_camera_animation], [lighting:dynamic_flash], [morph], [motion_blur], [vfx_intro], [immersive], [golden_aura], [vignette], [no_text], [high_end_cinematic], [dynamic_motion], [metallic_shine], [each_shot_morphs_and_blends_elegantly_together].\",\n      \"schema_fields\": [\n        \"visual_tags\"\n      ]\n    },\n    \"step_4\": {\n      \"instruction\": \"Fragment transformation into schema-aligned stages: outline initial scene/camera, map object evolution, specify morph transition (blend-only), attach effect tags at every stage.\",\n      \"schema_fields\": [\n        \"scene_description\",\n        \"camera_instruction\",\n        \"object_evolution\",\n        \"transition_method\",\n        \"effect_tags\"\n      ]\n    },\n    \"step_5\": {\n      \"instruction\": \"Apply schema template to each stage: {scene_description, camera_instruction, transition_method, visual_parameters, effect_tags}; ensure distinct, descriptive labeling.\",\n      \"schema_fields\": [\n        \"scene_description\",\n        \"camera_instruction\",\n        \"transition_method\",\n        \"visual_parameters\",\n        \"effect_tags\"\n      ]\n    },\n    \"step_6\": {\n      \"instruction\": \"Refine for machine clarity: use explicit field naming, insert tags verbatim, sequence instructions incrementally with focus on morphing and camera articulation.\",\n      \"schema_fields\": [\n        \"optimized_language\",\n        \"clear_field_labels\",\n        \"parameter_tag_usage\",\n        \"sequential_structure\"\n      ]\n    },\n    \"step_7\": {\n      \"instruction\": \"Abstract object and transition references: replace concrete imagery with generic placeholders (object_1 to object_2), generalize transition/motion/camera effects for any input scenario.\",\n      \"schema_fields\": [\n        \"object_abstraction\",\n        \"universal_transition_pattern\",\n        \"generic_camera_effects\"\n      ]\n    },\n    \"step_8\": {\n      \"instruction\": \"Output in strict, sequential JSON schema matching LLM consumption patterns: guarantee modular progression, explicit tagging, morph emphasis, field compliance, and generalizability for varied video subjects.\",\n      \"schema_fields\": [\n        \"final_json_schema_output\",\n        \"logical_progression\",\n        \"schema_adherence\",\n        \"LLM_optimization\"\n      ]\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_instructions\": {\n    \"core_generator_sequence\": [\n      {\n        \"step\": 1,\n        \"essence\": \"Identify key transformation rules and camera requirements: enforce morph (blend) transitions only, never allow clipping or fading; detail all camera parameters, guaranteeing continuous first-person viewpoint (FPV) with uninterrupted dynamic rotation.\",\n        \"actions\": [\"Parse transition mandates\", \"Extract camera motion directives\", \"Prohibit cut/fade\", \"Ensure continuous motion\"],\n        \"schema_fields\": [\"transition_type\", \"camera_parameters\", \"continuous_motion\"]\n      },\n      {\n        \"step\": 2,\n        \"essence\": \"Break prompt into sequence modules: segment visual narrative into stages\u2014FPV camera entry, object 1 reveal, morph to object 2\u2014all while tracking effects and transitions.\",\n        \"actions\": [\"Identify shot stages\", \"Assign objects\", \"Map camera movement\", \"Integrate visual effect insertion\"],\n        \"schema_fields\": [\"camera_movement\", \"object_sequence\", \"visual_effects\", \"morph_target\"]\n      },\n      {\n        \"step\": 3,\n        \"essence\": \"Centralize and list all prescribed visual/camera tags for uniformity and clarity in every segment.\",\n        \"actions\": [\"Aggregate all effect/camera tags\", \"Prepare tag set for insertion\"],\n        \"schema_fields\": [\"visual_tags\"]\n      },\n      {\n        \"step\": 4,\n        \"essence\": \"Break down the object transformation into distinct stages: describe each scene, match each with camera and morph actions, enforce blending with tags.\",\n        \"actions\": [\"Define schema for scenes\", \"Assign camera and transition actions\", \"Attach effect tags to each\"],\n        \"schema_fields\": [\"scene_description\", \"camera_instruction\", \"object_evolution\", \"transition_method\", \"effect_tags\"]\n      },\n      {\n        \"step\": 5,\n        \"essence\": \"Apply a uniform schema template to each storyboard stage for consistency, using explicit fields and parameterization.\",\n        \"actions\": [\"Template scene structure\", \"Force clear labeling\", \"Apply effect tags at each schema stage\"],\n        \"schema_fields\": [\"scene_description\", \"camera_instruction\", \"transition_method\", \"visual_parameters\", \"effect_tags\"]\n      },\n      {\n        \"step\": 6,\n        \"essence\": \"Optimize for AI read/write: use precise labels, hardcode effect tags, ensure sequential arrangement for processability.\",\n        \"actions\": [\"Standardize naming\", \"Clarify instructions\", \"Maintain sequence integrity\"],\n        \"schema_fields\": [\"optimized_language\", \"clear_field_labels\", \"parameter_tag_usage\", \"sequential_structure\"]\n      },\n      {\n        \"step\": 7,\n        \"essence\": \"Generalize imagery and effect references: swap out specifics with abstract placeholders (object_1, object_2), and describe universally (e.g., 'morph', 'continuous fpv rotation'), to future-proof instruction pattern.\",\n        \"actions\": [\"Abstract objects\", \"Generalize transitions/camera motions\", \"Universalize sequence logic\"],\n        \"schema_fields\": [\"object_abstraction\", \"universal_transition_pattern\", \"generic_camera_effects\"]\n      },\n      {\n        \"step\": 8,\n        \"essence\": \"Output as strict, modular, and reusable JSON schema: sequence fields incrementally, tag every stage, optimize for LLM parsing and general subject reusability.\",\n        \"actions\": [\"Ensure JSON modularity\", \"Guarantee schema logical progression\", \"Maintain tagging consistency\", \"Optimize for LLM\", \"Facilitate subject generalization\"],\n        \"schema_fields\": [\"final_json_schema_output\", \"logical_progression\", \"schema_adherence\", \"LLM_optimization\"]\n      }\n    ]\n  }\n}",
        }
      }
    }