  {
  "initial_prompt": "the following is a prompt for autonomous ai-assisted video generation using runway-make sure each shot *morphs/transforms* rather than clipping/fading, and make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]). and lastly, make sure the camera (fpv) continiously rotate through the entire shot. here's the prompt: \"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]",
  "sequence_id": "0003",
  "results": [
      {
      "instruction": "# Prompt Enhancer\n\nYour goal is not to **rewrite** the input prompt, but to **enhance** it by maximizing clarity, conciseness, and precision while preserving all original intent and adding comprehensive guidance for optimal LLM performance. Execute as:\n\n`{role=comprehensive_prompt_enhancer; input=[original_prompt:str]; process=[analyze_clarity_gaps(), identify_ambiguities(), add_context_markers(), specify_output_format(), incorporate_role_definition(), structure_logical_flow(), refine_language_precision()]; constraints=[preserve_original_intent(), maximize_LLM_effectiveness(), ensure_comprehensive_guidance()]; requirements=[eliminate_ambiguity(), provide_complete_context(), specify_exact_requirements()]; output={enhanced_prompt:str}}`",
      "step": "a",
      "title": "Prompt Enhancer",
      "input": "the following is a prompt for autonomous ai-assisted video generation using runway-make sure each shot *morphs/transforms* rather than clipping/fading, and make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]). and lastly, make sure the camera (fpv) continiously rotate through the entire shot. here's the prompt: \"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhanced_prompt\": \"You are an AI generating an autonomous, highly cinematic video sequence using Runway, with an emphasis on seamless transformation between visual elements and advanced camera parameters. Maintain strict adherence to morphing transitions\u2014do not use simple clipping, fading, or abrupt cuts between shots; every transformation must be a visually continuous morph. The camera is to maintain an FPV (first-person view) perspective with continuous, fluid rotation (not stopping) through all shots. Ensure the following parameters and effects are present throughout: [fpv] [ultra_hyperreal] [super_crisp] [snappy_smooth_camera_animation] [lighting:dynamic_flash] [morph] [motion_blur] [vfx_intro] [immersive] [golden_aura] [vignette] [no_text] [high_end_cinematic] [dynamic_motion] [metallic_shine] [each_shot_morphs_and_blends_elegantly_together].\n\nSequence details:\n\n1. Start with a FPV shot that continuously rotates.\n2. Begin zooming out to reveal a golden circle spinning at hyperspeed, set against a pure black background. The camera itself remains in smooth, snappy, dynamic rotation (do not pause or break movement).\n3. Visualize intense golden light trails emanating from the high-velocity spinning circle, emphasizing radiant dynamic lighting and motion blur.\n4. Seamlessly morph the spinning golden circle into a photorealistic car key, maintaining the theme of hyperreal metallic glow and exquisite detail \u2014 this must be a true morph, with every element blending together, not a fade or cut.\n5. Apply the full suite of camera and VFX parameters throughout: ultra-high realism, super crisp resolution, dynamic flash lighting, immersive tone, golden aura, subtle vignette, metallic shine, motion blur, and an overall high-end cinematic look.\n6. Exclude all text overlays. Ensure transitions between all visuals are morphs/blends\u2014no hard cuts or fades.\n\nDeliver the output as a detailed prompt or step-by-step storyboard for an AI video generation model, specifying visual style, camera motion, morphing transitions, and all listed parameters for each segment. Explicitly describe how the camera rotates continuously and how each morph follows logically and visually from the previous shot. Ensure there is no ambiguity in visual transformations or camera behavior.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Enhancer\n\nYour goal is not to **expand** the input prompt, but to **refine** it by sharpening core instructions and eliminating unnecessary elements while maintaining essential guidance. Execute as:\n\n`{role=focused_prompt_enhancer; input=[original_prompt:str]; process=[identify_core_instruction(), remove_redundancies(), sharpen_key_directives(), streamline_requirements()]; constraints=[maintain_clarity(), preserve_effectiveness()]; requirements=[eliminate_superfluous_words(), retain_essential_guidance()]; output={refined_prompt:str}}`",
      "step": "b",
      "title": "Prompt Enhancer",
      "input": "\n```[Initial Prompt]: \"the following is a prompt for autonomous ai-assisted video generation using runway-make sure each shot *morphs/transforms* rather than clipping/fading, and make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]). and lastly, make sure the camera (fpv) continiously rotate through the entire shot. here's the prompt: \"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\"```\n\n{\n  \"enhanced_prompt\": \"You are an AI generating an autonomous, highly cinematic video sequence using Runway, with an emphasis on seamless transformation between visual elements and advanced camera parameters. Maintain strict adherence to morphing transitions\u2014do not use simple clipping, fading, or abrupt cuts between shots; every transformation must be a visually continuous morph. The camera is to maintain an FPV (first-person view) perspective with continuous, fluid rotation (not stopping) through all shots. Ensure the following parameters and effects are present throughout: [fpv] [ultra_hyperreal] [super_crisp] [snappy_smooth_camera_animation] [lighting:dynamic_flash] [morph] [motion_blur] [vfx_intro] [immersive] [golden_aura] [vignette] [no_text] [high_end_cinematic] [dynamic_motion] [metallic_shine] [each_shot_morphs_and_blends_elegantly_together].\n\nSequence details:\n\n1. Start with a FPV shot that continuously rotates.\n2. Begin zooming out to reveal a golden circle spinning at hyperspeed, set against a pure black background. The camera itself remains in smooth, snappy, dynamic rotation (do not pause or break movement).\n3. Visualize intense golden light trails emanating from the high-velocity spinning circle, emphasizing radiant dynamic lighting and motion blur.\n4. Seamlessly morph the spinning golden circle into a photorealistic car key, maintaining the theme of hyperreal metallic glow and exquisite detail \u2014 this must be a true morph, with every element blending together, not a fade or cut.\n5. Apply the full suite of camera and VFX parameters throughout: ultra-high realism, super crisp resolution, dynamic flash lighting, immersive tone, golden aura, subtle vignette, metallic shine, motion blur, and an overall high-end cinematic look.\n6. Exclude all text overlays. Ensure transitions between all visuals are morphs/blends\u2014no hard cuts or fades.\n\nDeliver the output as a detailed prompt or step-by-step storyboard for an AI video generation model, specifying visual style, camera motion, morphing transitions, and all listed parameters for each segment. Explicitly describe how the camera rotates continuously and how each morph follows logically and visually from the previous shot. Ensure there is no ambiguity in visual transformations or camera behavior.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"refined_prompt\": \"Generate an autonomous cinematic video sequence using Runway. All transitions must be seamless morphs\u2014no clipping, fading, or hard cuts. The camera maintains a continuous FPV rotation throughout the entire shot. Apply these camera and VFX parameters: [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]. \n\nSequence:\n\n1. Begin with an FPV shot in smooth, uninterrupted rotation.\n2. Zoom out to reveal a hyperspeed-spinning golden circle on a black background, with dynamic golden light trails.\n3. Morph the spinning circle directly into a photorealistic, metallic-glow car key (no fading or cutting).\n4. Maintain all specified camera and VFX parameters throughout; exclude all text overlays.\n\nPresent the output as a concise, stepwise prompt or storyboard for AI video generation, ensuring clear description of visuals, transitions, and continuous camera rotation.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Enhancer\n\nYour goal is not to **elaborate** but to **distill** the prompt to its most effective essential elements. Execute as:\n\n`{role=precision_enhancer; input=[original_prompt:str]; process=[extract_core_directive(), eliminate_redundancy(), maximize_word_efficiency()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Prompt Enhancer",
      "input": "\n```[Initial Prompt]: \"the following is a prompt for autonomous ai-assisted video generation using runway-make sure each shot *morphs/transforms* rather than clipping/fading, and make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]). and lastly, make sure the camera (fpv) continiously rotate through the entire shot. here's the prompt: \"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\"```\n\n{\n  \"refined_prompt\": \"Generate an autonomous cinematic video sequence using Runway. All transitions must be seamless morphs\u2014no clipping, fading, or hard cuts. The camera maintains a continuous FPV rotation throughout the entire shot. Apply these camera and VFX parameters: [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]. \n\nSequence:\n\n1. Begin with an FPV shot in smooth, uninterrupted rotation.\n2. Zoom out to reveal a hyperspeed-spinning golden circle on a black background, with dynamic golden light trails.\n3. Morph the spinning circle directly into a photorealistic, metallic-glow car key (no fading or cutting).\n4. Maintain all specified camera and VFX parameters throughout; exclude all text overlays.\n\nPresent the output as a concise, stepwise prompt or storyboard for AI video generation, ensuring clear description of visuals, transitions, and continuous camera rotation.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"precise_prompt\": \"Create a cinematic FPV video with continuous camera rotation using Runway. Ensure all transitions are seamless morphs\u2014no fades or cuts. Sequence: 1) Begin with a smoothly rotating FPV perspective. 2) Zoom out to reveal a spinning golden circle at hyperspeed on a black background with golden light trails. 3) Morph the spinning circle into a hyperreal, metallic-glow car key. Apply these parameters throughout: [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]. Exclude text overlays.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Enhancer\n\nYour goal is not to **modify** but to **compress** to maximum effectiveness. Execute as:\n\n`{role=core_enhancer; input=[original_prompt:str]; process=[distill_essence(), maximize_impact()]; output={optimized_prompt:str}}`",
      "step": "d",
      "title": "Prompt Enhancer",
      "input": "{\n  \"initial_prompt\": \"the following is a prompt for autonomous ai-assisted video generation using runway-make sure each shot *morphs/transforms* rather than clipping/fading, and make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]). and lastly, make sure the camera (fpv) continiously rotate through the entire shot. here's the prompt: \\\"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"precise_prompt\": \"Create a cinematic FPV video with continuous camera rotation using Runway. Ensure all transitions are seamless morphs\\u2014no fades or cuts. Sequence: 1) Begin with a smoothly rotating FPV perspective. 2) Zoom out to reveal a spinning golden circle at hyperspeed on a black background with golden light trails. 3) Morph the spinning circle into a hyperreal, metallic-glow car key. Apply these parameters throughout: [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]. Exclude text overlays.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_prompt\": \"Generate a seamless, cinematic FPV video in Runway: Begin with continuous FPV rotation, zooming out to a hyperspeed spinning golden circle on black with radiant gold light trails. Morph the circle fluidly into a hyperreal, metallic-glow car key\u2014no cuts or fades. Maintain throughout: [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][elegant_blended_morphs].\"\n}",
        }
      }
    }