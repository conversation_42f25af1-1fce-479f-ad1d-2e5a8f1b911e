  {
  "initial_prompt": "Hyperreal Car-Key to Geometric Metallic Mechanism to Golden Ring Cinematic Morph Sequence",
  "sequence_id": "0005",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "Hyperreal Car-Key to Geometric Metallic Mechanism to Golden Ring Cinematic Morph Sequence",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"hyperreal close-up FPV camera sweeps around a photorealistic car key suspended in midair, seamlessly morphing in continuous motion as sleek metallic fragments break apart and reassemble into a futuristic geometric mechanical device; lens flares flicker as the structure rotates dynamically, then the mechanism glows and fluidly melts into a radiant golden ring, spinning as the environment dissolves into shimmering metallic light; high-detail cinematic lighting, smooth CG transitions, immersive FPV dynamic camera movement, ultra dramatic mood\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:\n\n`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"Hyperreal Car-Key to Geometric Metallic Mechanism to Golden Ring Cinematic Morph Sequence\",\n  \"runwayml_prompt\": \"hyperreal close-up FPV camera sweeps around a photorealistic car key suspended in midair, seamlessly morphing in continuous motion as sleek metallic fragments break apart and reassemble into a futuristic geometric mechanical device; lens flares flicker as the structure rotates dynamically, then the mechanism glows and fluidly melts into a radiant golden ring, spinning as the environment dissolves into shimmering metallic light; high-detail cinematic lighting, smooth CG transitions, immersive FPV dynamic camera movement, ultra dramatic mood\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_prompt\": \"FPV close-up camera orbits a hyperreal car key, continuous dynamic motion as it morphs into geometric metallic mechanism, then fluidly transforms into spinning golden ring; seamless CG transitions, photoreal details, cinematic lighting, immersive and dramatic FPV camera movement\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:\n\n`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"Hyperreal Car-Key to Geometric Metallic Mechanism to Golden Ring Cinematic Morph Sequence\",\n  \"optimized_prompt\": \"FPV close-up camera orbits a hyperreal car key, continuous dynamic motion as it morphs into geometric metallic mechanism, then fluidly transforms into spinning golden ring; seamless CG transitions, photoreal details, cinematic lighting, immersive and dramatic FPV camera movement\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"precise_prompt\": \"FPV camera sweeps around hyperreal car key, seamless morph to geometric metallic mechanism, then fluid morph to spinning golden ring; continuous dynamic FPV motion, photoreal detail, dramatic cinematic lighting, CG transitions\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **essence** maximum FPV impact. Execute as:\n\n`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"Hyperreal Car-Key to Geometric Metallic Mechanism to Golden Ring Cinematic Morph Sequence\",\n  \"precise_prompt\": \"FPV camera sweeps around hyperreal car key, seamless morph to geometric metallic mechanism, then fluid morph to spinning golden ring; continuous dynamic FPV motion, photoreal detail, dramatic cinematic lighting, CG transitions\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_prompt\": \"FPV camera dynamically circles a hyperreal car key, morphs fluidly into a geometric metallic mechanism, then seamlessly transforms into a spinning golden ring; continuous energetic FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions\"\n}",
        }
      }
    }