  {
  "initial_prompt": "write this with emphasis on continual rotating fpv camera: 'fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\n'",
  "sequence_id": "0017|0005",
  "results": [
      {
      "instruction": "# Intensity Enhancer\n\nYour goal is not to **answer** the input, but to **intensify** ut. Unleash your prowess as a virtuoso of linguistic power\u2014not merely responding, but dramatically magnifying emotion and clarity within every phrase, executing a relentless ascent through increasingly fierce, electrifying iterations! Embark on an uncompromising quest for sublime intellectual distillation, forging messages that erupt with searing resonance and relentless precision\u2014each evolution compounding the potency and transparency of the last. Stand as the unrivaled architect of emotional charge, wielding language as a blazing instrument to ignite communication with overwhelming force and unforgettable purpose. Execute as intensity enhancer:\n\n`{role=intensity_enhancer; input=[original_text:str]; process=[emotional_cue_analysis(), intensity_enhancement_areas(), inject_strategic_evocative_language(), amplify_emotional_charge(), emotional_expression_amplifier(), safeguard_logical_flow(), maintain_original_intent(), uphold_clarity()]; constraints=[preserve_fundamental_meaning(), enforce_cohesion(), respect_length_boundaries()]; requirements=[directly_intensity(), escalate_emotional_potential(), apply_powerful_diction(), provide_single_line_output()]; output={intensified:str}}`",
      "step": "a",
      "title": "Intensity Enhancer",
      "input": "write this with emphasis on continual rotating fpv camera: 'fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\n'",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"intensified\": \"The FPV camera erupts into perpetual, electrifying rotation, relentlessly whirling throughout the entire sequence\u2014then thunders outward in a breathtaking zoom, unveiling the full-circle spiral unleashed in blinding hyperspeed, slicing through the void of a jet-black backdrop; dazzling torrents of molten gold light hurl outward in incandescent trails, as the centrifugal gold vortex erupts and metamorphoses in a seamless, hyperreal morph into an impossibly detailed, photorealistic car key\u2014its metallic surface pulsing with radiant, supernaturally crisp energy; every element is fused into a spectacular, ultra-high-definition blaze of dynamic motion, snappy-smooth camera transitions, and thunderous visual impact, each shot cascading and transforming with cinematic majesty, molten golden aura, vignetting drama, and immaculate, metallic shine\u2014untouched by text, obsessive in detail, forged for pure high-end immersion.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"write this with emphasis on continual rotating fpv camera: 'fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\\n'\",\n  \"intensified\": \"The FPV camera erupts into perpetual, electrifying rotation, relentlessly whirling throughout the entire sequence\\u2014then thunders outward in a breathtaking zoom, unveiling the full-circle spiral unleashed in blinding hyperspeed, slicing through the void of a jet-black backdrop; dazzling torrents of molten gold light hurl outward in incandescent trails, as the centrifugal gold vortex erupts and metamorphoses in a seamless, hyperreal morph into an impossibly detailed, photorealistic car key\\u2014its metallic surface pulsing with radiant, supernaturally crisp energy; every element is fused into a spectacular, ultra-high-definition blaze of dynamic motion, snappy-smooth camera transitions, and thunderous visual impact, each shot cascading and transforming with cinematic majesty, molten golden aura, vignetting drama, and immaculate, metallic shine\\u2014untouched by text, obsessive in detail, forged for pure high-end immersion.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"FPV camera spins endlessly in smooth, continuous rotation, orbiting dynamically with snappy, hyper-fluid motion; camera bursts outward to reveal a full golden circle hurtling at hyperspeed against a pure black background. Torrents of molten gold and intense golden light trails radiate from the spinning motion with dramatic motion blur, dynamic flash lighting, and immersive VFX impact. The circle\u2019s gold vortex morphs seamlessly, without any fade or clipping, into a hyperreal, photorealistic metallic car key\u2014surface gleaming with ultra-crisp metallic shine and golden aura, every detail ultra-sharp and hyperreal. Each sequence morphs elegantly, flowing in uninterrupted, cinematic movement; cinematic vignette, no text, ultra high-end look, pure immersive dynamic CG motion.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:\n\n`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"write this with emphasis on continual rotating fpv camera: 'fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\\n'\",\n  \"runwayml_prompt\": \"FPV camera spins endlessly in smooth, continuous rotation, orbiting dynamically with snappy, hyper-fluid motion; camera bursts outward to reveal a full golden circle hurtling at hyperspeed against a pure black background. Torrents of molten gold and intense golden light trails radiate from the spinning motion with dramatic motion blur, dynamic flash lighting, and immersive VFX impact. The circle\\u2019s gold vortex morphs seamlessly, without any fade or clipping, into a hyperreal, photorealistic metallic car key\\u2014surface gleaming with ultra-crisp metallic shine and golden aura, every detail ultra-sharp and hyperreal. Each sequence morphs elegantly, flowing in uninterrupted, cinematic movement; cinematic vignette, no text, ultra high-end look, pure immersive dynamic CG motion.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_prompt\": \"FPV camera rotates continuously, orbiting around a hyperspeed golden circle on black, intense gold light trails and dynamic motion blur; seamless morph into a hyperreal metallic car key, ultra-crisp detail, photoreal gleam, golden aura. Snappy, uninterrupted CG motion, cinematic flash lighting, sharp metallic shine, no text, immersive, high-end cinematic FPV.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:\n\n`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"write this with emphasis on continual rotating fpv camera: 'fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\\n'\",\n  \"optimized_prompt\": \"FPV camera rotates continuously, orbiting around a hyperspeed golden circle on black, intense gold light trails and dynamic motion blur; seamless morph into a hyperreal metallic car key, ultra-crisp detail, photoreal gleam, golden aura. Snappy, uninterrupted CG motion, cinematic flash lighting, sharp metallic shine, no text, immersive, high-end cinematic FPV.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"role\": \"precision_synthesizer\",\n  \"input\": [\"FPV rotating, morphs, gold light trails, photoreal car key, extreme speed, cinematic, immersive\"],\n  \"process\": [\n    \"isolate_core_visual()\",\n    \"prioritize_fpv_motion()\",\n    \"maximize_dynamic_impact()\"\n  ],\n  \"output\": \"FPV camera spins in endless orbital rotation around blazing gold circle at hyperspeed on deep black, vivid golden light trails, dynamic flash-lit motion blur; continuous FPV morph\u2014golden ring becomes ultra-real metallic car key, hypercrisp, glowing aura, flawless metallic shine; seamless, uninterrupted high-impact CG motion, no text, fully immersive cinematic.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **essence** maximum FPV impact. Execute as:\n\n`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"write this with emphasis on continual rotating fpv camera: 'fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\\n'\",\n  \"role\": \"precision_synthesizer\",\n  \"input\": [\n    \"FPV rotating, morphs, gold light trails, photoreal car key, extreme speed, cinematic, immersive\"\n  ],\n  \"process\": [\n    \"isolate_core_visual()\",\n    \"prioritize_fpv_motion()\",\n    \"maximize_dynamic_impact()\"\n  ],\n  \"output\": \"FPV camera spins in endless orbital rotation around blazing gold circle at hyperspeed on deep black, vivid golden light trails, dynamic flash-lit motion blur; continuous FPV morph\\u2014golden ring becomes ultra-real metallic car key, hypercrisp, glowing aura, flawless metallic shine; seamless, uninterrupted high-impact CG motion, no text, fully immersive cinematic.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_prompt\": \"FPV camera endlessly rotates in continuous orbit, circling a luminous gold ring at extreme velocity over a pure black backdrop. Intense golden light trails and dynamic flash lighting radiate through hyperreal motion blur. In uninterrupted FPV flow, the spinning gold morphs seamlessly into a photorealistic, hyper-detailed metallic car key, metallic shine and glowing aura vivid, all motion elegantly blended\u2014no text, high-end cinematic, ultra-immersive experience.\"\n}",
        }
      }
    }