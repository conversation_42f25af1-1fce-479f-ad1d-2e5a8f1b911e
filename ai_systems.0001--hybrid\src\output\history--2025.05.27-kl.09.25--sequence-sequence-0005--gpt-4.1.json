  {
  "initial_prompt": "[fpv] Hyperreal macro: glossy keyless car remote, cinematic rim lighting. [continuous motion] Camera spirals around remote as it begins morphing\u2014plastic casing flows like liquid metal, buttons dissolve into emerging gear teeth, electronic circuits transform into mechanical linkages. [dynamic cg movement] Camera dives through morphing structure as remote becomes intricate mechanical gear assembly, gears rotating and interlocking in fluid motion. [fpv sweep] Camera weaves between spinning gears as metallic surfaces begin glowing, gear teeth softening and flowing together like molten gold. [continuous spiral] Mechanical complexity dissolves into elegant simplicity as assembly morphs into polished gold ring, camera orbiting the transformation. [dynamic close] Final approach to gleaming ring surface.",
  "sequence_id": "0005",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "[fpv] Hyperreal macro: glossy keyless car remote, cinematic rim lighting. [continuous motion] Camera spirals around remote as it begins morphing\u2014plastic casing flows like liquid metal, buttons dissolve into emerging gear teeth, electronic circuits transform into mechanical linkages. [dynamic cg movement] Camera dives through morphing structure as remote becomes intricate mechanical gear assembly, gears rotating and interlocking in fluid motion. [fpv sweep] Camera weaves between spinning gears as metallic surfaces begin glowing, gear teeth softening and flowing together like molten gold. [continuous spiral] Mechanical complexity dissolves into elegant simplicity as assembly morphs into polished gold ring, camera orbiting the transformation. [dynamic close] Final approach to gleaming ring surface.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"FPV hyperreal macro: glossy keyless car remote under cinematic rim lighting, camera in continuous spiral motion encircling remote. As camera moves, plastic casing morphs into flowing liquid metal, buttons dissolve and transform into emerging gear teeth, electronics morph into intricate mechanical linkages\u2014continuous dynamic CG movement. FPV camera dives through the morphing structure as remote evolves into a complex, rotating gear assembly; gears interlocking, camera weaving between spinning gears. Metallic surfaces glow, gear teeth soften and flow into molten gold, all in seamless FPV continuous motion. Mechanical complexity dissolves into elegant simplicity as the assembly morphs into a polished gold ring. Camera orbits the transformation, ending with a dynamic FPV close up: final approach to the gleaming gold ring surface. Hyperreal, immersive, fluid cinematic transitions, intricate detail, smooth motion.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:\n\n`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[fpv] Hyperreal macro: glossy keyless car remote, cinematic rim lighting. [continuous motion] Camera spirals around remote as it begins morphing\\u2014plastic casing flows like liquid metal, buttons dissolve into emerging gear teeth, electronic circuits transform into mechanical linkages. [dynamic cg movement] Camera dives through morphing structure as remote becomes intricate mechanical gear assembly, gears rotating and interlocking in fluid motion. [fpv sweep] Camera weaves between spinning gears as metallic surfaces begin glowing, gear teeth softening and flowing together like molten gold. [continuous spiral] Mechanical complexity dissolves into elegant simplicity as assembly morphs into polished gold ring, camera orbiting the transformation. [dynamic close] Final approach to gleaming ring surface.\",\n  \"runwayml_prompt\": \"FPV hyperreal macro: glossy keyless car remote under cinematic rim lighting, camera in continuous spiral motion encircling remote. As camera moves, plastic casing morphs into flowing liquid metal, buttons dissolve and transform into emerging gear teeth, electronics morph into intricate mechanical linkages\\u2014continuous dynamic CG movement. FPV camera dives through the morphing structure as remote evolves into a complex, rotating gear assembly; gears interlocking, camera weaving between spinning gears. Metallic surfaces glow, gear teeth soften and flow into molten gold, all in seamless FPV continuous motion. Mechanical complexity dissolves into elegant simplicity as the assembly morphs into a polished gold ring. Camera orbits the transformation, ending with a dynamic FPV close up: final approach to the gleaming gold ring surface. Hyperreal, immersive, fluid cinematic transitions, intricate detail, smooth motion.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_prompt\": \"FPV hyperreal macro: glossy keyless car remote, cinematic rim lighting. Continuous FPV spiral as remote morphs\u2014casing flows liquid metal, buttons become gear teeth, electronics turn to mechanical linkages. Dynamic CG camera dives through transformation into complex spinning gears, weaving FPV path between glowing, molten metallic forms. Assembly seamlessly morphs into polished gold ring, FPV orbit and close-up final approach to gleaming surface. Fluid, immersive, intricate, smooth motion.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:\n\n`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[fpv] Hyperreal macro: glossy keyless car remote, cinematic rim lighting. [continuous motion] Camera spirals around remote as it begins morphing\\u2014plastic casing flows like liquid metal, buttons dissolve into emerging gear teeth, electronic circuits transform into mechanical linkages. [dynamic cg movement] Camera dives through morphing structure as remote becomes intricate mechanical gear assembly, gears rotating and interlocking in fluid motion. [fpv sweep] Camera weaves between spinning gears as metallic surfaces begin glowing, gear teeth softening and flowing together like molten gold. [continuous spiral] Mechanical complexity dissolves into elegant simplicity as assembly morphs into polished gold ring, camera orbiting the transformation. [dynamic close] Final approach to gleaming ring surface.\",\n  \"optimized_prompt\": \"FPV hyperreal macro: glossy keyless car remote, cinematic rim lighting. Continuous FPV spiral as remote morphs\\u2014casing flows liquid metal, buttons become gear teeth, electronics turn to mechanical linkages. Dynamic CG camera dives through transformation into complex spinning gears, weaving FPV path between glowing, molten metallic forms. Assembly seamlessly morphs into polished gold ring, FPV orbit and close-up final approach to gleaming surface. Fluid, immersive, intricate, smooth motion.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"precise_prompt\": \"FPV macro: glossy keyless car remote, rim-lit. Camera spirals close, remote morphs\u2014plastic casing liquefies, buttons fuse into gear teeth, circuits transmute to moving linkages. FPV sweeps through spinning interlocking gears, molten metallic transitions, glowing gold surfaces. FPV orbit: gears fluidly merge into polished gold ring. Final tight FPV close-up on gleaming ring, seamless dynamic motion throughout.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **essence** maximum FPV impact. Execute as:\n\n`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[fpv] Hyperreal macro: glossy keyless car remote, cinematic rim lighting. [continuous motion] Camera spirals around remote as it begins morphing\\u2014plastic casing flows like liquid metal, buttons dissolve into emerging gear teeth, electronic circuits transform into mechanical linkages. [dynamic cg movement] Camera dives through morphing structure as remote becomes intricate mechanical gear assembly, gears rotating and interlocking in fluid motion. [fpv sweep] Camera weaves between spinning gears as metallic surfaces begin glowing, gear teeth softening and flowing together like molten gold. [continuous spiral] Mechanical complexity dissolves into elegant simplicity as assembly morphs into polished gold ring, camera orbiting the transformation. [dynamic close] Final approach to gleaming ring surface.\",\n  \"precise_prompt\": \"FPV macro: glossy keyless car remote, rim-lit. Camera spirals close, remote morphs\\u2014plastic casing liquefies, buttons fuse into gear teeth, circuits transmute to moving linkages. FPV sweeps through spinning interlocking gears, molten metallic transitions, glowing gold surfaces. FPV orbit: gears fluidly merge into polished gold ring. Final tight FPV close-up on gleaming ring, seamless dynamic motion throughout.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_prompt\": \"FPV macro shot: glossy car remote in rim light. Camera spirals in, remote smoothly morphs\u2014plastic melts to metal, buttons and circuits flow into intricate moving gears. FPV dives and weaves through spinning gears, metal glowing and turning molten gold. Mechanics merge into a polished gold ring, camera orbits transformation, ending with a tight FPV close-up on the gleaming surface\u2014seamless, flowing motion throughout.\"\n}",
        }
      }
    }