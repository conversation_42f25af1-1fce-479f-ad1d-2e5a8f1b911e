  {
  "initial_prompt": "[fpv] Hyperreal macro: glossy keyless car remote, cinematic rim lighting. [continuous motion] FPV macro shot: snappy seamless tansitions with a continual spiraling camera rotation",
  "sequence_id": "0005",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "[fpv] Hyperreal macro: glossy keyless car remote, cinematic rim lighting. [continuous motion] FPV macro shot: snappy seamless tansitions with a continual spiraling camera rotation",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"runwayml_prompt\":\"FPV macro perspective of a hyperreal, glossy keyless car remote, intense cinematic rim lighting accentuating textures, camera spirals smoothly around the remote in continuous, dynamic motion, snappy seamless transitions as the lens glides over gleaming buttons and edges, immersive sense of scale and depth, subtle reflections morph and dissolve with shifting lighting, all movements fluid and uninterrupted, evoking a high-tech, dramatic, and visually striking atmosphere, maximizing cinematic impact while maintaining immersive storytelling through continuous FPV camera motion.\"}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:\n\n`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[fpv] Hyperreal macro: glossy keyless car remote, cinematic rim lighting. [continuous motion] FPV macro shot: snappy seamless tansitions with a continual spiraling camera rotation\",\n  \"runwayml_prompt\": \"FPV macro perspective of a hyperreal, glossy keyless car remote, intense cinematic rim lighting accentuating textures, camera spirals smoothly around the remote in continuous, dynamic motion, snappy seamless transitions as the lens glides over gleaming buttons and edges, immersive sense of scale and depth, subtle reflections morph and dissolve with shifting lighting, all movements fluid and uninterrupted, evoking a high-tech, dramatic, and visually striking atmosphere, maximizing cinematic impact while maintaining immersive storytelling through continuous FPV camera motion.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_prompt\": \"FPV macro shot, hyperreal glossy keyless car remote, cinematic rim lighting, camera spirals smoothly around the remote in continuous motion, seamless snappy transitions over buttons and edges, immersive sense of scale, dynamic uninterrupted FPV movement\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:\n\n`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[fpv] Hyperreal macro: glossy keyless car remote, cinematic rim lighting. [continuous motion] FPV macro shot: snappy seamless tansitions with a continual spiraling camera rotation\",\n  \"optimized_prompt\": \"FPV macro shot, hyperreal glossy keyless car remote, cinematic rim lighting, camera spirals smoothly around the remote in continuous motion, seamless snappy transitions over buttons and edges, immersive sense of scale, dynamic uninterrupted FPV movement\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"precise_prompt\": \"FPV macro shot, hyperreal glossy keyless car remote, cinematic rim lighting, camera spirals in one smooth continuous motion around remote, sharp seamless transitions highlight buttons and edges, dynamic uninterrupted FPV flow with impactful sense of scale\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **essence** maximum FPV impact. Execute as:\n\n`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[fpv] Hyperreal macro: glossy keyless car remote, cinematic rim lighting. [continuous motion] FPV macro shot: snappy seamless tansitions with a continual spiraling camera rotation\",\n  \"precise_prompt\": \"FPV macro shot, hyperreal glossy keyless car remote, cinematic rim lighting, camera spirals in one smooth continuous motion around remote, sharp seamless transitions highlight buttons and edges, dynamic uninterrupted FPV flow with impactful sense of scale\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_prompt\": \"FPV macro shot of a hyperreal, glossy keyless car remote, cinematic rim lighting, camera spirals smoothly and continuously around the remote, sharp seamless transitions accentuate buttons and edges, uninterrupted dynamic FPV flow, conveying impactful scale and motion\"\n}",
        }
      }
    }