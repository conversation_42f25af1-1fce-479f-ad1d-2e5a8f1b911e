  {
  "initial_prompt": "[fpv] 1. Keyless Car Remote: Reshapes into intricate mechanical gears\n2. Mechanical Gear Assembly: Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Polished Gold Ring: Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\n[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]",
  "sequence_id": "0005",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "[fpv] 1. Keyless Car Remote: Reshapes into intricate mechanical gears\n2. Mechanical Gear Assembly: Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Polished Gold Ring: Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\n[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"FPV camera dives toward a sleek keyless car remote lying on a glass table; as it approaches, the remote transforms in real time, surfaces splitting and expanding into an intricate mechanical gear assembly. The FPV viewpoint glides through and around the gears, weaving between shifting, polished metallic cogs that rotate and interlock seamlessly, reflecting dramatic cinematic lighting. As the gears spin hypnotically, the FPV locks onto a single core gear which morphs\u2014its hard metallic edges gradually dissolve and flow into a spinning, polished gold ring with fluid organic curves, refracting photorealistic highlights. Ultra-smooth CG transitions, immersive detail, continuous dynamic camera motion, hyperreal textures. Dramatic, mesmerizing, cinematic.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:\n\n`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[fpv] 1. Keyless Car Remote: Reshapes into intricate mechanical gears\\n2. Mechanical Gear Assembly: Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Polished Gold Ring: Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\\n[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\",\n  \"runwayml_prompt\": \"FPV camera dives toward a sleek keyless car remote lying on a glass table; as it approaches, the remote transforms in real time, surfaces splitting and expanding into an intricate mechanical gear assembly. The FPV viewpoint glides through and around the gears, weaving between shifting, polished metallic cogs that rotate and interlock seamlessly, reflecting dramatic cinematic lighting. As the gears spin hypnotically, the FPV locks onto a single core gear which morphs\\u2014its hard metallic edges gradually dissolve and flow into a spinning, polished gold ring with fluid organic curves, refracting photorealistic highlights. Ultra-smooth CG transitions, immersive detail, continuous dynamic camera motion, hyperreal textures. Dramatic, mesmerizing, cinematic.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_prompt\": \"FPV camera races toward a keyless car remote, which transforms into complex interlocking gears as the lens weaves through shifting metallic machinery, light reflecting off polished surfaces; core gear morphs in real time into a spinning organic gold ring. Continuous FPV motion, seamless CG transitions, photoreal textures, cinematic lighting.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:\n\n`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[fpv] 1. Keyless Car Remote: Reshapes into intricate mechanical gears\\n2. Mechanical Gear Assembly: Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Polished Gold Ring: Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\\n[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\",\n  \"optimized_prompt\": \"FPV camera races toward a keyless car remote, which transforms into complex interlocking gears as the lens weaves through shifting metallic machinery, light reflecting off polished surfaces; core gear morphs in real time into a spinning organic gold ring. Continuous FPV motion, seamless CG transitions, photoreal textures, cinematic lighting.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"role\": \"precision_synthesizer\",\n  \"input\": \"Keyless car remote morphs into gears, gears expand/interlock, then transform into spinning gold ring; FPV camera flies through transforming machinery.\",\n  \"process\": [\n    \"isolate_core_visual()\",\n    \"prioritize_fpv_motion()\",\n    \"maximize_dynamic_impact()\"\n  ],\n  \"output\": \"FPV drone speeds into keyless car remote; device unfolds into intricate, meshing metallic gears; camera weaves through shifting mechanisms as gears merge, central gear seamlessly morphs into spinning polished gold ring. Fluid motion, hyperreal textures, bold cinematic lighting, ultra-smooth CG transitions.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **essence** maximum FPV impact. Execute as:\n\n`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[fpv] 1. Keyless Car Remote: Reshapes into intricate mechanical gears\\n2. Mechanical Gear Assembly: Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Polished Gold Ring: Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\\n[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\",\n  \"role\": \"precision_synthesizer\",\n  \"input\": \"Keyless car remote morphs into gears, gears expand/interlock, then transform into spinning gold ring; FPV camera flies through transforming machinery.\",\n  \"process\": [\n    \"isolate_core_visual()\",\n    \"prioritize_fpv_motion()\",\n    \"maximize_dynamic_impact()\"\n  ],\n  \"output\": \"FPV drone speeds into keyless car remote; device unfolds into intricate, meshing metallic gears; camera weaves through shifting mechanisms as gears merge, central gear seamlessly morphs into spinning polished gold ring. Fluid motion, hyperreal textures, bold cinematic lighting, ultra-smooth CG transitions.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_prompt\": \"FPV drone dives into a keyless car remote that transforms into interlocking, expanding metallic gears; camera fluidly weaves through shifting mechanisms as gears seamlessly morph into a spinning polished gold ring. Hyperreal textures, cinematic dramatic lighting, ultra-smooth CG transitions, intense sense of motion.\"\n}",
        }
      }
    }