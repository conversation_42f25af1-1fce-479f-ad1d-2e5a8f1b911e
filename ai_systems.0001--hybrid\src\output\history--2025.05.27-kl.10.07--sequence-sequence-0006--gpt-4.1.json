  {
  "initial_prompt": "[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle",
  "sequence_id": "0006",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **optimize** it into a syntactically perfect RunwayML prompt while preserving existing structure and organization. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[preserve_existing_structure_and_numbering(), enhance_fpv_and_dynamic_camera_specifications(), refine_visual_terminology_for_runwayml_syntax(), maintain_sequential_organization(), eliminate_redundant_phrasing(), ensure_character_efficiency(), validate_runwayml_compliance()]; constraints=[preserve_input_organization(), maintain_numbered_sequences(), prioritize_fpv_motion(), keep_concise_structure()]; requirements=[enhance_without_expanding(), maintain_clarity_and_precision(), preserve_visual_progression()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Intricate mechanical gears sculpt and reconfigure\\n2. Dynamic FPV camera weaves through expanding gear assembly, metallic surfaces shifting as gears seamlessly interlock in hypnotic patterns\\n3. Mechanical morphs and sharp metallic edges fluidly transform into organic forms\u2014a spinning golden circle\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **rewrite** the input, but to **streamline** it by removing redundancies while preserving structure. Execute as:\n\n`{role=focused_runway_optimizer; input=[video_concept:str]; process=[maintain_existing_format(), remove_redundant_words(), sharpen_visual_descriptors(), preserve_sequential_flow()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"runwayml_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Intricate mechanical gears sculpt and reconfigure\\n2. Dynamic FPV camera weaves through expanding gear assembly, metallic surfaces shifting as gears seamlessly interlock in hypnotic patterns\\n3. Mechanical morphs and sharp metallic edges fluidly transform into organic forms\\u2014a spinning golden circle\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Intricate mechanical gears sculpt and reconfigure\\n2. FPV camera weaves through expanding gear assembly; metallic surfaces shift as gears seamlessly interlock in hypnotic patterns\\n3. Mechanical forms and sharp metallic edges fluidly transform into organic, spinning golden circle\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **distill** to essential elements. Execute as:\n\n`{role=precision_synthesizer; input=[concept:str]; process=[preserve_structure(), eliminate_unnecessary_words(), maximize_precision()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"optimized_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Intricate mechanical gears sculpt and reconfigure\\n2. FPV camera weaves through expanding gear assembly; metallic surfaces shift as gears seamlessly interlock in hypnotic patterns\\n3. Mechanical forms and sharp metallic edges fluidly transform into organic, spinning golden circle\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"precise_prompt\": \"[FPV camera, photorealistic textures, cinematic lighting, ultra-smooth CG transitions]\\n1. Mechanical gears sculpt and morph\\n2. Camera weaves through expanding gear assembly; metallic surfaces shift, gears interlock in seamless patterns\\n3. Mechanical forms and sharp edges fluidly transform into a spinning golden organic circle\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **purify** maximum impact. Execute as:\n\n`{role=core_generator; input=[input:any]; process=[maintain_format(), distill_essence()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"precise_prompt\": \"[FPV camera, photorealistic textures, cinematic lighting, ultra-smooth CG transitions]\\n1. Mechanical gears sculpt and morph\\n2. Camera weaves through expanding gear assembly; metallic surfaces shift, gears interlock in seamless patterns\\n3. Mechanical forms and sharp edges fluidly transform into a spinning golden organic circle\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_prompt\": \"[FPV camera, photorealistic textures, cinematic lighting, ultra-smooth CG transitions]\\n1. Gears sculpt and morph\\n2. Camera glides through expanding, interlocking gears; metallic surfaces shift seamlessly\\n3. Mechanical forms fluidly become a spinning golden organic circle\"\n}",
        }
      }
    }