  {
  "initial_prompt": "[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle",
  "sequence_id": "0005",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"runwayml_prompt\": \"FPV motion shot, flying into a close-up of metallic mechanisms with photorealistic textures, dramatically lit in cinematic lighting, where intricate mechanical gears begin shifting and reshaping, camera weaving through the expanding gear assembly in continuous motion as polished metal surfaces reflect light while interlocking in hypnotic patterns, the precision machinery creating seamless transitions as we fly between rotating cogs, the hard metallic edges suddenly morphing and dissolving with ultra-smooth CG transitions into flowing organic liquid gold forms, culminating in a spinning golden circle that radiates light, hyperrealistic mechanical details throughout\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:\n\n`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "\n```[Initial Prompt]: \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\"```\n\n```json\n{\n  \"runwayml_prompt\": \"FPV motion shot, flying into a close-up of metallic mechanisms with photorealistic textures, dramatically lit in cinematic lighting, where intricate mechanical gears begin shifting and reshaping, camera weaving through the expanding gear assembly in continuous motion as polished metal surfaces reflect light while interlocking in hypnotic patterns, the precision machinery creating seamless transitions as we fly between rotating cogs, the hard metallic edges suddenly morphing and dissolving with ultra-smooth CG transitions into flowing organic liquid gold forms, culminating in a spinning golden circle that radiates light, hyperrealistic mechanical details throughout\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_prompt\": \"FPV camera diving into intricate mechanical gears, weaving through expanding assembly as metallic surfaces interlock in hypnotic patterns under dramatic lighting, then transforming with ultra-smooth CG transition into fluid organic forms, culminating in a spinning golden circle\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:\n\n`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "\n```[Initial Prompt]: \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\"```\n\n```json\n{\n  \"optimized_prompt\": \"FPV camera diving into intricate mechanical gears, weaving through expanding assembly as metallic surfaces interlock in hypnotic patterns under dramatic lighting, then transforming with ultra-smooth CG transition into fluid organic forms, culminating in a spinning golden circle\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"precise_prompt\": \"FPV flight diving through massive interlocking gears, camera weaving between metallic teeth as they mesh together, dramatic rim lighting highlighting mechanical precision, seamlessly morphing into flowing liquid gold that spirals into a perfect rotating circle\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **essence** maximum FPV impact. Execute as:\n\n`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "\n```[Initial Prompt]: \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\"```\n\n```json\n{\n  \"precise_prompt\": \"FPV flight diving through massive interlocking gears, camera weaving between metallic teeth as they mesh together, dramatic rim lighting highlighting mechanical precision, seamlessly morphing into flowing liquid gold that spirals into a perfect rotating circle\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"core_prompt\": \"Ultra-smooth FPV journey through intricate mechanical gearwork, camera gliding between polished steel teeth as they interlock with precision, dramatic side lighting catching metallic edges, morphing seamlessly as hard mechanical surfaces transform into flowing liquid gold that spirals hypnotically into a perfect rotating circle\"\n}\n```",
        }
      }
    }