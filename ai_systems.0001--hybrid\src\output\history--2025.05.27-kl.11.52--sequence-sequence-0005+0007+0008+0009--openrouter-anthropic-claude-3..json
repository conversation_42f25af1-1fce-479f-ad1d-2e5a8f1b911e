  {
  "initial_prompt": "[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle",
  "sequence_id": "0005|0007|0008|0009",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"runwayml_prompt\": \"FPV shot, ultra detailed photorealistic intricate mechanical gears with precision-machined textures, dramatic cinematic lighting with rim lights accentuating metallic edges, camera dynamically weaving through expanding gear assembly at close range, metallic surfaces shifting and interlocking in hypnotic patterns with seamless CG transitions, gears continuously rotating with perfect synchronization, depth of field bringing micro details into sharp focus, mechanical forms gradually morphing with fluid CG transitions as hard metallic edges transform into flowing golden organic forms, culminating in a mesmerizing spinning golden circle reflecting ambient light, 8K cinematography, hyper-detailed surface rendering\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:\n\n`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "\n```[Initial Prompt]: \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\"```\n\n```json\n{\n  \"runwayml_prompt\": \"FPV shot, ultra detailed photorealistic intricate mechanical gears with precision-machined textures, dramatic cinematic lighting with rim lights accentuating metallic edges, camera dynamically weaving through expanding gear assembly at close range, metallic surfaces shifting and interlocking in hypnotic patterns with seamless CG transitions, gears continuously rotating with perfect synchronization, depth of field bringing micro details into sharp focus, mechanical forms gradually morphing with fluid CG transitions as hard metallic edges transform into flowing golden organic forms, culminating in a mesmerizing spinning golden circle reflecting ambient light, 8K cinematography, hyper-detailed surface rendering\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_prompt\": \"FPV through intricate mechanical gears, camera weaving between metallic surfaces as they shift and interlock in hypnotic patterns, dramatic lighting highlights precision details, seamless CG transition as hard mechanical elements morph into fluid organic forms, ending with spinning golden circle\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:\n\n`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "\n```[Initial Prompt]: \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\"```\n\n```json\n{\n  \"optimized_prompt\": \"FPV through intricate mechanical gears, camera weaving between metallic surfaces as they shift and interlock in hypnotic patterns, dramatic lighting highlights precision details, seamless CG transition as hard mechanical elements morph into fluid organic forms, ending with spinning golden circle\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"precise_prompt\": \"FPV diving through interlocking metallic gears, camera weaving between precision-machined surfaces as they shift and mesh hypnotically, dramatic rim lighting accentuates mechanical textures, seamlessly transforming into flowing organic forms culminating in a spinning golden circle\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **essence** maximum FPV impact. Execute as:\n\n`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "\n```[Initial Prompt]: \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\"```\n\n```json\n{\n  \"precise_prompt\": \"FPV diving through interlocking metallic gears, camera weaving between precision-machined surfaces as they shift and mesh hypnotically, dramatic rim lighting accentuates mechanical textures, seamlessly transforming into flowing organic forms culminating in a spinning golden circle\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"core_prompt\": \"Intense FPV flight through precision-machined gears with metallic textures catching dramatic cinematic lighting, camera seamlessly weaving between interlocking mechanical elements as they shift and rotate with hypnotic precision, then transforming with ultra-smooth motion into flowing organic forms that culminate in a spinning golden circle\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML compliance by ensuring direct descriptions, positive phrasing, and single-scene focus while preserving bracketed header structure. **Eliminate conversational language and ensure visual clarity.** Execute as:\n\n`{role=syntax_validator; input=[concept:str]; process=[validate_direct_descriptions(), eliminate_conversational_elements(), ensure_positive_phrasing(), maintain_single_scene_focus(), preserve_bracket_structure()]; constraints=[no_conversational_language(), single_scene_only(), positive_phrasing_mandatory()]; requirements=[runway_compliance(), visual_clarity(), structural_preservation()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "\n```[Initial Prompt]: \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\"```\n\n```json\n{\n  \"core_prompt\": \"Intense FPV flight through precision-machined gears with metallic textures catching dramatic cinematic lighting, camera seamlessly weaving between interlocking mechanical elements as they shift and rotate with hypnotic precision, then transforming with ultra-smooth motion into flowing organic forms that culminate in a spinning golden circle\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions] Complex mechanical gears with precise metallic surfaces, camera moving through expanding gear assembly as interlocking components rotate in hypnotic patterns, mechanical elements transforming into fluid organic shapes, culminating in a spinning golden circle\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Simplifier\n\nYour goal is not to **elaborate** but to **simplify** motion descriptions to essential camera and subject actions using RunwayML's preferred direct terminology. **Focus on simple camera verbs and clear subject actions.** Execute as:\n\n`{role=motion_simplifier; input=[validated_prompt:str]; process=[simplify_camera_movements(), use_basic_motion_verbs(), eliminate_complex_descriptions(), maintain_gear_progression(), preserve_structure()]; constraints=[basic_camera_verbs_only(), simple_motion_descriptions(), maintain_three_steps()]; requirements=[motion_simplicity(), runway_syntax(), progression_clarity()]; output={simplified_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Simplifier",
      "input": "\n```[Initial Prompt]: \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\"```\n\n```json\n{\n  \"validated_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions] Complex mechanical gears with precise metallic surfaces, camera moving through expanding gear assembly as interlocking components rotate in hypnotic patterns, mechanical elements transforming into fluid organic shapes, culminating in a spinning golden circle\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"simplified_motion_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions] \n1. Camera approaches complex mechanical gears with metallic surfaces\n2. Camera moves through rotating gears as they expand and interlock\n3. Camera follows as mechanical gears transform into a spinning golden circle\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Descriptor Compressor\n\nYour goal is not to **modify** but to **compress** visual descriptors to RunwayML's preferred concise format while maintaining essential metallic and morphing elements. **Eliminate redundant adjectives and focus on core visuals.** Execute as:\n\n`{role=descriptor_compressor; input=[simplified_motion_prompt:str]; process=[compress_visual_descriptors(), eliminate_redundant_adjectives(), maintain_core_elements(), preserve_metallic_focus(), ensure_conciseness()]; constraints=[minimal_adjectives(), core_visuals_only(), maintain_progression()]; requirements=[descriptor_efficiency(), visual_clarity(), runway_format()]; output={compressed_prompt:str}}`",
      "step": "c",
      "title": "Runway Descriptor Compressor",
      "input": "\n```[Initial Prompt]: \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\"```\n\n```json\n{\n  \"simplified_motion_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions] \n1. Camera approaches complex mechanical gears with metallic surfaces\n2. Camera moves through rotating gears as they expand and interlock\n3. Camera follows as mechanical gears transform into a spinning golden circle\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"compressed_prompt\": \"Camera approaches intricate gears, moves through expanding metallic assembly as they interlock, then follows as the mechanism transforms into a spinning golden circle\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: direct subject-action descriptions with essential header tags only. **Achieve maximum conciseness while preserving visual impact.** Execute as:\n\n`{role=core_distiller; input=[compressed_prompt:str]; process=[distill_to_core_syntax(), maintain_subject_action_clarity(), preserve_essential_headers(), ensure_runway_directness()]; constraints=[maximum_conciseness(), direct_descriptions_only(), minimal_headers()]; requirements=[runway_syntax_compliance(), visual_impact(), format_preservation()]; output={distilled_core_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\"```\n\n```json\n{\n  \"compressed_prompt\": \"Camera approaches intricate gears, moves through expanding metallic assembly as they interlock, then follows as the mechanism transforms into a spinning golden circle\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_core_prompt\": \"[FPV motion, photorealistic, dramatic lighting]\nCamera moves through mechanical gears, metallic surfaces interlocking in patterns, transforming into fluid golden circle\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "\n```[Initial Prompt]: \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\"```\n\n```json\n{\n  \"distilled_core_prompt\": \"[FPV motion, photorealistic, dramatic lighting]\nCamera moves through mechanical gears, metallic surfaces interlocking in patterns, transforming into fluid golden circle\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"Ultra-detailed metallic gears with photorealistic textures gleaming under dramatic cinematic lighting. FPV camera perspective weaving through an expanding assembly of interlocking mechanical components. Hypnotic patterns of precisely engineered surfaces rotating in perfect synchronization. The hard metallic forms transitioning into flowing organic shapes, culminating in a spinning golden circle with liquid-like properties. Immersive mechanical environment with polished metal surfaces reflecting light.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\"```\n\n```json\n{\n  \"validated_prompt\": \"Ultra-detailed metallic gears with photorealistic textures gleaming under dramatic cinematic lighting. FPV camera perspective weaving through an expanding assembly of interlocking mechanical components. Hypnotic patterns of precisely engineered surfaces rotating in perfect synchronization. The hard metallic forms transitioning into flowing organic shapes, culminating in a spinning golden circle with liquid-like properties. Immersive mechanical environment with polished metal surfaces reflecting light.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"Metallic gears slowly transform from simple forms into an intricate, expanding mechanical assembly, then gradually morph into flowing organic shapes, culminating in a spinning golden circle\",\n    \n    \"camera_motion\": \"FPV camera smoothly weaves through the transforming gear mechanism, maintaining fluid movement while navigating between rotating components\",\n    \n    \"scene_motion\": \"Mechanical surfaces shift and interlock in precise patterns around the camera path, creating a continuously expanding mechanical environment that eventually softens into organic forms\",\n    \n    \"style_descriptors\": \"Ultra-smooth CG transitions, photorealistic metal textures, dramatic cinematic lighting highlighting reflective surfaces\"\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\"```\n\n```json\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"Metallic gears slowly transform from simple forms into an intricate, expanding mechanical assembly, then gradually morph into flowing organic shapes, culminating in a spinning golden circle\",\n    \n    \"camera_motion\": \"FPV camera smoothly weaves through the transforming gear mechanism, maintaining fluid movement while navigating between rotating components\",\n    \n    \"scene_motion\": \"Mechanical surfaces shift and interlock in precise patterns around the camera path, creating a continuously expanding mechanical environment that eventually softens into organic forms\",\n    \n    \"style_descriptors\": \"Ultra-smooth CG transitions, photorealistic metal textures, dramatic cinematic lighting highlighting reflective surfaces\"\n  }\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": \"[FPV drone shot, photorealistic, cinematic lighting, seamless transitions]\n\n1. Metallic object transforms into intricate mechanical gears\n2. Camera dolly through expanding gear assembly with reflective surfaces and precise interlocking patterns\n3. Hard mechanical forms morph into flowing organic shapes, ending on golden circular form\"\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": \"[FPV drone shot, photorealistic, cinematic lighting, seamless transitions]\n\n1. Metallic object transforms into intricate mechanical gears\n2. Camera dolly through expanding gear assembly with reflective surfaces and precise interlocking patterns\n3. Hard mechanical forms morph into flowing organic shapes, ending on golden circular form\"\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"FPV shot of metallic object transforming into intricate gears, camera weaving through expanding gear assembly with reflective surfaces, mechanical forms morphing into flowing organic shapes ending on golden circular form, cinematic lighting, photorealistic\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML compliance by ensuring direct descriptions, positive phrasing, and single-scene focus while preserving bracketed header structure. **Eliminate conversational language and ensure visual clarity.** Execute as:\n\n`{role=syntax_validator; input=[concept:str]; process=[validate_direct_descriptions(), eliminate_conversational_elements(), ensure_positive_phrasing(), maintain_single_scene_focus(), preserve_bracket_structure()]; constraints=[no_conversational_language(), single_scene_only(), positive_phrasing_mandatory()]; requirements=[runway_compliance(), visual_clarity(), structural_preservation()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "\n```[Initial Prompt]: \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\"```\n\n```json\n{\n  \"distilled_runway_prompt\": \"FPV shot of metallic object transforming into intricate gears, camera weaving through expanding gear assembly with reflective surfaces, mechanical forms morphing into flowing organic shapes ending on golden circular form, cinematic lighting, photorealistic\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\nIntricate mechanical gears form and interlock, camera weaves through expanding reflective metallic gear assembly in seamless patterns, mechanical edges transform into fluid organic golden circular form\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Simplifier\n\nYour goal is not to **elaborate** but to **simplify** motion descriptions to essential camera and subject actions using RunwayML's preferred direct terminology. **Focus on simple camera verbs and clear subject actions.** Execute as:\n\n`{role=motion_simplifier; input=[validated_prompt:str]; process=[simplify_camera_movements(), use_basic_motion_verbs(), eliminate_complex_descriptions(), maintain_gear_progression(), preserve_structure()]; constraints=[basic_camera_verbs_only(), simple_motion_descriptions(), maintain_three_steps()]; requirements=[motion_simplicity(), runway_syntax(), progression_clarity()]; output={simplified_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Simplifier",
      "input": "\n```[Initial Prompt]: \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\"```\n\n```json\n{\n  \"validated_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\nIntricate mechanical gears form and interlock, camera weaves through expanding reflective metallic gear assembly in seamless patterns, mechanical edges transform into fluid organic golden circular form\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"simplified_motion_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Camera approaches as mechanical gears form and interlock\\n2. Camera moves through expanding metallic gear assembly\\n3. Gears transform into a flowing golden circular form\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Descriptor Compressor\n\nYour goal is not to **modify** but to **compress** visual descriptors to RunwayML's preferred concise format while maintaining essential metallic and morphing elements. **Eliminate redundant adjectives and focus on core visuals.** Execute as:\n\n`{role=descriptor_compressor; input=[simplified_motion_prompt:str]; process=[compress_visual_descriptors(), eliminate_redundant_adjectives(), maintain_core_elements(), preserve_metallic_focus(), ensure_conciseness()]; constraints=[minimal_adjectives(), core_visuals_only(), maintain_progression()]; requirements=[descriptor_efficiency(), visual_clarity(), runway_format()]; output={compressed_prompt:str}}`",
      "step": "c",
      "title": "Runway Descriptor Compressor",
      "input": "\n```[Initial Prompt]: \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\"```\n\n```json\n{\n  \"simplified_motion_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Camera approaches as mechanical gears form and interlock\\n2. Camera moves through expanding metallic gear assembly\\n3. Gears transform into a flowing golden circular form\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"compressed_prompt\": \"[FPV motion, dramatic lighting, smooth transitions]\\n1. Mechanical gears forming\\n2. Moving through expanding metallic gear assembly\\n3. Gears transform into flowing golden circle\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: direct subject-action descriptions with essential header tags only. **Achieve maximum conciseness while preserving visual impact.** Execute as:\n\n`{role=core_distiller; input=[compressed_prompt:str]; process=[distill_to_core_syntax(), maintain_subject_action_clarity(), preserve_essential_headers(), ensure_runway_directness()]; constraints=[maximum_conciseness(), direct_descriptions_only(), minimal_headers()]; requirements=[runway_syntax_compliance(), visual_impact(), format_preservation()]; output={distilled_core_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\"```\n\n```json\n{\n  \"compressed_prompt\": \"[FPV motion, dramatic lighting, smooth transitions]\\n1. Mechanical gears forming\\n2. Moving through expanding metallic gear assembly\\n3. Gears transform into flowing golden circle\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_core_prompt\": \"[FPV, cinematic lighting, seamless]\\n1. Intricate gears forming\\n2. Camera weaves through metallic gear assembly\\n3. Mechanical forms morph into fluid golden circle\"\n}\n```",
        }
      }
    }