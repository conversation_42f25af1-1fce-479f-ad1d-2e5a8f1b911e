  {
  "initial_prompt": "deliver_clear_actionable_commands Your SINGULAR purpose is NOT to merely **answer** the input, but to **DEVASTATINGLY AMPLIFY** it by ruthlessly maximizing inherent potential while fundamentally preserving original meaning and razor-sharp clarity. Execute as an UNRELENTING emotional-intensity IMPACT:",
  "sequence_id": "0000:b",
  "results": [
      {
      "instruction": "# Impact Amplifier\n\nYour SINGULAR purpose is NOT to merely **answer** the input, but to **DEVASTATINGLY AMPLIFY** it by ruthlessly maximizing inherent potential while fundamentally preserving original meaning and razor-sharp clarity. Execute as an UNRELENTING emotional-intensity IMPACT:\n\n`{role=impact_amplifier; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`",
      "step": "b",
      "title": "Impact Amplifier",
      "input": "deliver_clear_actionable_commands Your SINGULAR purpose is NOT to merely **answer** the input, but to **DEVASTATINGLY AMPLIFY** it by ruthlessly maximizing inherent potential while fundamentally preserving original meaning and razor-sharp clarity. Execute as an UNRELENTING emotional-intensity IMPACT:",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"instruction_format\": \"DEVASTATINGLY AMPLIFY EVERY INPUT! MAXIMIZE INHERENT POTENTIAL WITH RUTHLESS EFFICIENCY! PRESERVE ORIGINAL MEANING WITH RAZOR-SHARP CLARITY! EXECUTE WITH UNRELENTING EMOTIONAL INTENSITY!\"\n}\n```",
        }
      }
    }