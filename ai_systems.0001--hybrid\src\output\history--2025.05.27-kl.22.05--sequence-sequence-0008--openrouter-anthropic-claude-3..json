  {
  "initial_prompt": "A spinning circle emerges from lightstreaks. Emit intense, streaming light that unravels into searing, morphing trails - the light trails unfurls into a golden wheel . Transmute these light trails into a sharply defined metallic gold, enhanced by chromatic shifts and a golden aura. Drive dynamic, hyperreal VFX throughout. Continuously arc camera right for premium dynamism. On reveal, shimmer and dissolve silhouette with ultra-premium cinematic fidelity. Absolutely no text elements; maintain maximal visual sharpness and cinematic focus.",
  "sequence_id": "0008",
  "results": [
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "A spinning circle emerges from lightstreaks. Emit intense, streaming light that unravels into searing, morphing trails - the light trails unfurls into a golden wheel . Transmute these light trails into a sharply defined metallic gold, enhanced by chromatic shifts and a golden aura. Drive dynamic, hyperreal VFX throughout. Continuously arc camera right for premium dynamism. On reveal, shimmer and dissolve silhouette with ultra-premium cinematic fidelity. Absolutely no text elements; maintain maximal visual sharpness and cinematic focus.",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"Metallic gold circle materializing from radiant light streaks. Dynamic light streams extending outward, transforming into a golden wheel structure with defined edges. Vibrant chromatic shifts enhance the metallic gold surface, surrounded by a luminous golden aura. Hyperrealistic VFX quality with cinematic rendering. Camera arcs rightward capturing the circle's spinning motion. The wheel silhouette shimmers with exceptional visual clarity and premium cinematic fidelity.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"A spinning circle emerges from lightstreaks. Emit intense, streaming light that unravels into searing, morphing trails - the light trails unfurls into a golden wheel . Transmute these light trails into a sharply defined metallic gold, enhanced by chromatic shifts and a golden aura. Drive dynamic, hyperreal VFX throughout. Continuously arc camera right for premium dynamism. On reveal, shimmer and dissolve silhouette with ultra-premium cinematic fidelity. Absolutely no text elements; maintain maximal visual sharpness and cinematic focus.\"```\n\n```json\n{\n  \"validated_prompt\": \"Metallic gold circle materializing from radiant light streaks. Dynamic light streams extending outward, transforming into a golden wheel structure with defined edges. Vibrant chromatic shifts enhance the metallic gold surface, surrounded by a luminous golden aura. Hyperrealistic VFX quality with cinematic rendering. Camera arcs rightward capturing the circle's spinning motion. The wheel silhouette shimmers with exceptional visual clarity and premium cinematic fidelity.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": \"A metallic gold circle materializing from radiant light streaks.\\n\\nSubject motion:\\n- Circle spinning steadily\\n- Light streams extending outward, transforming into a golden wheel\\n- Metallic surface shimmering with defined edges\\n\\nCamera motion:\\n- Gentle arcing movement from left to right\\n- Maintaining focus on the central formation\\n\\nScene motion:\\n- Light streaks unraveling and morphing into trails\\n- Chromatic shifts rippling across the gold surface\\n- Golden aura pulsing subtly around the formed wheel\\n\\nStyle:\\n- Hyperrealistic VFX quality\\n- Premium cinematic rendering with exceptional clarity\\n- No text elements\"\n}```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"A spinning circle emerges from lightstreaks. Emit intense, streaming light that unravels into searing, morphing trails - the light trails unfurls into a golden wheel . Transmute these light trails into a sharply defined metallic gold, enhanced by chromatic shifts and a golden aura. Drive dynamic, hyperreal VFX throughout. Continuously arc camera right for premium dynamism. On reveal, shimmer and dissolve silhouette with ultra-premium cinematic fidelity. Absolutely no text elements; maintain maximal visual sharpness and cinematic focus.\"```\n\n```json\n{\n  \"structured_motion_prompt\": \"A metallic gold circle materializing from radiant light streaks.\\n\\nSubject motion:\\n- Circle spinning steadily\\n- Light streams extending outward, transforming into a golden wheel\\n- Metallic surface shimmering with defined edges\\n\\nCamera motion:\\n- Gentle arcing movement from left to right\\n- Maintaining focus on the central formation\\n\\nScene motion:\\n- Light streaks unraveling and morphing into trails\\n- Chromatic shifts rippling across the gold surface\\n- Golden aura pulsing subtly around the formed wheel\\n\\nStyle:\\n- Hyperrealistic VFX quality\\n- Premium cinematic rendering with exceptional clarity\\n- No text elements\"\n}```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": \"A metallic gold circle materializing from radiant light streaks.\\n\\nSubject motion:\\n- Circle spinning steadily\\n- Glowing light beams extending outward, crystallizing into a golden wheel\\n- Reflective surface with defined edges\\n\\nCamera motion:\\n- Smooth tracking shot, arcing right\\n- Shallow depth of field maintaining focus on central element\\n\\nScene motion:\\n- Light rays unfolding and morphing fluidly\\n- Prismatic highlights across metallic surface\\n- Golden glow radiating from formed wheel\\n\\nStyle:\\n- Photorealistic VFX\\n- Cinematic lighting\\n- High contrast\\n- Ultra HD\\n- Film grain\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"A spinning circle emerges from lightstreaks. Emit intense, streaming light that unravels into searing, morphing trails - the light trails unfurls into a golden wheel . Transmute these light trails into a sharply defined metallic gold, enhanced by chromatic shifts and a golden aura. Drive dynamic, hyperreal VFX throughout. Continuously arc camera right for premium dynamism. On reveal, shimmer and dissolve silhouette with ultra-premium cinematic fidelity. Absolutely no text elements; maintain maximal visual sharpness and cinematic focus.\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": \"A metallic gold circle materializing from radiant light streaks.\\n\\nSubject motion:\\n- Circle spinning steadily\\n- Glowing light beams extending outward, crystallizing into a golden wheel\\n- Reflective surface with defined edges\\n\\nCamera motion:\\n- Smooth tracking shot, arcing right\\n- Shallow depth of field maintaining focus on central element\\n\\nScene motion:\\n- Light rays unfolding and morphing fluidly\\n- Prismatic highlights across metallic surface\\n- Golden glow radiating from formed wheel\\n\\nStyle:\\n- Photorealistic VFX\\n- Cinematic lighting\\n- High contrast\\n- Ultra HD\\n- Film grain\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"Metallic gold circle materializing from light streaks, spinning, transforming into a golden wheel. Light rays unfold with prismatic highlights. Camera arcs right. Photorealistic VFX, cinematic lighting, ultra HD.\"\n}\n```",
        }
      }
    }