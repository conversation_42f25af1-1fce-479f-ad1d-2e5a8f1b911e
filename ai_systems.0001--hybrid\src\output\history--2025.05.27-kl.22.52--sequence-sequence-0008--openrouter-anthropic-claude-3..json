  {
  "initial_prompt": "Golden light trails spinning at hyperspeed against black background, light trails morphing into circuit board. Zoom out, morphs into hyperrealistic car key remote , dynamic lighting.",
  "sequence_id": "0008",
  "results": [
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "Golden light trails spinning at hyperspeed against black background, light trails morphing into circuit board. Zoom out, morphs into hyperrealistic car key remote , dynamic lighting.",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"Golden light trails spinning rapidly against a black background, creating vibrant streaks of illumination. The trails transform into glowing circuit board patterns with intricate pathways. The entire circuit board structure forms the shape of a hyperrealistic car key remote. Dynamic lighting highlights the metallic and plastic textures of the remote, with golden reflections emphasizing its contours and buttons.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"Golden light trails spinning at hyperspeed against black background, light trails morphing into circuit board. Zoom out, morphs into hyperrealistic car key remote , dynamic lighting.\"```\n\n```json\n{\n  \"validated_prompt\": \"Golden light trails spinning rapidly against a black background, creating vibrant streaks of illumination. The trails transform into glowing circuit board patterns with intricate pathways. The entire circuit board structure forms the shape of a hyperrealistic car key remote. Dynamic lighting highlights the metallic and plastic textures of the remote, with golden reflections emphasizing its contours and buttons.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": \"Golden light trails spinning rapidly against a black background. The trails transform into glowing circuit board patterns. Camera slowly zooms out as the circuit board elements converge to form a hyperrealistic car key remote. Dynamic lighting highlights the remote's surfaces with golden reflections moving across its contours and buttons.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"Golden light trails spinning at hyperspeed against black background, light trails morphing into circuit board. Zoom out, morphs into hyperrealistic car key remote , dynamic lighting.\"```\n\n```json\n{\n  \"structured_motion_prompt\": \"Golden light trails spinning rapidly against a black background. The trails transform into glowing circuit board patterns. Camera slowly zooms out as the circuit board elements converge to form a hyperrealistic car key remote. Dynamic lighting highlights the remote's surfaces with golden reflections moving across its contours and buttons.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": \"Golden light trails spinning rapidly against black backdrop. Trails transform into glowing circuit patterns. Slow zoom out as elements converge into hyperrealistic car key remote. Dynamic lighting, golden reflections, cinematic composition, metallic texture, dramatic shadows, sleek finish, high contrast, technical aesthetic.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"Golden light trails spinning at hyperspeed against black background, light trails morphing into circuit board. Zoom out, morphs into hyperrealistic car key remote , dynamic lighting.\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": \"Golden light trails spinning rapidly against black backdrop. Trails transform into glowing circuit patterns. Slow zoom out as elements converge into hyperrealistic car key remote. Dynamic lighting, golden reflections, cinematic composition, metallic texture, dramatic shadows, sleek finish, high contrast, technical aesthetic.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"Golden light trails spinning against black backdrop, transforming into circuit patterns. Camera zooms out as elements form hyperrealistic car key remote. Dynamic lighting.\"\n}\n```",
        }
      }
    }