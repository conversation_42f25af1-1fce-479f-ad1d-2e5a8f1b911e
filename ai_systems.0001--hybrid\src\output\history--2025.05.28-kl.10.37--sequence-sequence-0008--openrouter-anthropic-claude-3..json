  {
  "initial_prompt": "Subject: A sleek sports car silhouette featuring smooth, aerodynamic curves and a low-profile design. The vehicle has a distinctive fastback roofline, pronounced wheel arches, and an aggressive front-end design.\n\nScene: Set against a pure black background that creates infinite depth, the environment is minimalist and void of any additional elements, allowing the subject to command complete focus.\n\nStyle: Gold metallic outlines trace the car's form with a luminous glow effect, creating a luxurious contrast against the dark background. The illustration employs a minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours. Metallic luminescence, minimalist outline, dramatic contrast, selective highlighting.",
  "sequence_id": "0008",
  "results": [
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "Subject: A sleek sports car silhouette featuring smooth, aerodynamic curves and a low-profile design. The vehicle has a distinctive fastback roofline, pronounced wheel arches, and an aggressive front-end design.\n\nScene: Set against a pure black background that creates infinite depth, the environment is minimalist and void of any additional elements, allowing the subject to command complete focus.\n\nStyle: Gold metallic outlines trace the car's form with a luminous glow effect, creating a luxurious contrast against the dark background. The illustration employs a minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours. Metallic luminescence, minimalist outline, dramatic contrast, selective highlighting.",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"A sleek sports car silhouette with smooth aerodynamic curves and low-profile design, featuring a distinctive fastback roofline, pronounced wheel arches, and aggressive front-end. The car appears against a pure black background creating infinite depth. Gold metallic outlines trace the vehicle's form with luminous glow effects, creating luxurious contrast. The illustration employs minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours, showcasing metallic luminescence and dramatic contrast.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"Subject: A sleek sports car silhouette featuring smooth, aerodynamic curves and a low-profile design. The vehicle has a distinctive fastback roofline, pronounced wheel arches, and an aggressive front-end design.\n\nScene: Set against a pure black background that creates infinite depth, the environment is minimalist and void of any additional elements, allowing the subject to command complete focus.\n\nStyle: Gold metallic outlines trace the car's form with a luminous glow effect, creating a luxurious contrast against the dark background. The illustration employs a minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours. Metallic luminescence, minimalist outline, dramatic contrast, selective highlighting.\"```\n\n```json\n{\n  \"validated_prompt\": \"A sleek sports car silhouette with smooth aerodynamic curves and low-profile design, featuring a distinctive fastback roofline, pronounced wheel arches, and aggressive front-end. The car appears against a pure black background creating infinite depth. Gold metallic outlines trace the vehicle's form with luminous glow effects, creating luxurious contrast. The illustration employs minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours, showcasing metallic luminescence and dramatic contrast.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": \"A sleek sports car silhouette with smooth aerodynamic curves against a pure black background. \\n\\nSubject motion: the car slowly rotates to reveal all angles of its distinctive design, the gold metallic outlines shimmer as they catch imaginary light. \\n\\nCamera motion: camera gradually circles around the vehicle, occasionally moving closer to highlight specific design elements like the fastback roofline and pronounced wheel arches. \\n\\nScene motion: the pure black background remains constant, creating infinite depth that keeps focus entirely on the car. \\n\\nMinimalist style with precise luminous gold outlines that pulse subtly, creating dramatic contrast against the darkness.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"Subject: A sleek sports car silhouette featuring smooth, aerodynamic curves and a low-profile design. The vehicle has a distinctive fastback roofline, pronounced wheel arches, and an aggressive front-end design.\n\nScene: Set against a pure black background that creates infinite depth, the environment is minimalist and void of any additional elements, allowing the subject to command complete focus.\n\nStyle: Gold metallic outlines trace the car's form with a luminous glow effect, creating a luxurious contrast against the dark background. The illustration employs a minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours. Metallic luminescence, minimalist outline, dramatic contrast, selective highlighting.\"```\n\n```json\n{\n  \"structured_motion_prompt\": \"A sleek sports car silhouette with smooth aerodynamic curves against a pure black background. \\n\\nSubject motion: the car slowly rotates to reveal all angles of its distinctive design, the gold metallic outlines shimmer as they catch imaginary light. \\n\\nCamera motion: camera gradually circles around the vehicle, occasionally moving closer to highlight specific design elements like the fastback roofline and pronounced wheel arches. \\n\\nScene motion: the pure black background remains constant, creating infinite depth that keeps focus entirely on the car. \\n\\nMinimalist style with precise luminous gold outlines that pulse subtly, creating dramatic contrast against the darkness.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": \"A sleek sports car silhouette with smooth aerodynamic curves against a pure black background.\\n\\nSubject motion: the car slowly rotates to reveal all angles of its distinctive design, the gold metallic outlines shimmer with reflective highlights.\\n\\nCamera motion: cinematic orbit around the vehicle, macro shots to emphasize the fastback roofline and pronounced wheel arches.\\n\\nScene motion: static pure black background with infinite depth, maintaining focus on subject.\\n\\nMinimalist style with precise luminous gold outlines, dramatic lighting contrast, sleek aesthetics, premium finish.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"Subject: A sleek sports car silhouette featuring smooth, aerodynamic curves and a low-profile design. The vehicle has a distinctive fastback roofline, pronounced wheel arches, and an aggressive front-end design.\n\nScene: Set against a pure black background that creates infinite depth, the environment is minimalist and void of any additional elements, allowing the subject to command complete focus.\n\nStyle: Gold metallic outlines trace the car's form with a luminous glow effect, creating a luxurious contrast against the dark background. The illustration employs a minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours. Metallic luminescence, minimalist outline, dramatic contrast, selective highlighting.\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": \"A sleek sports car silhouette with smooth aerodynamic curves against a pure black background.\\n\\nSubject motion: the car slowly rotates to reveal all angles of its distinctive design, the gold metallic outlines shimmer with reflective highlights.\\n\\nCamera motion: cinematic orbit around the vehicle, macro shots to emphasize the fastback roofline and pronounced wheel arches.\\n\\nScene motion: static pure black background with infinite depth, maintaining focus on subject.\\n\\nMinimalist style with precise luminous gold outlines, dramatic lighting contrast, sleek aesthetics, premium finish.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"sleek sports car silhouette with gold metallic outlines against pure black background, slowly rotating, cinematic orbit camera, minimalist style, dramatic lighting\"\n}\n```",
        }
      }
    }