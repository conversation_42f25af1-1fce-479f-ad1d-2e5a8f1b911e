  {
  "initial_prompt": "A sleek sports car silhouette with smooth aerodynamic curves and low-profile design, featuring a distinctive fastback roofline, pronounced wheel arches, and aggressive front-end. The car appears against a pure black background creating infinite depth. Gold metallic outlines trace the vehicle's form with luminous glow effects, creating luxurious contrast. The illustration employs minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours, showcasing metallic luminescence and dramatic contrast. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]",
  "sequence_id": "0008",
  "results": [
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "A sleek sports car silhouette with smooth aerodynamic curves and low-profile design, featuring a distinctive fastback roofline, pronounced wheel arches, and aggressive front-end. The car appears against a pure black background creating infinite depth. Gold metallic outlines trace the vehicle's form with luminous glow effects, creating luxurious contrast. The illustration employs minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours, showcasing metallic luminescence and dramatic contrast. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"validated_prompt\": \"A sleek sports car silhouette with smooth aerodynamic curves and low-profile design, featuring a distinctive fastback roofline, pronounced wheel arches, and aggressive front-end against a pure black background. Gold metallic outlines trace the vehicle's form with luminous glow effects. The illustration employs minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours, showcasing metallic luminescence and dramatic contrast. Dynamic lighting creates flash effects around the golden aura of the car, while the metallic surfaces shine brilliantly. The image has a cinematic quality with elegant vignetting framing the central subject.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "{\n  \"initial_prompt\": \"A sleek sports car silhouette with smooth aerodynamic curves and low-profile design, featuring a distinctive fastback roofline, pronounced wheel arches, and aggressive front-end. The car appears against a pure black background creating infinite depth. Gold metallic outlines trace the vehicle's form with luminous glow effects, creating luxurious contrast. The illustration employs minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours, showcasing metallic luminescence and dramatic contrast. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"validated_prompt\": \"A sleek sports car silhouette with smooth aerodynamic curves and low-profile design, featuring a distinctive fastback roofline, pronounced wheel arches, and aggressive front-end against a pure black background. Gold metallic outlines trace the vehicle's form with luminous glow effects. The illustration employs minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours, showcasing metallic luminescence and dramatic contrast. Dynamic lighting creates flash effects around the golden aura of the car, while the metallic surfaces shine brilliantly. The image has a cinematic quality with elegant vignetting framing the central subject.\"\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"the sports car silhouette slowly rotates 360 degrees, revealing all its aerodynamic curves and angles\",\n    \"camera_motion\": \"camera smoothly glides around the vehicle, occasionally moving closer to highlight specific design details\",\n    \"scene_motion\": \"the gold metallic outlines pulse with light that flows along the car's contours, creating waves of luminescence against the black background\",\n    \"style_descriptors\": \"super crisp, hyperrealistic metallic textures with dynamic lighting that creates brief flash highlights as the car rotates\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "{\n  \"initial_prompt\": \"A sleek sports car silhouette with smooth aerodynamic curves and low-profile design, featuring a distinctive fastback roofline, pronounced wheel arches, and aggressive front-end. The car appears against a pure black background creating infinite depth. Gold metallic outlines trace the vehicle's form with luminous glow effects, creating luxurious contrast. The illustration employs minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours, showcasing metallic luminescence and dramatic contrast. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"the sports car silhouette slowly rotates 360 degrees, revealing all its aerodynamic curves and angles\",\n    \"camera_motion\": \"camera smoothly glides around the vehicle, occasionally moving closer to highlight specific design details\",\n    \"scene_motion\": \"the gold metallic outlines pulse with light that flows along the car's contours, creating waves of luminescence against the black background\",\n    \"style_descriptors\": \"super crisp, hyperrealistic metallic textures with dynamic lighting that creates brief flash highlights as the car rotates\"\n  }\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"sports car silhouette rotates 360_degrees, showcasing aerodynamic curves\",\n    \"camera_motion\": \"dolly_around smooth_tracking close_up reveal\",\n    \"scene_motion\": \"metallic_outline pulse glow_effect flow_motion\",\n    \"style_descriptors\": \"ultra_hyperreal super_crisp dynamic_flash golden_aura metallic_shine vignette high_end_cinematic morph motion_blur\"\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"A sleek sports car silhouette with smooth aerodynamic curves and low-profile design, featuring a distinctive fastback roofline, pronounced wheel arches, and aggressive front-end. The car appears against a pure black background creating infinite depth. Gold metallic outlines trace the vehicle's form with luminous glow effects, creating luxurious contrast. The illustration employs minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours, showcasing metallic luminescence and dramatic contrast. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"sports car silhouette rotates 360_degrees, showcasing aerodynamic curves\",\n    \"camera_motion\": \"dolly_around smooth_tracking close_up reveal\",\n    \"scene_motion\": \"metallic_outline pulse glow_effect flow_motion\",\n    \"style_descriptors\": \"ultra_hyperreal super_crisp dynamic_flash golden_aura metallic_shine vignette high_end_cinematic morph motion_blur\"\n  }\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"sports car silhouette with gold metallic outlines against black background, golden aura, ultra hyperreal, super crisp, dynamic flash, motion blur\"\n}\n```",
        }
      }
    }