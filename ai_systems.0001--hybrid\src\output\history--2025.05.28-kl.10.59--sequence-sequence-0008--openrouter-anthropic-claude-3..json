  {
  "initial_prompt": "INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT EACH SHOT INSTEAD OF BAD TRANSITIONS\n\nA sleek sports car silhouette with smooth aerodynamic curves and low-profile design, featuring a distinctive fastback roofline, pronounced wheel arches, and aggressive front-end. The car appears against a pure black background creating infinite depth. Gold metallic outlines trace the vehicle's form with luminous glow effects, creating luxurious contrast. The illustration employs minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours, showcasing metallic luminescence and dramatic contrast. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]",
  "sequence_id": "0008",
  "results": [
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT EACH SHOT INSTEAD OF BAD TRANSITIONS\n\nA sleek sports car silhouette with smooth aerodynamic curves and low-profile design, featuring a distinctive fastback roofline, pronounced wheel arches, and aggressive front-end. The car appears against a pure black background creating infinite depth. Gold metallic outlines trace the vehicle's form with luminous glow effects, creating luxurious contrast. The illustration employs minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours, showcasing metallic luminescence and dramatic contrast. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"validated_prompt\": \"Sleek sports car silhouette with smooth aerodynamic curves and low-profile design, featuring distinctive fastback roofline, pronounced wheel arches, and aggressive front-end. Pure black background creates infinite depth. Gold metallic outlines trace the vehicle's form with luminous glow effects. Minimalist illustration with precise linework and selective highlighting emphasizes the vehicle's contours. Metallic luminescence with dramatic contrast. Ultra hyperreal, super crisp image with dynamic flash lighting. Golden aura surrounds the car. High-end cinematic quality with metallic shine and vignette edges.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "{\n  \"initial_prompt\": \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT EACH SHOT INSTEAD OF BAD TRANSITIONS\\n\\nA sleek sports car silhouette with smooth aerodynamic curves and low-profile design, featuring a distinctive fastback roofline, pronounced wheel arches, and aggressive front-end. The car appears against a pure black background creating infinite depth. Gold metallic outlines trace the vehicle's form with luminous glow effects, creating luxurious contrast. The illustration employs minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours, showcasing metallic luminescence and dramatic contrast. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"validated_prompt\": \"Sleek sports car silhouette with smooth aerodynamic curves and low-profile design, featuring distinctive fastback roofline, pronounced wheel arches, and aggressive front-end. Pure black background creates infinite depth. Gold metallic outlines trace the vehicle's form with luminous glow effects. Minimalist illustration with precise linework and selective highlighting emphasizes the vehicle's contours. Metallic luminescence with dramatic contrast. Ultra hyperreal, super crisp image with dynamic flash lighting. Golden aura surrounds the car. High-end cinematic quality with metallic shine and vignette edges.\"\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"The sports car rotates slowly, revealing its sleek silhouette from different angles\",\n    \"camera_motion\": \"Camera slowly circles the car while occasionally pushing in to highlight specific design elements\",\n    \"scene_motion\": \"Gold metallic outlines pulse and illuminate along the car's form, creating waves of luminous energy against the black background\",\n    \"style_descriptors\": \"Ultra hyperreal, snappy smooth camera animation, dynamic flash lighting, golden aura, metallic shine\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "{\n  \"initial_prompt\": \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT EACH SHOT INSTEAD OF BAD TRANSITIONS\\n\\nA sleek sports car silhouette with smooth aerodynamic curves and low-profile design, featuring a distinctive fastback roofline, pronounced wheel arches, and aggressive front-end. The car appears against a pure black background creating infinite depth. Gold metallic outlines trace the vehicle's form with luminous glow effects, creating luxurious contrast. The illustration employs minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours, showcasing metallic luminescence and dramatic contrast. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"The sports car rotates slowly, revealing its sleek silhouette from different angles\",\n    \"camera_motion\": \"Camera slowly circles the car while occasionally pushing in to highlight specific design elements\",\n    \"scene_motion\": \"Gold metallic outlines pulse and illuminate along the car's form, creating waves of luminous energy against the black background\",\n    \"style_descriptors\": \"Ultra hyperreal, snappy smooth camera animation, dynamic flash lighting, golden aura, metallic shine\"\n  }\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"The sports car rotates slowly, revealing its silhouette from different angles\",\n    \"camera_motion\": \"Orbital camera with push_in moments highlighting specific design elements\",\n    \"scene_motion\": \"Gold metallic outlines pulse with glow_effect against black background\",\n    \"style_descriptors\": \"hyperreal, smooth_camera, dynamic_lighting, high_contrast, metallic_finish, anamorphic, cinematic_look\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "{\n  \"initial_prompt\": \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT EACH SHOT INSTEAD OF BAD TRANSITIONS\\n\\nA sleek sports car silhouette with smooth aerodynamic curves and low-profile design, featuring a distinctive fastback roofline, pronounced wheel arches, and aggressive front-end. The car appears against a pure black background creating infinite depth. Gold metallic outlines trace the vehicle's form with luminous glow effects, creating luxurious contrast. The illustration employs minimalist approach with precise linework and selective highlighting that emphasizes the vehicle's contours, showcasing metallic luminescence and dramatic contrast. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"The sports car rotates slowly, revealing its silhouette from different angles\",\n    \"camera_motion\": \"Orbital camera with push_in moments highlighting specific design elements\",\n    \"scene_motion\": \"Gold metallic outlines pulse with glow_effect against black background\",\n    \"style_descriptors\": \"hyperreal, smooth_camera, dynamic_lighting, high_contrast, metallic_finish, anamorphic, cinematic_look\"\n  }\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"distilled_runway_prompt\": \"Sports car silhouette with gold metallic outlines glowing against black background. Orbital camera with push-in moments. Hyperreal, dynamic lighting, cinematic.\"\n}",
        }
      }
    }