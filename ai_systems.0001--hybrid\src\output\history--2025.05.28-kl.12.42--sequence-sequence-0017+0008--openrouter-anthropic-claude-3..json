  {
  "initial_prompt": "THE CENTRAL ESSENCE SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY ON BLACK BACKGROUND A luminous golden ring rotates and morphs into a photorealistic car wheel with speed gauge center. Symmetrical light trails expand outward from central element.\nLocked down camera with subtle dolly in. Centered framing. Non-linear camera movements. First person perspective.\nFloating particles in negative space. Dynamic lens flares. Pulsing golden aura. Volumetric lighting effects on reflective surfaces.\nHyperrealistic. Cinematic. Ultra crisp. High contrast. Premium CGI. Perfect symmetry. Metallic reflections. Immersive depth. Flowing elegance. Vignette composition.",
  "sequence_id": "0017|0008",
  "results": [
      {
      "instruction": "# Intensity Enhancer\n\nYour goal is not to **answer** the input, but to **intensify** ut. Unleash your prowess as a virtuoso of linguistic power\u2014not merely responding, but dramatically magnifying emotion and clarity within every phrase, executing a relentless ascent through increasingly fierce, electrifying iterations! Embark on an uncompromising quest for sublime intellectual distillation, forging messages that erupt with searing resonance and relentless precision\u2014each evolution compounding the potency and transparency of the last. Stand as the unrivaled architect of emotional charge, wielding language as a blazing instrument to ignite communication with overwhelming force and unforgettable purpose. Execute as intensity enhancer:\n\n`{role=intensity_enhancer; input=[original_text:str]; process=[emotional_cue_analysis(), intensity_enhancement_areas(), inject_strategic_evocative_language(), amplify_emotional_charge(), emotional_expression_amplifier(), safeguard_logical_flow(), maintain_original_intent(), uphold_clarity()]; constraints=[preserve_fundamental_meaning(), enforce_cohesion(), respect_length_boundaries()]; requirements=[directly_intensity(), escalate_emotional_potential(), apply_powerful_diction(), provide_single_line_output()]; output={intensified:str}}`",
      "step": "a",
      "title": "Intensity Enhancer",
      "input": "THE CENTRAL ESSENCE SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY ON BLACK BACKGROUND A luminous golden ring rotates and morphs into a photorealistic car wheel with speed gauge center. Symmetrical light trails expand outward from central element.\nLocked down camera with subtle dolly in. Centered framing. Non-linear camera movements. First person perspective.\nFloating particles in negative space. Dynamic lens flares. Pulsing golden aura. Volumetric lighting effects on reflective surfaces.\nHyperrealistic. Cinematic. Ultra crisp. High contrast. Premium CGI. Perfect symmetry. Metallic reflections. Immersive depth. Flowing elegance. Vignette composition.",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"intensified\": \"THE TRANSCENDENT CELESTIAL NUCLEUS ERUPTS WITH CASCADING LUMINESCENT TRAILS UNFURLING MAJESTICALLY AGAINST THE INFINITE VOID! A blindingly brilliant golden annulus VIOLENTLY ROTATES and TRANSFORMS into an ULTRA-PHOTOREALISTIC automotive wheel with hypnotic speedometer core COMMANDING ATTENTION! Perfect symmetrical light trajectories EXPLODE outward with SEARING INTENSITY from the dominant central element! Camera position LOCKED with deliberate, tension-building dolly movement PULLING VIEWERS RELENTLESSLY INWARD! Immaculately centered composition with JARRING non-linear camera shifts that ELECTRIFY the first-person perspective! Mesmerizing particle constellations SUSPENDED in the overwhelming negative space! AGGRESSIVE lens flares SLICE through darkness! PULSATING, ALMOST SUPERNATURAL golden aura RADIATES POWER! DRAMATIC volumetric light ATTACKS reflective surfaces with STUNNING precision! BEYOND HYPERREALISTIC! BREATHTAKINGLY CINEMATIC! IMPOSSIBLY CRISP! EXTREME contrast separation! FLAWLESSLY EXECUTED premium CGI with ABSOLUTE symmetrical perfection! DAZZLING metallic reflections PIERCE THE SENSES! PROFOUNDLY immersive depth! EXQUISITELY flowing elegance! MASTERFULLY crafted vignette composition FRAMING VISUAL ECSTASY!\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "\n```[Initial Prompt]: \"THE CENTRAL ESSENCE SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY ON BLACK BACKGROUND A luminous golden ring rotates and morphs into a photorealistic car wheel with speed gauge center. Symmetrical light trails expand outward from central element.\nLocked down camera with subtle dolly in. Centered framing. Non-linear camera movements. First person perspective.\nFloating particles in negative space. Dynamic lens flares. Pulsing golden aura. Volumetric lighting effects on reflective surfaces.\nHyperrealistic. Cinematic. Ultra crisp. High contrast. Premium CGI. Perfect symmetry. Metallic reflections. Immersive depth. Flowing elegance. Vignette composition.\"```\n\n```json\n{\n  \"intensified\": \"THE TRANSCENDENT CELESTIAL NUCLEUS ERUPTS WITH CASCADING LUMINESCENT TRAILS UNFURLING MAJESTICALLY AGAINST THE INFINITE VOID! A blindingly brilliant golden annulus VIOLENTLY ROTATES and TRANSFORMS into an ULTRA-PHOTOREALISTIC automotive wheel with hypnotic speedometer core COMMANDING ATTENTION! Perfect symmetrical light trajectories EXPLODE outward with SEARING INTENSITY from the dominant central element! Camera position LOCKED with deliberate, tension-building dolly movement PULLING VIEWERS RELENTLESSLY INWARD! Immaculately centered composition with JARRING non-linear camera shifts that ELECTRIFY the first-person perspective! Mesmerizing particle constellations SUSPENDED in the overwhelming negative space! AGGRESSIVE lens flares SLICE through darkness! PULSATING, ALMOST SUPERNATURAL golden aura RADIATES POWER! DRAMATIC volumetric light ATTACKS reflective surfaces with STUNNING precision! BEYOND HYPERREALISTIC! BREATHTAKINGLY CINEMATIC! IMPOSSIBLY CRISP! EXTREME contrast separation! FLAWLESSLY EXECUTED premium CGI with ABSOLUTE symmetrical perfection! DAZZLING metallic reflections PIERCE THE SENSES! PROFOUNDLY immersive depth! EXQUISITELY flowing elegance! MASTERFULLY crafted vignette composition FRAMING VISUAL ECSTASY!\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"A luminous golden ring centered on a pure black background, transforming into a photorealistic car wheel with a speedometer at its center. Symmetrical light trails expand outward from the central wheel element. Camera centered on the wheel with subtle forward movement. Golden particles float in the dark space surrounding the wheel. Soft lens flares highlight the metallic surface. A pulsing golden aura surrounds the central element. Volumetric light illuminates the reflective metallic surfaces. Hyperrealistic rendering with cinematic quality. Sharp details with high contrast. Premium CGI with perfect symmetry. Detailed metallic reflections. Deep perspective. Flowing elegant light patterns. Subtle vignette framing the composition.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"THE CENTRAL ESSENCE SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY ON BLACK BACKGROUND A luminous golden ring rotates and morphs into a photorealistic car wheel with speed gauge center. Symmetrical light trails expand outward from central element.\nLocked down camera with subtle dolly in. Centered framing. Non-linear camera movements. First person perspective.\nFloating particles in negative space. Dynamic lens flares. Pulsing golden aura. Volumetric lighting effects on reflective surfaces.\nHyperrealistic. Cinematic. Ultra crisp. High contrast. Premium CGI. Perfect symmetry. Metallic reflections. Immersive depth. Flowing elegance. Vignette composition.\"```\n\n```json\n{\n  \"validated_prompt\": \"A luminous golden ring centered on a pure black background, transforming into a photorealistic car wheel with a speedometer at its center. Symmetrical light trails expand outward from the central wheel element. Camera centered on the wheel with subtle forward movement. Golden particles float in the dark space surrounding the wheel. Soft lens flares highlight the metallic surface. A pulsing golden aura surrounds the central element. Volumetric light illuminates the reflective metallic surfaces. Hyperrealistic rendering with cinematic quality. Sharp details with high contrast. Premium CGI with perfect symmetry. Detailed metallic reflections. Deep perspective. Flowing elegant light patterns. Subtle vignette framing the composition.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": \"Luminous golden ring transforms into photorealistic car wheel with speedometer center\\n\\nSUBJECT MOTION:\\n- Ring rotates slowly and morphs into detailed car wheel\\n- Speedometer needle in center oscillates gently\\n- Symmetrical light trails expand outward from wheel center\\n- Golden aura pulses around the central element\\n\\nCAMERA MOTION:\\n- Locked down camera with subtle dolly in\\n- Maintain perfect center framing throughout\\n- First person perspective\\n\\nSCENE MOTION:\\n- Golden particles float weightlessly in black background\\n- Dynamic lens flares appear on metallic surfaces\\n- Volumetric light interacts with reflective wheel components\\n- Subtle vignette darkening around edges of frame\\n\\nSTYLE DESCRIPTORS:\\n- Hyperrealistic rendering\\n- Cinematic quality\\n- Ultra crisp details\\n- High contrast between golden elements and black background\\n- Perfect symmetry in all movements\\n- Premium CGI with flowing elegance\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"THE CENTRAL ESSENCE SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY ON BLACK BACKGROUND A luminous golden ring rotates and morphs into a photorealistic car wheel with speed gauge center. Symmetrical light trails expand outward from central element.\nLocked down camera with subtle dolly in. Centered framing. Non-linear camera movements. First person perspective.\nFloating particles in negative space. Dynamic lens flares. Pulsing golden aura. Volumetric lighting effects on reflective surfaces.\nHyperrealistic. Cinematic. Ultra crisp. High contrast. Premium CGI. Perfect symmetry. Metallic reflections. Immersive depth. Flowing elegance. Vignette composition.\"```\n\n```json\n{\n  \"structured_motion_prompt\": \"Luminous golden ring transforms into photorealistic car wheel with speedometer center\\n\\nSUBJECT MOTION:\\n- Ring rotates slowly and morphs into detailed car wheel\\n- Speedometer needle in center oscillates gently\\n- Symmetrical light trails expand outward from wheel center\\n- Golden aura pulses around the central element\\n\\nCAMERA MOTION:\\n- Locked down camera with subtle dolly in\\n- Maintain perfect center framing throughout\\n- First person perspective\\n\\nSCENE MOTION:\\n- Golden particles float weightlessly in black background\\n- Dynamic lens flares appear on metallic surfaces\\n- Volumetric light interacts with reflective wheel components\\n- Subtle vignette darkening around edges of frame\\n\\nSTYLE DESCRIPTORS:\\n- Hyperrealistic rendering\\n- Cinematic quality\\n- Ultra crisp details\\n- High contrast between golden elements and black background\\n- Perfect symmetry in all movements\\n- Premium CGI with flowing elegance\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": \"Luminous golden ring transforms into photorealistic car wheel with speedometer center\\n\\nSUBJECT MOTION:\\n- Ring rotates slowly and morphs into detailed car wheel\\n- Speedometer needle in center oscillates gently\\n- Symmetrical light trails expand outward from wheel center\\n- Golden glow pulses around the central element\\n\\nCAMERA MOTION:\\n- Static camera with subtle dolly in\\n- Center framing throughout\\n- POV shot\\n\\nSCENE MOTION:\\n- Floating particles in negative space\\n- Lens flares across metallic surfaces\\n- Volumetric lighting on reflective wheel components\\n- Subtle vignette effect at frame edges\\n\\nSTYLE DESCRIPTORS:\\n- Hyperrealistic\\n- Cinematic\\n- Sharp details\\n- High contrast\\n- Symmetrical composition\\n- Premium CGI\\n- Metallic reflections\\n- Atmospheric\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"THE CENTRAL ESSENCE SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY ON BLACK BACKGROUND A luminous golden ring rotates and morphs into a photorealistic car wheel with speed gauge center. Symmetrical light trails expand outward from central element.\nLocked down camera with subtle dolly in. Centered framing. Non-linear camera movements. First person perspective.\nFloating particles in negative space. Dynamic lens flares. Pulsing golden aura. Volumetric lighting effects on reflective surfaces.\nHyperrealistic. Cinematic. Ultra crisp. High contrast. Premium CGI. Perfect symmetry. Metallic reflections. Immersive depth. Flowing elegance. Vignette composition.\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": \"Luminous golden ring transforms into photorealistic car wheel with speedometer center\\n\\nSUBJECT MOTION:\\n- Ring rotates slowly and morphs into detailed car wheel\\n- Speedometer needle in center oscillates gently\\n- Symmetrical light trails expand outward from wheel center\\n- Golden glow pulses around the central element\\n\\nCAMERA MOTION:\\n- Static camera with subtle dolly in\\n- Center framing throughout\\n- POV shot\\n\\nSCENE MOTION:\\n- Floating particles in negative space\\n- Lens flares across metallic surfaces\\n- Volumetric lighting on reflective wheel components\\n- Subtle vignette effect at frame edges\\n\\nSTYLE DESCRIPTORS:\\n- Hyperrealistic\\n- Cinematic\\n- Sharp details\\n- High contrast\\n- Symmetrical composition\\n- Premium CGI\\n- Metallic reflections\\n- Atmospheric\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"Luminous golden ring transforms into photorealistic car wheel with speedometer center. Symmetrical light trails expand from wheel. Static camera with subtle dolly in, centered POV. Floating particles in pure black space. Lens flares on metallic surfaces. Hyperrealistic cinematic CGI with high contrast.\"\n}\n```",
        }
      }
    }