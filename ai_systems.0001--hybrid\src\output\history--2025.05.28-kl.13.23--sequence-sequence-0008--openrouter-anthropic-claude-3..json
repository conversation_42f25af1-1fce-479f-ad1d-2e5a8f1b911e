  {
  "initial_prompt": "INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\nseamlessly morphing camera transitions fpv-spin-zoomout beautifully-splined-animation",
  "sequence_id": "0008",
  "results": [
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\nseamlessly morphing camera transitions fpv-spin-zoomout beautifully-splined-animation",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"Hyper-realistic 3D render of a camera movement with seamless morphing transitions, featuring dynamic FPV spin and zoom-out effects with beautifully splined animation paths, rendered with cinematic lighting and smooth motion blur.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\nseamlessly morphing camera transitions fpv-spin-zoomout beautifully-splined-animation\"```\n\n```json\n{\n  \"validated_prompt\": \"Hyper-realistic 3D render of a camera movement with seamless morphing transitions, featuring dynamic FPV spin and zoom-out effects with beautifully splined animation paths, rendered with cinematic lighting and smooth motion blur.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": \"Hyper-realistic 3D render with:\\n\\nSubject motion:\\n- Seamlessly morphing between shapes\\n- Smooth transformations\\n\\nCamera motion:\\n- FPV perspective\\n- Spinning around subject\\n- Gradually zooming out\\n- Following beautifully splined paths\\n\\nScene motion:\\n- Cinematic lighting shifts\\n- Environmental elements reacting to transitions\\n\\nStyle:\\n- Hyper-realistic 3D render quality\\n- Smooth motion blur\\n- Polished CG transformation effects\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\nseamlessly morphing camera transitions fpv-spin-zoomout beautifully-splined-animation\"```\n\n```json\n{\n  \"structured_motion_prompt\": \"Hyper-realistic 3D render with:\\n\\nSubject motion:\\n- Seamlessly morphing between shapes\\n- Smooth transformations\\n\\nCamera motion:\\n- FPV perspective\\n- Spinning around subject\\n- Gradually zooming out\\n- Following beautifully splined paths\\n\\nScene motion:\\n- Cinematic lighting shifts\\n- Environmental elements reacting to transitions\\n\\nStyle:\\n- Hyper-realistic 3D render quality\\n- Smooth motion blur\\n- Polished CG transformation effects\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": \"Hyper-realistic 3D render with:\\n\\nSubject motion:\\n- Morphing transforms\\n- Fluid transitions\\n\\nCamera motion:\\n- POV perspective\\n- Orbital tracking\\n- Zoom out\\n- Spline path\\n\\nScene motion:\\n- Dynamic lighting\\n- Reactive environment\\n\\nStyle:\\n- Photorealistic\\n- Motion blur\\n- Polished CGI\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\nseamlessly morphing camera transitions fpv-spin-zoomout beautifully-splined-animation\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": \"Hyper-realistic 3D render with:\\n\\nSubject motion:\\n- Morphing transforms\\n- Fluid transitions\\n\\nCamera motion:\\n- POV perspective\\n- Orbital tracking\\n- Zoom out\\n- Spline path\\n\\nScene motion:\\n- Dynamic lighting\\n- Reactive environment\\n\\nStyle:\\n- Photorealistic\\n- Motion blur\\n- Polished CGI\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"3D render, morphing transforms, fluid perspective. POV camera orbital zoom-out on spline path. Photorealistic CGI, motion blur.\"\n}\n```",
        }
      }
    }