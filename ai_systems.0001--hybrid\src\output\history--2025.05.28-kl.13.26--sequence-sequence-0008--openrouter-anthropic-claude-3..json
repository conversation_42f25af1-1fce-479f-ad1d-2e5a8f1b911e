  {
  "initial_prompt": "INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\nseamlessly morphing camera transitions fpv-spin-zoomout beautifully-splined-animation. [SEAMLESS MORPH CG-TRANSFORMERS-EFFECT] [SINGLE-TAKE-CONTINOUS-FPV]",
  "sequence_id": "0008",
  "results": [
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\nseamlessly morphing camera transitions fpv-spin-zoomout beautifully-splined-animation. [SEAMLESS MORPH CG-TRANSFORMERS-EFFECT] [SINGLE-TAKE-CONTINOUS-FPV]",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"Dynamic CGI transformation sequence visualized as a seamless morphing effect. Camera executes a fluid first-person-view spin with gradual zoom-out. Beautiful splined animation creates smooth continuous motion. Advanced computer-generated Transformers-style mechanical folding and unfolding elements integrated within the visual. Single continuous take with unbroken FPV perspective.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\nseamlessly morphing camera transitions fpv-spin-zoomout beautifully-splined-animation. [SEAMLESS MORPH CG-TRANSFORMERS-EFFECT] [SINGLE-TAKE-CONTINOUS-FPV]\"```\n\n```json\n{\n  \"validated_prompt\": \"Dynamic CGI transformation sequence visualized as a seamless morphing effect. Camera executes a fluid first-person-view spin with gradual zoom-out. Beautiful splined animation creates smooth continuous motion. Advanced computer-generated Transformers-style mechanical folding and unfolding elements integrated within the visual. Single continuous take with unbroken FPV perspective.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": \"A CGI transformation sequence where:\n\nSubject motion:\n- Mechanical elements fold and unfold in Transformers-style\n- Parts seamlessly morph between configurations\n- Smooth transformation with precision mechanics\n\nCamera motion:\n- First-person perspective maintained throughout\n- Fluid spin around central transformation\n- Gradual zoom-out revealing more of the sequence\n- Single continuous take without cuts\n\nScene motion:\n- Beautiful splined animation paths\n- Seamless transitions between elements\n- Environment reacts to transformation energy\n\nStyle descriptors:\n- Advanced CGI rendering\n- Dynamic mechanical precision\n- Fluid continuous motion\"\n}```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\nseamlessly morphing camera transitions fpv-spin-zoomout beautifully-splined-animation. [SEAMLESS MORPH CG-TRANSFORMERS-EFFECT] [SINGLE-TAKE-CONTINOUS-FPV]\"```\n\n```json\n{\n  \"structured_motion_prompt\": \"A CGI transformation sequence where:\n\nSubject motion:\n- Mechanical elements fold and unfold in Transformers-style\n- Parts seamlessly morph between configurations\n- Smooth transformation with precision mechanics\n\nCamera motion:\n- First-person perspective maintained throughout\n- Fluid spin around central transformation\n- Gradual zoom-out revealing more of the sequence\n- Single continuous take without cuts\n\nScene motion:\n- Beautiful splined animation paths\n- Seamless transitions between elements\n- Environment reacts to transformation energy\n\nStyle descriptors:\n- Advanced CGI rendering\n- Dynamic mechanical precision\n- Fluid continuous motion\"\n}```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": \"CGI transformation sequence featuring:\n\nSubject motion:\n- Precise mechanical morphing elements\n- Seamless component transitions\n- Fluid transformation mechanics\n\nCamera motion:\n- Continuous first-person view\n- Dynamic orbital spin \n- Cinematic zoom-out\n- Uncut single-take sequence\n\nScene motion:\n- Splined animation curves\n- Seamless transition flow\n- Reactive environment elements\n\nStyle:\n- Hyperrealistic CGI\n- Technical precision\n- Kinetic fluidity\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\nseamlessly morphing camera transitions fpv-spin-zoomout beautifully-splined-animation. [SEAMLESS MORPH CG-TRANSFORMERS-EFFECT] [SINGLE-TAKE-CONTINOUS-FPV]\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": \"CGI transformation sequence featuring:\n\nSubject motion:\n- Precise mechanical morphing elements\n- Seamless component transitions\n- Fluid transformation mechanics\n\nCamera motion:\n- Continuous first-person view\n- Dynamic orbital spin \n- Cinematic zoom-out\n- Uncut single-take sequence\n\nScene motion:\n- Splined animation curves\n- Seamless transition flow\n- Reactive environment elements\n\nStyle:\n- Hyperrealistic CGI\n- Technical precision\n- Kinetic fluidity\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"Hyperrealistic CGI transformation sequence, mechanical parts morphing fluidly, continuous first-person view with orbital spin and zoom-out, single uncut take\"\n}\n```",
        }
      }
    }