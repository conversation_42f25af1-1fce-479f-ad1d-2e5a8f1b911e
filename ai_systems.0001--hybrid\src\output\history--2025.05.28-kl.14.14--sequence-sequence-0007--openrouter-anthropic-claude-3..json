  {
  "initial_prompt": "Against a black background-Gold metallic outlines trace the vehicle's form with luminous glow effects. Showcasing metallic luminescence and flowing elegance. [fpv][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][golden_aura][vignette][no_text][high_end_cinematic][.",
  "sequence_id": "0007",
  "results": [
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML compliance by ensuring direct descriptions, positive phrasing, and single-scene focus while preserving bracketed header structure. **Eliminate conversational language and ensure visual clarity.** Execute as:\n\n`{role=syntax_validator; input=[concept:str]; process=[validate_direct_descriptions(), eliminate_conversational_elements(), ensure_positive_phrasing(), maintain_single_scene_focus(), preserve_bracket_structure()]; constraints=[no_conversational_language(), single_scene_only(), positive_phrasing_mandatory()]; requirements=[runway_compliance(), visual_clarity(), structural_preservation()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "Against a black background-Gold metallic outlines trace the vehicle's form with luminous glow effects. Showcasing metallic luminescence and flowing elegance. [fpv][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][golden_aura][vignette][no_text][high_end_cinematic][.",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"[fpv][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][golden_aura][vignette][no_text][high_end_cinematic] Against a black background, gold metallic outlines trace the vehicle's form with luminous glow effects, showcasing metallic luminescence and flowing elegance.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Simplifier\n\nYour goal is not to **elaborate** but to **simplify** motion descriptions to essential camera and subject actions using RunwayML's preferred direct terminology. **Focus on simple camera verbs and clear subject actions.** Execute as:\n\n`{role=motion_simplifier; input=[validated_prompt:str]; process=[simplify_camera_movements(), use_basic_motion_verbs(), eliminate_complex_descriptions(), maintain_gear_progression(), preserve_structure()]; constraints=[basic_camera_verbs_only(), simple_motion_descriptions(), maintain_three_steps()]; requirements=[motion_simplicity(), runway_syntax(), progression_clarity()]; output={simplified_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Simplifier",
      "input": "\n```[Initial Prompt]: \"Against a black background-Gold metallic outlines trace the vehicle's form with luminous glow effects. Showcasing metallic luminescence and flowing elegance. [fpv][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][golden_aura][vignette][no_text][high_end_cinematic][.\"```\n\n```json\n{\n  \"validated_prompt\": \"[fpv][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][golden_aura][vignette][no_text][high_end_cinematic] Against a black background, gold metallic outlines trace the vehicle's form with luminous glow effects, showcasing metallic luminescence and flowing elegance.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"simplified_motion_prompt\": \"Camera moves slowly around the vehicle, revealing gold outlines against black background. Camera zooms in as golden light traces the vehicle's shape. Camera pulls back to show the complete glowing vehicle form.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Descriptor Compressor\n\nYour goal is not to **modify** but to **compress** visual descriptors to RunwayML's preferred concise format while maintaining essential metallic and morphing elements. **Eliminate redundant adjectives and focus on core visuals.** Execute as:\n\n`{role=descriptor_compressor; input=[simplified_motion_prompt:str]; process=[compress_visual_descriptors(), eliminate_redundant_adjectives(), maintain_core_elements(), preserve_metallic_focus(), ensure_conciseness()]; constraints=[minimal_adjectives(), core_visuals_only(), maintain_progression()]; requirements=[descriptor_efficiency(), visual_clarity(), runway_format()]; output={compressed_prompt:str}}`",
      "step": "c",
      "title": "Runway Descriptor Compressor",
      "input": "\n```[Initial Prompt]: \"Against a black background-Gold metallic outlines trace the vehicle's form with luminous glow effects. Showcasing metallic luminescence and flowing elegance. [fpv][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][golden_aura][vignette][no_text][high_end_cinematic][.\"```\n\n```json\n{\n  \"simplified_motion_prompt\": \"Camera moves slowly around the vehicle, revealing gold outlines against black background. Camera zooms in as golden light traces the vehicle's shape. Camera pulls back to show the complete glowing vehicle form.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"compressed_prompt\": \"Gold metallic outlines against black, luminous glow tracing vehicle form. Camera circles slowly, zooms to highlight golden details, then pulls back revealing complete glowing silhouette. [fpv][ultra_hyperreal][morph][motion_blur][golden_aura][vignette]\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: direct subject-action descriptions with essential header tags only. **Achieve maximum conciseness while preserving visual impact.** Execute as:\n\n`{role=core_distiller; input=[compressed_prompt:str]; process=[distill_to_core_syntax(), maintain_subject_action_clarity(), preserve_essential_headers(), ensure_runway_directness()]; constraints=[maximum_conciseness(), direct_descriptions_only(), minimal_headers()]; requirements=[runway_syntax_compliance(), visual_impact(), format_preservation()]; output={distilled_core_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"Against a black background-Gold metallic outlines trace the vehicle's form with luminous glow effects. Showcasing metallic luminescence and flowing elegance. [fpv][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][golden_aura][vignette][no_text][high_end_cinematic][.\"```\n\n```json\n{\n  \"compressed_prompt\": \"Gold metallic outlines against black, luminous glow tracing vehicle form. Camera circles slowly, zooms to highlight golden details, then pulls back revealing complete glowing silhouette. [fpv][ultra_hyperreal][morph][motion_blur][golden_aura][vignette]\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_core_prompt\": \"Gold metallic outlines trace vehicle shape against black background. Glowing silhouette reveals flowing details. [ultra_hyperreal][motion_blur][golden_aura]\"\n}\n```",
        }
      }
    }