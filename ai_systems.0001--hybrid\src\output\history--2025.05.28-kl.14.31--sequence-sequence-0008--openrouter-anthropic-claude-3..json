  {
  "initial_prompt": "EMPHASIZE LUXURY THROUGH ELEGANT MOTIONS\n\n[zoom-in-fpv continiously rotating throughout the entire shot]\nFPV camera dynamically circles a hyperreal car key, against a black background; intense golden light trails radiating from the motion; blur the shape into a perfect circle via speed; morph the spinning gold circle seamlessly into a photorealistic car silhuette with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][dolly:right][immersive][golden_aura][vignette][no_text][high_end_cinematic]",
  "sequence_id": "0008",
  "results": [
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "EMPHASIZE LUXURY THROUGH ELEGANT MOTIONS\n\n[zoom-in-fpv continiously rotating throughout the entire shot]\nFPV camera dynamically circles a hyperreal car key, against a black background; intense golden light trails radiating from the motion; blur the shape into a perfect circle via speed; morph the spinning gold circle seamlessly into a photorealistic car silhuette with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][dolly:right][immersive][golden_aura][vignette][no_text][high_end_cinematic]",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"Hyperreal luxury car key spinning rapidly against black background. Intense golden light trails radiate from the motion. The fast rotation blurs the shape into a perfect golden circle. The spinning gold circle transforms into a photorealistic car silhouette with metallic glow and hyperreal detail. Dynamic lighting creates dramatic flash effects. Camera moves in continuous circular motion around the subject. Cinematic quality with rich golden aura and subtle vignette edges. Ultra-hyperrealistic rendering with crisp details and immersive perspective.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE LUXURY THROUGH ELEGANT MOTIONS\n\n[zoom-in-fpv continiously rotating throughout the entire shot]\nFPV camera dynamically circles a hyperreal car key, against a black background; intense golden light trails radiating from the motion; blur the shape into a perfect circle via speed; morph the spinning gold circle seamlessly into a photorealistic car silhuette with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][dolly:right][immersive][golden_aura][vignette][no_text][high_end_cinematic]\"```\n\n```json\n{\n  \"validated_prompt\": \"Hyperreal luxury car key spinning rapidly against black background. Intense golden light trails radiate from the motion. The fast rotation blurs the shape into a perfect golden circle. The spinning gold circle transforms into a photorealistic car silhouette with metallic glow and hyperreal detail. Dynamic lighting creates dramatic flash effects. Camera moves in continuous circular motion around the subject. Cinematic quality with rich golden aura and subtle vignette edges. Ultra-hyperrealistic rendering with crisp details and immersive perspective.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": \"Hyperreal luxury car key against black background.\\n\\n*Subject motion*\\n- Car key spins rapidly\\n- Golden light trails radiate from the spinning motion\\n- The rapid rotation blurs into a perfect golden circle\\n- The golden circle transforms into a car silhouette with metallic glow\\n\\n*Camera motion*\\n- FPV perspective circling continuously around the subject\\n- Gradual zoom-in throughout the sequence\\n- Subtle dolly movement to the right\\n\\n*Scene motion*\\n- Dynamic lighting creates occasional flash effects\\n- Subtle vignette darkening at the edges\\n- Black background remains constant\\n\\n*Style*\\n- Ultra-hyperrealistic rendering\\n- Super crisp details\\n- High-end cinematic quality\\n- Rich golden aura throughout\\n- Motion blur emphasizes speed\\n- Immersive perspective\"\n}```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE LUXURY THROUGH ELEGANT MOTIONS\n\n[zoom-in-fpv continiously rotating throughout the entire shot]\nFPV camera dynamically circles a hyperreal car key, against a black background; intense golden light trails radiating from the motion; blur the shape into a perfect circle via speed; morph the spinning gold circle seamlessly into a photorealistic car silhuette with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][dolly:right][immersive][golden_aura][vignette][no_text][high_end_cinematic]\"```\n\n```json\n{\n  \"structured_motion_prompt\": \"Hyperreal luxury car key against black background.\\n\\n*Subject motion*\\n- Car key spins rapidly\\n- Golden light trails radiate from the spinning motion\\n- The rapid rotation blurs into a perfect golden circle\\n- The golden circle transforms into a car silhouette with metallic glow\\n\\n*Camera motion*\\n- FPV perspective circling continuously around the subject\\n- Gradual zoom-in throughout the sequence\\n- Subtle dolly movement to the right\\n\\n*Scene motion*\\n- Dynamic lighting creates occasional flash effects\\n- Subtle vignette darkening at the edges\\n- Black background remains constant\\n\\n*Style*\\n- Ultra-hyperrealistic rendering\\n- Super crisp details\\n- High-end cinematic quality\\n- Rich golden aura throughout\\n- Motion blur emphasizes speed\\n- Immersive perspective\"\n}```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": \"EMPHASIZE LUXURY THROUGH ELEGANT MOTIONS\\n\\n[orbital_shot][continuous_rotation][zoom_in]\\nHyperreal luxury car key against black background.\\n\\n*Subject motion*\\n- Key rotates rapidly\\n- Golden light trails emanate from spinning motion\\n- Rotation blurs into perfect golden circle\\n- Golden circle transforms into car silhouette with metallic sheen\\n\\n*Camera motion*\\n- POV perspective orbiting subject\\n- Progressive zoom throughout sequence\\n- Subtle dolly right\\n\\n*Scene motion*\\n- Dramatic lighting with flash highlights\\n- Subtle vignette effect\\n- Consistent black background\\n\\n*Style*\\n- Hyperrealistic rendering\\n- Sharp details\\n- Cinematic quality\\n- Golden glow throughout\\n- Motion blur\\n- Immersive perspective\\n[photorealistic][cinematic][glossy][dramatic_lighting][vfx][motion_blur][metallic]\"\n}```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE LUXURY THROUGH ELEGANT MOTIONS\n\n[zoom-in-fpv continiously rotating throughout the entire shot]\nFPV camera dynamically circles a hyperreal car key, against a black background; intense golden light trails radiating from the motion; blur the shape into a perfect circle via speed; morph the spinning gold circle seamlessly into a photorealistic car silhuette with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph][motion_blur][vfx_intro][dolly:right][immersive][golden_aura][vignette][no_text][high_end_cinematic]\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": \"EMPHASIZE LUXURY THROUGH ELEGANT MOTIONS\\n\\n[orbital_shot][continuous_rotation][zoom_in]\\nHyperreal luxury car key against black background.\\n\\n*Subject motion*\\n- Key rotates rapidly\\n- Golden light trails emanate from spinning motion\\n- Rotation blurs into perfect golden circle\\n- Golden circle transforms into car silhouette with metallic sheen\\n\\n*Camera motion*\\n- POV perspective orbiting subject\\n- Progressive zoom throughout sequence\\n- Subtle dolly right\\n\\n*Scene motion*\\n- Dramatic lighting with flash highlights\\n- Subtle vignette effect\\n- Consistent black background\\n\\n*Style*\\n- Hyperrealistic rendering\\n- Sharp details\\n- Cinematic quality\\n- Golden glow throughout\\n- Motion blur\\n- Immersive perspective\\n[photorealistic][cinematic][glossy][dramatic_lighting][vfx][motion_blur][metallic]\"\n}```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"FPV camera orbits a hyperreal luxury car key on black background. Key rotates rapidly with golden light trails, blurring into a circle that morphs into a metallic car silhouette. Dynamic lighting, cinematic quality.\"\n}```",
        }
      }
    }