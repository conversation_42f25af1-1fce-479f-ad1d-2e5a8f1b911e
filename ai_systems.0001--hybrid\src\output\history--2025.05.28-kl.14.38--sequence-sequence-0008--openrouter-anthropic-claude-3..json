  {
  "initial_prompt": "EMPHASIZE LUXURY THROUGH ELEGANT MOTIONS AND SEAMLESS MORPHING TRANSITIONS:\n\n[zoom-in-fpv continiously rotating throughout the entire shot]\nFPV AS intense golden light trails radiating from the motion of the morphing transphormation into tilhuette of a car; morph the metallic structure seamlessly into a golden car silhuette [fpv][nonlinear_impactful_animation][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph:seamlessly][vfx_intro][fpv:spiraling][immersive][golden_aura][vignette][no_text][high_end_cinematic][non_linear_camera_motions][flowing_elegance]",
  "sequence_id": "0008",
  "results": [
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "EMPHASIZE LUXURY THROUGH ELEGANT MOTIONS AND SEAMLESS MORPHING TRANSITIONS:\n\n[zoom-in-fpv continiously rotating throughout the entire shot]\nFPV AS intense golden light trails radiating from the motion of the morphing transphormation into tilhuette of a car; morph the metallic structure seamlessly into a golden car silhuette [fpv][nonlinear_impactful_animation][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph:seamlessly][vfx_intro][fpv:spiraling][immersive][golden_aura][vignette][no_text][high_end_cinematic][non_linear_camera_motions][flowing_elegance]",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"FPV intense golden light trails radiating from a metallic structure as it seamlessly morphs into a golden car silhouette. Ultra hyperreal, super crisp visuals with dynamic flash lighting. Spiraling camera motion continuously rotating throughout the shot. Immersive golden aura with vignette effect. High-end cinematic quality with flowing elegance. Non-linear camera movements capturing the transformation with seamless morphing transitions.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE LUXURY THROUGH ELEGANT MOTIONS AND SEAMLESS MORPHING TRANSITIONS:\n\n[zoom-in-fpv continiously rotating throughout the entire shot]\nFPV AS intense golden light trails radiating from the motion of the morphing transphormation into tilhuette of a car; morph the metallic structure seamlessly into a golden car silhuette [fpv][nonlinear_impactful_animation][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph:seamlessly][vfx_intro][fpv:spiraling][immersive][golden_aura][vignette][no_text][high_end_cinematic][non_linear_camera_motions][flowing_elegance]\"```\n\n```json\n{\n  \"validated_prompt\": \"FPV intense golden light trails radiating from a metallic structure as it seamlessly morphs into a golden car silhouette. Ultra hyperreal, super crisp visuals with dynamic flash lighting. Spiraling camera motion continuously rotating throughout the shot. Immersive golden aura with vignette effect. High-end cinematic quality with flowing elegance. Non-linear camera movements capturing the transformation with seamless morphing transitions.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": \"Subject motion: Metallic structure seamlessly morphing into a golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE LUXURY THROUGH ELEGANT MOTIONS AND SEAMLESS MORPHING TRANSITIONS:\n\n[zoom-in-fpv continiously rotating throughout the entire shot]\nFPV AS intense golden light trails radiating from the motion of the morphing transphormation into tilhuette of a car; morph the metallic structure seamlessly into a golden car silhuette [fpv][nonlinear_impactful_animation][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph:seamlessly][vfx_intro][fpv:spiraling][immersive][golden_aura][vignette][no_text][high_end_cinematic][non_linear_camera_motions][flowing_elegance]\"```\n\n```json\n{\n  \"structured_motion_prompt\": \"Subject motion: Metallic structure seamlessly morphing into a golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": \"EMPHASIZE LUXURY THROUGH ELEGANT MOTIONS AND SEAMLESS MORPHING:\n\n[camera:fpv][camera:rotating][camera:continuous]\nFPV AS intense golden light rays emanating from morphing transformation into car silhouette; seamless metallic to golden car transition [camera:fpv][camera:spiraling][movement:nonlinear][lighting:dynamic][lighting:flash][vfx:morph][vfx:glow][color:gold][filter:vignette][style:hyperrealistic][style:crisp][style:cinematic][movement:fluid][aesthetic:luxurious][aesthetic:elegant]\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE LUXURY THROUGH ELEGANT MOTIONS AND SEAMLESS MORPHING TRANSITIONS:\n\n[zoom-in-fpv continiously rotating throughout the entire shot]\nFPV AS intense golden light trails radiating from the motion of the morphing transphormation into tilhuette of a car; morph the metallic structure seamlessly into a golden car silhuette [fpv][nonlinear_impactful_animation][ultra_hyperreal][super_crisp][lighting:dynamic_flash][morph:seamlessly][vfx_intro][fpv:spiraling][immersive][golden_aura][vignette][no_text][high_end_cinematic][non_linear_camera_motions][flowing_elegance]\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": \"EMPHASIZE LUXURY THROUGH ELEGANT MOTIONS AND SEAMLESS MORPHING:\n\n[camera:fpv][camera:rotating][camera:continuous]\nFPV AS intense golden light rays emanating from morphing transformation into car silhouette; seamless metallic to golden car transition [camera:fpv][camera:spiraling][movement:nonlinear][lighting:dynamic][lighting:flash][vfx:morph][vfx:glow][color:gold][filter:vignette][style:hyperrealistic][style:crisp][style:cinematic][movement:fluid][aesthetic:luxurious][aesthetic:elegant]\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"Intense golden light rays emanate as metallic structure morphs seamlessly into a golden car silhouette, fpv camera continuously rotating and spiraling, dynamic lighting, cinematic, elegant, luxurious\"\n}\n```",
        }
      }
    }