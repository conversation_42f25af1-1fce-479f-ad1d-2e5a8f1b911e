  {
  "initial_prompt": "EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions",
  "sequence_id": "0008|0009",
  "results": [
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"Ultra-hyperrealistic golden car key filmed in extreme close-up with crisp details. The golden key emits vibrant light trails as it dynamically transforms into a sleek golden car silhouette. First-person-view camera spirals around the transformation with smooth, weighted movements, capturing the object from multiple angles. Dynamic flash lighting illuminates the golden metamorphosis, creating a luminous aura throughout the scene. High-end cinematic quality with seamless transitions and flowing elegant motion. Super crisp visuals with perfect clarity showing the intricate details of the golden object as it changes form.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions\"```\n\n```json\n{\n  \"validated_prompt\": \"Ultra-hyperrealistic golden car key filmed in extreme close-up with crisp details. The golden key emits vibrant light trails as it dynamically transforms into a sleek golden car silhouette. First-person-view camera spirals around the transformation with smooth, weighted movements, capturing the object from multiple angles. Dynamic flash lighting illuminates the golden metamorphosis, creating a luminous aura throughout the scene. High-end cinematic quality with seamless transitions and flowing elegant motion. Super crisp visuals with perfect clarity showing the intricate details of the golden object as it changes form.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"Golden car key smoothly transforms into a sleek car silhouette, emitting vibrant light trails during the metamorphosis\",\n    \n    \"camera_motion\": \"First-person-view camera spirals around the transformation with weighted tangents creating smooth non-linear movements\",\n    \n    \"scene_motion\": \"Dynamic flash lighting pulses gently, illuminating the golden object and creating a luminous aura that intensifies during key transformation moments\",\n    \n    \"style_descriptors\": \"Ultra-hyperrealistic, super crisp visuals with high-end cinematic quality and flowing elegant transitions\"\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions\"```\n\n```json\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"Golden car key smoothly transforms into a sleek car silhouette, emitting vibrant light trails during the metamorphosis\",\n    \n    \"camera_motion\": \"First-person-view camera spirals around the transformation with weighted tangents creating smooth non-linear movements\",\n    \n    \"scene_motion\": \"Dynamic flash lighting pulses gently, illuminating the golden object and creating a luminous aura that intensifies during key transformation moments\",\n    \n    \"style_descriptors\": \"Ultra-hyperrealistic, super crisp visuals with high-end cinematic quality and flowing elegant transitions\"\n  }\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"Golden car key transforms into sleek car silhouette, emitting golden light trails during metamorphosis\",\n    \n    \"camera_motion\": \"FPV camera, orbital movement, dynamic rotation, smooth transitions, non-linear movement\",\n    \n    \"scene_motion\": \"High-key lighting, golden hour glow, volumetric lighting, lens flares, subtle vignette\",\n    \n    \"style_descriptors\": \"Hyperrealistic, cinematic, 8K resolution, shallow depth of field, glossy finish, elegant composition\"\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"Golden car key transforms into sleek car silhouette, emitting golden light trails during metamorphosis\",\n    \n    \"camera_motion\": \"FPV camera, orbital movement, dynamic rotation, smooth transitions, non-linear movement\",\n    \n    \"scene_motion\": \"High-key lighting, golden hour glow, volumetric lighting, lens flares, subtle vignette\",\n    \n    \"style_descriptors\": \"Hyperrealistic, cinematic, 8K resolution, shallow depth of field, glossy finish, elegant composition\"\n  }\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"distilled_runway_prompt\": \"Golden car key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML compliance by ensuring direct descriptions, positive phrasing, and single-scene focus while preserving bracketed header structure. **Eliminate conversational language and ensure visual clarity.** Execute as:\n\n`{role=syntax_validator; input=[concept:str]; process=[validate_direct_descriptions(), eliminate_conversational_elements(), ensure_positive_phrasing(), maintain_single_scene_focus(), preserve_bracket_structure()]; constraints=[no_conversational_language(), single_scene_only(), positive_phrasing_mandatory()]; requirements=[runway_compliance(), visual_clarity(), structural_preservation()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "{\n  \"initial_prompt\": \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\\n\\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\\\n\\\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\\\n\\\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\\\n\\\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions\",\n  \"distilled_runway_prompt\": \"Golden car key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic.\"\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"validated_prompt\": \"[Cinematography]\\nGolden car key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Motion Simplifier\n\nYour goal is not to **elaborate** but to **simplify** motion descriptions to essential camera and subject actions using RunwayML's preferred direct terminology. **Focus on simple camera verbs and clear subject actions.** Execute as:\n\n`{role=motion_simplifier; input=[validated_prompt:str]; process=[simplify_camera_movements(), use_basic_motion_verbs(), eliminate_complex_descriptions(), maintain_gear_progression(), preserve_structure()]; constraints=[basic_camera_verbs_only(), simple_motion_descriptions(), maintain_three_steps()]; requirements=[motion_simplicity(), runway_syntax(), progression_clarity()]; output={simplified_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Simplifier",
      "input": "{\n  \"initial_prompt\": \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\\n\\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\\\n\\\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\\\n\\\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\\\n\\\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions\",\n  \"validated_prompt\": \"[Cinematography]\\nGolden car key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.\"\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"simplified_motion_prompt\": \"Camera moves: FPV starts on car key, rotates smoothly around as key transforms into golden car silhouette, continues orbiting as golden light trails emit from silhouette.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Descriptor Compressor\n\nYour goal is not to **modify** but to **compress** visual descriptors to RunwayML's preferred concise format while maintaining essential metallic and morphing elements. **Eliminate redundant adjectives and focus on core visuals.** Execute as:\n\n`{role=descriptor_compressor; input=[simplified_motion_prompt:str]; process=[compress_visual_descriptors(), eliminate_redundant_adjectives(), maintain_core_elements(), preserve_metallic_focus(), ensure_conciseness()]; constraints=[minimal_adjectives(), core_visuals_only(), maintain_progression()]; requirements=[descriptor_efficiency(), visual_clarity(), runway_format()]; output={compressed_prompt:str}}`",
      "step": "c",
      "title": "Runway Descriptor Compressor",
      "input": "{\n  \"initial_prompt\": \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\\n\\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\\\n\\\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\\\n\\\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\\\n\\\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions\",\n  \"simplified_motion_prompt\": \"Camera moves: FPV starts on car key, rotates smoothly around as key transforms into golden car silhouette, continues orbiting as golden light trails emit from silhouette.\"\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"compressed_prompt\": \"FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: direct subject-action descriptions with essential header tags only. **Achieve maximum conciseness while preserving visual impact.** Execute as:\n\n`{role=core_distiller; input=[compressed_prompt:str]; process=[distill_to_core_syntax(), maintain_subject_action_clarity(), preserve_essential_headers(), ensure_runway_directness()]; constraints=[maximum_conciseness(), direct_descriptions_only(), minimal_headers()]; requirements=[runway_syntax_compliance(), visual_impact(), format_preservation()]; output={distilled_core_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "{\n  \"initial_prompt\": \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\\n\\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\\\n\\\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\\\n\\\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\\\n\\\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions\",\n  \"compressed_prompt\": \"FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails\"\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"distilled_core_prompt\": \"FPV camera orbit, car key transforms into golden car silhouette, emits light trails\"\n}",
        }
      }
    }