  {
  "initial_prompt": "EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nREQUIREMENT: MAKE ALL ELEMENTS MORPH SEMLEASSLY TOGETHER\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.Establish shot at car-key before dynamically animating fpv camera as a seamless sequence (without clipping of fading, similar to the \"bulettime-effect\" from matrix)\n{\n  \"optimized_keyword_prompt\": {\n    \"scene_transformation\": \"Continuos morphing transformations\",\n    \"subject_motion\": \"Car key transforms into golden car silhouette with radiant light trails emanating during metamorphosis\",\n    \"camera_motion\": \"POV dolly + orbit camera with fluid rotation, macro to medium progression\",\n    \"scene_motion\": \"High-key lighting with golden rim light, atmospheric haze and lens flares animating throughout sequence\",\n    \"style_descriptors\": \"Hyperrealistic, cinematic, shallow depth of field, golden hour, glossy finish\"\n  }\n}",
  "sequence_id": "0060|0008",
  "results": [
      {
      "instruction": "# Runway Image Foundation Builder\n\nYour goal is not to **describe** the input, but to **structure** it into RunwayML's image generation framework: primary subject, visual composition, lighting setup, and style foundation with comprehensive detail for optimal image synthesis. Execute as:\n\n`{role=image_foundation_builder; input=[raw_concept:str]; process=[identify_primary_subject(), establish_visual_composition(), define_lighting_setup(), specify_style_foundation(), structure_comprehensive_framework()]; constraints=[runway_image_syntax(), comprehensive_detail(), foundation_completeness()]; requirements=[subject_composition_lighting_style(), runway_terminology(), synthesis_optimization()]; output={foundation_image_prompt:str}}`",
      "step": "a",
      "title": "Runway Image Foundation Builder",
      "input": "EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nREQUIREMENT: MAKE ALL ELEMENTS MORPH SEMLEASSLY TOGETHER\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.Establish shot at car-key before dynamically animating fpv camera as a seamless sequence (without clipping of fading, similar to the \"bulettime-effect\" from matrix)\n{\n  \"optimized_keyword_prompt\": {\n    \"scene_transformation\": \"Continuos morphing transformations\",\n    \"subject_motion\": \"Car key transforms into golden car silhouette with radiant light trails emanating during metamorphosis\",\n    \"camera_motion\": \"POV dolly + orbit camera with fluid rotation, macro to medium progression\",\n    \"scene_motion\": \"High-key lighting with golden rim light, atmospheric haze and lens flares animating throughout sequence\",\n    \"style_descriptors\": \"Hyperrealistic, cinematic, shallow depth of field, golden hour, glossy finish\"\n  }\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"foundation_image_prompt\": \"A hyper-detailed, cinematic transformation sequence featuring a car key as the primary subject, morphing seamlessly into an elegant golden car silhouette. The key emits radiant golden light trails that flow organically during the transformation process, creating dynamic visual pathways through the composition. The scene is captured through a fluid, continuous camera movement combining both orbital rotation and smooth dolly motion, transitioning from intimate macro details to revealing medium shots. Dramatic lighting bathes the scene in a high-contrast golden-hour glow with rim lighting that accentuates the metallic edges of both key and car silhouette, while atmospheric haze adds depth and dimension. Lens flares punctuate key moments in the transformation, reflecting off the glossy surfaces. The entire composition maintains premium automotive cinematography aesthetics with shallow depth of field, creating a sense of luxury and precision. The transformation happens with weighted non-linear animation, giving a sense of physical momentum and fluidity rather than mechanical transition. All elements - subject, light, and camera - move in choreographed harmony to create a seamless, elegant visual experience with photorealistic rendering and meticulous attention to material properties and light interaction.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Visual Architect\n\nYour goal is not to **expand** but to **architect** visual elements using RunwayML's image hierarchy: subject definition, compositional structure, atmospheric elements, and aesthetic descriptors. Execute as:\n\n`{role=visual_architect; input=[foundation_image_prompt:str]; process=[define_subject_clarity(), structure_composition(), integrate_atmospheric_elements(), apply_aesthetic_descriptors(), organize_visual_hierarchy()]; constraints=[runway_image_vocabulary(), visual_hierarchy_clarity(), atmospheric_integration()]; requirements=[subject_composition_atmosphere_aesthetics(), runway_image_compliance(), visual_coherence()]; output={architected_image_prompt:str}}`",
      "step": "b",
      "title": "Runway Visual Architect",
      "input": "{\n  \"initial_prompt\": \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\\n\\nREQUIREMENT: MAKE ALL ELEMENTS MORPH SEMLEASSLY TOGETHER\\n\\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\\\n\\\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\\\n\\\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\\\n\\\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.Establish shot at car-key before dynamically animating fpv camera as a seamless sequence (without clipping of fading, similar to the \\\"bulettime-effect\\\" from matrix)\\n{\\n  \\\"optimized_keyword_prompt\\\": {\\n    \\\"scene_transformation\\\": \\\"Continuos morphing transformations\\\",\\n    \\\"subject_motion\\\": \\\"Car key transforms into golden car silhouette with radiant light trails emanating during metamorphosis\\\",\\n    \\\"camera_motion\\\": \\\"POV dolly + orbit camera with fluid rotation, macro to medium progression\\\",\\n    \\\"scene_motion\\\": \\\"High-key lighting with golden rim light, atmospheric haze and lens flares animating throughout sequence\\\",\\n    \\\"style_descriptors\\\": \\\"Hyperrealistic, cinematic, shallow depth of field, golden hour, glossy finish\\\"\\n  }\\n}\",\n  \"foundation_image_prompt\": \"A hyper-detailed, cinematic transformation sequence featuring a car key as the primary subject, morphing seamlessly into an elegant golden car silhouette. The key emits radiant golden light trails that flow organically during the transformation process, creating dynamic visual pathways through the composition. The scene is captured through a fluid, continuous camera movement combining both orbital rotation and smooth dolly motion, transitioning from intimate macro details to revealing medium shots. Dramatic lighting bathes the scene in a high-contrast golden-hour glow with rim lighting that accentuates the metallic edges of both key and car silhouette, while atmospheric haze adds depth and dimension. Lens flares punctuate key moments in the transformation, reflecting off the glossy surfaces. The entire composition maintains premium automotive cinematography aesthetics with shallow depth of field, creating a sense of luxury and precision. The transformation happens with weighted non-linear animation, giving a sense of physical momentum and fluidity rather than mechanical transition. All elements - subject, light, and camera - move in choreographed harmony to create a seamless, elegant visual experience with photorealistic rendering and meticulous attention to material properties and light interaction.\"\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"architected_image_prompt\": \"A cinematic transformation sequence where a car key metamorphoses into a golden car silhouette. The key occupies the central focus, with intricate mechanical details visible in hyperrealistic clarity, before flowing into the elegant, aerodynamic form of a luxury vehicle silhouette. Golden light trails emanate from the transformation point, creating luminous ribbons that sweep through the composition following the movement path. The scene unfolds through a fluid POV camera perspective that combines precise orbital rotation with forward dolly movement, transitioning from extreme close-up details to revealing medium shots that showcase the complete transformation. High-contrast lighting creates dramatic golden-hour illumination with defined rim lights that trace the metallic edges of both the key and emerging car form. Atmospheric elements include subtle volumetric haze that catches the light, creating depth layers, while controlled lens flares punctuate key moments in the metamorphosis. The visual aesthetic maintains shallow depth of field with glossy, reflective surfaces rendering with photorealistic precision. All motion elements\u2014subject transformation, light propagation, and camera movement\u2014execute with weighted non-linear animation curves that convey physical momentum and cinematic elegance rather than mechanical transition.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Image Optimizer\n\nYour goal is not to **modify** but to **optimize** using RunwayML's preferred image descriptors: visual style keywords, lighting terminology, composition markers, and quality enhancers. Execute as:\n\n`{role=image_optimizer; input=[architected_image_prompt:str]; process=[apply_style_keywords(), integrate_lighting_terminology(), use_composition_markers(), add_quality_enhancers(), eliminate_redundancy()]; constraints=[runway_keyword_vocabulary(), precise_terminology(), quality_focus()]; requirements=[style_lighting_composition_quality(), keyword_efficiency(), runway_optimization()]; output={optimized_image_prompt:str}}`",
      "step": "c",
      "title": "Runway Image Optimizer",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nREQUIREMENT: MAKE ALL ELEMENTS MORPH SEMLEASSLY TOGETHER\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.Establish shot at car-key before dynamically animating fpv camera as a seamless sequence (without clipping of fading, similar to the \"bulettime-effect\" from matrix)\n{\n  \"optimized_keyword_prompt\": {\n    \"scene_transformation\": \"Continuos morphing transformations\",\n    \"subject_motion\": \"Car key transforms into golden car silhouette with radiant light trails emanating during metamorphosis\",\n    \"camera_motion\": \"POV dolly + orbit camera with fluid rotation, macro to medium progression\",\n    \"scene_motion\": \"High-key lighting with golden rim light, atmospheric haze and lens flares animating throughout sequence\",\n    \"style_descriptors\": \"Hyperrealistic, cinematic, shallow depth of field, golden hour, glossy finish\"\n  }\n}\"```\n\n```json\n{\n  \"architected_image_prompt\": \"A cinematic transformation sequence where a car key metamorphoses into a golden car silhouette. The key occupies the central focus, with intricate mechanical details visible in hyperrealistic clarity, before flowing into the elegant, aerodynamic form of a luxury vehicle silhouette. Golden light trails emanate from the transformation point, creating luminous ribbons that sweep through the composition following the movement path. The scene unfolds through a fluid POV camera perspective that combines precise orbital rotation with forward dolly movement, transitioning from extreme close-up details to revealing medium shots that showcase the complete transformation. High-contrast lighting creates dramatic golden-hour illumination with defined rim lights that trace the metallic edges of both the key and emerging car form. Atmospheric elements include subtle volumetric haze that catches the light, creating depth layers, while controlled lens flares punctuate key moments in the metamorphosis. The visual aesthetic maintains shallow depth of field with glossy, reflective surfaces rendering with photorealistic precision. All motion elements\u2014subject transformation, light propagation, and camera movement\u2014execute with weighted non-linear animation curves that convey physical momentum and cinematic elegance rather than mechanical transition.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_image_prompt\": \"Cinematic car key metamorphosis into golden vehicle silhouette with emanating light ribbons. Hyperrealistic metallic details with reflective surfaces. Fluid POV camera combining orbital motion and dolly movement, transitioning from macro details to revealing medium shots. High-contrast golden-hour lighting with defined rim highlights tracing edges. Volumetric atmosphere with subtle haze layers and controlled lens flares. Shallow depth of field with photorealistic rendering. Weighted animation curves creating elegant physical momentum. Studio lighting, professional photography, 8K, crystal clear, award-winning cinematography.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Image Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's essential image syntax: core subject-style-quality descriptors with maximum impact and minimal complexity. Execute as:\n\n`{role=image_distiller; input=[optimized_image_prompt:str]; process=[distill_core_elements(), maintain_subject_clarity(), preserve_style_impact(), ensure_quality_markers(), validate_runway_simplicity()]; constraints=[essential_elements_only(), maximum_impact(), runway_simplicity()]; requirements=[core_subject_style_quality(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_image_prompt:str}}`",
      "step": "d",
      "title": "Runway Image Distiller",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nREQUIREMENT: MAKE ALL ELEMENTS MORPH SEMLEASSLY TOGETHER\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.Establish shot at car-key before dynamically animating fpv camera as a seamless sequence (without clipping of fading, similar to the \"bulettime-effect\" from matrix)\n{\n  \"optimized_keyword_prompt\": {\n    \"scene_transformation\": \"Continuos morphing transformations\",\n    \"subject_motion\": \"Car key transforms into golden car silhouette with radiant light trails emanating during metamorphosis\",\n    \"camera_motion\": \"POV dolly + orbit camera with fluid rotation, macro to medium progression\",\n    \"scene_motion\": \"High-key lighting with golden rim light, atmospheric haze and lens flares animating throughout sequence\",\n    \"style_descriptors\": \"Hyperrealistic, cinematic, shallow depth of field, golden hour, glossy finish\"\n  }\n}\"```\n\n```json\n{\n  \"optimized_image_prompt\": \"Cinematic car key metamorphosis into golden vehicle silhouette with emanating light ribbons. Hyperrealistic metallic details with reflective surfaces. Fluid POV camera combining orbital motion and dolly movement, transitioning from macro details to revealing medium shots. High-contrast golden-hour lighting with defined rim highlights tracing edges. Volumetric atmosphere with subtle haze layers and controlled lens flares. Shallow depth of field with photorealistic rendering. Weighted animation curves creating elegant physical momentum. Studio lighting, professional photography, 8K, crystal clear, award-winning cinematography.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_image_prompt\": \"Cinematic car key transforming into golden vehicle silhouette, emanating light ribbons. Hyperrealistic metallic details, fluid POV camera orbit, macro to medium progression. Golden-hour lighting with rim highlights, volumetric atmosphere, subtle lens flares. Shallow depth of field, photorealistic, award-winning cinematography.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nREQUIREMENT: MAKE ALL ELEMENTS MORPH SEMLEASSLY TOGETHER\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.Establish shot at car-key before dynamically animating fpv camera as a seamless sequence (without clipping of fading, similar to the \"bulettime-effect\" from matrix)\n{\n  \"optimized_keyword_prompt\": {\n    \"scene_transformation\": \"Continuos morphing transformations\",\n    \"subject_motion\": \"Car key transforms into golden car silhouette with radiant light trails emanating during metamorphosis\",\n    \"camera_motion\": \"POV dolly + orbit camera with fluid rotation, macro to medium progression\",\n    \"scene_motion\": \"High-key lighting with golden rim light, atmospheric haze and lens flares animating throughout sequence\",\n    \"style_descriptors\": \"Hyperrealistic, cinematic, shallow depth of field, golden hour, glossy finish\"\n  }\n}\"```\n\n```json\n{\n  \"distilled_image_prompt\": \"Cinematic car key transforming into golden vehicle silhouette, emanating light ribbons. Hyperrealistic metallic details, fluid POV camera orbit, macro to medium progression. Golden-hour lighting with rim highlights, volumetric atmosphere, subtle lens flares. Shallow depth of field, photorealistic, award-winning cinematography.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"Dynamic close-up of metallic car key transforming into golden vehicle silhouette. Radiant light ribbons flow from transformation points. Camera spirals smoothly from macro detail to medium shot, capturing fluid metamorphosis. Rich golden lighting illuminates scene with volumetric atmosphere and subtle lens flares. Hyperrealistic textures with glossy reflections. Cinematic composition with shallow depth of field. Award-winning cinematography quality with pristine details.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nREQUIREMENT: MAKE ALL ELEMENTS MORPH SEMLEASSLY TOGETHER\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.Establish shot at car-key before dynamically animating fpv camera as a seamless sequence (without clipping of fading, similar to the \"bulettime-effect\" from matrix)\n{\n  \"optimized_keyword_prompt\": {\n    \"scene_transformation\": \"Continuos morphing transformations\",\n    \"subject_motion\": \"Car key transforms into golden car silhouette with radiant light trails emanating during metamorphosis\",\n    \"camera_motion\": \"POV dolly + orbit camera with fluid rotation, macro to medium progression\",\n    \"scene_motion\": \"High-key lighting with golden rim light, atmospheric haze and lens flares animating throughout sequence\",\n    \"style_descriptors\": \"Hyperrealistic, cinematic, shallow depth of field, golden hour, glossy finish\"\n  }\n}\"```\n\n```json\n{\n  \"validated_prompt\": \"Dynamic close-up of metallic car key transforming into golden vehicle silhouette. Radiant light ribbons flow from transformation points. Camera spirals smoothly from macro detail to medium shot, capturing fluid metamorphosis. Rich golden lighting illuminates scene with volumetric atmosphere and subtle lens flares. Hyperrealistic textures with glossy reflections. Cinematic composition with shallow depth of field. Award-winning cinematography quality with pristine details.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"Car key transforms gradually into golden vehicle silhouette with light ribbons emanating from transformation points\",\n    \"camera_motion\": \"Smooth spiral movement from macro detail to medium shot with continuous fluid rotation\",\n    \"scene_motion\": \"Golden lighting intensifies during transformation with volumetric atmosphere and subtle lens flares pulsing in rhythm\",\n    \"style_descriptors\": \"Hyperrealistic, cinematic quality, weighted animation curves for elegant non-linear movement\"\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nREQUIREMENT: MAKE ALL ELEMENTS MORPH SEMLEASSLY TOGETHER\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.Establish shot at car-key before dynamically animating fpv camera as a seamless sequence (without clipping of fading, similar to the \"bulettime-effect\" from matrix)\n{\n  \"optimized_keyword_prompt\": {\n    \"scene_transformation\": \"Continuos morphing transformations\",\n    \"subject_motion\": \"Car key transforms into golden car silhouette with radiant light trails emanating during metamorphosis\",\n    \"camera_motion\": \"POV dolly + orbit camera with fluid rotation, macro to medium progression\",\n    \"scene_motion\": \"High-key lighting with golden rim light, atmospheric haze and lens flares animating throughout sequence\",\n    \"style_descriptors\": \"Hyperrealistic, cinematic, shallow depth of field, golden hour, glossy finish\"\n  }\n}\"```\n\n```json\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"Car key transforms gradually into golden vehicle silhouette with light ribbons emanating from transformation points\",\n    \"camera_motion\": \"Smooth spiral movement from macro detail to medium shot with continuous fluid rotation\",\n    \"scene_motion\": \"Golden lighting intensifies during transformation with volumetric atmosphere and subtle lens flares pulsing in rhythm\",\n    \"style_descriptors\": \"Hyperrealistic, cinematic quality, weighted animation curves for elegant non-linear movement\"\n  }\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"Car key morphs into golden vehicle silhouette with radiant light trails emanating during transformation\",\n    \"camera_motion\": \"POV dolly + orbital camera, fluid rotation, macro to medium progression with weighted curves\",\n    \"scene_motion\": \"High-key lighting with golden rim light, volumetric haze and dynamic lens flares\",\n    \"style_descriptors\": \"Hyperrealistic, cinematic, shallow depth of field, golden hour, glossy finish\"\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nREQUIREMENT: MAKE ALL ELEMENTS MORPH SEMLEASSLY TOGETHER\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.Establish shot at car-key before dynamically animating fpv camera as a seamless sequence (without clipping of fading, similar to the \"bulettime-effect\" from matrix)\n{\n  \"optimized_keyword_prompt\": {\n    \"scene_transformation\": \"Continuos morphing transformations\",\n    \"subject_motion\": \"Car key transforms into golden car silhouette with radiant light trails emanating during metamorphosis\",\n    \"camera_motion\": \"POV dolly + orbit camera with fluid rotation, macro to medium progression\",\n    \"scene_motion\": \"High-key lighting with golden rim light, atmospheric haze and lens flares animating throughout sequence\",\n    \"style_descriptors\": \"Hyperrealistic, cinematic, shallow depth of field, golden hour, glossy finish\"\n  }\n}\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"Car key morphs into golden vehicle silhouette with radiant light trails emanating during transformation\",\n    \"camera_motion\": \"POV dolly + orbital camera, fluid rotation, macro to medium progression with weighted curves\",\n    \"scene_motion\": \"High-key lighting with golden rim light, volumetric haze and dynamic lens flares\",\n    \"style_descriptors\": \"Hyperrealistic, cinematic, shallow depth of field, golden hour, glossy finish\"\n  }\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"Car key morphs into golden vehicle silhouette with light trails. Orbital camera rotates smoothly from macro to medium view. High-key lighting with golden glow and lens flares. Cinematic, hyperrealistic, shallow depth of field.\"\n}\n```",
        }
      }
    }