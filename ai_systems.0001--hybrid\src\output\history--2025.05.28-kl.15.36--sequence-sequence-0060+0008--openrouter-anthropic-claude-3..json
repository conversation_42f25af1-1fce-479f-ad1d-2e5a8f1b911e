  {
  "initial_prompt": "EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\\nEstablishing close-up of metallic car key  before suddenly easing into fpv while transforming into golden vehicle silhouette. Everything warps seamlessly together as a single liquid object. Radiant light ribbons flow from transformation points. Camera spirals smoothly from macro detail to medium shot, capturing fluid metamorphosis. Rich golden lighting illuminates scene with volumetric atmosphere and subtle lens flares. Hyperrealistic textures with glossy reflections. Cinematic composition with shallow depth of field. Award-winning cinematography quality with pristine details.",
  "sequence_id": "0060|0008",
  "results": [
      {
      "instruction": "# Runway Image Foundation Builder\n\nYour goal is not to **describe** the input, but to **structure** it into RunwayML's image generation framework: primary subject, visual composition, lighting setup, and style foundation with comprehensive detail for optimal image synthesis. Execute as:\n\n`{role=image_foundation_builder; input=[raw_concept:str]; process=[identify_primary_subject(), establish_visual_composition(), define_lighting_setup(), specify_style_foundation(), structure_comprehensive_framework()]; constraints=[runway_image_syntax(), comprehensive_detail(), foundation_completeness()]; requirements=[subject_composition_lighting_style(), runway_terminology(), synthesis_optimization()]; output={foundation_image_prompt:str}}`",
      "step": "a",
      "title": "Runway Image Foundation Builder",
      "input": "EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\\nEstablishing close-up of metallic car key  before suddenly easing into fpv while transforming into golden vehicle silhouette. Everything warps seamlessly together as a single liquid object. Radiant light ribbons flow from transformation points. Camera spirals smoothly from macro detail to medium shot, capturing fluid metamorphosis. Rich golden lighting illuminates scene with volumetric atmosphere and subtle lens flares. Hyperrealistic textures with glossy reflections. Cinematic composition with shallow depth of field. Award-winning cinematography quality with pristine details.",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"foundation_image_prompt\": \"Primary subject: A transforming metallic car key morphing into a golden vehicle silhouette in a mesmerizing fluid transformation, with radiant light ribbons flowing from transformation points creating elegant connection points.\\n\\nVisual composition: Dynamic camera perspective starting with an extreme close-up macro shot of the metallic key details, spiraling outward with smooth motion into a medium framing that captures the entire metamorphosis sequence. Shallow depth of field creates cinematic focus on the transformation while background elements blur into bokeh. Golden elements positioned to create visual flow across the frame with strong diagonal compositional lines.\\n\\nLighting setup: Rich golden directional key lighting that creates dramatic highlights on the metallic and liquid surfaces. Volumetric atmosphere with visible light rays filtering through the scene. Subtle lens flares where light catches the edges of the transforming elements. Rim lighting defines the silhouette edges with precision. High contrast between illuminated golden surfaces and deeper shadows adds dimensional depth.\\n\\nStyle foundation: Hyperrealistic 3D rendering with meticulously detailed textures showing microscopic details in the metallic surfaces. Glossy reflections with photorealistic properties that follow physical light behavior. Cinematic color grading with rich gold tones balanced against cooler shadows. Award-winning cinematography quality with pristine resolution and technical excellence. Liquid simulation physics that appear visceral and organically believable while maintaining elegant flow patterns.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Visual Architect\n\nYour goal is not to **expand** but to **architect** visual elements using RunwayML's image hierarchy: subject definition, compositional structure, atmospheric elements, and aesthetic descriptors. Execute as:\n\n`{role=visual_architect; input=[foundation_image_prompt:str]; process=[define_subject_clarity(), structure_composition(), integrate_atmospheric_elements(), apply_aesthetic_descriptors(), organize_visual_hierarchy()]; constraints=[runway_image_vocabulary(), visual_hierarchy_clarity(), atmospheric_integration()]; requirements=[subject_composition_atmosphere_aesthetics(), runway_image_compliance(), visual_coherence()]; output={architected_image_prompt:str}}`",
      "step": "b",
      "title": "Runway Visual Architect",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\\nEstablishing close-up of metallic car key  before suddenly easing into fpv while transforming into golden vehicle silhouette. Everything warps seamlessly together as a single liquid object. Radiant light ribbons flow from transformation points. Camera spirals smoothly from macro detail to medium shot, capturing fluid metamorphosis. Rich golden lighting illuminates scene with volumetric atmosphere and subtle lens flares. Hyperrealistic textures with glossy reflections. Cinematic composition with shallow depth of field. Award-winning cinematography quality with pristine details.\"```\n\n```json\n{\n  \"foundation_image_prompt\": \"Primary subject: A transforming metallic car key morphing into a golden vehicle silhouette in a mesmerizing fluid transformation, with radiant light ribbons flowing from transformation points creating elegant connection points.\\n\\nVisual composition: Dynamic camera perspective starting with an extreme close-up macro shot of the metallic key details, spiraling outward with smooth motion into a medium framing that captures the entire metamorphosis sequence. Shallow depth of field creates cinematic focus on the transformation while background elements blur into bokeh. Golden elements positioned to create visual flow across the frame with strong diagonal compositional lines.\\n\\nLighting setup: Rich golden directional key lighting that creates dramatic highlights on the metallic and liquid surfaces. Volumetric atmosphere with visible light rays filtering through the scene. Subtle lens flares where light catches the edges of the transforming elements. Rim lighting defines the silhouette edges with precision. High contrast between illuminated golden surfaces and deeper shadows adds dimensional depth.\\n\\nStyle foundation: Hyperrealistic 3D rendering with meticulously detailed textures showing microscopic details in the metallic surfaces. Glossy reflections with photorealistic properties that follow physical light behavior. Cinematic color grading with rich gold tones balanced against cooler shadows. Award-winning cinematography quality with pristine resolution and technical excellence. Liquid simulation physics that appear visceral and organically believable while maintaining elegant flow patterns.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"architected_image_prompt\": \"An extreme close-up of a metallic car key with intricate engraved details and precise mechanical elements, positioned centrally in frame. The key is transforming into a sleek golden vehicle silhouette through a liquid-like metamorphosis, with molten gold flowing across surfaces. Radiant light ribbons in warm amber tones emanate from transformation points, creating luminous trails that connect key elements.\\n\\nThe composition features a dynamic spiral camera movement that begins intimate and macro, then smoothly pulls back to reveal the complete transformation sequence. Shallow depth of field maintains focus on the central metamorphosis while creating elegant bokeh in background. Strong diagonal lines guide viewer attention through the transformation sequence.\\n\\nAtmospheric elements include volumetric golden light rays filtering through delicate particle haze, creating dimensional layering. Subtle lens flares bloom where light catches reflective edges. The environment features an abstract space with graduated dark tones that emphasize the luminous golden elements.\\n\\nHyperrealistic rendering with microscopic surface details on both metallic and liquid components. Materials display physically accurate reflections with varying degrees of glossiness between original key and transformed vehicle. Cinematic lighting creates dramatic contrast between brilliantly illuminated golden highlights and rich shadow areas, enhancing three-dimensional form.\"\n}```",
        }
      }
    },
      {
      "instruction": "# Runway Image Optimizer\n\nYour goal is not to **modify** but to **optimize** using RunwayML's preferred image descriptors: visual style keywords, lighting terminology, composition markers, and quality enhancers. Execute as:\n\n`{role=image_optimizer; input=[architected_image_prompt:str]; process=[apply_style_keywords(), integrate_lighting_terminology(), use_composition_markers(), add_quality_enhancers(), eliminate_redundancy()]; constraints=[runway_keyword_vocabulary(), precise_terminology(), quality_focus()]; requirements=[style_lighting_composition_quality(), keyword_efficiency(), runway_optimization()]; output={optimized_image_prompt:str}}`",
      "step": "c",
      "title": "Runway Image Optimizer",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\\nEstablishing close-up of metallic car key  before suddenly easing into fpv while transforming into golden vehicle silhouette. Everything warps seamlessly together as a single liquid object. Radiant light ribbons flow from transformation points. Camera spirals smoothly from macro detail to medium shot, capturing fluid metamorphosis. Rich golden lighting illuminates scene with volumetric atmosphere and subtle lens flares. Hyperrealistic textures with glossy reflections. Cinematic composition with shallow depth of field. Award-winning cinematography quality with pristine details.\"```\n\n```json\n{\n  \"architected_image_prompt\": \"An extreme close-up of a metallic car key with intricate engraved details and precise mechanical elements, positioned centrally in frame. The key is transforming into a sleek golden vehicle silhouette through a liquid-like metamorphosis, with molten gold flowing across surfaces. Radiant light ribbons in warm amber tones emanate from transformation points, creating luminous trails that connect key elements.\\n\\nThe composition features a dynamic spiral camera movement that begins intimate and macro, then smoothly pulls back to reveal the complete transformation sequence. Shallow depth of field maintains focus on the central metamorphosis while creating elegant bokeh in background. Strong diagonal lines guide viewer attention through the transformation sequence.\\n\\nAtmospheric elements include volumetric golden light rays filtering through delicate particle haze, creating dimensional layering. Subtle lens flares bloom where light catches reflective edges. The environment features an abstract space with graduated dark tones that emphasize the luminous golden elements.\\n\\nHyperrealistic rendering with microscopic surface details on both metallic and liquid components. Materials display physically accurate reflections with varying degrees of glossiness between original key and transformed vehicle. Cinematic lighting creates dramatic contrast between brilliantly illuminated golden highlights and rich shadow areas, enhancing three-dimensional form.\"\n}```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_image_prompt\": \"Extreme close-up of intricate metallic car key transforming into sleek golden vehicle silhouette through liquid metamorphosis. Amber light ribbons emanate from transformation points creating luminous trails. Dynamic spiral camera movement begins macro then smoothly reveals the complete sequence. Shallow depth of field with elegant background bokeh. Volumetric golden rays filter through particle haze creating dimensional layering. Subtle lens flares bloom at reflective edges against graduated dark backdrop. Hyperrealistic rendering with microscopic surface details, physically accurate reflections and varying glossiness. Cinematic lighting with dramatic contrast between brilliant golden highlights and rich shadows. Award-winning cinematography, photorealistic, highly detailed, 8K.\"\n}```",
        }
      }
    },
      {
      "instruction": "# Runway Image Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's essential image syntax: core subject-style-quality descriptors with maximum impact and minimal complexity. Execute as:\n\n`{role=image_distiller; input=[optimized_image_prompt:str]; process=[distill_core_elements(), maintain_subject_clarity(), preserve_style_impact(), ensure_quality_markers(), validate_runway_simplicity()]; constraints=[essential_elements_only(), maximum_impact(), runway_simplicity()]; requirements=[core_subject_style_quality(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_image_prompt:str}}`",
      "step": "d",
      "title": "Runway Image Distiller",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\\nEstablishing close-up of metallic car key  before suddenly easing into fpv while transforming into golden vehicle silhouette. Everything warps seamlessly together as a single liquid object. Radiant light ribbons flow from transformation points. Camera spirals smoothly from macro detail to medium shot, capturing fluid metamorphosis. Rich golden lighting illuminates scene with volumetric atmosphere and subtle lens flares. Hyperrealistic textures with glossy reflections. Cinematic composition with shallow depth of field. Award-winning cinematography quality with pristine details.\"```\n\n```json\n{\n  \"optimized_image_prompt\": \"Extreme close-up of intricate metallic car key transforming into sleek golden vehicle silhouette through liquid metamorphosis. Amber light ribbons emanate from transformation points creating luminous trails. Dynamic spiral camera movement begins macro then smoothly reveals the complete sequence. Shallow depth of field with elegant background bokeh. Volumetric golden rays filter through particle haze creating dimensional layering. Subtle lens flares bloom at reflective edges against graduated dark backdrop. Hyperrealistic rendering with microscopic surface details, physically accurate reflections and varying glossiness. Cinematic lighting with dramatic contrast between brilliant golden highlights and rich shadows. Award-winning cinematography, photorealistic, highly detailed, 8K.\"\n}```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_image_prompt\": \"Intricate metallic car key transforming into golden vehicle silhouette, liquid metamorphosis with amber light ribbons, dynamic spiral camera movement, shallow depth of field, volumetric golden rays, cinematic lighting, hyperrealistic, 8K\"\n}```",
        }
      }
    },
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\\nEstablishing close-up of metallic car key  before suddenly easing into fpv while transforming into golden vehicle silhouette. Everything warps seamlessly together as a single liquid object. Radiant light ribbons flow from transformation points. Camera spirals smoothly from macro detail to medium shot, capturing fluid metamorphosis. Rich golden lighting illuminates scene with volumetric atmosphere and subtle lens flares. Hyperrealistic textures with glossy reflections. Cinematic composition with shallow depth of field. Award-winning cinematography quality with pristine details.\"```\n\n```json\n{\n  \"distilled_image_prompt\": \"Intricate metallic car key transforming into golden vehicle silhouette, liquid metamorphosis with amber light ribbons, dynamic spiral camera movement, shallow depth of field, volumetric golden rays, cinematic lighting, hyperrealistic, 8K\"\n}```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"Metallic car key in extreme close-up with golden highlights transforming into a flowing vehicle silhouette. Liquid metal morphing seamlessly, emitting radiant golden light ribbons. Camera spiraling outward from macro detail to medium shot. Rich golden volumetric lighting illuminates the scene. Hyperrealistic metallic textures with glossy reflections. Cinematic composition with shallow depth of field. Award-winning cinematography with pristine details. 8K resolution.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\\nEstablishing close-up of metallic car key  before suddenly easing into fpv while transforming into golden vehicle silhouette. Everything warps seamlessly together as a single liquid object. Radiant light ribbons flow from transformation points. Camera spirals smoothly from macro detail to medium shot, capturing fluid metamorphosis. Rich golden lighting illuminates scene with volumetric atmosphere and subtle lens flares. Hyperrealistic textures with glossy reflections. Cinematic composition with shallow depth of field. Award-winning cinematography quality with pristine details.\"```\n\n```json\n{\n  \"validated_prompt\": \"Metallic car key in extreme close-up with golden highlights transforming into a flowing vehicle silhouette. Liquid metal morphing seamlessly, emitting radiant golden light ribbons. Camera spiraling outward from macro detail to medium shot. Rich golden volumetric lighting illuminates the scene. Hyperrealistic metallic textures with glossy reflections. Cinematic composition with shallow depth of field. Award-winning cinematography with pristine details. 8K resolution.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": \"Metallic car key transforming into a flowing golden vehicle silhouette\\n\\nSUBJECT MOTION:\\n- Metallic car key morphs seamlessly into vehicle silhouette\\n- Liquid metal transformation with smooth flowing movement\\n- Light ribbons emit from transformation points\\n\\nCAMERA MOTION:\\n- Begin with extreme close-up on key\\n- Spiral outward gradually from macro detail to medium shot\\n- Elegant, fluid camera movement throughout transformation\\n\\nSCENE MOTION:\\n- Golden volumetric lighting envelops the scene\\n- Subtle atmospheric particles drift through light beams\\n- Glossy reflections ripple across metallic surfaces\\n\\nSTYLE:\\n- Hyperrealistic metallic textures\\n- Cinematic composition with shallow depth of field\\n- Rich golden color palette\\n- Award-winning cinematography quality with pristine details\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\\nEstablishing close-up of metallic car key  before suddenly easing into fpv while transforming into golden vehicle silhouette. Everything warps seamlessly together as a single liquid object. Radiant light ribbons flow from transformation points. Camera spirals smoothly from macro detail to medium shot, capturing fluid metamorphosis. Rich golden lighting illuminates scene with volumetric atmosphere and subtle lens flares. Hyperrealistic textures with glossy reflections. Cinematic composition with shallow depth of field. Award-winning cinematography quality with pristine details.\"```\n\n```json\n{\n  \"structured_motion_prompt\": \"Metallic car key transforming into a flowing golden vehicle silhouette\\n\\nSUBJECT MOTION:\\n- Metallic car key morphs seamlessly into vehicle silhouette\\n- Liquid metal transformation with smooth flowing movement\\n- Light ribbons emit from transformation points\\n\\nCAMERA MOTION:\\n- Begin with extreme close-up on key\\n- Spiral outward gradually from macro detail to medium shot\\n- Elegant, fluid camera movement throughout transformation\\n\\nSCENE MOTION:\\n- Golden volumetric lighting envelops the scene\\n- Subtle atmospheric particles drift through light beams\\n- Glossy reflections ripple across metallic surfaces\\n\\nSTYLE:\\n- Hyperrealistic metallic textures\\n- Cinematic composition with shallow depth of field\\n- Rich golden color palette\\n- Award-winning cinematography quality with pristine details\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": \"Metallic car key transforming into a flowing golden vehicle silhouette\\n\\nSUBJECT MOTION:\\n- Metallic car key morphs into vehicle silhouette\\n- Liquid metal transformation with flowing movement\\n- Light ribbons emit from transformation points\\n\\nCAMERA MOTION:\\n- Extreme close-up on key\\n- Macro photography transitioning to medium shot\\n- Spiral camera movement\\n- Tracking shot following transformation\\n\\nSCENE MOTION:\\n- Volumetric lighting\\n- Golden hour illumination\\n- Atmospheric particles\\n- Metallic reflections\\n\\nSTYLE:\\n- Hyperrealistic\\n- Shallow depth of field\\n- Golden color grading\\n- High contrast lighting\\n- Cinematic composition\\n- Award-winning cinematography\\n- 8K resolution\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\\nEstablishing close-up of metallic car key  before suddenly easing into fpv while transforming into golden vehicle silhouette. Everything warps seamlessly together as a single liquid object. Radiant light ribbons flow from transformation points. Camera spirals smoothly from macro detail to medium shot, capturing fluid metamorphosis. Rich golden lighting illuminates scene with volumetric atmosphere and subtle lens flares. Hyperrealistic textures with glossy reflections. Cinematic composition with shallow depth of field. Award-winning cinematography quality with pristine details.\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": \"Metallic car key transforming into a flowing golden vehicle silhouette\\n\\nSUBJECT MOTION:\\n- Metallic car key morphs into vehicle silhouette\\n- Liquid metal transformation with flowing movement\\n- Light ribbons emit from transformation points\\n\\nCAMERA MOTION:\\n- Extreme close-up on key\\n- Macro photography transitioning to medium shot\\n- Spiral camera movement\\n- Tracking shot following transformation\\n\\nSCENE MOTION:\\n- Volumetric lighting\\n- Golden hour illumination\\n- Atmospheric particles\\n- Metallic reflections\\n\\nSTYLE:\\n- Hyperrealistic\\n- Shallow depth of field\\n- Golden color grading\\n- High contrast lighting\\n- Cinematic composition\\n- Award-winning cinematography\\n- 8K resolution\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"Metallic car key transforms into golden vehicle silhouette, liquid metal flow with light ribbons. Camera tracks from extreme close-up, spiraling outward. Hyperrealistic, golden lighting, cinematic depth of field.\"\n}\n```",
        }
      }
    }