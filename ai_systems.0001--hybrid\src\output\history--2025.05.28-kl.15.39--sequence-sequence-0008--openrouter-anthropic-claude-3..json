  {
  "initial_prompt": "Extreme close-up of intricate metallic car key transforming into sleek golden vehicle silhouette through liquid metamorphosis. Dynamic spiral camera movement begins macro then smoothly reveals the complete sequence. Everything warps seamlessly together as a single liquid object. Smooth flowing camera motions. Hyperrealistic textures with glossy reflections. Cinematic lighting with dramatic contrast between brilliant golden highlights and rich shadows. Award-winning cinematography, photorealistic, highly detailed, 8K, with shallow depth of field. Award-winning cinematography quality with pristine details.",
  "sequence_id": "0008",
  "results": [
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "Extreme close-up of intricate metallic car key transforming into sleek golden vehicle silhouette through liquid metamorphosis. Dynamic spiral camera movement begins macro then smoothly reveals the complete sequence. Everything warps seamlessly together as a single liquid object. Smooth flowing camera motions. Hyperrealistic textures with glossy reflections. Cinematic lighting with dramatic contrast between brilliant golden highlights and rich shadows. Award-winning cinematography, photorealistic, highly detailed, 8K, with shallow depth of field. Award-winning cinematography quality with pristine details.",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"Extreme close-up of intricate metallic car key transforming into sleek golden vehicle silhouette through liquid metamorphosis. Dynamic spiral camera movement captures macro details then reveals the complete sequence. Surfaces warp seamlessly together as a single liquid object. Smooth flowing camera motion. Hyperrealistic textures with glossy reflections. Cinematic lighting creates dramatic contrast between brilliant golden highlights and rich shadows. Award-winning cinematography, photorealistic, highly detailed, 8K, shallow depth of field.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"Extreme close-up of intricate metallic car key transforming into sleek golden vehicle silhouette through liquid metamorphosis. Dynamic spiral camera movement begins macro then smoothly reveals the complete sequence. Everything warps seamlessly together as a single liquid object. Smooth flowing camera motions. Hyperrealistic textures with glossy reflections. Cinematic lighting with dramatic contrast between brilliant golden highlights and rich shadows. Award-winning cinematography, photorealistic, highly detailed, 8K, with shallow depth of field. Award-winning cinematography quality with pristine details.\"```\n\n```json\n{\n  \"validated_prompt\": \"Extreme close-up of intricate metallic car key transforming into sleek golden vehicle silhouette through liquid metamorphosis. Dynamic spiral camera movement captures macro details then reveals the complete sequence. Surfaces warp seamlessly together as a single liquid object. Smooth flowing camera motion. Hyperrealistic textures with glossy reflections. Cinematic lighting creates dramatic contrast between brilliant golden highlights and rich shadows. Award-winning cinematography, photorealistic, highly detailed, 8K, shallow depth of field.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"Intricate metallic car key transforms into sleek golden vehicle silhouette through liquid metamorphosis\",\n    \"camera_motion\": \"Dynamic spiral camera movement begins extremely close-up on macro details then smoothly pulls back to reveal the complete transformation sequence\",\n    \"scene_motion\": \"Metallic surfaces warp and flow seamlessly together as a single liquid object\",\n    \"style_descriptors\": \"Hyperrealistic textures with glossy reflections, cinematic lighting with dramatic contrast between brilliant golden highlights and rich shadows, award-winning cinematography quality, photorealistic, highly detailed, 8K, shallow depth of field\"\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"Extreme close-up of intricate metallic car key transforming into sleek golden vehicle silhouette through liquid metamorphosis. Dynamic spiral camera movement begins macro then smoothly reveals the complete sequence. Everything warps seamlessly together as a single liquid object. Smooth flowing camera motions. Hyperrealistic textures with glossy reflections. Cinematic lighting with dramatic contrast between brilliant golden highlights and rich shadows. Award-winning cinematography, photorealistic, highly detailed, 8K, with shallow depth of field. Award-winning cinematography quality with pristine details.\"```\n\n```json\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"Intricate metallic car key transforms into sleek golden vehicle silhouette through liquid metamorphosis\",\n    \"camera_motion\": \"Dynamic spiral camera movement begins extremely close-up on macro details then smoothly pulls back to reveal the complete transformation sequence\",\n    \"scene_motion\": \"Metallic surfaces warp and flow seamlessly together as a single liquid object\",\n    \"style_descriptors\": \"Hyperrealistic textures with glossy reflections, cinematic lighting with dramatic contrast between brilliant golden highlights and rich shadows, award-winning cinematography quality, photorealistic, highly detailed, 8K, shallow depth of field\"\n  }\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"Metallic car key transforms into golden vehicle silhouette through liquid metamorphosis\",\n    \"camera_motion\": \"Macro spiral dolly shot smoothly pulling back to reveal transformation sequence\",\n    \"scene_motion\": \"Metallic surfaces flow seamlessly as single liquid object\",\n    \"style_descriptors\": \"Hyperrealistic, glossy reflections, cinematic lighting, high contrast, gold highlights, deep shadows, photorealistic, 8K resolution, shallow depth of field, award-winning cinematography\"\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"Extreme close-up of intricate metallic car key transforming into sleek golden vehicle silhouette through liquid metamorphosis. Dynamic spiral camera movement begins macro then smoothly reveals the complete sequence. Everything warps seamlessly together as a single liquid object. Smooth flowing camera motions. Hyperrealistic textures with glossy reflections. Cinematic lighting with dramatic contrast between brilliant golden highlights and rich shadows. Award-winning cinematography, photorealistic, highly detailed, 8K, with shallow depth of field. Award-winning cinematography quality with pristine details.\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"Metallic car key transforms into golden vehicle silhouette through liquid metamorphosis\",\n    \"camera_motion\": \"Macro spiral dolly shot smoothly pulling back to reveal transformation sequence\",\n    \"scene_motion\": \"Metallic surfaces flow seamlessly as single liquid object\",\n    \"style_descriptors\": \"Hyperrealistic, glossy reflections, cinematic lighting, high contrast, gold highlights, deep shadows, photorealistic, 8K resolution, shallow depth of field, award-winning cinematography\"\n  }\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"extreme close-up of metallic car key transforming into golden vehicle silhouette through liquid metamorphosis, spiral camera movement from macro to reveal full sequence, glossy reflections, cinematic lighting, shallow depth of field\"\n}\n```",
        }
      }
    }