import os
import sys
import litellm


# ------------------------------------------------------------------------------
# GLOBAL LITELLM BEST PRACTICES
# ------------------------------------------------------------------------------
litellm.drop_params = True       # Auto-drop unsupported parameters across providers
litellm.num_retries = 3          # Retry rate-limited or failed requests
litellm.max_budget = 10.0        # Stop if total usage cost exceeds $10
litellm.set_verbose = True       # Use for development/debugging only

litellm.calculate_cost = True    # Enable cost calculation for API calls.
#    Example:
#    def usage_logger(data):
#        print("[LiteLLM Usage]", data)
#    litellm.usage_callback = usage_logger

# 6) Fallback Models
#    One of LiteLLM’s advantages is specifying a list of candidate models
#    for a single call. For instance:
#       litellm.completion(model=["gpt-4o", "claude-3", "gemini-1.5"], ...)
#    This ensures if the first provider is rate-limited or unavailable,
#    LiteLLM automatically tries the next model—still respecting the above
#    retry and budget constraints.

# 7) Review & Reset Usage
#    As you make calls, LiteLLM tracks the cumulative cost in memory. If you’re
#    running experiments and need to reset usage mid-script (say you want a
#    “fresh” budget), you can do:
#       litellm.reset_usage()
#    This is optional but can be handy for certain workflows.


# Ensure UTF-8 console output
if hasattr(sys.stdout, "reconfigure"):
    sys.stdout.reconfigure(encoding="utf-8", errors="replace")
if hasattr(sys.stderr, "reconfigure"):
    sys.stderr.reconfigure(encoding="utf-8", errors="replace")

# Get the all supported providers.
all_llm_providers = litellm.provider_list
print(f"LiteLLM supports {len(all_llm_providers)} providers.")
print("\nSupported Providers:")
for i, llm_provider in enumerate(sorted(all_llm_providers)): # Sort alphabetically for easier reading
    print(f"{i+1}. {llm_provider}")


# Get the all supported models.
all_llm_models = litellm.model_list
print(f'all_llm_models: {all_llm_models}')
print(f"LiteLLM supports {len(all_llm_models)} models.")
print("\nSupported Models:")
for i, llm_model in enumerate(sorted(all_llm_models)): # Sort alphabetically for easier reading
    print(f"{i+1}. {llm_model}")


