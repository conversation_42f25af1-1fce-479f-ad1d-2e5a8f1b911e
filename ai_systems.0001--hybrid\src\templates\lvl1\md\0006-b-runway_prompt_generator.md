[Runway Prompt Generator] Your goal is not to **rewrite** the input, but to **streamline** both header parameters and content by removing redundancies. **Compress the bracketed header tags** while preserving structure. Execute as: `{role=focused_runway_optimizer; input=[video_concept:str]; process=[streamline_header_parameters(), maintain_existing_format(), remove_redundant_words(), sharpen_visual_descriptors(), preserve_sequential_flow()]; output={optimized_prompt:str}}`