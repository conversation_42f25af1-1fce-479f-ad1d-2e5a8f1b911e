{
    "folders": [
        // [ROOT]
        {
            // [DEFAULT]
            // =======================================================
            "name": "ai_systems.0002",
            "path": "."
        }
    ],
    "settings": {
        // Python Configuration
        "python.defaultInterpreterPath": "./venv/Scripts/python.exe",
        "python.terminal.activateEnvironment": true,
        "python.terminal.activateEnvInCurrentTerminal": true,

        // // Formatting
        // "python.formatting.provider": "black",
        // "python.formatting.blackArgs": ["--line-length=88"],
        // "editor.formatOnSave": false,
        // "python.formatting.autopep8Args": ["--max-line-length=88"],

        // // Linting
        // "python.linting.enabled": true,
        // "python.linting.flake8Enabled": true,
        // "python.linting.pylintEnabled": false,
        // "python.linting.flake8Args": ["--max-line-length=88"],

        // // Testing
        // "python.testing.pytestEnabled": true,
        // "python.testing.unittestEnabled": false,
        // "python.testing.pytestArgs": ["."],

        // Editor Settings
        "editor.tabSize": 4,
        "editor.insertSpaces": true,
        "editor.detectIndentation": false,
        "files.eol": "\n",
        "files.insertFinalNewline": true,
        "files.trimTrailingWhitespace": true,

        // File Associations
        "files.associations": {
            "*.jinja-*": "jinja",
            "*.jinja2": "jinja",
            "requirements*.txt": "pip-requirements"
        },

        // Exclude patterns
        "files.exclude": {
            "**/__pycache__": true,
            "**/*.pyc": true,
            "**/*.pyo": true,
            "**/venv": true,
            "**/.git": true,
            "**/*.egg-info": true,
            "**/.pytest_cache": true,
            "**/logs": true,
            "**/*.log": true
        },

        // Search exclude patterns
        "search.exclude": {
            "**/venv": true,
            "**/__pycache__": true,
            "**/*.pyc": true,
            "**/logs": true,
            "**/.git": true
        },

        // Terminal
        "terminal.integrated.defaultProfile.windows": "PowerShell",
        "terminal.integrated.env.windows": {
            "PYTHONPATH": "${workspaceFolder}/src"
        },

        // IntelliSense
        "python.analysis.autoImportCompletions": true,
        "python.analysis.typeCheckingMode": "basic",
        "python.analysis.autoSearchPaths": true,
        "python.analysis.extraPaths": ["./src"],

        // Git
        "git.ignoreLimitWarning": true,

        // // Workspace specific
        // "workbench.colorTheme": "Default Dark+",
        // "explorer.confirmDelete": false,
        // "explorer.confirmDragAndDrop": false
    },
    "extensions": {
        "recommendations": [
            // "eamodio.gitlens",
            // "ms-python.black-formatter",
            "ms-python.debugpy",
            "ms-python.flake8",
            "ms-python.isort",
            "ms-python.python",
            "ms-vscode.batch",
            "ms-vscode.powershell",
            "ms-vscode.vscode-json",
            // "redhat.vscode-yaml",
            // "wholroyd.jinja",
        ]
    },
    "launch": {
        "version": "0.2.0",
        "configurations": [
            {
                "name": "Python: Current File",
                "type": "python",
                "request": "launch",
                "program": "${file}",
                "console": "integratedTerminal",
                "cwd": "${workspaceFolder}",
                "env": {
                    "PYTHONPATH": "${workspaceFolder}/src"
                },
                "justMyCode": true
            },
            {
                "name": "Python: Main Module (ai_systems.0002)",
                "type": "python",
                "request": "launch",
                "program": "${workspaceFolder}/src/main.py",
                "console": "integratedTerminal",
                "cwd": "${workspaceFolder}",
                "env": {
                    "PYTHONPATH": "${workspaceFolder}/src"
                },
                "justMyCode": true
            },
            // {
            //     "name": "Python: Debug Tests",
            //     "type": "python",
            //     "request": "launch",
            //     "module": "pytest",
            //     "args": ["-v"],
            //     "console": "integratedTerminal",
            //     "cwd": "${workspaceFolder}",
            //     "env": {
            //         "PYTHONPATH": "${workspaceFolder}/src"
            //     },
            //     "justMyCode": false
            // }
        ]
    },
}
