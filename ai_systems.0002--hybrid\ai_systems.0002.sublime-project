{
    "folders": [
        {
            "path": ".",
            "folder_exclude_patterns": [".git", "__pycache__", "venv", "*.egg-info", ],
            "file_exclude_patterns": ["*.sublime-workspace", "*.pyc", "*.pyo", "*.swp", "*.tmp", ".DS_Store", ],
        },

    ],
    "settings": {
        "tab_size": 4,
        "default_line_ending": "unix",
        "translate_tabs_to_spaces": true,
        "ensure_newline_at_eof_on_save": true,
        "trim_trailing_white_space_on_save": true,
        "python_interpreter": "$project_path\\venv\\Scripts\\python",
        // "python_interpreter": "\\__GOTO__\\Scripts\\Python\\playground\\venv\\Scripts\\python",
        "python_formatter": "black",
        "python_linter": "flake8",
        "python_format_on_save": false
    },
    "build_systems": [
        // [py:build2]
        // =======================================================
        {
            "name": "[py:build2] \t ai_systems.0002.sublime-project",
            "cmd": [
                "$project_path\\venv\\Scripts\\python",
                // "C:\\Users\\<USER>\\Desktop\\User\\Nas_Flow_Jorn\\__GOTO__\\Scripts\\Python\\playground\\venv\\Scripts\\python",
                "-u",
                "${file}"
            ],
            "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)",
            "selector": "source.python",
            "shell": true,
            // "syntax": "Packages/build2/buildfile.sublime-syntax",
            // "syntax": "JSON.sublime-syntax",
            "syntax": "Python.sublime-syntax",
            // "syntax": "Markdown.sublime-syntax",
            "working_dir": "${project_path}"
        },

        // [py:terminus:open]
        // =======================================================
        {
            "name": "[py:terminus_open] \t ai_systems.0002.sublime-project",
            "target": "terminus_open",
            "auto_close": false,
            "title": "py.output",
            "focus": true,
            "timeit": true,
            "post_window_hooks": [["carry_file_to_pane", {"direction": "right"}]],
            "shell_cmd": "\"$project_path\\venv\\Scripts\\python\" -u \"${file}\"",
            "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)",
            "selector": "source.python",
            "env": {"PYTHONIOENCODING": "utf-8"},
            "working_dir": "${project_path}",
        },

        // [py:terminus:panel]
        // =======================================================
        {
            "name": "[py:terminus_panel] \t ai_systems.0002.sublime-project",
            "target": "toggle_terminus_panel",
            "cancel": "terminus_cancel_build",
            "auto_close": false,
            "focus": true,
            "timeit": true,
            "shell_cmd": "\"$project_path\\venv\\Scripts\\python\" -u \"${file}\"",
            "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)",
            "selector": "source.python",
            "env": {"PYTHONIOENCODING": "utf-8"},
            "working_dir": "${project_path}",
        },

        // [batch:terminus:open]
        // =======================================================
        {
            "name": "[bat:terminus_open] \t ai_systems.0002.sublime-project",
            "target": "terminus_open",
            "auto_close": false,
            "focus": true,
            "timeit": true,
            "cmd": ["cmd", "/c", "${file}"],
            "selector": "source.Batch",
            "working_dir": "${project_path}",
        },
    ]
}
