#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Template Generator

This script generates template files based on a predefined dictionary structure.
It creates the necessary directory structure and writes each template to its own file.

Usage:
    python template_generator.py

The script will generate template files in the OUTPUT_DIR directory (default: 'templates').
To change the output directory, modify the OUTPUT_DIR constant at the top of the script.

Each template file will be created with the format:
[Title] Interpretation Execute as: Transformation

Example:
[Instruction Amplifier A] Your mandate is not to... Execute as: `{role=instruction_amplifier_a;...}`
"""

import os
import sys
from typing import Dict

# Configuration
# Change this to modify the output directory
OUTPUT_DIR = "templates"

# Template definitions
# To add a new template, add a new entry to this dictionary with the following structure:
# "filename.md": {
#     "title": "Template Title",
#     "interpretation": "Template interpretation text...",
#     "transformation": "`{role=...}`",
#     "keywords": "keyword1|keyword2|..."
# }


TEMPLATES = {
    "0001-a-rephrase-insdddtruction-converter.md": {
        "title": "Instruction Converter",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: ",
        "transformation": "{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}",
    },
}


def ensure_directory_exists(directory_path: str) -> None:
    """Create directory if it doesn't exist."""
    if not os.path.exists(directory_path):
        try:
            os.makedirs(directory_path)
            print(f"Created directory: {directory_path}")
        except OSError as e:
            print(f"Error creating directory '{directory_path}': {e}", file=sys.stderr)
            raise


def generate_template_content(template_data: Dict[str, str]) -> str:
    """Generate template content from template data."""
    title = template_data.get("title", "")
    interpretation = template_data.get("interpretation", "")
    transformation = template_data.get("transformation", "")

    # Format the template content according to the expected structure
    # Check if interpretation already ends with "Execute as:" to avoid duplication
    if interpretation.strip().endswith("Execute as:"):
        content = f"[{title}] {interpretation} {transformation}"
    else:
        content = f"[{title}] {interpretation} Execute as: {transformation}"

    return content


def write_template_file(file_path: str, content: str) -> None:
    """Write content to a template file."""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Created template file: {file_path}")
    except IOError as e:
        print(f"Error writing template file '{file_path}': {e}", file=sys.stderr)
        raise


def generate_templates(templates_data: Dict[str, Dict[str, str]], output_dir: str = "templates") -> None:
    """Generate template files from templates data."""
    # Ensure the output directory exists
    ensure_directory_exists(output_dir)

    # Generate each template file
    for filename, template_data in templates_data.items():
        # Generate the template content
        content = generate_template_content(template_data)

        # Write the template file
        file_path = os.path.join(output_dir, filename)
        write_template_file(file_path, content)


#===================================================================
# SCRIPT EXECUTION
#===================================================================
if __name__ == "__main__":
    """
    Main function to generate template files.

    To use this script:
    1. Modify the TEMPLATES dictionary to add/edit/remove templates
    2. Change OUTPUT_DIR if you want to save templates in a different directory
    3. Run the script: python template_generator.py
    """
    OUTPUT_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), OUTPUT_DIR)

    print(f"Generating templates in directory: {OUTPUT_DIR}")
    print(f"- Path: {OUTPUT_PATH}\n")

    # Generate templates
    generate_templates(TEMPLATES, OUTPUT_PATH)

    print(f"\nTemplate generation complete. Templates saved to: {OUTPUT_DIR}")
