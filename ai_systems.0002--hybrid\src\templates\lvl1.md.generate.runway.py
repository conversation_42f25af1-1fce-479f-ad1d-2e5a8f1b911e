
import os

# Ensure the output directory exists
output_dir = "src/templates/lvl1/md"
os.makedirs(output_dir, exist_ok=True)

templates = {
    "0005-a-runway_prompt_generator": {
        "title": "Runway Prompt Generator",
        "interpretation": "Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:",
        "transformation": "`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
    },

}

def main():
    """Create markdown files in the same format as the batch file."""
    created_files = []

    for filename, template in templates.items():
        filepath = os.path.join(output_dir, f"{filename}.md")
        with open(filepath, "w", encoding="utf-8") as f:
            # Format: [Title] interpretation transformation
            content = (f"[{template['title']}] {template['interpretation']} "
                       f"{template['transformation']}")
            f.write(content)
        created_files.append(f"{filename}.md")

    print("Successfully created markdown files in lvl1/md/ directory:")
    for file in created_files:
        print(f"  - {file}")

if __name__ == "__main__":
    main()
