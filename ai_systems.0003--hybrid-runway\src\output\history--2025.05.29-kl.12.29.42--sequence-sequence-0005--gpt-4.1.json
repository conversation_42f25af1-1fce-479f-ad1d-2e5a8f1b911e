  {
  "initial_prompt": "Cinematic FPV camera surges through a dazzling vortex of electric words, each phrase erupting with fiery emotion and razor-sharp clarity. The camera spirals in dynamic, continuous motion, words colliding and morphing into luminous calligraphy, charging the air with fierce energy. Electric sparks illuminate the shifting scene as intensifying language crescendos—every phrase amplifies, compounding passion and purpose, surging toward a climactic eruption of linguistic power. Bold lighting shifts cast dramatic shadows, while the camera weaves through the vortex with relentless velocity, immersing the viewer in an overwhelming visual symphony of escalating emotion and searing resonance. Hyper-detailed, high contrast, maximalist style, relentless momentum.",
  "sequence_id": "0005",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "Cinematic FPV camera surges through a dazzling vortex of electric words, each phrase erupting with fiery emotion and razor-sharp clarity. The camera spirals in dynamic, continuous motion, words colliding and morphing into luminous calligraphy, charging the air with fierce energy. Electric sparks illuminate the shifting scene as intensifying language crescendos—every phrase amplifies, compounding passion and purpose, surging toward a climactic eruption of linguistic power. Bold lighting shifts cast dramatic shadows, while the camera weaves through the vortex with relentless velocity, immersing the viewer in an overwhelming visual symphony of escalating emotion and searing resonance. Hyper-detailed, high contrast, maximalist style, relentless momentum.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"FPV camera surges and spirals through a dazzling electric vortex of glowing words, each phrase bursting with fiery emotion and razor-sharp clarity. Camera executes dynamic, continuous motion, weaving and accelerating as words violently collide, morph, and dissolve into radiant, luminous calligraphy. Electric sparks crackle, illuminating an ever-shifting tunnel; bold, dramatic lighting shifts cast deep shadows, amplifying the vortex's intensity. The language rapidly crescendos, phrases amplifying and compounding in passion and purpose, charging the air with fierce, relentless energy. Visuals build in a maximalist, hyper-detailed, high-contrast style with seamless morphing, continuous CG movement, and escalating motion, culminating in a climactic explosion of linguistic power. Immersive, cinematic, overwhelming, non-stop flow.\"\n}",
        }
      }
    }