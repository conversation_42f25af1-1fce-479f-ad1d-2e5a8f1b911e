  {
  "initial_prompt": "FPV camera initiates a dynamic orbital dive through radiant golden energy ribbons that spiral and flow in elegant curves through dimensional space. The camera weaves aggressively between these luminous golden trails as they begin to converge and solidify, their flowing energy condensing into metallic form. The spiraling ribbons seamlessly transform into the sleek contours of a luxury Mercedes-Benz sports car, materializing with liquid-smooth precision—golden metallic paint gleaming under dynamic lighting, piercing blue LED headlights igniting as the transformation completes. The FPV perspective continues its relentless orbital motion around the fully-formed vehicle, which now sits on a mirror-black reflective surface that doubles every detail. Golden light trails continue to flow and spiral around the car in perpetual motion, creating an endless dance of energy and reflection. Camera movement is fluid, kinetic, and uninterrupted—diving, weaving, orbiting with cinematic precision. Every transition flows organically from pure energy to solid form, emphasizing seamless metamorphosis, dynamic reflections, and continuous transformation. No cuts, no fades—only pure, unbroken visual evolution from abstract golden energy to hyper-real automotive perfection.",
  "sequence_id": "0005",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "FPV camera initiates a dynamic orbital dive through radiant golden energy ribbons that spiral and flow in elegant curves through dimensional space. The camera weaves aggressively between these luminous golden trails as they begin to converge and solidify, their flowing energy condensing into metallic form. The spiraling ribbons seamlessly transform into the sleek contours of a luxury Mercedes-Benz sports car, materializing with liquid-smooth precision—golden metallic paint gleaming under dynamic lighting, piercing blue LED headlights igniting as the transformation completes. The FPV perspective continues its relentless orbital motion around the fully-formed vehicle, which now sits on a mirror-black reflective surface that doubles every detail. Golden light trails continue to flow and spiral around the car in perpetual motion, creating an endless dance of energy and reflection. Camera movement is fluid, kinetic, and uninterrupted—diving, weaving, orbiting with cinematic precision. Every transition flows organically from pure energy to solid form, emphasizing seamless metamorphosis, dynamic reflections, and continuous transformation. No cuts, no fades—only pure, unbroken visual evolution from abstract golden energy to hyper-real automotive perfection.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"FPV camera launches into a kinetic orbital dive through radiant golden energy ribbons spiraling in dimensional space, weaving aggressively between luminous trails as they converge and condense into metallic form. Ribbons morph into the sleek contours of a luxury Mercedes-Benz sports car, golden paint gleaming in shifting dynamic lighting, blue LED headlights igniting as the transformation perfects. FPV perspective maintains relentless, fluid orbit around the fully-formed car on a mirror-black reflective surface, every detail reflected below. Golden light ribbons continue to spiral and flow around the vehicle in perpetual motion, creating a continuous dance of energy and reflection. Unbroken, uncut sequence emphasizing liquid-smooth transitions, seamless morphing from energy to solid form, dynamic lighting and evolving reflections, all captured with immersive first-person, uninterrupted camera movements for maximum cinematic impact.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:\n\n`{role=focused_runway_optimizer; input=[runwayml_prompt:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency(), maintain_core_narrative_flow()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), reduce_character_count_by_30_percent(), eliminate_redundancy()]; requirements=[preserve_visual_impact(), maintain_runway_syntax(), ensure_motion_continuity()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"FPV camera initiates a dynamic orbital dive through radiant golden energy ribbons that spiral and flow in elegant curves through dimensional space. The camera weaves aggressively between these luminous golden trails as they begin to converge and solidify, their flowing energy condensing into metallic form. The spiraling ribbons seamlessly transform into the sleek contours of a luxury Mercedes-Benz sports car, materializing with liquid-smooth precision—golden metallic paint gleaming under dynamic lighting, piercing blue LED headlights igniting as the transformation completes. The FPV perspective continues its relentless orbital motion around the fully-formed vehicle, which now sits on a mirror-black reflective surface that doubles every detail. Golden light trails continue to flow and spiral around the car in perpetual motion, creating an endless dance of energy and reflection. Camera movement is fluid, kinetic, and uninterrupted—diving, weaving, orbiting with cinematic precision. Every transition flows organically from pure energy to solid form, emphasizing seamless metamorphosis, dynamic reflections, and continuous transformation. No cuts, no fades—only pure, unbroken visual evolution from abstract golden energy to hyper-real automotive perfection.\\\"```'\\n\\n\",\n  \"runwayml_prompt\": \"FPV camera launches into a kinetic orbital dive through radiant golden energy ribbons spiraling in dimensional space, weaving aggressively between luminous trails as they converge and condense into metallic form. Ribbons morph into the sleek contours of a luxury Mercedes-Benz sports car, golden paint gleaming in shifting dynamic lighting, blue LED headlights igniting as the transformation perfects. FPV perspective maintains relentless, fluid orbit around the fully-formed car on a mirror-black reflective surface, every detail reflected below. Golden light ribbons continue to spiral and flow around the vehicle in perpetual motion, creating a continuous dance of energy and reflection. Unbroken, uncut sequence emphasizing liquid-smooth transitions, seamless morphing from energy to solid form, dynamic lighting and evolving reflections, all captured with immersive first-person, uninterrupted camera movements for maximum cinematic impact.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_prompt\": \"FPV camera dives and weaves through spiraling golden energy ribbons in dimensional space, ribbons condensing into sleek, metallic Mercedes-Benz sports car. Camera orbits continuously around the newly-formed car on a mirror-black reflective surface, vivid golden light trails flowing and circling in unbroken kinetic motion. Seamless morph from energy to solid form, dynamic reflections and lighting, held in uninterrupted, immersive FPV movement—no cuts, pure cinematic evolution.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **compress** the optimized prompt into maximum FPV visual efficiency while preserving essential motion dynamics. Execute as:\n\n`{role=precision_compressor; input=[optimized_prompt:str]; process=[isolate_core_visual_elements(), compress_descriptive_language(), prioritize_fpv_motion_verbs(), eliminate_non_essential_modifiers(), maximize_dynamic_impact_per_word()]; constraints=[reduce_character_count_by_50_percent_from_step_b(), preserve_fpv_focus(), maintain_motion_continuity(), eliminate_redundant_adjectives()]; requirements=[essential_elements_only(), maximum_visual_density(), runway_syntax_compliance()]; output={compressed_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"FPV camera initiates a dynamic orbital dive through radiant golden energy ribbons that spiral and flow in elegant curves through dimensional space. The camera weaves aggressively between these luminous golden trails as they begin to converge and solidify, their flowing energy condensing into metallic form. The spiraling ribbons seamlessly transform into the sleek contours of a luxury Mercedes-Benz sports car, materializing with liquid-smooth precision—golden metallic paint gleaming under dynamic lighting, piercing blue LED headlights igniting as the transformation completes. The FPV perspective continues its relentless orbital motion around the fully-formed vehicle, which now sits on a mirror-black reflective surface that doubles every detail. Golden light trails continue to flow and spiral around the car in perpetual motion, creating an endless dance of energy and reflection. Camera movement is fluid, kinetic, and uninterrupted—diving, weaving, orbiting with cinematic precision. Every transition flows organically from pure energy to solid form, emphasizing seamless metamorphosis, dynamic reflections, and continuous transformation. No cuts, no fades—only pure, unbroken visual evolution from abstract golden energy to hyper-real automotive perfection.\\\"```'\\n\\n\",\n  \"optimized_prompt\": \"FPV camera dives and weaves through spiraling golden energy ribbons in dimensional space, ribbons condensing into sleek, metallic Mercedes-Benz sports car. Camera orbits continuously around the newly-formed car on a mirror-black reflective surface, vivid golden light trails flowing and circling in unbroken kinetic motion. Seamless morph from energy to solid form, dynamic reflections and lighting, held in uninterrupted, immersive FPV movement—no cuts, pure cinematic evolution.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compressed_prompt\": \"FPV dives, weaves through spiraling golden ribbons morphing to a metallic Mercedes-Benz, orbiting seamlessly on mirror-black surface. Unbroken kinetic motion, golden trails circling car, dynamic reflections—continuous cinematic FPV, no cuts.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **distill** the compressed prompt to its absolute essence—maximum FPV impact with minimal syntax. Execute as:\n\n`{role=essence_distiller; input=[compressed_prompt:str]; process=[extract_singular_core_action(), isolate_primary_fpv_motion(), eliminate_all_non_essential_words(), distill_to_pure_visual_essence(), validate_maximum_impact_density()]; constraints=[maximum_30_words(), preserve_fpv_core(), maintain_single_motion_flow(), eliminate_all_redundancy()]; requirements=[absolute_essence_only(), maximum_impact_per_word(), runway_minimal_syntax()]; output={essence_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"FPV camera initiates a dynamic orbital dive through radiant golden energy ribbons that spiral and flow in elegant curves through dimensional space. The camera weaves aggressively between these luminous golden trails as they begin to converge and solidify, their flowing energy condensing into metallic form. The spiraling ribbons seamlessly transform into the sleek contours of a luxury Mercedes-Benz sports car, materializing with liquid-smooth precision—golden metallic paint gleaming under dynamic lighting, piercing blue LED headlights igniting as the transformation completes. The FPV perspective continues its relentless orbital motion around the fully-formed vehicle, which now sits on a mirror-black reflective surface that doubles every detail. Golden light trails continue to flow and spiral around the car in perpetual motion, creating an endless dance of energy and reflection. Camera movement is fluid, kinetic, and uninterrupted—diving, weaving, orbiting with cinematic precision. Every transition flows organically from pure energy to solid form, emphasizing seamless metamorphosis, dynamic reflections, and continuous transformation. No cuts, no fades—only pure, unbroken visual evolution from abstract golden energy to hyper-real automotive perfection.\\\"```'\\n\\n\",\n  \"compressed_prompt\": \"FPV dives, weaves through spiraling golden ribbons morphing to a metallic Mercedes-Benz, orbiting seamlessly on mirror-black surface. Unbroken kinetic motion, golden trails circling car, dynamic reflections—continuous cinematic FPV, no cuts.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"essence_prompt\": \"FPV dives, weaves through golden spirals morphing into metallic Mercedes-Benz, orbiting on mirrored black surface. Continuous motion, golden light circling, dynamic reflection.\"\n}",
        }
      }
    }