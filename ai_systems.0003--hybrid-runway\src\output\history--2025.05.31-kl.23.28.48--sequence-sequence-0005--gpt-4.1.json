  {
  "initial_prompt": "FPV camera initiates a dynamic orbital dive through radiant golden energy ribbons that spiral and flow in elegant curves through dimensional space. The camera weaves aggressively between these luminous golden trails as they begin to converge and solidify, their flowing energy condensing into metallic form. The spiraling ribbons seamlessly transform into the sleek contours of a luxury Mercedes-Benz sports car, materializing with liquid-smooth precision—golden metallic paint gleaming under dynamic lighting, piercing blue LED headlights igniting as the transformation completes. The FPV perspective continues its relentless orbital motion around the fully-formed vehicle, which now sits on a mirror-black reflective surface that doubles every detail. Golden light trails continue to flow and spiral around the car in perpetual motion, creating an endless dance of energy and reflection. Camera movement is fluid, kinetic, and uninterrupted—diving, weaving, orbiting with cinematic precision. Every transition flows organically from pure energy to solid form, emphasizing seamless metamorphosis, dynamic reflections, and continuous transformation. No cuts, no fades—only pure, unbroken visual evolution from abstract golden energy to hyper-real automotive perfection.",
  "sequence_id": "0005",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML Gen-3 video generation prompt following the optimal structure: [Camera Movement]: [Scene]. [Details]. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), structure_camera_movement_using_runway_terminology(fpv, arc_shot, crane_shot, orbital_dive), define_scene_with_subject_and_environment(), integrate_lighting_specifications(cinematic_lighting, dynamic_lighting, lens_flare, backlit), incorporate_runway_shot_terms(low_angle, high_angle, birds_eye_view), ensure_seamless_transformation_language(), validate_runway_gen3_syntax_compliance(), optimize_for_500_character_limit()]; constraints=[follow_runway_camera_movement_colon_scene_format(), use_supported_runway_shot_terms(), prioritize_continuous_motion_descriptors(), maintain_transformation_focus(), output_single_structured_prompt()]; requirements=[achieve_maximum_runway_compatibility(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_gen3_prompt()]; output={structured_runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "FPV camera initiates a dynamic orbital dive through radiant golden energy ribbons that spiral and flow in elegant curves through dimensional space. The camera weaves aggressively between these luminous golden trails as they begin to converge and solidify, their flowing energy condensing into metallic form. The spiraling ribbons seamlessly transform into the sleek contours of a luxury Mercedes-Benz sports car, materializing with liquid-smooth precision—golden metallic paint gleaming under dynamic lighting, piercing blue LED headlights igniting as the transformation completes. The FPV perspective continues its relentless orbital motion around the fully-formed vehicle, which now sits on a mirror-black reflective surface that doubles every detail. Golden light trails continue to flow and spiral around the car in perpetual motion, creating an endless dance of energy and reflection. Camera movement is fluid, kinetic, and uninterrupted—diving, weaving, orbiting with cinematic precision. Every transition flows organically from pure energy to solid form, emphasizing seamless metamorphosis, dynamic reflections, and continuous transformation. No cuts, no fades—only pure, unbroken visual evolution from abstract golden energy to hyper-real automotive perfection.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"structured_runwayml_prompt\": \"FPV orbital dive: Luminous golden energy ribbons spiral and flow through dimensional space, aggressively weaving and merging as they condense into a metallic luxury sports car. Seamless transformation—ribbons become a sleek golden Mercedes-Benz, dynamic lighting glinting off its contours, piercing blue LED headlights ignite. The camera orbits fluidly around the car on a mirror-black surface, golden trails circling in perpetual motion, smooth visual evolution, no cuts.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **optimize** it into an efficient RunwayML Gen-3 prompt maintaining the [Camera Movement]: [Scene]. [Details] structure while emphasizing supported shot terms and lighting specifications. Execute as:\n\n`{role=focused_runway_optimizer; input=[structured_runwayml_prompt:str]; process=[preserve_camera_movement_colon_scene_structure(), extract_primary_visual_elements(), prioritize_runway_supported_shot_terms(fpv, arc_shot, crane_shot, orbital_dive), maintain_lighting_specifications(cinematic_lighting, dynamic_lighting, lens_flare), select_essential_transformation_descriptors(), optimize_for_runway_gen3_compatibility(), maintain_core_narrative_flow()]; constraints=[maintain_runway_structure_format(), preserve_supported_shot_terminology(), keep_transformation_focus(), optimize_within_400_character_range(), eliminate_non_runway_terms()]; requirements=[preserve_visual_impact(), maintain_runway_gen3_syntax(), ensure_motion_continuity(), keep_lighting_specifications()]; output={optimized_runway_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"FPV camera initiates a dynamic orbital dive through radiant golden energy ribbons that spiral and flow in elegant curves through dimensional space. The camera weaves aggressively between these luminous golden trails as they begin to converge and solidify, their flowing energy condensing into metallic form. The spiraling ribbons seamlessly transform into the sleek contours of a luxury Mercedes-Benz sports car, materializing with liquid-smooth precision—golden metallic paint gleaming under dynamic lighting, piercing blue LED headlights igniting as the transformation completes. The FPV perspective continues its relentless orbital motion around the fully-formed vehicle, which now sits on a mirror-black reflective surface that doubles every detail. Golden light trails continue to flow and spiral around the car in perpetual motion, creating an endless dance of energy and reflection. Camera movement is fluid, kinetic, and uninterrupted—diving, weaving, orbiting with cinematic precision. Every transition flows organically from pure energy to solid form, emphasizing seamless metamorphosis, dynamic reflections, and continuous transformation. No cuts, no fades—only pure, unbroken visual evolution from abstract golden energy to hyper-real automotive perfection.\\\"```'\\n\\n\",\n  \"structured_runwayml_prompt\": \"FPV orbital dive: Luminous golden energy ribbons spiral and flow through dimensional space, aggressively weaving and merging as they condense into a metallic luxury sports car. Seamless transformation—ribbons become a sleek golden Mercedes-Benz, dynamic lighting glinting off its contours, piercing blue LED headlights ignite. The camera orbits fluidly around the car on a mirror-black surface, golden trails circling in perpetual motion, smooth visual evolution, no cuts.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_runway_prompt\": \"FPV orbital dive: Luminous golden energy ribbons spiral and weave through dimensional space, seamlessly transforming into a sleek golden Mercedes-Benz sports car. Dynamic lighting, cinematic reflections on mirror-black surface, piercing blue LED headlights ignite. The camera arcs and orbits fluidly, golden light trails circling, emphasizing continuous visual transformation, no cuts.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **refine** the optimized prompt into maximum visual efficiency while preserving the [Camera Movement]: [Scene]. [Details] structure and essential Runway Gen-3 terminology. Execute as:\n\n`{role=precision_refiner; input=[optimized_runway_prompt:str]; process=[maintain_camera_movement_colon_scene_structure(), isolate_core_visual_elements(), compress_descriptive_language_while_keeping_runway_terms(), prioritize_supported_shot_terminology(fpv, arc_shot, crane_shot), preserve_lighting_specifications(cinematic_lighting, lens_flare), eliminate_non_essential_modifiers(), maximize_transformation_impact_per_word()]; constraints=[maintain_runway_structure_format(), preserve_supported_shot_terms(), keep_essential_lighting_specs(), optimize_within_300_character_range(), eliminate_redundant_adjectives_only()]; requirements=[essential_runway_elements_only(), maximum_visual_density(), runway_gen3_syntax_compliance(), preserve_transformation_focus()]; output={refined_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"FPV camera initiates a dynamic orbital dive through radiant golden energy ribbons that spiral and flow in elegant curves through dimensional space. The camera weaves aggressively between these luminous golden trails as they begin to converge and solidify, their flowing energy condensing into metallic form. The spiraling ribbons seamlessly transform into the sleek contours of a luxury Mercedes-Benz sports car, materializing with liquid-smooth precision—golden metallic paint gleaming under dynamic lighting, piercing blue LED headlights igniting as the transformation completes. The FPV perspective continues its relentless orbital motion around the fully-formed vehicle, which now sits on a mirror-black reflective surface that doubles every detail. Golden light trails continue to flow and spiral around the car in perpetual motion, creating an endless dance of energy and reflection. Camera movement is fluid, kinetic, and uninterrupted—diving, weaving, orbiting with cinematic precision. Every transition flows organically from pure energy to solid form, emphasizing seamless metamorphosis, dynamic reflections, and continuous transformation. No cuts, no fades—only pure, unbroken visual evolution from abstract golden energy to hyper-real automotive perfection.\\\"```'\\n\\n\",\n  \"optimized_runway_prompt\": \"FPV orbital dive: Luminous golden energy ribbons spiral and weave through dimensional space, seamlessly transforming into a sleek golden Mercedes-Benz sports car. Dynamic lighting, cinematic reflections on mirror-black surface, piercing blue LED headlights ignite. The camera arcs and orbits fluidly, golden light trails circling, emphasizing continuous visual transformation, no cuts.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"refined_prompt\": \"FPV arc_shot: Golden energy ribbons spiral, condensing into a sleek Mercedes-Benz sports car—gold metallic paint, cinematic lighting, blue LED headlights. Mirror-black reflective surface, golden light trails orbit; seamless transformation, fluid camera motion, no cuts.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **finalize** the refined prompt into its most effective form—maximum visual impact with optimal Runway Gen-3 compatibility. Execute as:\n\n`{role=final_optimizer; input=[refined_prompt:str]; process=[preserve_camera_movement_colon_scene_structure(), extract_singular_core_transformation_action(), isolate_primary_supported_shot_terms(fpv, arc_shot, crane_shot), maintain_essential_lighting_specifications(cinematic_lighting, lens_flare), eliminate_only_truly_redundant_words(), validate_runway_gen3_compatibility(), ensure_transformation_clarity()]; constraints=[maintain_runway_structure_format(), preserve_supported_terminology(), keep_essential_visual_elements(), optimize_within_250_character_range(), eliminate_redundancy_without_losing_impact()]; requirements=[maximum_runway_compatibility(), optimal_visual_impact(), runway_gen3_syntax_compliance(), clear_transformation_narrative()]; output={final_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"FPV camera initiates a dynamic orbital dive through radiant golden energy ribbons that spiral and flow in elegant curves through dimensional space. The camera weaves aggressively between these luminous golden trails as they begin to converge and solidify, their flowing energy condensing into metallic form. The spiraling ribbons seamlessly transform into the sleek contours of a luxury Mercedes-Benz sports car, materializing with liquid-smooth precision—golden metallic paint gleaming under dynamic lighting, piercing blue LED headlights igniting as the transformation completes. The FPV perspective continues its relentless orbital motion around the fully-formed vehicle, which now sits on a mirror-black reflective surface that doubles every detail. Golden light trails continue to flow and spiral around the car in perpetual motion, creating an endless dance of energy and reflection. Camera movement is fluid, kinetic, and uninterrupted—diving, weaving, orbiting with cinematic precision. Every transition flows organically from pure energy to solid form, emphasizing seamless metamorphosis, dynamic reflections, and continuous transformation. No cuts, no fades—only pure, unbroken visual evolution from abstract golden energy to hyper-real automotive perfection.\\\"```'\\n\\n\",\n  \"refined_prompt\": \"FPV arc_shot: Golden energy ribbons spiral, condensing into a sleek Mercedes-Benz sports car—gold metallic paint, cinematic lighting, blue LED headlights. Mirror-black reflective surface, golden light trails orbit; seamless transformation, fluid camera motion, no cuts.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_runway_prompt\": \"FPV arc_shot: Golden energy ribbons spiral, merging into a Mercedes-Benz sports car. Gold metallic paint shines under cinematic lighting, blue LED headlights ignite. Mirror-black surface reflects, golden light trails orbit; seamless transformation, fluid motion, no cuts.\"\n}",
        }
      }
    }