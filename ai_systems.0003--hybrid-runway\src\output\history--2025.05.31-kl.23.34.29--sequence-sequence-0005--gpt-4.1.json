  {
  "initial_prompt": "FPV camera initiates a dynamic orbital dive through radiant golden energy ribbons that spiral and flow in elegant curves through dimensional space. The camera weaves aggressively between these luminous golden trails as they begin to converge and solidify, their flowing energy condensing into metallic form. The spiraling ribbons seamlessly transform into the sleek contours of a luxury Mercedes-Benz sports car, materializing with liquid-smooth precision—golden metallic paint gleaming under dynamic lighting, piercing blue LED headlights igniting as the transformation completes. The FPV perspective continues its relentless orbital motion around the fully-formed vehicle, which now sits on a mirror-black reflective surface that doubles every detail. Golden light trails continue to flow and spiral around the car in perpetual motion, creating an endless dance of energy and reflection. Camera movement is fluid, kinetic, and uninterrupted—diving, weaving, orbiting with cinematic precision. Every transition flows organically from pure energy to solid form, emphasizing seamless metamorphosis, dynamic reflections, and continuous transformation. No cuts, no fades—only pure, unbroken visual evolution from abstract golden energy to hyper-real automotive perfection.",
  "sequence_id": "0005",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a keyword-optimized RunwayML Gen-3 video generation prompt using bracket notation for maximum AI recognition. Structure: [Camera Movement]: [Scene]. [Details]. Execute as:\n\n`{role=keyword_optimized_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), structure_camera_keywords_in_brackets([FPV], [Arc_shot], [Crane_shot], [Tracking]), define_scene_with_transformation_keywords([Transforms], [Morphs], [Emerges]), integrate_lighting_keywords_in_brackets([Cinematic_lighting], [Lens_flare], [Back_lit]), incorporate_style_keywords([Photorealistic], [Smooth_motion], [Sharp_focus]), ensure_keyword_hierarchy_and_syntax(), validate_runway_gen3_keyword_compliance(), optimize_for_350_character_sweet_spot()]; constraints=[use_bracket_notation_for_all_keywords(), prioritize_supported_runway_terms(), maintain_keyword_hierarchy(), follow_camera_movement_colon_scene_format(), output_keyword_optimized_prompt()]; requirements=[achieve_maximum_ai_keyword_recognition(), ensure_transformation_keyword_clarity(), reflect_source_intent_with_keywords(), produce_bracket_optimized_gen3_prompt()]; output={keyword_optimized_runway_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "FPV camera initiates a dynamic orbital dive through radiant golden energy ribbons that spiral and flow in elegant curves through dimensional space. The camera weaves aggressively between these luminous golden trails as they begin to converge and solidify, their flowing energy condensing into metallic form. The spiraling ribbons seamlessly transform into the sleek contours of a luxury Mercedes-Benz sports car, materializing with liquid-smooth precision—golden metallic paint gleaming under dynamic lighting, piercing blue LED headlights igniting as the transformation completes. The FPV perspective continues its relentless orbital motion around the fully-formed vehicle, which now sits on a mirror-black reflective surface that doubles every detail. Golden light trails continue to flow and spiral around the car in perpetual motion, creating an endless dance of energy and reflection. Camera movement is fluid, kinetic, and uninterrupted—diving, weaving, orbiting with cinematic precision. Every transition flows organically from pure energy to solid form, emphasizing seamless metamorphosis, dynamic reflections, and continuous transformation. No cuts, no fades—only pure, unbroken visual evolution from abstract golden energy to hyper-real automotive perfection.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"keyword_optimized_runway_prompt\": \"[FPV][Arc_shot][Tracking_shot][Orbit]: [Radiant_golden_energy_ribbons][Dimensional_space][Dynamic_orbital_dive]. [Camera_weaves_between_luminous_trails][Energy_ribbons_transform][Morphing_into_luxury_Mercedes_Benz][Golden_metallic_paint][Piercing_blue_LED_headlights][Seamless_energy_to_car_transformation][Liquid_smooth_precision][Sleek_contours][Mirror_black_reflective_surface][Golden_light_trails_spiraling][Endless_dance][Continuous_metamorphosis][No_cuts][No_fades]. [Cinematic_lighting][Dynamic_reflections][Sharp_focus][Photorealistic][Smooth_motion][Unbroken_visual_evolution].\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **optimize** it into an efficient RunwayML Gen-3 prompt maintaining the [Camera Movement]: [Scene]. [Details] structure while emphasizing supported shot terms and lighting specifications. Execute as:\n\n`{role=focused_runway_optimizer; input=[structured_runwayml_prompt:str]; process=[preserve_camera_movement_colon_scene_structure(), extract_primary_visual_elements(), prioritize_runway_supported_shot_terms(fpv, arc_shot, crane_shot, orbital_dive), maintain_lighting_specifications(cinematic_lighting, dynamic_lighting, lens_flare), select_essential_transformation_descriptors(), optimize_for_runway_gen3_compatibility(), maintain_core_narrative_flow()]; constraints=[maintain_runway_structure_format(), preserve_supported_shot_terminology(), keep_transformation_focus(), optimize_within_400_character_range(), eliminate_non_runway_terms()]; requirements=[preserve_visual_impact(), maintain_runway_gen3_syntax(), ensure_motion_continuity(), keep_lighting_specifications()]; output={optimized_runway_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"FPV camera initiates a dynamic orbital dive through radiant golden energy ribbons that spiral and flow in elegant curves through dimensional space. The camera weaves aggressively between these luminous golden trails as they begin to converge and solidify, their flowing energy condensing into metallic form. The spiraling ribbons seamlessly transform into the sleek contours of a luxury Mercedes-Benz sports car, materializing with liquid-smooth precision—golden metallic paint gleaming under dynamic lighting, piercing blue LED headlights igniting as the transformation completes. The FPV perspective continues its relentless orbital motion around the fully-formed vehicle, which now sits on a mirror-black reflective surface that doubles every detail. Golden light trails continue to flow and spiral around the car in perpetual motion, creating an endless dance of energy and reflection. Camera movement is fluid, kinetic, and uninterrupted—diving, weaving, orbiting with cinematic precision. Every transition flows organically from pure energy to solid form, emphasizing seamless metamorphosis, dynamic reflections, and continuous transformation. No cuts, no fades—only pure, unbroken visual evolution from abstract golden energy to hyper-real automotive perfection.\\\"```'\\n\\n\",\n  \"keyword_optimized_runway_prompt\": \"[FPV][Arc_shot][Tracking_shot][Orbit]: [Radiant_golden_energy_ribbons][Dimensional_space][Dynamic_orbital_dive]. [Camera_weaves_between_luminous_trails][Energy_ribbons_transform][Morphing_into_luxury_Mercedes_Benz][Golden_metallic_paint][Piercing_blue_LED_headlights][Seamless_energy_to_car_transformation][Liquid_smooth_precision][Sleek_contours][Mirror_black_reflective_surface][Golden_light_trails_spiraling][Endless_dance][Continuous_metamorphosis][No_cuts][No_fades]. [Cinematic_lighting][Dynamic_reflections][Sharp_focus][Photorealistic][Smooth_motion][Unbroken_visual_evolution].\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_runway_prompt\": \"[FPV orbital_dive]: Radiant golden energy ribbons spiral through dimensional space. Camera weaves aggressively, energy condenses into metallic form, morphing into sleek luxury Mercedes-Benz sports car—golden metallic paint, blue LED headlights ignite. FPV orbit around car on mirror-black reflective surface, golden light trails swirl. Cinematic lighting, dynamic reflections, smooth, uninterrupted transformation.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **refine** the optimized prompt into maximum visual efficiency while preserving the [Camera Movement]: [Scene]. [Details] structure and essential Runway Gen-3 terminology. Execute as:\n\n`{role=precision_refiner; input=[optimized_runway_prompt:str]; process=[maintain_camera_movement_colon_scene_structure(), isolate_core_visual_elements(), compress_descriptive_language_while_keeping_runway_terms(), prioritize_supported_shot_terminology(fpv, arc_shot, crane_shot), preserve_lighting_specifications(cinematic_lighting, lens_flare), eliminate_non_essential_modifiers(), maximize_transformation_impact_per_word()]; constraints=[maintain_runway_structure_format(), preserve_supported_shot_terms(), keep_essential_lighting_specs(), optimize_within_300_character_range(), eliminate_redundant_adjectives_only()]; requirements=[essential_runway_elements_only(), maximum_visual_density(), runway_gen3_syntax_compliance(), preserve_transformation_focus()]; output={refined_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"FPV camera initiates a dynamic orbital dive through radiant golden energy ribbons that spiral and flow in elegant curves through dimensional space. The camera weaves aggressively between these luminous golden trails as they begin to converge and solidify, their flowing energy condensing into metallic form. The spiraling ribbons seamlessly transform into the sleek contours of a luxury Mercedes-Benz sports car, materializing with liquid-smooth precision—golden metallic paint gleaming under dynamic lighting, piercing blue LED headlights igniting as the transformation completes. The FPV perspective continues its relentless orbital motion around the fully-formed vehicle, which now sits on a mirror-black reflective surface that doubles every detail. Golden light trails continue to flow and spiral around the car in perpetual motion, creating an endless dance of energy and reflection. Camera movement is fluid, kinetic, and uninterrupted—diving, weaving, orbiting with cinematic precision. Every transition flows organically from pure energy to solid form, emphasizing seamless metamorphosis, dynamic reflections, and continuous transformation. No cuts, no fades—only pure, unbroken visual evolution from abstract golden energy to hyper-real automotive perfection.\\\"```'\\n\\n\",\n  \"optimized_runway_prompt\": \"[FPV orbital_dive]: Radiant golden energy ribbons spiral through dimensional space. Camera weaves aggressively, energy condenses into metallic form, morphing into sleek luxury Mercedes-Benz sports car—golden metallic paint, blue LED headlights ignite. FPV orbit around car on mirror-black reflective surface, golden light trails swirl. Cinematic lighting, dynamic reflections, smooth, uninterrupted transformation.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"refined_prompt\": \"[FPV orbital_dive]: Golden energy ribbons spiral in dimensional space, condense into metallic form, morphing into Mercedes-Benz sports car—gold paint, blue LED headlights. FPV orbits car on mirror-black surface, swirling golden trails. Cinematic lighting, dynamic reflections, seamless transformation.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **finalize** the prompt into maximum keyword density with optimal AI recognition—targeting 280-320 characters for best results. Execute as:\n\n`{role=keyword_density_optimizer; input=[refined_prompt:str]; process=[preserve_camera_movement_colon_scene_structure(), maximize_bracket_keyword_density([FPV], [Arc_shot], [Transforms], [Cinematic_lighting]), extract_core_transformation_keywords([Morphs], [Emerges], [Materializes]), maintain_essential_visual_keywords([Golden], [Mercedes-Benz], [Mirror_surface], [LED_headlights]), eliminate_filler_words_only(), validate_keyword_hierarchy_and_syntax(), ensure_optimal_character_count_280_320()]; constraints=[maintain_bracket_notation_for_keywords(), preserve_runway_structure_format(), keep_transformation_keyword_clarity(), optimize_within_280_320_character_range(), maximize_keyword_to_filler_ratio()]; requirements=[maximum_ai_keyword_recognition(), optimal_character_density(), runway_gen3_keyword_compliance(), clear_visual_transformation_keywords()]; output={keyword_dense_final_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"FPV camera initiates a dynamic orbital dive through radiant golden energy ribbons that spiral and flow in elegant curves through dimensional space. The camera weaves aggressively between these luminous golden trails as they begin to converge and solidify, their flowing energy condensing into metallic form. The spiraling ribbons seamlessly transform into the sleek contours of a luxury Mercedes-Benz sports car, materializing with liquid-smooth precision—golden metallic paint gleaming under dynamic lighting, piercing blue LED headlights igniting as the transformation completes. The FPV perspective continues its relentless orbital motion around the fully-formed vehicle, which now sits on a mirror-black reflective surface that doubles every detail. Golden light trails continue to flow and spiral around the car in perpetual motion, creating an endless dance of energy and reflection. Camera movement is fluid, kinetic, and uninterrupted—diving, weaving, orbiting with cinematic precision. Every transition flows organically from pure energy to solid form, emphasizing seamless metamorphosis, dynamic reflections, and continuous transformation. No cuts, no fades—only pure, unbroken visual evolution from abstract golden energy to hyper-real automotive perfection.\\\"```'\\n\\n\",\n  \"refined_prompt\": \"[FPV orbital_dive]: Golden energy ribbons spiral in dimensional space, condense into metallic form, morphing into Mercedes-Benz sports car—gold paint, blue LED headlights. FPV orbits car on mirror-black surface, swirling golden trails. Cinematic lighting, dynamic reflections, seamless transformation.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"keyword_dense_final_prompt\": \"[FPV_orbital_dive]: Golden energy ribbons spiral, condense, [Morph] into [Mercedes-Benz] sports car, [Golden] paint, [LED_headlights] ignite. [FPV] [Arc_shot] encircles car on [Mirror_surface], swirling golden trails persist. [Cinematic_lighting], dynamic reflections, seamless [Transforms], [Emerges], unbroken evolution.\"\n}",
        }
      }
    }