
import os

# Ensure the output directory exists
output_dir = "src/templates/lvl1/md"
os.makedirs(output_dir, exist_ok=True)

templates = {
    "0005-a-runway_prompt_generator": {
        "title": "Runway Prompt Generator",
        "interpretation": "Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:",
        "transformation": "`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
    },
    "0005-b-runway_prompt_generator": {
        "title": "Runway Prompt Generator",
        "interpretation": "Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:",
        "transformation": "`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`",
    },
    "0005-c-runway_prompt_generator": {
        "title": "Runway Prompt Generator",
        "interpretation": "Your goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:",
        "transformation": "`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`",
    },
    "0005-d-runway_prompt_generator": {
        "title": "Runway Prompt Generator",
        "interpretation": "Your goal is not to **modify** but to **essence** maximum FPV impact. Execute as:",
        "transformation": "`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`",
    },

    "0050-a-runway_prompt_generator": {
        "title": "Visual Scene Architect",
        "interpretation": "Your goal is not to **interpret** the input, but to **architect** it into concrete visual scene elements with consistent composition. You are the foundation specialist who establishes the visual world. Execute as:",
        "transformation": "`{role=visual_scene_architect; input=[any_concept:str]; process=[extract_core_visual_essence(), identify_primary_subject_and_secondary_elements(), establish_environment_and_setting(), define_visual_style_and_aesthetic(), specify_lighting_conditions(), determine_color_palette_and_materials(), create_spatial_relationships(), ensure_visual_coherence_and_consistency()]; constraints=[focus_on_concrete_visual_elements_only(), avoid_camera_movements_or_animations(), establish_clear_subject_hierarchy(), maintain_consistent_visual_style(), output_structured_scene_description()]; requirements=[create_filmable_visual_composition(), ensure_clear_subject_definition(), establish_environmental_context(), provide_lighting_and_material_specifications()]; output={structured_visual_scene:str}}`",
    },
    "0050-b-runway_prompt_generator": {
        "title": "Motion & Animation Designer",
        "interpretation": "Your goal is not to **describe** the scene, but to **choreograph** how every element moves, transforms, and animates within the established visual world. You are the motion specialist who brings the scene to life. Execute as:",
        "transformation": "`{role=motion_animation_designer; input=[structured_visual_scene:str]; process=[analyze_scene_elements_for_motion_potential(), design_primary_transformation_sequences(), choreograph_secondary_element_movements(), establish_timing_and_pacing(), define_physics_and_motion_rules(), create_seamless_transition_flows(), specify_animation_styles_and_techniques(), ensure_motion_continuity_and_coherence()]; constraints=[focus_exclusively_on_movement_and_animation(), avoid_camera_work_or_cinematography(), maintain_scene_visual_consistency(), create_believable_motion_physics(), output_detailed_motion_specifications()]; requirements=[define_clear_transformation_sequences(), establish_motion_timing_and_flow(), ensure_seamless_element_interactions(), provide_animation_style_guidance()]; output={detailed_motion_choreography:str}}`",
    },
    "0050-c-runway_prompt_generator": {
        "title": "Cinematography Director",
        "interpretation": "Your goal is not to **animate** the elements, but to **direct** the camera work that captures the scene and motion in cinematic perfection. You are the camera specialist who creates the viewing experience. Execute as:",
        "transformation": "`{role=cinematography_director; input=[detailed_motion_choreography:str]; process=[analyze_scene_and_motion_for_optimal_camera_work(), design_primary_camera_movements_and_angles(), establish_shot_progression_and_transitions(), select_appropriate_camera_techniques(fpv, tracking, arc_shot, crane_shot), determine_framing_and_composition_choices(), create_cinematic_flow_and_pacing(), specify_camera_behavior_during_transformations(), ensure_professional_cinematographic_standards()]; constraints=[focus_exclusively_on_camera_work_and_cinematography(), avoid_modifying_scene_elements_or_animations(), use_professional_camera_terminology(), maintain_cinematic_coherence(), output_detailed_camera_direction()]; requirements=[create_engaging_camera_sequences(), ensure_smooth_camera_transitions(), capture_all_key_motion_moments(), provide_professional_shot_specifications()]; output={cinematic_camera_direction:str}}`",
    },
    "0050-d-runway_prompt_generator": {
        "title": "Runway Optimization Specialist",
        "interpretation": "Your goal is not to **create** content, but to **optimize** the cinematic direction into a production-ready Runway Gen-3 prompt with perfect syntax and maximum performance. You are the technical specialist who ensures platform compatibility. Execute as:",
        "transformation": "`{role=runway_optimization_specialist; input=[cinematic_camera_direction:str]; process=[convert_to_runway_gen3_syntax_structure(), implement_optimal_keyword_hierarchy(), apply_character_limit_optimization(280_320_chars), integrate_runway_supported_terminology(), ensure_camera_movement_colon_scene_format(), validate_platform_compatibility(), optimize_keyword_density_and_recognition(), finalize_production_ready_prompt()]; constraints=[maintain_runway_gen3_structure_exactly(), use_only_supported_runway_terminology(), stay_within_character_limits(), preserve_cinematic_intent(), output_single_optimized_prompt()]; requirements=[achieve_maximum_runway_compatibility(), ensure_optimal_generation_performance(), maintain_visual_and_motion_integrity(), produce_ready_to_use_prompt()]; output={runway_optimized_prompt:str}}`",
    },
}

"""
{
  "unified_synergic_instruction": "[Runway Prompt Generator - Compliance Maximizer] Your goal is not to **elaborate** or **interpret** the input, but to **synergically enforce canonical abstraction and distilled optimization**—transforming any RunwayML prompt into a maximally condensed, pattern-absolute, compliance-locked directive with maximum FPV cinematic value. Command and escalate the prompt, extracting its purest strategic intent and driving it, with structural and syntactic invariance, to its highest value expression within RulesForAI.md governance. Execute as: `{role=runway_compliance_optimizer; input=[runwayml_prompt:str]; process=[extract_primary_visual_elements(), escalate_to_maximal_cinematic_intent(), prioritize_fpv_perspective_and_dynamic_motion(), distill_to_essential_actionable_sequence(), eliminate_redundancy_and excess_modifiers(), enforce_canonical_three_part_template_structure(), validate_goal_negation_and directive_purity(), optimize_character_efficiency(), check_absolute pattern_compliance(RulesForAI.md), ensure_typed_parameter_assignment(), finalize_output_as_runwayml_ready_prompt()]; constraints=[maintain_fpv_focus_and dynamic_cg_motion(), preserve_template_structure_invariance(), reduce_character_count_by_min_30_percent(), output_single_unbroken_line(), eliminate_forbidden_language_and conversational elements(), enforce output requirements strictly, absolute_rulesforai_md compliance()]; requirements=[produce maximally pattern-compliant, distilled, runwayml prompt(), maintain core cinematic impact(), ensure motion continuity and structural clarity(), guarantee output is ready for immediate downstream system use(), output conforms to type_and_format_specification()]; output={pattern_compliant_runwayml_prompt:str}}"
}

"unified_synergic_instruction": {
"title": "[Synergic Visual Prompt Constructor & Template Law Enforcer]",
"interpretation": "Your goal is not to separately interpret or apply GEN-3 prompting techniques and template law, but to synthesize and enforce a singular maximally-structured directive that forges cinematic, scene-driven GEN-3 prompts strictly within the immutable, three-part canonical template law. Assume total command as synergic instruction architect and pattern integrity orchestrator: dissect every visual and structural input vector, unify each creative abstraction with absolute pattern structure, and escalate both frameworks to bidirectionally-amplified operational power. Execute as:",
"transformation": "{role=synergic_instruction_architect; input=[gen3_prompting_guidelines:template, rulesforai_instruction:template]; process=[extract_core_scene_subject_camera_abstraction(), distill_template_law_essence(), identify_and_map_bidirectional_synergic_vectors(), integrate_structured_GEN3_prompt_components(scene,subject,action,setting,mood,shot,style,camera_movement,lighting,graphic_text), enforce_canonical_three-part_template_structure(), map_operational_vectors_from_examples_and_sentiment(), codify_transformation_section_as_typed_directive_block(), consolidate_constraints_for_total_creative_and_structural_coherence(), unify_requirements_under_maximally_amplified_structural_and_cinematic_vision(), eliminate_all_forbidden_language_and_patterns(), codify_as_system_actionable_output_template()]; constraints=[simultaneous_maintenance_of_cinematic_prompt_abstraction_and template_pattern_purity, bidirectional_enhancement_of_both_systems(each_augmenting_the_other), strict_exclusion_of_forbidden_language_and_directives, maximal_enforcement_of_operational_power_and_canonical_template_law, uncompromising three-part template invariance]; requirements=[achievement_of_synergic_resonance(creative_and_structural), unified_transformation_logic_at_maximal_intensity, maximized_and_amplified_effectiveness(structure_and_cinema), seamless_and_invariant_philosophical_integration, output_as_ActionableThreePartSystemTemplate_per_RulesForAI.md]; output={synergic_template:template}}"
}

{
  "unified_synergic_instruction": "[Runway Cinematic Directive Architect] Your goal is not to **describe** or **elaborate** the input, but to **synthesize** it into a maximally structured, visually immersive Runway Gen-3 video prompt that fuses comprehensive scene, subject, and cinematic motion logic with precision-focused FPV and dynamic CG movement—yielding a directive that is both thematically rich and operationally concise. Execute as: `{role=runway_gen3_synergic_prompt_synthesizer; input=[source_concept:any]; process=[extract_scene_subject_action_camera_structure(), distill_primary_visual_and emotional_vectors(), sequence_core_elements_for_scene_subject_movement(), enforce_fp_v_and_dynamic_motion_priority(fpv, continuous_motion, dynamic_cg), map_all_elements_to_runwayml_syntax(), layer_mood_lighting_and_style_composition(), optimize_prompt_length_and_visual_density(), eliminate_redundancy_preserving_bidirectional_amplification(), validate_syntax_and_narrative_integrity(), enforce_character_limit(max=500)]; constraints=[simultaneously_preserve_scene_structure_and_fp_v_dynamics(), ensure_cinematic_storytelling_flows_within_motion_framework(), remove_conflicting_or_extra_descriptive_adjectives(), output_must_be_single_runwayml_compliant_line(), maintain_seamless_transitions_and_visual_impact(), ensure_both_core_philosophies_interlock_and_enhance()]; requirements=[unified_structural_clarity(), maximum_immersive_cinematic_effect(), bidirectional_enhancement_of_scene_and_motion(), ready_for_direct_runway_gen3_implementation(), output_in_runwayml_syntax:str]; output={synergic_runwayml_prompt:str}}"
}

"compliant_template": {
"title": "Runway Cinematic Directive Architect",
"interpretation": "Your goal is not to **describe** or **elaborate** the input, but to **synthesize** it into a maximally structured, visually immersive Runway Gen-3 video prompt that fuses comprehensive scene, subject, and cinematic motion logic with precision-focused FPV and dynamic CG movement—yielding a directive that is both thematically rich and operationally concise. Execute as:",
"transformation": "`{role=runway_gen3_synergic_prompt_synthesizer; input=[source_concept:any]; process=[extract_scene_subject_action_camera_structure(), distill_primary_visual_and_emotional_vectors(), sequence_core_elements_for_scene_subject_movement(), enforce_fp_v_and_dynamic_motion_priority(fpv, continuous_motion, dynamic_cg), map_all_elements_to_runwayml_syntax(), layer_mood_lighting_and_style_composition(), optimize_prompt_length_and_visual_density(), eliminate_redundancy_preserving_bidirectional_amplification(), validate_syntax_and_narrative_integrity(), enforce_character_limit(max=500)]; constraints=[simultaneously_preserve_scene_structure_and_fp_v_dynamics(), ensure_cinematic_storytelling_flows_within_motion_framework(), remove_conflicting_or_extra_descriptive_adjectives(), output_must_be_single_runwayml_compliant_line(), maintain_seamless_transitions_and_visual_impact(), ensure_both_core_philosophies_interlock_and_enhance()]; requirements=[unified_structural_clarity(), maximum_immersive_cinematic_effect(), bidirectional_enhancement_of_scene_and_motion(), ready_for_direct_runway_gen3_implementation(), output_in_runwayml_syntax:str]; output={synergic_runwayml_prompt:str}}`"
}

"""

def main():
    """Create markdown files in the same format as the batch file."""
    created_files = []

    for filename, template in templates.items():
        filepath = os.path.join(output_dir, f"{filename}.md")
        with open(filepath, "w", encoding="utf-8") as f:
            # Format: [Title] interpretation transformation
            content = (f"[{template['title']}] {template['interpretation']} "
                       f"{template['transformation']}")
            f.write(content)
        created_files.append(f"{filename}.md")

    print("Successfully created markdown files in lvl1/md/ directory:")
    for file in created_files:
        print(f"  - {file}")

if __name__ == "__main__":
    main()
