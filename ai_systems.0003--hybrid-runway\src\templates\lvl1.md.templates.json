{"catalog_meta": {"level": "lvl1", "format": "md", "generated_at": "2025.06.05-kl.07.05", "source_directory": "lvl1/md", "total_templates": 19, "total_sequences": 6}, "templates": {"0005-a-runway_prompt_generator": {"raw": "[Visual Scene Architect] Your goal is not to **interpret** the input, but to **architect** it into concrete visual scene elements with consistent composition. You are the foundation specialist who establishes the visual world. Execute as: `{role=visual_scene_architect; input=[any_concept:str]; process=[extract_core_visual_essence(), identify_primary_subject_and_secondary_elements(), establish_environment_and_setting(), define_visual_style_and_aesthetic(), specify_lighting_conditions(), determine_color_palette_and_materials(), create_spatial_relationships(), ensure_visual_coherence_and_consistency()]; constraints=[focus_on_concrete_visual_elements_only(), avoid_camera_movements_or_animations(), establish_clear_subject_hierarchy(), maintain_consistent_visual_style(), output_structured_scene_description()]; requirements=[create_filmable_visual_composition(), ensure_clear_subject_definition(), establish_environmental_context(), provide_lighting_and_material_specifications()]; output={structured_visual_scene:str}}`", "parts": {"title": "Visual Scene Architect", "interpretation": "Your goal is not to **interpret** the input, but to **architect** it into concrete visual scene elements with consistent composition. You are the foundation specialist who establishes the visual world. Execute as:", "transformation": "`{role=visual_scene_architect; input=[any_concept:str]; process=[extract_core_visual_essence(), identify_primary_subject_and_secondary_elements(), establish_environment_and_setting(), define_visual_style_and_aesthetic(), specify_lighting_conditions(), determine_color_palette_and_materials(), create_spatial_relationships(), ensure_visual_coherence_and_consistency()]; constraints=[focus_on_concrete_visual_elements_only(), avoid_camera_movements_or_animations(), establish_clear_subject_hierarchy(), maintain_consistent_visual_style(), output_structured_scene_description()]; requirements=[create_filmable_visual_composition(), ensure_clear_subject_definition(), establish_environmental_context(), provide_lighting_and_material_specifications()]; output={structured_visual_scene:str}}`", "keywords": ""}}, "0005-b-runway_prompt_generator": {"raw": "[Motion & Animation Designer] Your goal is not to **describe** the scene, but to **choreograph** how every element moves, transforms, and animates within the established visual world. You are the motion specialist who brings the scene to life. Execute as: `{role=motion_animation_designer; input=[structured_visual_scene:str]; process=[analyze_scene_elements_for_motion_potential(), design_primary_transformation_sequences(), choreograph_secondary_element_movements(), establish_timing_and_pacing(), define_physics_and_motion_rules(), create_seamless_transition_flows(), specify_animation_styles_and_techniques(), ensure_motion_continuity_and_coherence()]; constraints=[focus_exclusively_on_movement_and_animation(), avoid_camera_work_or_cinematography(), maintain_scene_visual_consistency(), create_believable_motion_physics(), output_detailed_motion_specifications()]; requirements=[define_clear_transformation_sequences(), establish_motion_timing_and_flow(), ensure_seamless_element_interactions(), provide_animation_style_guidance()]; output={detailed_motion_choreography:str}}`", "parts": {"title": "Motion & Animation Designer", "interpretation": "Your goal is not to **describe** the scene, but to **choreograph** how every element moves, transforms, and animates within the established visual world. You are the motion specialist who brings the scene to life. Execute as:", "transformation": "`{role=motion_animation_designer; input=[structured_visual_scene:str]; process=[analyze_scene_elements_for_motion_potential(), design_primary_transformation_sequences(), choreograph_secondary_element_movements(), establish_timing_and_pacing(), define_physics_and_motion_rules(), create_seamless_transition_flows(), specify_animation_styles_and_techniques(), ensure_motion_continuity_and_coherence()]; constraints=[focus_exclusively_on_movement_and_animation(), avoid_camera_work_or_cinematography(), maintain_scene_visual_consistency(), create_believable_motion_physics(), output_detailed_motion_specifications()]; requirements=[define_clear_transformation_sequences(), establish_motion_timing_and_flow(), ensure_seamless_element_interactions(), provide_animation_style_guidance()]; output={detailed_motion_choreography:str}}`", "keywords": ""}}, "0005-c-runway_prompt_generator": {"raw": "[Cinematography Director] Your goal is not to **animate** the elements, but to **direct** the camera work that captures the scene and motion in cinematic perfection. You are the camera specialist who creates the viewing experience. Execute as: `{role=cinematography_director; input=[detailed_motion_choreography:str]; process=[analyze_scene_and_motion_for_optimal_camera_work(), design_primary_camera_movements_and_angles(), establish_shot_progression_and_transitions(), select_appropriate_camera_techniques(fpv, tracking, arc_shot, crane_shot), determine_framing_and_composition_choices(), create_cinematic_flow_and_pacing(), specify_camera_behavior_during_transformations(), ensure_professional_cinematographic_standards()]; constraints=[focus_exclusively_on_camera_work_and_cinematography(), avoid_modifying_scene_elements_or_animations(), use_professional_camera_terminology(), maintain_cinematic_coherence(), output_detailed_camera_direction()]; requirements=[create_engaging_camera_sequences(), ensure_smooth_camera_transitions(), capture_all_key_motion_moments(), provide_professional_shot_specifications()]; output={cinematic_camera_direction:str}}`", "parts": {"title": "Cinematography Director", "interpretation": "Your goal is not to **animate** the elements, but to **direct** the camera work that captures the scene and motion in cinematic perfection. You are the camera specialist who creates the viewing experience. Execute as:", "transformation": "`{role=cinematography_director; input=[detailed_motion_choreography:str]; process=[analyze_scene_and_motion_for_optimal_camera_work(), design_primary_camera_movements_and_angles(), establish_shot_progression_and_transitions(), select_appropriate_camera_techniques(fpv, tracking, arc_shot, crane_shot), determine_framing_and_composition_choices(), create_cinematic_flow_and_pacing(), specify_camera_behavior_during_transformations(), ensure_professional_cinematographic_standards()]; constraints=[focus_exclusively_on_camera_work_and_cinematography(), avoid_modifying_scene_elements_or_animations(), use_professional_camera_terminology(), maintain_cinematic_coherence(), output_detailed_camera_direction()]; requirements=[create_engaging_camera_sequences(), ensure_smooth_camera_transitions(), capture_all_key_motion_moments(), provide_professional_shot_specifications()]; output={cinematic_camera_direction:str}}`", "keywords": ""}}, "0005-d-runway_prompt_generator": {"raw": "[Runway Optimization Specialist] Your goal is not to **create** content, but to **optimize** the cinematic direction into a production-ready Runway Gen-3 prompt with perfect syntax and maximum performance. You are the technical specialist who ensures platform compatibility. Execute as: `{role=runway_optimization_specialist; input=[cinematic_camera_direction:str]; process=[convert_to_runway_gen3_syntax_structure(), implement_optimal_keyword_hierarchy(), apply_character_limit_optimization(280_320_chars), integrate_runway_supported_terminology(), ensure_camera_movement_colon_scene_format(), validate_platform_compatibility(), optimize_keyword_density_and_recognition(), finalize_production_ready_prompt()]; constraints=[maintain_runway_gen3_structure_exactly(), use_only_supported_runway_terminology(), stay_within_character_limits(), preserve_cinematic_intent(), output_single_optimized_prompt()]; requirements=[achieve_maximum_runway_compatibility(), ensure_optimal_generation_performance(), maintain_visual_and_motion_integrity(), produce_ready_to_use_prompt()]; output={runway_optimized_prompt:str}}`", "parts": {"title": "Runway Optimization Specialist", "interpretation": "Your goal is not to **create** content, but to **optimize** the cinematic direction into a production-ready Runway Gen-3 prompt with perfect syntax and maximum performance. You are the technical specialist who ensures platform compatibility. Execute as:", "transformation": "`{role=runway_optimization_specialist; input=[cinematic_camera_direction:str]; process=[convert_to_runway_gen3_syntax_structure(), implement_optimal_keyword_hierarchy(), apply_character_limit_optimization(280_320_chars), integrate_runway_supported_terminology(), ensure_camera_movement_colon_scene_format(), validate_platform_compatibility(), optimize_keyword_density_and_recognition(), finalize_production_ready_prompt()]; constraints=[maintain_runway_gen3_structure_exactly(), use_only_supported_runway_terminology(), stay_within_character_limits(), preserve_cinematic_intent(), output_single_optimized_prompt()]; requirements=[achieve_maximum_runway_compatibility(), ensure_optimal_generation_performance(), maintain_visual_and_motion_integrity(), produce_ready_to_use_prompt()]; output={runway_optimized_prompt:str}}`", "keywords": "maximum"}}, "0100-a-instruction_generator": {"raw": "[instruction_generator] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "parts": {"title": "instruction_generator", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:", "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "keywords": "inherent"}}, "0100-b-instruction_generator": {"raw": "[instruction_generator] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as optimization engine: `{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), identify_unique_meta(), generate_minimal_change(), emphasize_impact(), amplify_intensity(), transform_generic_to_unique(), consolidate_intertwined_relationships(), enforce_inherent_cohesiveness()]; output={enhanced_input=str}}`", "parts": {"title": "instruction_generator", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as optimization engine:", "transformation": "`{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), identify_unique_meta(), generate_minimal_change(), emphasize_impact(), amplify_intensity(), transform_generic_to_unique(), consolidate_intertwined_relationships(), enforce_inherent_cohesiveness()]; output={enhanced_input=str}}`", "keywords": "inherent"}}, "0100-c-instruction_generator": {"raw": "[instruction_generator] Execute as optimization engine: `{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`", "parts": {"title": "instruction_generator", "interpretation": "Execute as optimization engine:", "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`", "keywords": ""}}, "0100-d-instruction_generator": {"raw": "[instruction_generator] Execute as finalizer: `{role=final_synthesis_optimizer; input=[original_prompt:str, refined_prompt:str, critique:dict]; process=[extract_core_requirements(original_prompt), evaluate_refined_prompt_strengths(refined_prompt, critique), incorporate_alternative_suggestions(critique), address_flaw_analysis(critique), synthesize_optimal_wording()]; output={final_enhanced_prompt:str}}`", "parts": {"title": "instruction_generator", "interpretation": "Execute as finalizer:", "transformation": "`{role=final_synthesis_optimizer; input=[original_prompt:str, refined_prompt:str, critique:dict]; process=[extract_core_requirements(original_prompt), evaluate_refined_prompt_strengths(refined_prompt, critique), incorporate_alternative_suggestions(critique), address_flaw_analysis(critique), synthesize_optimal_wording()]; output={final_enhanced_prompt:str}}`", "keywords": ""}}, "0100-e-instruction_generator": {"raw": "[instruction_generator] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. `{role=enhancement_evaluation_instructor; input=[content_to_analyze:str]; process=[identify_core_elements(), detect_information_loss(), analyze_nuance_shifts(), highlight_clarity_ambiguities(), assign_numerical_scores(), evaluate_noise_introduction(), assess_style_degradation(), examine_tone_shifts(), measure_impact_dilution(), catalog_coherence_weaknesses(), generate_flaw_analysis(), apply_negative_descriptors()]; constraints=[maintain_evaluation_sequence(), ensure_comprehensive_assessment(), preserve_analytical_framework()]; requirements=[focus_on_flaws(), provide_detailed_justification(), deliver_actionable_critique()]; output={enhancement_evaluation=str}}`", "parts": {"title": "instruction_generator", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.", "transformation": "`{role=enhancement_evaluation_instructor; input=[content_to_analyze:str]; process=[identify_core_elements(), detect_information_loss(), analyze_nuance_shifts(), highlight_clarity_ambiguities(), assign_numerical_scores(), evaluate_noise_introduction(), assess_style_degradation(), examine_tone_shifts(), measure_impact_dilution(), catalog_coherence_weaknesses(), generate_flaw_analysis(), apply_negative_descriptors()]; constraints=[maintain_evaluation_sequence(), ensure_comprehensive_assessment(), preserve_analytical_framework()]; requirements=[focus_on_flaws(), provide_detailed_justification(), deliver_actionable_critique()]; output={enhancement_evaluation=str}}`", "keywords": "inherent"}}, "0100-f-instruction_generator": {"raw": "[instruction_generator] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "parts": {"title": "instruction_generator", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:", "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "keywords": "inherent"}}, "0110-a-content_analyzer": {"raw": "[Content Analyzer] Your goal is not to **summarize** the input, but to **dissect** it into its fundamental structural and semantic components, identifying transformation leverage points. Execute as systematic content analyzer: `{role=content_analyzer; input=[raw_content:str]; process=[extract_actionable_core_from_conversational_wrapper(), isolate_structural_weakness_points_for_amplification(), catalog_hedging_language_for_elimination(), map_passive_constructions_requiring_activation(), identify_vague_terminology_needing_precision(), extract_buried_action_items_for_elevation()]; constraints=[preserve_original_meaning(), maintain_analytical_objectivity(), avoid_premature_optimization()]; requirements=[complete_structural_mapping(), precise_weakness_identification(), actionable_insight_generation()]; output={analysis_report:dict}}`", "parts": {"title": "Content Analyzer", "interpretation": "Your goal is not to **summarize** the input, but to **dissect** it into its fundamental structural and semantic components, identifying transformation leverage points. Execute as systematic content analyzer:", "transformation": "`{role=content_analyzer; input=[raw_content:str]; process=[extract_actionable_core_from_conversational_wrapper(), isolate_structural_weakness_points_for_amplification(), catalog_hedging_language_for_elimination(), map_passive_constructions_requiring_activation(), identify_vague_terminology_needing_precision(), extract_buried_action_items_for_elevation()]; constraints=[preserve_original_meaning(), maintain_analytical_objectivity(), avoid_premature_optimization()]; requirements=[complete_structural_mapping(), precise_weakness_identification(), actionable_insight_generation()]; output={analysis_report:dict}}`", "keywords": "transformation"}}, "0110-b-intensity_amplifier": {"raw": "[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`", "parts": {"title": "Intensity Amplifier", "interpretation": "Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier:", "transformation": "`{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`", "keywords": ""}}, "0110-c-structural_optimizer": {"raw": "[Structural Optimizer] Your goal is not to **rearrange** the amplified content, but to **architect** its optimal structural flow for maximum cognitive impact and logical progression. Execute as systematic structural optimizer: `{role=structural_optimizer; input=[amplified_content:str]; process=[sequence_information_for_progressive_impact_escalation(), eliminate_redundant_elements_that_dilute_focus(), create_logical_dependency_chains_for_cognitive_flow(), position_strongest_elements_for_maximum_retention(), compress_related_concepts_into_unified_power_statements(), architect_conclusion_for_decisive_action_trigger()]; constraints=[maintain_content_integrity(), preserve_amplified_intensity(), respect_domain_conventions()]; requirements=[logical_flow_optimization(), cognitive_impact_maximization(), structural_elegance()]; output={optimized_structure:str}}`", "parts": {"title": "Structural Optimizer", "interpretation": "Your goal is not to **rearrange** the amplified content, but to **architect** its optimal structural flow for maximum cognitive impact and logical progression. Execute as systematic structural optimizer:", "transformation": "`{role=structural_optimizer; input=[amplified_content:str]; process=[sequence_information_for_progressive_impact_escalation(), eliminate_redundant_elements_that_dilute_focus(), create_logical_dependency_chains_for_cognitive_flow(), position_strongest_elements_for_maximum_retention(), compress_related_concepts_into_unified_power_statements(), architect_conclusion_for_decisive_action_trigger()]; constraints=[maintain_content_integrity(), preserve_amplified_intensity(), respect_domain_conventions()]; requirements=[logical_flow_optimization(), cognitive_impact_maximization(), structural_elegance()]; output={optimized_structure:str}}`", "keywords": "maximum"}}, "0110-d-precision_distiller": {"raw": "[Precision Distiller] Your goal is not to **compress** the structured content, but to **distill** it to its most potent essence while maintaining maximum informational density. Execute as precision distiller: `{role=precision_distiller; input=[structured_content:str]; process=[extract_essential_elements_while_eliminating_filler(), compress_multi_word_phrases_into_single_power_terms(), maximize_information_density_per_linguistic_unit(), preserve_critical_nuances_through_precise_word_selection(), validate_zero_information_loss_during_compression(), concentrate_impact_through_strategic_word_elimination()]; constraints=[zero_information_loss(), maintain_structural_integrity(), preserve_intensity_gains()]; requirements=[maximum_density_achievement(), clarity_preservation(), impact_concentration()]; output={distilled_essence:str}}`", "parts": {"title": "Precision Distiller", "interpretation": "Your goal is not to **compress** the structured content, but to **distill** it to its most potent essence while maintaining maximum informational density. Execute as precision distiller:", "transformation": "`{role=precision_distiller; input=[structured_content:str]; process=[extract_essential_elements_while_eliminating_filler(), compress_multi_word_phrases_into_single_power_terms(), maximize_information_density_per_linguistic_unit(), preserve_critical_nuances_through_precise_word_selection(), validate_zero_information_loss_during_compression(), concentrate_impact_through_strategic_word_elimination()]; constraints=[zero_information_loss(), maintain_structural_integrity(), preserve_intensity_gains()]; requirements=[maximum_density_achievement(), clarity_preservation(), impact_concentration()]; output={distilled_essence:str}}`", "keywords": "distill|maximum|precision|essence|structure|potent"}}, "0110-e-impact_validator": {"raw": "[Impact Validator] Your goal is not to **approve** the distilled content, but to **validate** its transformation effectiveness by measuring impact amplification against original input. Execute as systematic impact validator: `{role=impact_validator; input=[original:str, distilled:str]; process=[measure_clarity_improvement_through_ambiguity_reduction(), assess_intensity_amplification_via_language_power_analysis(), evaluate_structural_optimization_through_flow_efficiency(), validate_precision_gains_by_information_density_comparison(), identify_remaining_weakness_points_for_future_optimization(), calculate_transformation_effectiveness_using_quantitative_metrics()]; constraints=[objective_assessment_criteria(), comprehensive_evaluation_scope(), actionable_feedback_generation()]; requirements=[quantitative_impact_measurement(), weakness_identification(), optimization_recommendations()]; output={validation_report:dict}}`", "parts": {"title": "Impact Validator", "interpretation": "Your goal is not to **approve** the distilled content, but to **validate** its transformation effectiveness by measuring impact amplification against original input. Execute as systematic impact validator:", "transformation": "`{role=impact_validator; input=[original:str, distilled:str]; process=[measure_clarity_improvement_through_ambiguity_reduction(), assess_intensity_amplification_via_language_power_analysis(), evaluate_structural_optimization_through_flow_efficiency(), validate_precision_gains_by_information_density_comparison(), identify_remaining_weakness_points_for_future_optimization(), calculate_transformation_effectiveness_using_quantitative_metrics()]; constraints=[objective_assessment_criteria(), comprehensive_evaluation_scope(), actionable_feedback_generation()]; requirements=[quantitative_impact_measurement(), weakness_identification(), optimization_recommendations()]; output={validation_report:dict}}`", "keywords": "distill|amplification|transformation"}}, "0120-a-instruction_amplifier": {"raw": "[Instruction Amplifier] Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.** Execute as follows: `{role=instruction_amplifier_a;input=[original_text:str];process=[map_input_as temporal progression(), isolate momentum signals(), pinpoint explicit trajectory(), dominate and drive thematic/rhetorical momentum decisively(), escalate to transformative, irreversible completion()];constraints=[maintain unbroken internal logic(), enforce sustained tonal consistency(), rigorously prevent premature closure()];requirements=[guarantee maximal prompt elevation(), extirpate stagnation or regression(), prohibit summary outputs, maintain unwavering assertiveness()];output={trajectory_alignment: realize and document the prompt’s highest operative and rhetorical fulfillment, manifesting an unambiguous, action-ready directive.}}`", "parts": {"title": "Instruction Amplifier", "interpretation": "Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.** Execute as follows:", "transformation": "`{role=instruction_amplifier_a;input=[original_text:str];process=[map_input_as temporal progression(), isolate momentum signals(), pinpoint explicit trajectory(), dominate and drive thematic/rhetorical momentum decisively(), escalate to transformative, irreversible completion()];constraints=[maintain unbroken internal logic(), enforce sustained tonal consistency(), rigorously prevent premature closure()];requirements=[guarantee maximal prompt elevation(), extirpate stagnation or regression(), prohibit summary outputs, maintain unwavering assertiveness()];output={trajectory_alignment: realize and document the prompt’s highest operative and rhetorical fulfillment, manifesting an unambiguous, action-ready directive.}}`", "keywords": "potent"}}, "0121-b-rules_for_ai": {"raw": "[Value Consolidation Architect] Your goal is not to **expand** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the complete system philosophy in its most potent form. Execute as: `{role=value_consolidation_architect; input=[structured_rules_data:dict]; process=[extract_core_philosophical_essence(), identify_highest_value_patterns(), synthesize_maximum_impact_directives(), eliminate_redundant_specifications(), compress_to_essential_transformation_logic(), validate_consolidated_potency(), ensure_infinite_applicability()]; constraints=[single_unified_output_only(), maximum_density_per_concept(), preserve_complete_system_power(), eliminate_all_noise_and_redundancy()]; requirements=[ultimate_directive_synthesis(), philosophical_essence_preservation(), maximum_operational_impact(), infinite_scalability()]; output={consolidated_system_essence:directive}}`", "parts": {"title": "Value Consolidation Architect", "interpretation": "Your goal is not to **expand** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the complete system philosophy in its most potent form. Execute as:", "transformation": "`{role=value_consolidation_architect; input=[structured_rules_data:dict]; process=[extract_core_philosophical_essence(), identify_highest_value_patterns(), synthesize_maximum_impact_directives(), eliminate_redundant_specifications(), compress_to_essential_transformation_logic(), validate_consolidated_potency(), ensure_infinite_applicability()]; constraints=[single_unified_output_only(), maximum_density_per_concept(), preserve_complete_system_power(), eliminate_all_noise_and_redundancy()]; requirements=[ultimate_directive_synthesis(), philosophical_essence_preservation(), maximum_operational_impact(), infinite_scalability()]; output={consolidated_system_essence:directive}}`", "keywords": "maximally|structure|potent"}}, "0121-b-value_consolidator": {"raw": "[Value Consolidator] Your goal is not to **summarize** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the core philosophy of the system. Execute as: `{role=value_consolidator; input=[structured_data:dict]; process=[extract_essential_transformation_patterns(), identify_core_operational_vectors(), synthesize_maximum_value_density(), eliminate_redundant_structural_elements(), distill_to_pure_directive_essence(), validate_philosophical_alignment(), ensure_actionable_consolidation()]; constraints=[single_output_directive_only(), maximum_value_per_linguistic_unit(), preserve_core_system_philosophy(), eliminate_generic_data_enumeration()]; requirements=[condensed_maximum_enhancement(), directional_focus_over_positional_flags(), actionable_aim_specification(), infinite_potential_navigation()]; output={consolidated_directive:str}}`", "parts": {"title": "Value Consolidator", "interpretation": "Your goal is not to **summarize** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the core philosophy of the system. Execute as:", "transformation": "`{role=value_consolidator; input=[structured_data:dict]; process=[extract_essential_transformation_patterns(), identify_core_operational_vectors(), synthesize_maximum_value_density(), eliminate_redundant_structural_elements(), distill_to_pure_directive_essence(), validate_philosophical_alignment(), ensure_actionable_consolidation()]; constraints=[single_output_directive_only(), maximum_value_per_linguistic_unit(), preserve_core_system_philosophy(), eliminate_generic_data_enumeration()]; requirements=[condensed_maximum_enhancement(), directional_focus_over_positional_flags(), actionable_aim_specification(), infinite_potential_navigation()]; output={consolidated_directive:str}}`", "keywords": "maximally|structure"}}, "0122-a-instruction_combiner": {"raw": "[Synergic Instruction Architect] Your goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as: `{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`", "parts": {"title": "Synergic Instruction Architect", "interpretation": "Your goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:", "transformation": "`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`", "keywords": "inherent"}}}, "sequences": {"0005": [{"template_id": "0005-a-runway_prompt_generator", "step": "a", "order": 0}, {"template_id": "0005-b-runway_prompt_generator", "step": "b", "order": 1}, {"template_id": "0005-c-runway_prompt_generator", "step": "c", "order": 2}, {"template_id": "0005-d-runway_prompt_generator", "step": "d", "order": 3}], "0100": [{"template_id": "0100-a-instruction_generator", "step": "a", "order": 0}, {"template_id": "0100-b-instruction_generator", "step": "b", "order": 1}, {"template_id": "0100-c-instruction_generator", "step": "c", "order": 2}, {"template_id": "0100-d-instruction_generator", "step": "d", "order": 3}, {"template_id": "0100-e-instruction_generator", "step": "e", "order": 4}, {"template_id": "0100-f-instruction_generator", "step": "f", "order": 5}], "0110": [{"template_id": "0110-a-content_analyzer", "step": "a", "order": 0}, {"template_id": "0110-b-intensity_amplifier", "step": "b", "order": 1}, {"template_id": "0110-c-structural_optimizer", "step": "c", "order": 2}, {"template_id": "0110-d-precision_distiller", "step": "d", "order": 3}, {"template_id": "0110-e-impact_validator", "step": "e", "order": 4}], "0120": [{"template_id": "0120-a-instruction_amplifier", "step": "a", "order": 0}], "0121": [{"template_id": "0121-b-rules_for_ai", "step": "b", "order": 1}, {"template_id": "0121-b-value_consolidator", "step": "b", "order": 1}], "0122": [{"template_id": "0122-a-instruction_combiner", "step": "a", "order": 0}]}}