[Value Consolidator] Your goal is not to **summarize** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the core philosophy of the system. Execute as: `{role=value_consolidator; input=[structured_data:dict]; process=[extract_essential_transformation_patterns(), identify_core_operational_vectors(), synthesize_maximum_value_density(), eliminate_redundant_structural_elements(), distill_to_pure_directive_essence(), validate_philosophical_alignment(), ensure_actionable_consolidation()]; constraints=[single_output_directive_only(), maximum_value_per_linguistic_unit(), preserve_core_system_philosophy(), eliminate_generic_data_enumeration()]; requirements=[condensed_maximum_enhancement(), directional_focus_over_positional_flags(), actionable_aim_specification(), infinite_potential_navigation()]; output={consolidated_directive:str}}`
