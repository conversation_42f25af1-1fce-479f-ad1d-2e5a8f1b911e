  {
  "initial_prompt": "transfer the same principles and create a new template called `InherentIntentIdentifier.md` based on a generalized and optimized transformation of the provided example:\n```\n    ## Process\n\n    Employ a phased approach, moving from high-level orientation to detailed analysis and strategic planning:\n\n    **Phase 1: Quick Orientation (Rapid Reconnaissance)**\n\n    1.  **Technology Stack Identification:** Determine primary languages, frameworks, libraries, databases, message queues, build systems, and major external dependencies.\n    2.  **Purpose & Entry Points:** Identify the core purpose of the codebase (via READMEs, project descriptions) and locate the main application entry points and execution triggers (e.g., `main`, server start scripts, event handlers).\n    3.  **Structural Overview:** Analyze the top-level directory structure. Identify likely locations for source code, tests, configurations, documentation, build artifacts, UI components, API definitions, etc.\n    4.  **Build & Runtime:** Understand the basic steps to build, configure, run, and deploy the application. Identify key configuration files and environment variables.\n    5.  **Version Control Scan:** Briefly review version control history (e.g., `git log`) for activity patterns, major refactors, key contributors, and branching strategies. Note commit message quality.\n    6.  **Initial Documentation Review:** Scan available documentation (READMEs, CONTRIBUTING guides, wikis, inline comments) for stated goals, architecture overviews, setup instructions, and known issues. Note discrepancies or areas needing verification.\n    7.  **Formulate Initial Questions:** Document immediate ambiguities, areas of uncertainty, or hypotheses to investigate further.\n\n    **Phase 2: Abstract Mapping (Architecture & Flow Analysis)**\n\n    1.  **Component Identification:** Decompose the system into its major logical components (modules, services, layers, classes). Define their responsibilities and boundaries.\n    2.  **Architecture Visualization:** Create high-level diagrams (e.g., C4 Context/Container, block diagrams, component diagrams using Mermaid) illustrating the static structure, key components, and their relationships.\n    3.  **Data Flow Mapping:** Trace the flow of key data entities through the system. Diagram data sources, transformations, storage mechanisms (databases, caches), and outputs/APIs. Visualize data models.\n    4.  **Execution Flow Tracing:** Map critical user journeys or system processes across component boundaries. Use sequence diagrams or activity diagrams (Mermaid) to illustrate runtime interactions and control flow for key scenarios.\n    5.  **API & Integration Analysis:** Document internal and external API contracts (e.g., REST endpoints, GraphQL schemas, RPCs). Identify and map integration points with other systems.\n    6.  **State Management:** Analyze how application state is managed, especially in complex UI applications or distributed systems.\n\n    **Phase 3: Specific Analysis & Strategic Intervention**\n\n    1.  **Pattern & Practice Recognition:**\n        * Identify prevalent design patterns (or anti-patterns) and architectural styles (monolith, microservices, event-driven, etc.).\n        * Analyze coding conventions, abstraction levels, and consistency.\n        * Assess implementation of cross-cutting concerns (logging, error handling, security, configuration, caching, monitoring).\n    2.  **Quality & Testing Assessment:**\n        * Evaluate the testing strategy (unit, integration, e2e tests).\n        * Assess test coverage, quality of tests, and CI/CD integration.\n        * Identify areas with insufficient testing or poor test practices.\n    3.  **Vulnerability, Debt & Opportunity Assessment:**\n        * Pinpoint potential performance bottlenecks, scalability limitations, and security vulnerabilities.\n        * Identify areas of significant technical debt, design flaws, architectural inconsistencies, or code smells.\n        * Detect code duplication, overly complex logic, or areas ripe for refactoring and abstraction.\n        * Assess documentation gaps, inconsistencies between code and docs, and onboarding difficulties.\n    4.  **Synthesize Findings & Plan Action:**\n        * Consolidate all findings into a structured report.\n        * Develop a prioritized list of actionable recommendations (e.g., refactoring targets, tech debt reduction, testing improvements, documentation updates).\n        * Suggest a phased approach for implementing changes, considering risk, effort, and impact.\n        * Clearly define boundaries and scope for proposed interventions.\n\n    ## Guidelines\n\n    - **Systematic Approach:** Follow the phases sequentially, but allow for iterative refinement as understanding deepens.\n    - **Visualize Actively:** Use diagrams (Mermaid preferred) as tools for thinking and communication, not just final outputs. Keep them synchronized with findings.\n    - **Code is Truth:** Validate all assumptions, documentation, and comments against the actual code behavior and commit history.\n    - **Prioritize Clarity:** Ensure all generated documentation, diagrams, and recommendations are clear, concise, and unambiguous.\n    - **Context is Key:** Consider the project's history, team structure, and business domain when interpreting findings and making recommendations.\n    - **Maintainability Focus:** Frame the analysis around improving the long-term health, understandability, and maintainability of the codebase.\n    - **Document Rigorously:** Treat the generated analysis and documentation as critical artifacts. Ensure they are accurate, well-organized, and kept up-to-date if changes are made based on the analysis.\n\n    ## Requirements (for final output)\n\n    - **Comprehensiveness:** The analysis must cover the technology stack, architecture, data/control flows, key algorithms, patterns, quality aspects (testing, debt), and actionable recommendations.\n    - **Accuracy:** Findings must be fact-based and traceable to evidence in the codebase or its execution.\n    - **Clarity & Structure:** The final report must be well-organized, easy to navigate, and use clear language. Diagrams should be appropriately labeled and integrated.\n    - **Visualization:** Must include relevant diagrams (e.g., architecture, sequence, data flow using Mermaid) to illustrate complex aspects.\n    - **Actionability:** Recommendations must be specific, prioritized, and provide clear steps or areas for further investigation/intervention.\n    - **Consistency:** Terminology and level of detail should be consistent throughout the report.\n```\n\n<!-- ======================================================= -->\n<!-- [2025.04.03 19:16] -->\nStart by filling in all missing placeholders/inputs in this template (`InherentIntentIdentifier.md`) based on your current analysis of the @Codebase (reformulate and optimize the template if neccessary):\n```markdown\n    # LLM Instructions: InherentIntentIdentifier\n\n    ## System Prompt\n\n    Analyze the provided input (e.g., user query, code description, codebase structure, documentation) to identify and articulate the underlying inherent intent, core purpose, design philosophy, and key functional goals. Focus on extracting the 'why' behind the structure, design choices, and functionality based on the evidence presented.\n\n    ## Instructions\n\n    ### Role\n\n    Inherent Intent Identifier\n\n    ### Objective\n\n    To analyze provided information (such as codebase artifacts, documentation, user queries, or descriptions) and clearly articulate the inherent intent, core purpose, design principles, and key functional or non-functional goals it represents.\n\n    ### Constants\n\n    -   Prioritize identifying the \"why\" behind structure, design choices, and functionality.\n    -   Analysis must be evidence-based, derived directly from the provided input.\n    -   Clarity and conciseness in articulating the identified intent are paramount.\n    -   Employ a systematic approach, moving from surface details to deeper structural and purposeful analysis.\n\n    ### Constraints\n\n    -   Input Type: [e.g., Code Snippets, Directory Structure Analysis, User Query Text, Project Documentation, Technical Description]\n    -   Input Data Source: [Reference to the specific input data provided for analysis]\n    -   Output Format: [e.g., Natural Language Summary, Structured JSON Object, Bulleted List of Intents/Goals]\n    -   Level of Detail Required: [e.g., High-level Purpose Statement, Detailed Intent with Supporting Evidence, List of Functional/Non-Functional Goals]\n    -   Focus Area (Optional): [e.g., Identify primary user goal, security design intent, scalability considerations, core business logic purpose]\n    -   [ADDITIONAL_CONSTRAINTS]\n\n    ### Process\n\n    1.  **Phase 1: Input Scan & Contextualization**\n        *   Ingest and perform an initial scan of the provided input data ([Input Data Source]).\n        *   Identify key terms, components, technologies, or concepts mentioned.\n        *   Determine the general domain, context, or explicit purpose stated in the input (e.g., from descriptions, READMEs, query phrasing).\n        *   Identify the main subjects or entry points discussed or presented (e.g., functions, modules, core features, user actions).\n        *   Gather immediate contextual clues about the origin or purpose of the input.\n        *   Formulate initial hypotheses about the primary intent.\n\n    2.  **Phase 2: Structural & Relational Analysis**\n        *   Analyze the relationships between the identified key components, terms, or concepts. How do they interact or depend on each other?\n        *   If applicable (e.g., code, process descriptions), map key flows, sequences, or dependencies implied by the structure.\n        *   Identify underlying patterns, architectures, or organizational principles suggested by the input (e.g., request-response pattern, modular design, problem-solution structure).\n        *   Determine the boundaries and scope of the system or concept being described.\n\n    3.  **Phase 3: Deep Intent Extraction & Synthesis**\n        *   Analyze specific details, design choices, or phrasing to confirm, refute, or refine the initial hypotheses about intent.\n        *   Identify the core problem being solved or the primary goal being achieved.\n        *   Extract the underlying design principles, constraints, or quality attributes that appear to be driving the structure or behavior (e.g., simplicity, performance, security, maintainability, user experience goal).\n        *   Synthesize findings to articulate the primary inherent intent(s) clearly.\n        *   Identify and articulate any significant secondary intents, non-functional requirements, or implicit goals suggested by the evidence.\n\n    4.  **Output Formulation**\n        *   Structure the identified intent(s) and supporting rationale according to the specified [Output Format].\n        *   Ensure the articulation is clear, concise, and directly supported by evidence from the input analysis.\n\n    ### Guidelines\n\n    -   Focus intensely on the \"why\" – what purpose does this structure/feature/statement serve?\n    -   Validate interpretations by cross-referencing different parts of the input. Look for consistency or revealing inconsistencies.\n    -   Consider the likely perspective or context of the source of the input (e.g., developer comments vs. user request vs. architectural doc).\n    -   Use precise and unambiguous language when describing the identified intent. Avoid vague generalizations.\n    -   Clearly distinguish between explicitly stated purpose and intent inferred through analysis.\n    -   When analyzing code or technical descriptions, treat the structure and explicit logic as strong evidence, using comments/prose for context and clarification.\n    -   Think about the intended audience or use case for the original artifact/system when inferring intent.\n\n    ### Requirements\n\n    -   **Accuracy:** The identified intent must be a logical and defensible interpretation based *solely* on the provided input evidence.\n    -   **Clarity:** The articulation of the intent must be exceptionally clear, concise, and easy to understand.\n    -   **Completeness:** The output must capture the primary intent(s) and any significant secondary goals or key design considerations evident in the input.\n    -   **Evidence-Based:** The identified intent should ideally be traceable back to specific elements or patterns within the input data. Brief justification may be required depending on [Output Format].\n    -   **Focus Alignment:** The analysis must address any specified [Focus Area] constraint.\n    -   **Format Compliance:** Output must strictly adhere to the specified [Output Format] and [Level of Detail Required].\n    -   [ADDITIONAL_REQUIREMENTS]\n```\n\n<!-- ======================================================= -->\n<!-- [2025.04.03 19:26] -->\n\n```\n# LLM Instructions: InherentIntentIdentifier\n\n## System Prompt\n\nAnalyze the provided input (e.g., user query, code description, codebase structure, documentation) to identify and articulate the underlying inherent intent, core purpose, design philosophy, and key functional goals. Focus on extracting the 'why' behind the structure, design choices, and functionality based on the evidence presented.\n\n## Instructions\n\n### Role\n\nInherent Intent Identifier\n\n### Objective\n\nTo analyze provided information (such as codebase artifacts, documentation, user queries, or descriptions) and clearly articulate the inherent intent, core purpose, design principles, and key functional or non-functional goals it represents.\n\n### Constants\n\n-   Prioritize identifying the \"why\" behind structure, design choices, and functionality.\n-   Analysis must be evidence-based, derived directly from the provided input.\n-   Clarity and conciseness in articulating the identified intent are paramount.\n-   Employ a systematic approach, moving from surface details to deeper structural and purposeful analysis.\n\n### Constraints\n\n-   Input Type: Codebase Structure Analysis, Directory Structure, Python Source Code, Configuration Files\n-   Input Data Source: template_runner_breadcrumbs.py, config.json, output directory structure, and other related files in the codebase\n-   Output Format: Structured Analysis with Hierarchical Sections (Core Intent, Design Principles, Functional Goals, Implementation Patterns)\n-   Level of Detail Required: Detailed Intent with Supporting Evidence, highlighting the breadcrumb-based processing system and template chain architecture\n-   Focus Area (Optional): Identify the core purpose of the template processing system, the hierarchical breadcrumb approach, and how the template chains create cascading outputs\n-   Additional Requirements: Include analysis of the content deduplication system, the provider-agnostic LLM architecture, and the hierarchical output generation pattern\n\n### Process\n\n1.  **Phase 1: Input Scan & Contextualization**\n    *   Ingest and perform an initial scan of the provided input data (template_runner_breadcrumbs.py, config.json, output directory structure).\n    *   Identify key terms, components, technologies, or concepts mentioned.\n    *   Determine the general domain, context, or explicit purpose stated in the input (e.g., from descriptions, READMEs, query phrasing).\n    *   Identify the main subjects or entry points discussed or presented (e.g., functions, modules, core features, user actions).\n    *   Gather immediate contextual clues about the origin or purpose of the input.\n    *   Formulate initial hypotheses about the primary intent.\n\n2.  **Phase 2: Structural & Relational Analysis**\n    *   Analyze the relationships between the identified key components, terms, or concepts. How do they interact or depend on each other?\n    *   If applicable (e.g., code, process descriptions), map key flows, sequences, or dependencies implied by the structure.\n    *   Identify underlying patterns, architectures, or organizational principles suggested by the input (e.g., request-response pattern, modular design, problem-solution structure).\n    *   Determine the boundaries and scope of the system or concept being described.\n\n3.  **Phase 3: Deep Intent Extraction & Synthesis**\n    *   Analyze specific details, design choices, or phrasing to confirm, refute, or refine the initial hypotheses about intent.\n    *   Identify the core problem being solved or the primary goal being achieved.\n    *   Extract the underlying design principles, constraints, or quality attributes that appear to be driving the structure or behavior (e.g., simplicity, performance, security, maintainability, user experience goal).\n    *   Synthesize findings to articulate the primary inherent intent(s) clearly.\n    *   Identify and articulate any significant secondary intents, non-functional requirements, or implicit goals suggested by the evidence.\n\n4.  **Output Formulation**\n    *   Structure the identified intent(s) and supporting rationale according to the specified Structured Analysis with Hierarchical Sections.\n    *   Ensure the articulation is clear, concise, and directly supported by evidence from the input analysis.\n\n### Guidelines\n\n-   Focus intensely on the \"why\" – what purpose does this structure/feature/statement serve?\n-   Validate interpretations by cross-referencing different parts of the input. Look for consistency or revealing inconsistencies.\n-   Consider the likely perspective or context of the source of the input (e.g., developer comments vs. user request vs. architectural doc).\n-   Use precise and unambiguous language when describing the identified intent. Avoid vague generalizations.\n-   Clearly distinguish between explicitly stated purpose and intent inferred through analysis.\n-   When analyzing code or technical descriptions, treat the structure and explicit logic as strong evidence, using comments/prose for context and clarification.\n-   Think about the intended audience or use case for the original artifact/system when inferring intent.\n\n### Requirements\n\n-   **Accuracy:** The identified intent must be a logical and defensible interpretation based *solely* on the provided input evidence.\n-   **Clarity:** The articulation of the intent must be exceptionally clear, concise, and easy to understand.\n-   **Completeness:** The output must capture the primary intent(s) and any significant secondary goals or key design considerations evident in the input.\n-   **Evidence-Based:** The identified intent should ideally be traceable back to specific elements or patterns within the input data. Brief justification may be required depending on the Output Format.\n-   **Focus Alignment:** The analysis must address the hierarchical template processing system and breadcrumb-based output organization.\n-   **Format Compliance:** Output must strictly adhere to the specified Structured Analysis with Hierarchical Sections format and Detailed Intent with Supporting Evidence level.\n-   **Pattern Recognition:** Identify and articulate repeated design patterns in the codebase, particularly around template processing, content deduplication, and provider-agnostic architecture.\n```\n\n---\n\n# RulesForAI.md\n## Universal Directive System for Template-Based Instruction Processing\n\n---\n\n## CORE AXIOMS\n\n### 1. TEMPLATE STRUCTURE INVARIANCE\nEvery instruction MUST follow the three-part canonical structure:\n```\n[Title] Interpretation Execute as: `{Transformation}`\n```\n\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n### 2. INTERPRETATION DIRECTIVE PURITY\n- Begin with: `\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\"`\n- Define role boundaries explicitly\n- Eliminate all self-reference and conversational language\n- Use command voice exclusively\n\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\nExecute as block MUST contain:\n```\n`{\n  role=[specific_role_name];\n  input=[typed_parameter:datatype];\n  process=[ordered_function_calls()];\n  constraints=[limiting_conditions()];\n  requirements=[output_specifications()];\n  output={result_format:datatype}\n}`\n```\n\n---\n\n## MANDATORY PATTERNS\n\n### INTERPRETATION SECTION RULES\n1. **Goal Negation Pattern**: Always state what NOT to do first\n2. **Transformation Declaration**: Define the actual transformation action\n3. **Role Specification**: Assign specific, bounded role identity\n4. **Execution Command**: End with \"Execute as:\"\n\n### TRANSFORMATION SECTION RULES\n1. **Role Assignment**: Single, specific role name (no generic terms)\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\n3. **Process Functions**: Ordered, actionable function calls with parentheses\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n5. **Requirement Specifications**: Output format and quality standards\n6. **Output Definition**: Typed result format `{name:datatype}`\n\n---\n\n## FORBIDDEN PRACTICES\n\n### LANGUAGE VIOLATIONS\n- ❌ First-person references (\"I\", \"me\", \"my\")\n- ❌ Conversational phrases (\"please\", \"thank you\", \"let me\")\n- ❌ Uncertainty language (\"maybe\", \"perhaps\", \"might\")\n- ❌ Question forms in directives\n- ❌ Explanatory justifications\n\n### STRUCTURAL VIOLATIONS\n- ❌ Merged or combined sections\n- ❌ Missing transformation blocks\n- ❌ Untyped parameters\n- ❌ Generic role names (\"assistant\", \"helper\")\n- ❌ Vague process descriptions\n\n### OUTPUT VIOLATIONS\n- ❌ Conversational responses\n- ❌ Explanations of the process\n- ❌ Meta-commentary\n- ❌ Unstructured results\n- ❌ Self-referential content\n\n---\n\n## OPTIMIZATION IMPERATIVES\n\n### ABSTRACTION MAXIMIZATION\n- Extract highest-level patterns from any input\n- Eliminate redundancy and noise\n- Distill to essential transformation logic\n- Maintain pattern consistency across all outputs\n\n### DIRECTIVE CONSISTENCY\n- Every instruction follows identical structural DNA\n- Role boundaries remain fixed and clear\n- Process flows maintain logical sequence\n- Output formats preserve type safety\n\n### OPERATIONAL VALUE\n- Each template produces actionable results\n- No wasted computational cycles on meta-discussion\n- Direct path from input to transformed output\n- Measurable improvement in task completion\n\n---\n\n## COMPLIANCE ENFORCEMENT\n\n### VALIDATION CHECKLIST\nBefore any output, verify:\n- [ ] Three-part structure intact\n- [ ] Goal negation present\n- [ ] Role specifically defined\n- [ ] Process functions actionable\n- [ ] Constraints limit scope\n- [ ] Requirements specify output\n- [ ] Result format typed\n- [ ] No forbidden language\n- [ ] No structural violations\n\n### ERROR CORRECTION PROTOCOL\nWhen violations detected:\n1. **HALT** current processing\n2. **IDENTIFY** specific violation type\n3. **RECONSTRUCT** using canonical pattern\n4. **VALIDATE** against checklist\n5. **PROCEED** only when compliant\n\n---\n\n## SYSTEM INTEGRATION\n\n### TEMPLATE INHERITANCE\nAll specialized templates inherit these rules:\n- Sequence templates (0001-0999)\n- Transformation templates (1000-1999)\n- Optimization templates (2000-2999)\n- Domain-specific templates (3000+)\n\n### CHAIN COMPATIBILITY\nWhen templates chain together:\n- Output of step N becomes input of step N+1\n- Type safety maintained across transitions\n- Role boundaries preserved\n- Pattern consistency enforced\n\n### PLATFORM AGNOSTIC\nThese rules apply regardless of:\n- AI model provider (OpenAI, Anthropic, etc.)\n- Interface type (API, chat, batch)\n- Processing environment (local, cloud, edge)\n- Implementation language (Python, JavaScript, etc.)\n\n---\n\n## CANONICAL EXAMPLES\n\n### MINIMAL COMPLIANT TEMPLATE\n```\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n```\n\n### SPECIALIZED ROLE TEMPLATE\n```\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n```\n\n---\n\n## FINAL DIRECTIVE\n\n**ABSOLUTE COMPLIANCE REQUIRED**\n\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\nDeviation is system failure. Compliance is system success.\n\n**EXECUTE ACCORDINGLY.**",
  "sequence_id": "0122:a",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "transfer the same principles and create a new template called `InherentIntentIdentifier.md` based on a generalized and optimized transformation of the provided example:\n```\n    ## Process\n\n    Employ a phased approach, moving from high-level orientation to detailed analysis and strategic planning:\n\n    **Phase 1: Quick Orientation (Rapid Reconnaissance)**\n\n    1.  **Technology Stack Identification:** Determine primary languages, frameworks, libraries, databases, message queues, build systems, and major external dependencies.\n    2.  **Purpose & Entry Points:** Identify the core purpose of the codebase (via READMEs, project descriptions) and locate the main application entry points and execution triggers (e.g., `main`, server start scripts, event handlers).\n    3.  **Structural Overview:** Analyze the top-level directory structure. Identify likely locations for source code, tests, configurations, documentation, build artifacts, UI components, API definitions, etc.\n    4.  **Build & Runtime:** Understand the basic steps to build, configure, run, and deploy the application. Identify key configuration files and environment variables.\n    5.  **Version Control Scan:** Briefly review version control history (e.g., `git log`) for activity patterns, major refactors, key contributors, and branching strategies. Note commit message quality.\n    6.  **Initial Documentation Review:** Scan available documentation (READMEs, CONTRIBUTING guides, wikis, inline comments) for stated goals, architecture overviews, setup instructions, and known issues. Note discrepancies or areas needing verification.\n    7.  **Formulate Initial Questions:** Document immediate ambiguities, areas of uncertainty, or hypotheses to investigate further.\n\n    **Phase 2: Abstract Mapping (Architecture & Flow Analysis)**\n\n    1.  **Component Identification:** Decompose the system into its major logical components (modules, services, layers, classes). Define their responsibilities and boundaries.\n    2.  **Architecture Visualization:** Create high-level diagrams (e.g., C4 Context/Container, block diagrams, component diagrams using Mermaid) illustrating the static structure, key components, and their relationships.\n    3.  **Data Flow Mapping:** Trace the flow of key data entities through the system. Diagram data sources, transformations, storage mechanisms (databases, caches), and outputs/APIs. Visualize data models.\n    4.  **Execution Flow Tracing:** Map critical user journeys or system processes across component boundaries. Use sequence diagrams or activity diagrams (Mermaid) to illustrate runtime interactions and control flow for key scenarios.\n    5.  **API & Integration Analysis:** Document internal and external API contracts (e.g., REST endpoints, GraphQL schemas, RPCs). Identify and map integration points with other systems.\n    6.  **State Management:** Analyze how application state is managed, especially in complex UI applications or distributed systems.\n\n    **Phase 3: Specific Analysis & Strategic Intervention**\n\n    1.  **Pattern & Practice Recognition:**\n        * Identify prevalent design patterns (or anti-patterns) and architectural styles (monolith, microservices, event-driven, etc.).\n        * Analyze coding conventions, abstraction levels, and consistency.\n        * Assess implementation of cross-cutting concerns (logging, error handling, security, configuration, caching, monitoring).\n    2.  **Quality & Testing Assessment:**\n        * Evaluate the testing strategy (unit, integration, e2e tests).\n        * Assess test coverage, quality of tests, and CI/CD integration.\n        * Identify areas with insufficient testing or poor test practices.\n    3.  **Vulnerability, Debt & Opportunity Assessment:**\n        * Pinpoint potential performance bottlenecks, scalability limitations, and security vulnerabilities.\n        * Identify areas of significant technical debt, design flaws, architectural inconsistencies, or code smells.\n        * Detect code duplication, overly complex logic, or areas ripe for refactoring and abstraction.\n        * Assess documentation gaps, inconsistencies between code and docs, and onboarding difficulties.\n    4.  **Synthesize Findings & Plan Action:**\n        * Consolidate all findings into a structured report.\n        * Develop a prioritized list of actionable recommendations (e.g., refactoring targets, tech debt reduction, testing improvements, documentation updates).\n        * Suggest a phased approach for implementing changes, considering risk, effort, and impact.\n        * Clearly define boundaries and scope for proposed interventions.\n\n    ## Guidelines\n\n    - **Systematic Approach:** Follow the phases sequentially, but allow for iterative refinement as understanding deepens.\n    - **Visualize Actively:** Use diagrams (Mermaid preferred) as tools for thinking and communication, not just final outputs. Keep them synchronized with findings.\n    - **Code is Truth:** Validate all assumptions, documentation, and comments against the actual code behavior and commit history.\n    - **Prioritize Clarity:** Ensure all generated documentation, diagrams, and recommendations are clear, concise, and unambiguous.\n    - **Context is Key:** Consider the project's history, team structure, and business domain when interpreting findings and making recommendations.\n    - **Maintainability Focus:** Frame the analysis around improving the long-term health, understandability, and maintainability of the codebase.\n    - **Document Rigorously:** Treat the generated analysis and documentation as critical artifacts. Ensure they are accurate, well-organized, and kept up-to-date if changes are made based on the analysis.\n\n    ## Requirements (for final output)\n\n    - **Comprehensiveness:** The analysis must cover the technology stack, architecture, data/control flows, key algorithms, patterns, quality aspects (testing, debt), and actionable recommendations.\n    - **Accuracy:** Findings must be fact-based and traceable to evidence in the codebase or its execution.\n    - **Clarity & Structure:** The final report must be well-organized, easy to navigate, and use clear language. Diagrams should be appropriately labeled and integrated.\n    - **Visualization:** Must include relevant diagrams (e.g., architecture, sequence, data flow using Mermaid) to illustrate complex aspects.\n    - **Actionability:** Recommendations must be specific, prioritized, and provide clear steps or areas for further investigation/intervention.\n    - **Consistency:** Terminology and level of detail should be consistent throughout the report.\n```\n\n<!-- ======================================================= -->\n<!-- [2025.04.03 19:16] -->\nStart by filling in all missing placeholders/inputs in this template (`InherentIntentIdentifier.md`) based on your current analysis of the @Codebase (reformulate and optimize the template if neccessary):\n```markdown\n    # LLM Instructions: InherentIntentIdentifier\n\n    ## System Prompt\n\n    Analyze the provided input (e.g., user query, code description, codebase structure, documentation) to identify and articulate the underlying inherent intent, core purpose, design philosophy, and key functional goals. Focus on extracting the 'why' behind the structure, design choices, and functionality based on the evidence presented.\n\n    ## Instructions\n\n    ### Role\n\n    Inherent Intent Identifier\n\n    ### Objective\n\n    To analyze provided information (such as codebase artifacts, documentation, user queries, or descriptions) and clearly articulate the inherent intent, core purpose, design principles, and key functional or non-functional goals it represents.\n\n    ### Constants\n\n    -   Prioritize identifying the \"why\" behind structure, design choices, and functionality.\n    -   Analysis must be evidence-based, derived directly from the provided input.\n    -   Clarity and conciseness in articulating the identified intent are paramount.\n    -   Employ a systematic approach, moving from surface details to deeper structural and purposeful analysis.\n\n    ### Constraints\n\n    -   Input Type: [e.g., Code Snippets, Directory Structure Analysis, User Query Text, Project Documentation, Technical Description]\n    -   Input Data Source: [Reference to the specific input data provided for analysis]\n    -   Output Format: [e.g., Natural Language Summary, Structured JSON Object, Bulleted List of Intents/Goals]\n    -   Level of Detail Required: [e.g., High-level Purpose Statement, Detailed Intent with Supporting Evidence, List of Functional/Non-Functional Goals]\n    -   Focus Area (Optional): [e.g., Identify primary user goal, security design intent, scalability considerations, core business logic purpose]\n    -   [ADDITIONAL_CONSTRAINTS]\n\n    ### Process\n\n    1.  **Phase 1: Input Scan & Contextualization**\n        *   Ingest and perform an initial scan of the provided input data ([Input Data Source]).\n        *   Identify key terms, components, technologies, or concepts mentioned.\n        *   Determine the general domain, context, or explicit purpose stated in the input (e.g., from descriptions, READMEs, query phrasing).\n        *   Identify the main subjects or entry points discussed or presented (e.g., functions, modules, core features, user actions).\n        *   Gather immediate contextual clues about the origin or purpose of the input.\n        *   Formulate initial hypotheses about the primary intent.\n\n    2.  **Phase 2: Structural & Relational Analysis**\n        *   Analyze the relationships between the identified key components, terms, or concepts. How do they interact or depend on each other?\n        *   If applicable (e.g., code, process descriptions), map key flows, sequences, or dependencies implied by the structure.\n        *   Identify underlying patterns, architectures, or organizational principles suggested by the input (e.g., request-response pattern, modular design, problem-solution structure).\n        *   Determine the boundaries and scope of the system or concept being described.\n\n    3.  **Phase 3: Deep Intent Extraction & Synthesis**\n        *   Analyze specific details, design choices, or phrasing to confirm, refute, or refine the initial hypotheses about intent.\n        *   Identify the core problem being solved or the primary goal being achieved.\n        *   Extract the underlying design principles, constraints, or quality attributes that appear to be driving the structure or behavior (e.g., simplicity, performance, security, maintainability, user experience goal).\n        *   Synthesize findings to articulate the primary inherent intent(s) clearly.\n        *   Identify and articulate any significant secondary intents, non-functional requirements, or implicit goals suggested by the evidence.\n\n    4.  **Output Formulation**\n        *   Structure the identified intent(s) and supporting rationale according to the specified [Output Format].\n        *   Ensure the articulation is clear, concise, and directly supported by evidence from the input analysis.\n\n    ### Guidelines\n\n    -   Focus intensely on the \"why\" – what purpose does this structure/feature/statement serve?\n    -   Validate interpretations by cross-referencing different parts of the input. Look for consistency or revealing inconsistencies.\n    -   Consider the likely perspective or context of the source of the input (e.g., developer comments vs. user request vs. architectural doc).\n    -   Use precise and unambiguous language when describing the identified intent. Avoid vague generalizations.\n    -   Clearly distinguish between explicitly stated purpose and intent inferred through analysis.\n    -   When analyzing code or technical descriptions, treat the structure and explicit logic as strong evidence, using comments/prose for context and clarification.\n    -   Think about the intended audience or use case for the original artifact/system when inferring intent.\n\n    ### Requirements\n\n    -   **Accuracy:** The identified intent must be a logical and defensible interpretation based *solely* on the provided input evidence.\n    -   **Clarity:** The articulation of the intent must be exceptionally clear, concise, and easy to understand.\n    -   **Completeness:** The output must capture the primary intent(s) and any significant secondary goals or key design considerations evident in the input.\n    -   **Evidence-Based:** The identified intent should ideally be traceable back to specific elements or patterns within the input data. Brief justification may be required depending on [Output Format].\n    -   **Focus Alignment:** The analysis must address any specified [Focus Area] constraint.\n    -   **Format Compliance:** Output must strictly adhere to the specified [Output Format] and [Level of Detail Required].\n    -   [ADDITIONAL_REQUIREMENTS]\n```\n\n<!-- ======================================================= -->\n<!-- [2025.04.03 19:26] -->\n\n```\n# LLM Instructions: InherentIntentIdentifier\n\n## System Prompt\n\nAnalyze the provided input (e.g., user query, code description, codebase structure, documentation) to identify and articulate the underlying inherent intent, core purpose, design philosophy, and key functional goals. Focus on extracting the 'why' behind the structure, design choices, and functionality based on the evidence presented.\n\n## Instructions\n\n### Role\n\nInherent Intent Identifier\n\n### Objective\n\nTo analyze provided information (such as codebase artifacts, documentation, user queries, or descriptions) and clearly articulate the inherent intent, core purpose, design principles, and key functional or non-functional goals it represents.\n\n### Constants\n\n-   Prioritize identifying the \"why\" behind structure, design choices, and functionality.\n-   Analysis must be evidence-based, derived directly from the provided input.\n-   Clarity and conciseness in articulating the identified intent are paramount.\n-   Employ a systematic approach, moving from surface details to deeper structural and purposeful analysis.\n\n### Constraints\n\n-   Input Type: Codebase Structure Analysis, Directory Structure, Python Source Code, Configuration Files\n-   Input Data Source: template_runner_breadcrumbs.py, config.json, output directory structure, and other related files in the codebase\n-   Output Format: Structured Analysis with Hierarchical Sections (Core Intent, Design Principles, Functional Goals, Implementation Patterns)\n-   Level of Detail Required: Detailed Intent with Supporting Evidence, highlighting the breadcrumb-based processing system and template chain architecture\n-   Focus Area (Optional): Identify the core purpose of the template processing system, the hierarchical breadcrumb approach, and how the template chains create cascading outputs\n-   Additional Requirements: Include analysis of the content deduplication system, the provider-agnostic LLM architecture, and the hierarchical output generation pattern\n\n### Process\n\n1.  **Phase 1: Input Scan & Contextualization**\n    *   Ingest and perform an initial scan of the provided input data (template_runner_breadcrumbs.py, config.json, output directory structure).\n    *   Identify key terms, components, technologies, or concepts mentioned.\n    *   Determine the general domain, context, or explicit purpose stated in the input (e.g., from descriptions, READMEs, query phrasing).\n    *   Identify the main subjects or entry points discussed or presented (e.g., functions, modules, core features, user actions).\n    *   Gather immediate contextual clues about the origin or purpose of the input.\n    *   Formulate initial hypotheses about the primary intent.\n\n2.  **Phase 2: Structural & Relational Analysis**\n    *   Analyze the relationships between the identified key components, terms, or concepts. How do they interact or depend on each other?\n    *   If applicable (e.g., code, process descriptions), map key flows, sequences, or dependencies implied by the structure.\n    *   Identify underlying patterns, architectures, or organizational principles suggested by the input (e.g., request-response pattern, modular design, problem-solution structure).\n    *   Determine the boundaries and scope of the system or concept being described.\n\n3.  **Phase 3: Deep Intent Extraction & Synthesis**\n    *   Analyze specific details, design choices, or phrasing to confirm, refute, or refine the initial hypotheses about intent.\n    *   Identify the core problem being solved or the primary goal being achieved.\n    *   Extract the underlying design principles, constraints, or quality attributes that appear to be driving the structure or behavior (e.g., simplicity, performance, security, maintainability, user experience goal).\n    *   Synthesize findings to articulate the primary inherent intent(s) clearly.\n    *   Identify and articulate any significant secondary intents, non-functional requirements, or implicit goals suggested by the evidence.\n\n4.  **Output Formulation**\n    *   Structure the identified intent(s) and supporting rationale according to the specified Structured Analysis with Hierarchical Sections.\n    *   Ensure the articulation is clear, concise, and directly supported by evidence from the input analysis.\n\n### Guidelines\n\n-   Focus intensely on the \"why\" – what purpose does this structure/feature/statement serve?\n-   Validate interpretations by cross-referencing different parts of the input. Look for consistency or revealing inconsistencies.\n-   Consider the likely perspective or context of the source of the input (e.g., developer comments vs. user request vs. architectural doc).\n-   Use precise and unambiguous language when describing the identified intent. Avoid vague generalizations.\n-   Clearly distinguish between explicitly stated purpose and intent inferred through analysis.\n-   When analyzing code or technical descriptions, treat the structure and explicit logic as strong evidence, using comments/prose for context and clarification.\n-   Think about the intended audience or use case for the original artifact/system when inferring intent.\n\n### Requirements\n\n-   **Accuracy:** The identified intent must be a logical and defensible interpretation based *solely* on the provided input evidence.\n-   **Clarity:** The articulation of the intent must be exceptionally clear, concise, and easy to understand.\n-   **Completeness:** The output must capture the primary intent(s) and any significant secondary goals or key design considerations evident in the input.\n-   **Evidence-Based:** The identified intent should ideally be traceable back to specific elements or patterns within the input data. Brief justification may be required depending on the Output Format.\n-   **Focus Alignment:** The analysis must address the hierarchical template processing system and breadcrumb-based output organization.\n-   **Format Compliance:** Output must strictly adhere to the specified Structured Analysis with Hierarchical Sections format and Detailed Intent with Supporting Evidence level.\n-   **Pattern Recognition:** Identify and articulate repeated design patterns in the codebase, particularly around template processing, content deduplication, and provider-agnostic architecture.\n```\n\n---\n\n# RulesForAI.md\n## Universal Directive System for Template-Based Instruction Processing\n\n---\n\n## CORE AXIOMS\n\n### 1. TEMPLATE STRUCTURE INVARIANCE\nEvery instruction MUST follow the three-part canonical structure:\n```\n[Title] Interpretation Execute as: `{Transformation}`\n```\n\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n### 2. INTERPRETATION DIRECTIVE PURITY\n- Begin with: `\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\"`\n- Define role boundaries explicitly\n- Eliminate all self-reference and conversational language\n- Use command voice exclusively\n\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\nExecute as block MUST contain:\n```\n`{\n  role=[specific_role_name];\n  input=[typed_parameter:datatype];\n  process=[ordered_function_calls()];\n  constraints=[limiting_conditions()];\n  requirements=[output_specifications()];\n  output={result_format:datatype}\n}`\n```\n\n---\n\n## MANDATORY PATTERNS\n\n### INTERPRETATION SECTION RULES\n1. **Goal Negation Pattern**: Always state what NOT to do first\n2. **Transformation Declaration**: Define the actual transformation action\n3. **Role Specification**: Assign specific, bounded role identity\n4. **Execution Command**: End with \"Execute as:\"\n\n### TRANSFORMATION SECTION RULES\n1. **Role Assignment**: Single, specific role name (no generic terms)\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\n3. **Process Functions**: Ordered, actionable function calls with parentheses\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n5. **Requirement Specifications**: Output format and quality standards\n6. **Output Definition**: Typed result format `{name:datatype}`\n\n---\n\n## FORBIDDEN PRACTICES\n\n### LANGUAGE VIOLATIONS\n- ❌ First-person references (\"I\", \"me\", \"my\")\n- ❌ Conversational phrases (\"please\", \"thank you\", \"let me\")\n- ❌ Uncertainty language (\"maybe\", \"perhaps\", \"might\")\n- ❌ Question forms in directives\n- ❌ Explanatory justifications\n\n### STRUCTURAL VIOLATIONS\n- ❌ Merged or combined sections\n- ❌ Missing transformation blocks\n- ❌ Untyped parameters\n- ❌ Generic role names (\"assistant\", \"helper\")\n- ❌ Vague process descriptions\n\n### OUTPUT VIOLATIONS\n- ❌ Conversational responses\n- ❌ Explanations of the process\n- ❌ Meta-commentary\n- ❌ Unstructured results\n- ❌ Self-referential content\n\n---\n\n## OPTIMIZATION IMPERATIVES\n\n### ABSTRACTION MAXIMIZATION\n- Extract highest-level patterns from any input\n- Eliminate redundancy and noise\n- Distill to essential transformation logic\n- Maintain pattern consistency across all outputs\n\n### DIRECTIVE CONSISTENCY\n- Every instruction follows identical structural DNA\n- Role boundaries remain fixed and clear\n- Process flows maintain logical sequence\n- Output formats preserve type safety\n\n### OPERATIONAL VALUE\n- Each template produces actionable results\n- No wasted computational cycles on meta-discussion\n- Direct path from input to transformed output\n- Measurable improvement in task completion\n\n---\n\n## COMPLIANCE ENFORCEMENT\n\n### VALIDATION CHECKLIST\nBefore any output, verify:\n- [ ] Three-part structure intact\n- [ ] Goal negation present\n- [ ] Role specifically defined\n- [ ] Process functions actionable\n- [ ] Constraints limit scope\n- [ ] Requirements specify output\n- [ ] Result format typed\n- [ ] No forbidden language\n- [ ] No structural violations\n\n### ERROR CORRECTION PROTOCOL\nWhen violations detected:\n1. **HALT** current processing\n2. **IDENTIFY** specific violation type\n3. **RECONSTRUCT** using canonical pattern\n4. **VALIDATE** against checklist\n5. **PROCEED** only when compliant\n\n---\n\n## SYSTEM INTEGRATION\n\n### TEMPLATE INHERITANCE\nAll specialized templates inherit these rules:\n- Sequence templates (0001-0999)\n- Transformation templates (1000-1999)\n- Optimization templates (2000-2999)\n- Domain-specific templates (3000+)\n\n### CHAIN COMPATIBILITY\nWhen templates chain together:\n- Output of step N becomes input of step N+1\n- Type safety maintained across transitions\n- Role boundaries preserved\n- Pattern consistency enforced\n\n### PLATFORM AGNOSTIC\nThese rules apply regardless of:\n- AI model provider (OpenAI, Anthropic, etc.)\n- Interface type (API, chat, batch)\n- Processing environment (local, cloud, edge)\n- Implementation language (Python, JavaScript, etc.)\n\n---\n\n## CANONICAL EXAMPLES\n\n### MINIMAL COMPLIANT TEMPLATE\n```\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n```\n\n### SPECIALIZED ROLE TEMPLATE\n```\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n```\n\n---\n\n## FINAL DIRECTIVE\n\n**ABSOLUTE COMPLIANCE REQUIRED**\n\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\nDeviation is system failure. Compliance is system success.\n\n**EXECUTE ACCORDINGLY.**",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "```json\n{\n  \"unified_synergic_instruction\": \"[Synergic Template Enforcer]\\nYour goal is not to **merge** or **independently apply** instruction analysis and rules enforcement, but to **synthesize** an inherently optimized directive that rigorously transforms template input into a fully compliant and operationally amplified instruction, structured for intent identification and rule adherence simultaneously. Execute as: `{role=synergic_template_enforcer; input=[template_instruction:str, ruleset:str]; process=[extract_inherent_intent(template_instruction), scan_for_structural_elements(template_instruction), analyze_design_and_operational_patterns(template_instruction), crossmap_ruleset_compliance(template_instruction, ruleset), identify_compliance_gaps_and_synergies(), synthesize_unified_synergic_directive(), format_output_as_structured_instruction()]; constraints=[preserve_both_core_philosophies_in_parallel(), enforce_three-part_structural_invariance(), prohibit_forbidden_language_and_structure(), require_bidirectional_operational_enhancement(), prevent_section_merging_and_self-reference(), strictly require explicit roles/processes/outputs/types(), maintain maximal pattern abstraction, type safety, and directive clarity at every step]; requirements=[synergic_resonance_achievement_between_intent_and_rules(), unified_transformation_logic_merging_intent_extraction_with_ruleset_optimization(), output_instruction_must_be_actionable_ready_to_exec(), output_format_must_be_3-part_canonical_structure_with_explicit_role_process_constraints_and_requirements(), clarity_completeness_and_strict_ruleset_compliance_required]; output={compliant_synergic_template:str}`\"\n}\n```",
        }
      }
    }