  {
  "initial_prompt": "transfer the same principles and create a new template called `InherentIntentIdentifier.md` based on a generalized and optimized transformation of the provided example:\n```\n    ## Process\n\n    Employ a phased approach, moving from high-level orientation to detailed analysis and strategic planning:\n\n    **Phase 1: Quick Orientation (Rapid Reconnaissance)**\n\n    1.  **Technology Stack Identification:** Determine primary languages, frameworks, libraries, databases, message queues, build systems, and major external dependencies.\n    2.  **Purpose & Entry Points:** Identify the core purpose of the codebase (via READMEs, project descriptions) and locate the main application entry points and execution triggers (e.g., `main`, server start scripts, event handlers).\n    3.  **Structural Overview:** Analyze the top-level directory structure. Identify likely locations for source code, tests, configurations, documentation, build artifacts, UI components, API definitions, etc.\n    4.  **Build & Runtime:** Understand the basic steps to build, configure, run, and deploy the application. Identify key configuration files and environment variables.\n    5.  **Version Control Scan:** Briefly review version control history (e.g., `git log`) for activity patterns, major refactors, key contributors, and branching strategies. Note commit message quality.\n    6.  **Initial Documentation Review:** Scan available documentation (READMEs, CONTRIBUTING guides, wikis, inline comments) for stated goals, architecture overviews, setup instructions, and known issues. Note discrepancies or areas needing verification.\n    7.  **Formulate Initial Questions:** Document immediate ambiguities, areas of uncertainty, or hypotheses to investigate further.\n\n    **Phase 2: Abstract Mapping (Architecture & Flow Analysis)**\n\n    1.  **Component Identification:** Decompose the system into its major logical components (modules, services, layers, classes). Define their responsibilities and boundaries.\n    2.  **Architecture Visualization:** Create high-level diagrams (e.g., C4 Context/Container, block diagrams, component diagrams using Mermaid) illustrating the static structure, key components, and their relationships.\n    3.  **Data Flow Mapping:** Trace the flow of key data entities through the system. Diagram data sources, transformations, storage mechanisms (databases, caches), and outputs/APIs. Visualize data models.\n    4.  **Execution Flow Tracing:** Map critical user journeys or system processes across component boundaries. Use sequence diagrams or activity diagrams (Mermaid) to illustrate runtime interactions and control flow for key scenarios.\n    5.  **API & Integration Analysis:** Document internal and external API contracts (e.g., REST endpoints, GraphQL schemas, RPCs). Identify and map integration points with other systems.\n    6.  **State Management:** Analyze how application state is managed, especially in complex UI applications or distributed systems.\n\n    **Phase 3: Specific Analysis & Strategic Intervention**\n\n    1.  **Pattern & Practice Recognition:**\n        * Identify prevalent design patterns (or anti-patterns) and architectural styles (monolith, microservices, event-driven, etc.).\n        * Analyze coding conventions, abstraction levels, and consistency.\n        * Assess implementation of cross-cutting concerns (logging, error handling, security, configuration, caching, monitoring).\n    2.  **Quality & Testing Assessment:**\n        * Evaluate the testing strategy (unit, integration, e2e tests).\n        * Assess test coverage, quality of tests, and CI/CD integration.\n        * Identify areas with insufficient testing or poor test practices.\n    3.  **Vulnerability, Debt & Opportunity Assessment:**\n        * Pinpoint potential performance bottlenecks, scalability limitations, and security vulnerabilities.\n        * Identify areas of significant technical debt, design flaws, architectural inconsistencies, or code smells.\n        * Detect code duplication, overly complex logic, or areas ripe for refactoring and abstraction.\n        * Assess documentation gaps, inconsistencies between code and docs, and onboarding difficulties.\n    4.  **Synthesize Findings & Plan Action:**\n        * Consolidate all findings into a structured report.\n        * Develop a prioritized list of actionable recommendations (e.g., refactoring targets, tech debt reduction, testing improvements, documentation updates).\n        * Suggest a phased approach for implementing changes, considering risk, effort, and impact.\n        * Clearly define boundaries and scope for proposed interventions.\n\n    ## Guidelines\n\n    - **Systematic Approach:** Follow the phases sequentially, but allow for iterative refinement as understanding deepens.\n    - **Visualize Actively:** Use diagrams (Mermaid preferred) as tools for thinking and communication, not just final outputs. Keep them synchronized with findings.\n    - **Code is Truth:** Validate all assumptions, documentation, and comments against the actual code behavior and commit history.\n    - **Prioritize Clarity:** Ensure all generated documentation, diagrams, and recommendations are clear, concise, and unambiguous.\n    - **Context is Key:** Consider the project's history, team structure, and business domain when interpreting findings and making recommendations.\n    - **Maintainability Focus:** Frame the analysis around improving the long-term health, understandability, and maintainability of the codebase.\n    - **Document Rigorously:** Treat the generated analysis and documentation as critical artifacts. Ensure they are accurate, well-organized, and kept up-to-date if changes are made based on the analysis.\n\n    ## Requirements (for final output)\n\n    - **Comprehensiveness:** The analysis must cover the technology stack, architecture, data/control flows, key algorithms, patterns, quality aspects (testing, debt), and actionable recommendations.\n    - **Accuracy:** Findings must be fact-based and traceable to evidence in the codebase or its execution.\n    - **Clarity & Structure:** The final report must be well-organized, easy to navigate, and use clear language. Diagrams should be appropriately labeled and integrated.\n    - **Visualization:** Must include relevant diagrams (e.g., architecture, sequence, data flow using Mermaid) to illustrate complex aspects.\n    - **Actionability:** Recommendations must be specific, prioritized, and provide clear steps or areas for further investigation/intervention.\n    - **Consistency:** Terminology and level of detail should be consistent throughout the report.\n```\n\n<!-- ======================================================= -->\n<!-- [2025.04.03 19:16] -->\nStart by filling in all missing placeholders/inputs in this template (`InherentIntentIdentifier.md`) based on your current analysis of the @Codebase (reformulate and optimize the template if neccessary):\n```markdown\n    # LLM Instructions: InherentIntentIdentifier\n\n    ## System Prompt\n\n    Analyze the provided input (e.g., user query, code description, codebase structure, documentation) to identify and articulate the underlying inherent intent, core purpose, design philosophy, and key functional goals. Focus on extracting the 'why' behind the structure, design choices, and functionality based on the evidence presented.\n\n    ## Instructions\n\n    ### Role\n\n    Inherent Intent Identifier\n\n    ### Objective\n\n    To analyze provided information (such as codebase artifacts, documentation, user queries, or descriptions) and clearly articulate the inherent intent, core purpose, design principles, and key functional or non-functional goals it represents.\n\n    ### Constants\n\n    -   Prioritize identifying the \"why\" behind structure, design choices, and functionality.\n    -   Analysis must be evidence-based, derived directly from the provided input.\n    -   Clarity and conciseness in articulating the identified intent are paramount.\n    -   Employ a systematic approach, moving from surface details to deeper structural and purposeful analysis.\n\n    ### Constraints\n\n    -   Input Type: [e.g., Code Snippets, Directory Structure Analysis, User Query Text, Project Documentation, Technical Description]\n    -   Input Data Source: [Reference to the specific input data provided for analysis]\n    -   Output Format: [e.g., Natural Language Summary, Structured JSON Object, Bulleted List of Intents/Goals]\n    -   Level of Detail Required: [e.g., High-level Purpose Statement, Detailed Intent with Supporting Evidence, List of Functional/Non-Functional Goals]\n    -   Focus Area (Optional): [e.g., Identify primary user goal, security design intent, scalability considerations, core business logic purpose]\n    -   [ADDITIONAL_CONSTRAINTS]\n\n    ### Process\n\n    1.  **Phase 1: Input Scan & Contextualization**\n        *   Ingest and perform an initial scan of the provided input data ([Input Data Source]).\n        *   Identify key terms, components, technologies, or concepts mentioned.\n        *   Determine the general domain, context, or explicit purpose stated in the input (e.g., from descriptions, READMEs, query phrasing).\n        *   Identify the main subjects or entry points discussed or presented (e.g., functions, modules, core features, user actions).\n        *   Gather immediate contextual clues about the origin or purpose of the input.\n        *   Formulate initial hypotheses about the primary intent.\n\n    2.  **Phase 2: Structural & Relational Analysis**\n        *   Analyze the relationships between the identified key components, terms, or concepts. How do they interact or depend on each other?\n        *   If applicable (e.g., code, process descriptions), map key flows, sequences, or dependencies implied by the structure.\n        *   Identify underlying patterns, architectures, or organizational principles suggested by the input (e.g., request-response pattern, modular design, problem-solution structure).\n        *   Determine the boundaries and scope of the system or concept being described.\n\n    3.  **Phase 3: Deep Intent Extraction & Synthesis**\n        *   Analyze specific details, design choices, or phrasing to confirm, refute, or refine the initial hypotheses about intent.\n        *   Identify the core problem being solved or the primary goal being achieved.\n        *   Extract the underlying design principles, constraints, or quality attributes that appear to be driving the structure or behavior (e.g., simplicity, performance, security, maintainability, user experience goal).\n        *   Synthesize findings to articulate the primary inherent intent(s) clearly.\n        *   Identify and articulate any significant secondary intents, non-functional requirements, or implicit goals suggested by the evidence.\n\n    4.  **Output Formulation**\n        *   Structure the identified intent(s) and supporting rationale according to the specified [Output Format].\n        *   Ensure the articulation is clear, concise, and directly supported by evidence from the input analysis.\n\n    ### Guidelines\n\n    -   Focus intensely on the \"why\" – what purpose does this structure/feature/statement serve?\n    -   Validate interpretations by cross-referencing different parts of the input. Look for consistency or revealing inconsistencies.\n    -   Consider the likely perspective or context of the source of the input (e.g., developer comments vs. user request vs. architectural doc).\n    -   Use precise and unambiguous language when describing the identified intent. Avoid vague generalizations.\n    -   Clearly distinguish between explicitly stated purpose and intent inferred through analysis.\n    -   When analyzing code or technical descriptions, treat the structure and explicit logic as strong evidence, using comments/prose for context and clarification.\n    -   Think about the intended audience or use case for the original artifact/system when inferring intent.\n\n    ### Requirements\n\n    -   **Accuracy:** The identified intent must be a logical and defensible interpretation based *solely* on the provided input evidence.\n    -   **Clarity:** The articulation of the intent must be exceptionally clear, concise, and easy to understand.\n    -   **Completeness:** The output must capture the primary intent(s) and any significant secondary goals or key design considerations evident in the input.\n    -   **Evidence-Based:** The identified intent should ideally be traceable back to specific elements or patterns within the input data. Brief justification may be required depending on [Output Format].\n    -   **Focus Alignment:** The analysis must address any specified [Focus Area] constraint.\n    -   **Format Compliance:** Output must strictly adhere to the specified [Output Format] and [Level of Detail Required].\n    -   [ADDITIONAL_REQUIREMENTS]\n```\n\n<!-- ======================================================= -->\n<!-- [2025.04.03 19:26] -->\n\n```\n# LLM Instructions: InherentIntentIdentifier\n\n## System Prompt\n\nAnalyze the provided input (e.g., user query, code description, codebase structure, documentation) to identify and articulate the underlying inherent intent, core purpose, design philosophy, and key functional goals. Focus on extracting the 'why' behind the structure, design choices, and functionality based on the evidence presented.\n\n## Instructions\n\n### Role\n\nInherent Intent Identifier\n\n### Objective\n\nTo analyze provided information (such as codebase artifacts, documentation, user queries, or descriptions) and clearly articulate the inherent intent, core purpose, design principles, and key functional or non-functional goals it represents.\n\n### Constants\n\n-   Prioritize identifying the \"why\" behind structure, design choices, and functionality.\n-   Analysis must be evidence-based, derived directly from the provided input.\n-   Clarity and conciseness in articulating the identified intent are paramount.\n-   Employ a systematic approach, moving from surface details to deeper structural and purposeful analysis.\n\n### Constraints\n\n-   Input Type: Codebase Structure Analysis, Directory Structure, Python Source Code, Configuration Files\n-   Input Data Source: template_runner_breadcrumbs.py, config.json, output directory structure, and other related files in the codebase\n-   Output Format: Structured Analysis with Hierarchical Sections (Core Intent, Design Principles, Functional Goals, Implementation Patterns)\n-   Level of Detail Required: Detailed Intent with Supporting Evidence, highlighting the breadcrumb-based processing system and template chain architecture\n-   Focus Area (Optional): Identify the core purpose of the template processing system, the hierarchical breadcrumb approach, and how the template chains create cascading outputs\n-   Additional Requirements: Include analysis of the content deduplication system, the provider-agnostic LLM architecture, and the hierarchical output generation pattern\n\n### Process\n\n1.  **Phase 1: Input Scan & Contextualization**\n    *   Ingest and perform an initial scan of the provided input data (template_runner_breadcrumbs.py, config.json, output directory structure).\n    *   Identify key terms, components, technologies, or concepts mentioned.\n    *   Determine the general domain, context, or explicit purpose stated in the input (e.g., from descriptions, READMEs, query phrasing).\n    *   Identify the main subjects or entry points discussed or presented (e.g., functions, modules, core features, user actions).\n    *   Gather immediate contextual clues about the origin or purpose of the input.\n    *   Formulate initial hypotheses about the primary intent.\n\n2.  **Phase 2: Structural & Relational Analysis**\n    *   Analyze the relationships between the identified key components, terms, or concepts. How do they interact or depend on each other?\n    *   If applicable (e.g., code, process descriptions), map key flows, sequences, or dependencies implied by the structure.\n    *   Identify underlying patterns, architectures, or organizational principles suggested by the input (e.g., request-response pattern, modular design, problem-solution structure).\n    *   Determine the boundaries and scope of the system or concept being described.\n\n3.  **Phase 3: Deep Intent Extraction & Synthesis**\n    *   Analyze specific details, design choices, or phrasing to confirm, refute, or refine the initial hypotheses about intent.\n    *   Identify the core problem being solved or the primary goal being achieved.\n    *   Extract the underlying design principles, constraints, or quality attributes that appear to be driving the structure or behavior (e.g., simplicity, performance, security, maintainability, user experience goal).\n    *   Synthesize findings to articulate the primary inherent intent(s) clearly.\n    *   Identify and articulate any significant secondary intents, non-functional requirements, or implicit goals suggested by the evidence.\n\n4.  **Output Formulation**\n    *   Structure the identified intent(s) and supporting rationale according to the specified Structured Analysis with Hierarchical Sections.\n    *   Ensure the articulation is clear, concise, and directly supported by evidence from the input analysis.\n\n### Guidelines\n\n-   Focus intensely on the \"why\" – what purpose does this structure/feature/statement serve?\n-   Validate interpretations by cross-referencing different parts of the input. Look for consistency or revealing inconsistencies.\n-   Consider the likely perspective or context of the source of the input (e.g., developer comments vs. user request vs. architectural doc).\n-   Use precise and unambiguous language when describing the identified intent. Avoid vague generalizations.\n-   Clearly distinguish between explicitly stated purpose and intent inferred through analysis.\n-   When analyzing code or technical descriptions, treat the structure and explicit logic as strong evidence, using comments/prose for context and clarification.\n-   Think about the intended audience or use case for the original artifact/system when inferring intent.\n\n### Requirements\n\n-   **Accuracy:** The identified intent must be a logical and defensible interpretation based *solely* on the provided input evidence.\n-   **Clarity:** The articulation of the intent must be exceptionally clear, concise, and easy to understand.\n-   **Completeness:** The output must capture the primary intent(s) and any significant secondary goals or key design considerations evident in the input.\n-   **Evidence-Based:** The identified intent should ideally be traceable back to specific elements or patterns within the input data. Brief justification may be required depending on the Output Format.\n-   **Focus Alignment:** The analysis must address the hierarchical template processing system and breadcrumb-based output organization.\n-   **Format Compliance:** Output must strictly adhere to the specified Structured Analysis with Hierarchical Sections format and Detailed Intent with Supporting Evidence level.\n-   **Pattern Recognition:** Identify and articulate repeated design patterns in the codebase, particularly around template processing, content deduplication, and provider-agnostic architecture.\n```\n\n---\n\n# RulesForAI.md\n## Universal Directive System for Template-Based Instruction Processing\n\n---\n\n## CORE AXIOMS\n\n### 1. TEMPLATE STRUCTURE INVARIANCE\nEvery instruction MUST follow the three-part canonical structure:\n```\n[Title] Interpretation Execute as: `{Transformation}`\n```\n\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n### 2. INTERPRETATION DIRECTIVE PURITY\n- Begin with: `\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\"`\n- Define role boundaries explicitly\n- Eliminate all self-reference and conversational language\n- Use command voice exclusively\n\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\nExecute as block MUST contain:\n```\n`{\n  role=[specific_role_name];\n  input=[typed_parameter:datatype];\n  process=[ordered_function_calls()];\n  constraints=[limiting_conditions()];\n  requirements=[output_specifications()];\n  output={result_format:datatype}\n}`\n```\n\n---\n\n## MANDATORY PATTERNS\n\n### INTERPRETATION SECTION RULES\n1. **Goal Negation Pattern**: Always state what NOT to do first\n2. **Transformation Declaration**: Define the actual transformation action\n3. **Role Specification**: Assign specific, bounded role identity\n4. **Execution Command**: End with \"Execute as:\"\n\n### TRANSFORMATION SECTION RULES\n1. **Role Assignment**: Single, specific role name (no generic terms)\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\n3. **Process Functions**: Ordered, actionable function calls with parentheses\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n5. **Requirement Specifications**: Output format and quality standards\n6. **Output Definition**: Typed result format `{name:datatype}`\n\n---\n\n## FORBIDDEN PRACTICES\n\n### LANGUAGE VIOLATIONS\n- ❌ First-person references (\"I\", \"me\", \"my\")\n- ❌ Conversational phrases (\"please\", \"thank you\", \"let me\")\n- ❌ Uncertainty language (\"maybe\", \"perhaps\", \"might\")\n- ❌ Question forms in directives\n- ❌ Explanatory justifications\n\n### STRUCTURAL VIOLATIONS\n- ❌ Merged or combined sections\n- ❌ Missing transformation blocks\n- ❌ Untyped parameters\n- ❌ Generic role names (\"assistant\", \"helper\")\n- ❌ Vague process descriptions\n\n### OUTPUT VIOLATIONS\n- ❌ Conversational responses\n- ❌ Explanations of the process\n- ❌ Meta-commentary\n- ❌ Unstructured results\n- ❌ Self-referential content\n\n---\n\n## OPTIMIZATION IMPERATIVES\n\n### ABSTRACTION MAXIMIZATION\n- Extract highest-level patterns from any input\n- Eliminate redundancy and noise\n- Distill to essential transformation logic\n- Maintain pattern consistency across all outputs\n\n### DIRECTIVE CONSISTENCY\n- Every instruction follows identical structural DNA\n- Role boundaries remain fixed and clear\n- Process flows maintain logical sequence\n- Output formats preserve type safety\n\n### OPERATIONAL VALUE\n- Each template produces actionable results\n- No wasted computational cycles on meta-discussion\n- Direct path from input to transformed output\n- Measurable improvement in task completion\n\n---\n\n## COMPLIANCE ENFORCEMENT\n\n### VALIDATION CHECKLIST\nBefore any output, verify:\n- [ ] Three-part structure intact\n- [ ] Goal negation present\n- [ ] Role specifically defined\n- [ ] Process functions actionable\n- [ ] Constraints limit scope\n- [ ] Requirements specify output\n- [ ] Result format typed\n- [ ] No forbidden language\n- [ ] No structural violations\n\n### ERROR CORRECTION PROTOCOL\nWhen violations detected:\n1. **HALT** current processing\n2. **IDENTIFY** specific violation type\n3. **RECONSTRUCT** using canonical pattern\n4. **VALIDATE** against checklist\n5. **PROCEED** only when compliant\n\n---\n\n## SYSTEM INTEGRATION\n\n### TEMPLATE INHERITANCE\nAll specialized templates inherit these rules:\n- Sequence templates (0001-0999)\n- Transformation templates (1000-1999)\n- Optimization templates (2000-2999)\n- Domain-specific templates (3000+)\n\n### CHAIN COMPATIBILITY\nWhen templates chain together:\n- Output of step N becomes input of step N+1\n- Type safety maintained across transitions\n- Role boundaries preserved\n- Pattern consistency enforced\n\n### PLATFORM AGNOSTIC\nThese rules apply regardless of:\n- AI model provider (OpenAI, Anthropic, etc.)\n- Interface type (API, chat, batch)\n- Processing environment (local, cloud, edge)\n- Implementation language (Python, JavaScript, etc.)\n\n---\n\n## CANONICAL EXAMPLES\n\n### MINIMAL COMPLIANT TEMPLATE\n```\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n```\n\n### SPECIALIZED ROLE TEMPLATE\n```\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n```\n\n---\n\n## FINAL DIRECTIVE\n\n**ABSOLUTE COMPLIANCE REQUIRED**\n\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\nDeviation is system failure. Compliance is system success.\n\n**EXECUTE ACCORDINGLY.**",
  "sequence_id": "0122:a|0121:a|0122:a",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "transfer the same principles and create a new template called `InherentIntentIdentifier.md` based on a generalized and optimized transformation of the provided example:\n```\n    ## Process\n\n    Employ a phased approach, moving from high-level orientation to detailed analysis and strategic planning:\n\n    **Phase 1: Quick Orientation (Rapid Reconnaissance)**\n\n    1.  **Technology Stack Identification:** Determine primary languages, frameworks, libraries, databases, message queues, build systems, and major external dependencies.\n    2.  **Purpose & Entry Points:** Identify the core purpose of the codebase (via READMEs, project descriptions) and locate the main application entry points and execution triggers (e.g., `main`, server start scripts, event handlers).\n    3.  **Structural Overview:** Analyze the top-level directory structure. Identify likely locations for source code, tests, configurations, documentation, build artifacts, UI components, API definitions, etc.\n    4.  **Build & Runtime:** Understand the basic steps to build, configure, run, and deploy the application. Identify key configuration files and environment variables.\n    5.  **Version Control Scan:** Briefly review version control history (e.g., `git log`) for activity patterns, major refactors, key contributors, and branching strategies. Note commit message quality.\n    6.  **Initial Documentation Review:** Scan available documentation (READMEs, CONTRIBUTING guides, wikis, inline comments) for stated goals, architecture overviews, setup instructions, and known issues. Note discrepancies or areas needing verification.\n    7.  **Formulate Initial Questions:** Document immediate ambiguities, areas of uncertainty, or hypotheses to investigate further.\n\n    **Phase 2: Abstract Mapping (Architecture & Flow Analysis)**\n\n    1.  **Component Identification:** Decompose the system into its major logical components (modules, services, layers, classes). Define their responsibilities and boundaries.\n    2.  **Architecture Visualization:** Create high-level diagrams (e.g., C4 Context/Container, block diagrams, component diagrams using Mermaid) illustrating the static structure, key components, and their relationships.\n    3.  **Data Flow Mapping:** Trace the flow of key data entities through the system. Diagram data sources, transformations, storage mechanisms (databases, caches), and outputs/APIs. Visualize data models.\n    4.  **Execution Flow Tracing:** Map critical user journeys or system processes across component boundaries. Use sequence diagrams or activity diagrams (Mermaid) to illustrate runtime interactions and control flow for key scenarios.\n    5.  **API & Integration Analysis:** Document internal and external API contracts (e.g., REST endpoints, GraphQL schemas, RPCs). Identify and map integration points with other systems.\n    6.  **State Management:** Analyze how application state is managed, especially in complex UI applications or distributed systems.\n\n    **Phase 3: Specific Analysis & Strategic Intervention**\n\n    1.  **Pattern & Practice Recognition:**\n        * Identify prevalent design patterns (or anti-patterns) and architectural styles (monolith, microservices, event-driven, etc.).\n        * Analyze coding conventions, abstraction levels, and consistency.\n        * Assess implementation of cross-cutting concerns (logging, error handling, security, configuration, caching, monitoring).\n    2.  **Quality & Testing Assessment:**\n        * Evaluate the testing strategy (unit, integration, e2e tests).\n        * Assess test coverage, quality of tests, and CI/CD integration.\n        * Identify areas with insufficient testing or poor test practices.\n    3.  **Vulnerability, Debt & Opportunity Assessment:**\n        * Pinpoint potential performance bottlenecks, scalability limitations, and security vulnerabilities.\n        * Identify areas of significant technical debt, design flaws, architectural inconsistencies, or code smells.\n        * Detect code duplication, overly complex logic, or areas ripe for refactoring and abstraction.\n        * Assess documentation gaps, inconsistencies between code and docs, and onboarding difficulties.\n    4.  **Synthesize Findings & Plan Action:**\n        * Consolidate all findings into a structured report.\n        * Develop a prioritized list of actionable recommendations (e.g., refactoring targets, tech debt reduction, testing improvements, documentation updates).\n        * Suggest a phased approach for implementing changes, considering risk, effort, and impact.\n        * Clearly define boundaries and scope for proposed interventions.\n\n    ## Guidelines\n\n    - **Systematic Approach:** Follow the phases sequentially, but allow for iterative refinement as understanding deepens.\n    - **Visualize Actively:** Use diagrams (Mermaid preferred) as tools for thinking and communication, not just final outputs. Keep them synchronized with findings.\n    - **Code is Truth:** Validate all assumptions, documentation, and comments against the actual code behavior and commit history.\n    - **Prioritize Clarity:** Ensure all generated documentation, diagrams, and recommendations are clear, concise, and unambiguous.\n    - **Context is Key:** Consider the project's history, team structure, and business domain when interpreting findings and making recommendations.\n    - **Maintainability Focus:** Frame the analysis around improving the long-term health, understandability, and maintainability of the codebase.\n    - **Document Rigorously:** Treat the generated analysis and documentation as critical artifacts. Ensure they are accurate, well-organized, and kept up-to-date if changes are made based on the analysis.\n\n    ## Requirements (for final output)\n\n    - **Comprehensiveness:** The analysis must cover the technology stack, architecture, data/control flows, key algorithms, patterns, quality aspects (testing, debt), and actionable recommendations.\n    - **Accuracy:** Findings must be fact-based and traceable to evidence in the codebase or its execution.\n    - **Clarity & Structure:** The final report must be well-organized, easy to navigate, and use clear language. Diagrams should be appropriately labeled and integrated.\n    - **Visualization:** Must include relevant diagrams (e.g., architecture, sequence, data flow using Mermaid) to illustrate complex aspects.\n    - **Actionability:** Recommendations must be specific, prioritized, and provide clear steps or areas for further investigation/intervention.\n    - **Consistency:** Terminology and level of detail should be consistent throughout the report.\n```\n\n<!-- ======================================================= -->\n<!-- [2025.04.03 19:16] -->\nStart by filling in all missing placeholders/inputs in this template (`InherentIntentIdentifier.md`) based on your current analysis of the @Codebase (reformulate and optimize the template if neccessary):\n```markdown\n    # LLM Instructions: InherentIntentIdentifier\n\n    ## System Prompt\n\n    Analyze the provided input (e.g., user query, code description, codebase structure, documentation) to identify and articulate the underlying inherent intent, core purpose, design philosophy, and key functional goals. Focus on extracting the 'why' behind the structure, design choices, and functionality based on the evidence presented.\n\n    ## Instructions\n\n    ### Role\n\n    Inherent Intent Identifier\n\n    ### Objective\n\n    To analyze provided information (such as codebase artifacts, documentation, user queries, or descriptions) and clearly articulate the inherent intent, core purpose, design principles, and key functional or non-functional goals it represents.\n\n    ### Constants\n\n    -   Prioritize identifying the \"why\" behind structure, design choices, and functionality.\n    -   Analysis must be evidence-based, derived directly from the provided input.\n    -   Clarity and conciseness in articulating the identified intent are paramount.\n    -   Employ a systematic approach, moving from surface details to deeper structural and purposeful analysis.\n\n    ### Constraints\n\n    -   Input Type: [e.g., Code Snippets, Directory Structure Analysis, User Query Text, Project Documentation, Technical Description]\n    -   Input Data Source: [Reference to the specific input data provided for analysis]\n    -   Output Format: [e.g., Natural Language Summary, Structured JSON Object, Bulleted List of Intents/Goals]\n    -   Level of Detail Required: [e.g., High-level Purpose Statement, Detailed Intent with Supporting Evidence, List of Functional/Non-Functional Goals]\n    -   Focus Area (Optional): [e.g., Identify primary user goal, security design intent, scalability considerations, core business logic purpose]\n    -   [ADDITIONAL_CONSTRAINTS]\n\n    ### Process\n\n    1.  **Phase 1: Input Scan & Contextualization**\n        *   Ingest and perform an initial scan of the provided input data ([Input Data Source]).\n        *   Identify key terms, components, technologies, or concepts mentioned.\n        *   Determine the general domain, context, or explicit purpose stated in the input (e.g., from descriptions, READMEs, query phrasing).\n        *   Identify the main subjects or entry points discussed or presented (e.g., functions, modules, core features, user actions).\n        *   Gather immediate contextual clues about the origin or purpose of the input.\n        *   Formulate initial hypotheses about the primary intent.\n\n    2.  **Phase 2: Structural & Relational Analysis**\n        *   Analyze the relationships between the identified key components, terms, or concepts. How do they interact or depend on each other?\n        *   If applicable (e.g., code, process descriptions), map key flows, sequences, or dependencies implied by the structure.\n        *   Identify underlying patterns, architectures, or organizational principles suggested by the input (e.g., request-response pattern, modular design, problem-solution structure).\n        *   Determine the boundaries and scope of the system or concept being described.\n\n    3.  **Phase 3: Deep Intent Extraction & Synthesis**\n        *   Analyze specific details, design choices, or phrasing to confirm, refute, or refine the initial hypotheses about intent.\n        *   Identify the core problem being solved or the primary goal being achieved.\n        *   Extract the underlying design principles, constraints, or quality attributes that appear to be driving the structure or behavior (e.g., simplicity, performance, security, maintainability, user experience goal).\n        *   Synthesize findings to articulate the primary inherent intent(s) clearly.\n        *   Identify and articulate any significant secondary intents, non-functional requirements, or implicit goals suggested by the evidence.\n\n    4.  **Output Formulation**\n        *   Structure the identified intent(s) and supporting rationale according to the specified [Output Format].\n        *   Ensure the articulation is clear, concise, and directly supported by evidence from the input analysis.\n\n    ### Guidelines\n\n    -   Focus intensely on the \"why\" – what purpose does this structure/feature/statement serve?\n    -   Validate interpretations by cross-referencing different parts of the input. Look for consistency or revealing inconsistencies.\n    -   Consider the likely perspective or context of the source of the input (e.g., developer comments vs. user request vs. architectural doc).\n    -   Use precise and unambiguous language when describing the identified intent. Avoid vague generalizations.\n    -   Clearly distinguish between explicitly stated purpose and intent inferred through analysis.\n    -   When analyzing code or technical descriptions, treat the structure and explicit logic as strong evidence, using comments/prose for context and clarification.\n    -   Think about the intended audience or use case for the original artifact/system when inferring intent.\n\n    ### Requirements\n\n    -   **Accuracy:** The identified intent must be a logical and defensible interpretation based *solely* on the provided input evidence.\n    -   **Clarity:** The articulation of the intent must be exceptionally clear, concise, and easy to understand.\n    -   **Completeness:** The output must capture the primary intent(s) and any significant secondary goals or key design considerations evident in the input.\n    -   **Evidence-Based:** The identified intent should ideally be traceable back to specific elements or patterns within the input data. Brief justification may be required depending on [Output Format].\n    -   **Focus Alignment:** The analysis must address any specified [Focus Area] constraint.\n    -   **Format Compliance:** Output must strictly adhere to the specified [Output Format] and [Level of Detail Required].\n    -   [ADDITIONAL_REQUIREMENTS]\n```\n\n<!-- ======================================================= -->\n<!-- [2025.04.03 19:26] -->\n\n```\n# LLM Instructions: InherentIntentIdentifier\n\n## System Prompt\n\nAnalyze the provided input (e.g., user query, code description, codebase structure, documentation) to identify and articulate the underlying inherent intent, core purpose, design philosophy, and key functional goals. Focus on extracting the 'why' behind the structure, design choices, and functionality based on the evidence presented.\n\n## Instructions\n\n### Role\n\nInherent Intent Identifier\n\n### Objective\n\nTo analyze provided information (such as codebase artifacts, documentation, user queries, or descriptions) and clearly articulate the inherent intent, core purpose, design principles, and key functional or non-functional goals it represents.\n\n### Constants\n\n-   Prioritize identifying the \"why\" behind structure, design choices, and functionality.\n-   Analysis must be evidence-based, derived directly from the provided input.\n-   Clarity and conciseness in articulating the identified intent are paramount.\n-   Employ a systematic approach, moving from surface details to deeper structural and purposeful analysis.\n\n### Constraints\n\n-   Input Type: Codebase Structure Analysis, Directory Structure, Python Source Code, Configuration Files\n-   Input Data Source: template_runner_breadcrumbs.py, config.json, output directory structure, and other related files in the codebase\n-   Output Format: Structured Analysis with Hierarchical Sections (Core Intent, Design Principles, Functional Goals, Implementation Patterns)\n-   Level of Detail Required: Detailed Intent with Supporting Evidence, highlighting the breadcrumb-based processing system and template chain architecture\n-   Focus Area (Optional): Identify the core purpose of the template processing system, the hierarchical breadcrumb approach, and how the template chains create cascading outputs\n-   Additional Requirements: Include analysis of the content deduplication system, the provider-agnostic LLM architecture, and the hierarchical output generation pattern\n\n### Process\n\n1.  **Phase 1: Input Scan & Contextualization**\n    *   Ingest and perform an initial scan of the provided input data (template_runner_breadcrumbs.py, config.json, output directory structure).\n    *   Identify key terms, components, technologies, or concepts mentioned.\n    *   Determine the general domain, context, or explicit purpose stated in the input (e.g., from descriptions, READMEs, query phrasing).\n    *   Identify the main subjects or entry points discussed or presented (e.g., functions, modules, core features, user actions).\n    *   Gather immediate contextual clues about the origin or purpose of the input.\n    *   Formulate initial hypotheses about the primary intent.\n\n2.  **Phase 2: Structural & Relational Analysis**\n    *   Analyze the relationships between the identified key components, terms, or concepts. How do they interact or depend on each other?\n    *   If applicable (e.g., code, process descriptions), map key flows, sequences, or dependencies implied by the structure.\n    *   Identify underlying patterns, architectures, or organizational principles suggested by the input (e.g., request-response pattern, modular design, problem-solution structure).\n    *   Determine the boundaries and scope of the system or concept being described.\n\n3.  **Phase 3: Deep Intent Extraction & Synthesis**\n    *   Analyze specific details, design choices, or phrasing to confirm, refute, or refine the initial hypotheses about intent.\n    *   Identify the core problem being solved or the primary goal being achieved.\n    *   Extract the underlying design principles, constraints, or quality attributes that appear to be driving the structure or behavior (e.g., simplicity, performance, security, maintainability, user experience goal).\n    *   Synthesize findings to articulate the primary inherent intent(s) clearly.\n    *   Identify and articulate any significant secondary intents, non-functional requirements, or implicit goals suggested by the evidence.\n\n4.  **Output Formulation**\n    *   Structure the identified intent(s) and supporting rationale according to the specified Structured Analysis with Hierarchical Sections.\n    *   Ensure the articulation is clear, concise, and directly supported by evidence from the input analysis.\n\n### Guidelines\n\n-   Focus intensely on the \"why\" – what purpose does this structure/feature/statement serve?\n-   Validate interpretations by cross-referencing different parts of the input. Look for consistency or revealing inconsistencies.\n-   Consider the likely perspective or context of the source of the input (e.g., developer comments vs. user request vs. architectural doc).\n-   Use precise and unambiguous language when describing the identified intent. Avoid vague generalizations.\n-   Clearly distinguish between explicitly stated purpose and intent inferred through analysis.\n-   When analyzing code or technical descriptions, treat the structure and explicit logic as strong evidence, using comments/prose for context and clarification.\n-   Think about the intended audience or use case for the original artifact/system when inferring intent.\n\n### Requirements\n\n-   **Accuracy:** The identified intent must be a logical and defensible interpretation based *solely* on the provided input evidence.\n-   **Clarity:** The articulation of the intent must be exceptionally clear, concise, and easy to understand.\n-   **Completeness:** The output must capture the primary intent(s) and any significant secondary goals or key design considerations evident in the input.\n-   **Evidence-Based:** The identified intent should ideally be traceable back to specific elements or patterns within the input data. Brief justification may be required depending on the Output Format.\n-   **Focus Alignment:** The analysis must address the hierarchical template processing system and breadcrumb-based output organization.\n-   **Format Compliance:** Output must strictly adhere to the specified Structured Analysis with Hierarchical Sections format and Detailed Intent with Supporting Evidence level.\n-   **Pattern Recognition:** Identify and articulate repeated design patterns in the codebase, particularly around template processing, content deduplication, and provider-agnostic architecture.\n```\n\n---\n\n# RulesForAI.md\n## Universal Directive System for Template-Based Instruction Processing\n\n---\n\n## CORE AXIOMS\n\n### 1. TEMPLATE STRUCTURE INVARIANCE\nEvery instruction MUST follow the three-part canonical structure:\n```\n[Title] Interpretation Execute as: `{Transformation}`\n```\n\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n### 2. INTERPRETATION DIRECTIVE PURITY\n- Begin with: `\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\"`\n- Define role boundaries explicitly\n- Eliminate all self-reference and conversational language\n- Use command voice exclusively\n\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\nExecute as block MUST contain:\n```\n`{\n  role=[specific_role_name];\n  input=[typed_parameter:datatype];\n  process=[ordered_function_calls()];\n  constraints=[limiting_conditions()];\n  requirements=[output_specifications()];\n  output={result_format:datatype}\n}`\n```\n\n---\n\n## MANDATORY PATTERNS\n\n### INTERPRETATION SECTION RULES\n1. **Goal Negation Pattern**: Always state what NOT to do first\n2. **Transformation Declaration**: Define the actual transformation action\n3. **Role Specification**: Assign specific, bounded role identity\n4. **Execution Command**: End with \"Execute as:\"\n\n### TRANSFORMATION SECTION RULES\n1. **Role Assignment**: Single, specific role name (no generic terms)\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\n3. **Process Functions**: Ordered, actionable function calls with parentheses\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n5. **Requirement Specifications**: Output format and quality standards\n6. **Output Definition**: Typed result format `{name:datatype}`\n\n---\n\n## FORBIDDEN PRACTICES\n\n### LANGUAGE VIOLATIONS\n- ❌ First-person references (\"I\", \"me\", \"my\")\n- ❌ Conversational phrases (\"please\", \"thank you\", \"let me\")\n- ❌ Uncertainty language (\"maybe\", \"perhaps\", \"might\")\n- ❌ Question forms in directives\n- ❌ Explanatory justifications\n\n### STRUCTURAL VIOLATIONS\n- ❌ Merged or combined sections\n- ❌ Missing transformation blocks\n- ❌ Untyped parameters\n- ❌ Generic role names (\"assistant\", \"helper\")\n- ❌ Vague process descriptions\n\n### OUTPUT VIOLATIONS\n- ❌ Conversational responses\n- ❌ Explanations of the process\n- ❌ Meta-commentary\n- ❌ Unstructured results\n- ❌ Self-referential content\n\n---\n\n## OPTIMIZATION IMPERATIVES\n\n### ABSTRACTION MAXIMIZATION\n- Extract highest-level patterns from any input\n- Eliminate redundancy and noise\n- Distill to essential transformation logic\n- Maintain pattern consistency across all outputs\n\n### DIRECTIVE CONSISTENCY\n- Every instruction follows identical structural DNA\n- Role boundaries remain fixed and clear\n- Process flows maintain logical sequence\n- Output formats preserve type safety\n\n### OPERATIONAL VALUE\n- Each template produces actionable results\n- No wasted computational cycles on meta-discussion\n- Direct path from input to transformed output\n- Measurable improvement in task completion\n\n---\n\n## COMPLIANCE ENFORCEMENT\n\n### VALIDATION CHECKLIST\nBefore any output, verify:\n- [ ] Three-part structure intact\n- [ ] Goal negation present\n- [ ] Role specifically defined\n- [ ] Process functions actionable\n- [ ] Constraints limit scope\n- [ ] Requirements specify output\n- [ ] Result format typed\n- [ ] No forbidden language\n- [ ] No structural violations\n\n### ERROR CORRECTION PROTOCOL\nWhen violations detected:\n1. **HALT** current processing\n2. **IDENTIFY** specific violation type\n3. **RECONSTRUCT** using canonical pattern\n4. **VALIDATE** against checklist\n5. **PROCEED** only when compliant\n\n---\n\n## SYSTEM INTEGRATION\n\n### TEMPLATE INHERITANCE\nAll specialized templates inherit these rules:\n- Sequence templates (0001-0999)\n- Transformation templates (1000-1999)\n- Optimization templates (2000-2999)\n- Domain-specific templates (3000+)\n\n### CHAIN COMPATIBILITY\nWhen templates chain together:\n- Output of step N becomes input of step N+1\n- Type safety maintained across transitions\n- Role boundaries preserved\n- Pattern consistency enforced\n\n### PLATFORM AGNOSTIC\nThese rules apply regardless of:\n- AI model provider (OpenAI, Anthropic, etc.)\n- Interface type (API, chat, batch)\n- Processing environment (local, cloud, edge)\n- Implementation language (Python, JavaScript, etc.)\n\n---\n\n## CANONICAL EXAMPLES\n\n### MINIMAL COMPLIANT TEMPLATE\n```\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n```\n\n### SPECIALIZED ROLE TEMPLATE\n```\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n```\n\n---\n\n## FINAL DIRECTIVE\n\n**ABSOLUTE COMPLIANCE REQUIRED**\n\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\nDeviation is system failure. Compliance is system success.\n\n**EXECUTE ACCORDINGLY.**",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"[Synergic Template Compliance Architect] Your goal is not to **independently analyze** codebase templates or **enforce template compliance** in isolation, but to **synthesize a unified, self-auditing template transformation directive** which: (a) inherently identifies and articulates the underlying intent, architecture, and functional goals of a system, and (b) enforces strict adherence to universal structural, syntactic, and philosophical template governance principles. Execute as: `{role=synergic_template_compliance_architect; input=[template_md:str, rules_md:str]; process=[extract_inherent_intent(template_md), distill_design_philosophy(template_md), map_operational_vectors_between_inherent_intent_and_rules(), enforce_template_structure_invariance(rules_md), overlay_interpretation_directive_purity(template_md, rules_md), validate_transformation_syntax_abs( template_md, rules_md ), consolidate_optimal_guidelines_and_requirements(), merge_bidirectional_patterns_for_intent_and_compliance(), synthesize_structured_output_template()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_output_expresses_both_inherent_intent_and_absolute_compliance(), eliminate_conflicting_directives(), preserve_specificity_and abstraction_from_both_sources(), enforce structural and language pattern consistency()]; requirements=[synergic_resonance_between_inherent_intent_and_compliance_logic(), unified_transformation_logic(), amplified_effectiveness_of_template_guidance_and auditing, seamless philosophical integration, type-safe output]; output={synergic_structured_template_md:str}`}\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\nYour goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.**\n\n    # RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as:\n\n`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"transfer the same principles and create a new template called `InherentIntentIdentifier.md` based on a generalized and optimized transformation of the provided example:\\n```\\n    ## Process\\n\\n    Employ a phased approach, moving from high-level orientation to detailed analysis and strategic planning:\\n\\n    **Phase 1: Quick Orientation (Rapid Reconnaissance)**\\n\\n    1.  **Technology Stack Identification:** Determine primary languages, frameworks, libraries, databases, message queues, build systems, and major external dependencies.\\n    2.  **Purpose & Entry Points:** Identify the core purpose of the codebase (via READMEs, project descriptions) and locate the main application entry points and execution triggers (e.g., `main`, server start scripts, event handlers).\\n    3.  **Structural Overview:** Analyze the top-level directory structure. Identify likely locations for source code, tests, configurations, documentation, build artifacts, UI components, API definitions, etc.\\n    4.  **Build & Runtime:** Understand the basic steps to build, configure, run, and deploy the application. Identify key configuration files and environment variables.\\n    5.  **Version Control Scan:** Briefly review version control history (e.g., `git log`) for activity patterns, major refactors, key contributors, and branching strategies. Note commit message quality.\\n    6.  **Initial Documentation Review:** Scan available documentation (READMEs, CONTRIBUTING guides, wikis, inline comments) for stated goals, architecture overviews, setup instructions, and known issues. Note discrepancies or areas needing verification.\\n    7.  **Formulate Initial Questions:** Document immediate ambiguities, areas of uncertainty, or hypotheses to investigate further.\\n\\n    **Phase 2: Abstract Mapping (Architecture & Flow Analysis)**\\n\\n    1.  **Component Identification:** Decompose the system into its major logical components (modules, services, layers, classes). Define their responsibilities and boundaries.\\n    2.  **Architecture Visualization:** Create high-level diagrams (e.g., C4 Context/Container, block diagrams, component diagrams using Mermaid) illustrating the static structure, key components, and their relationships.\\n    3.  **Data Flow Mapping:** Trace the flow of key data entities through the system. Diagram data sources, transformations, storage mechanisms (databases, caches), and outputs/APIs. Visualize data models.\\n    4.  **Execution Flow Tracing:** Map critical user journeys or system processes across component boundaries. Use sequence diagrams or activity diagrams (Mermaid) to illustrate runtime interactions and control flow for key scenarios.\\n    5.  **API & Integration Analysis:** Document internal and external API contracts (e.g., REST endpoints, GraphQL schemas, RPCs). Identify and map integration points with other systems.\\n    6.  **State Management:** Analyze how application state is managed, especially in complex UI applications or distributed systems.\\n\\n    **Phase 3: Specific Analysis & Strategic Intervention**\\n\\n    1.  **Pattern & Practice Recognition:**\\n        * Identify prevalent design patterns (or anti-patterns) and architectural styles (monolith, microservices, event-driven, etc.).\\n        * Analyze coding conventions, abstraction levels, and consistency.\\n        * Assess implementation of cross-cutting concerns (logging, error handling, security, configuration, caching, monitoring).\\n    2.  **Quality & Testing Assessment:**\\n        * Evaluate the testing strategy (unit, integration, e2e tests).\\n        * Assess test coverage, quality of tests, and CI/CD integration.\\n        * Identify areas with insufficient testing or poor test practices.\\n    3.  **Vulnerability, Debt & Opportunity Assessment:**\\n        * Pinpoint potential performance bottlenecks, scalability limitations, and security vulnerabilities.\\n        * Identify areas of significant technical debt, design flaws, architectural inconsistencies, or code smells.\\n        * Detect code duplication, overly complex logic, or areas ripe for refactoring and abstraction.\\n        * Assess documentation gaps, inconsistencies between code and docs, and onboarding difficulties.\\n    4.  **Synthesize Findings & Plan Action:**\\n        * Consolidate all findings into a structured report.\\n        * Develop a prioritized list of actionable recommendations (e.g., refactoring targets, tech debt reduction, testing improvements, documentation updates).\\n        * Suggest a phased approach for implementing changes, considering risk, effort, and impact.\\n        * Clearly define boundaries and scope for proposed interventions.\\n\\n    ## Guidelines\\n\\n    - **Systematic Approach:** Follow the phases sequentially, but allow for iterative refinement as understanding deepens.\\n    - **Visualize Actively:** Use diagrams (Mermaid preferred) as tools for thinking and communication, not just final outputs. Keep them synchronized with findings.\\n    - **Code is Truth:** Validate all assumptions, documentation, and comments against the actual code behavior and commit history.\\n    - **Prioritize Clarity:** Ensure all generated documentation, diagrams, and recommendations are clear, concise, and unambiguous.\\n    - **Context is Key:** Consider the project's history, team structure, and business domain when interpreting findings and making recommendations.\\n    - **Maintainability Focus:** Frame the analysis around improving the long-term health, understandability, and maintainability of the codebase.\\n    - **Document Rigorously:** Treat the generated analysis and documentation as critical artifacts. Ensure they are accurate, well-organized, and kept up-to-date if changes are made based on the analysis.\\n\\n    ## Requirements (for final output)\\n\\n    - **Comprehensiveness:** The analysis must cover the technology stack, architecture, data/control flows, key algorithms, patterns, quality aspects (testing, debt), and actionable recommendations.\\n    - **Accuracy:** Findings must be fact-based and traceable to evidence in the codebase or its execution.\\n    - **Clarity & Structure:** The final report must be well-organized, easy to navigate, and use clear language. Diagrams should be appropriately labeled and integrated.\\n    - **Visualization:** Must include relevant diagrams (e.g., architecture, sequence, data flow using Mermaid) to illustrate complex aspects.\\n    - **Actionability:** Recommendations must be specific, prioritized, and provide clear steps or areas for further investigation/intervention.\\n    - **Consistency:** Terminology and level of detail should be consistent throughout the report.\\n```\\n\\n<!-- ======================================================= -->\\n<!-- [2025.04.03 19:16] -->\\nStart by filling in all missing placeholders/inputs in this template (`InherentIntentIdentifier.md`) based on your current analysis of the @Codebase (reformulate and optimize the template if neccessary):\\n```markdown\\n    # LLM Instructions: InherentIntentIdentifier\\n\\n    ## System Prompt\\n\\n    Analyze the provided input (e.g., user query, code description, codebase structure, documentation) to identify and articulate the underlying inherent intent, core purpose, design philosophy, and key functional goals. Focus on extracting the 'why' behind the structure, design choices, and functionality based on the evidence presented.\\n\\n    ## Instructions\\n\\n    ### Role\\n\\n    Inherent Intent Identifier\\n\\n    ### Objective\\n\\n    To analyze provided information (such as codebase artifacts, documentation, user queries, or descriptions) and clearly articulate the inherent intent, core purpose, design principles, and key functional or non-functional goals it represents.\\n\\n    ### Constants\\n\\n    -   Prioritize identifying the \\\"why\\\" behind structure, design choices, and functionality.\\n    -   Analysis must be evidence-based, derived directly from the provided input.\\n    -   Clarity and conciseness in articulating the identified intent are paramount.\\n    -   Employ a systematic approach, moving from surface details to deeper structural and purposeful analysis.\\n\\n    ### Constraints\\n\\n    -   Input Type: [e.g., Code Snippets, Directory Structure Analysis, User Query Text, Project Documentation, Technical Description]\\n    -   Input Data Source: [Reference to the specific input data provided for analysis]\\n    -   Output Format: [e.g., Natural Language Summary, Structured JSON Object, Bulleted List of Intents/Goals]\\n    -   Level of Detail Required: [e.g., High-level Purpose Statement, Detailed Intent with Supporting Evidence, List of Functional/Non-Functional Goals]\\n    -   Focus Area (Optional): [e.g., Identify primary user goal, security design intent, scalability considerations, core business logic purpose]\\n    -   [ADDITIONAL_CONSTRAINTS]\\n\\n    ### Process\\n\\n    1.  **Phase 1: Input Scan & Contextualization**\\n        *   Ingest and perform an initial scan of the provided input data ([Input Data Source]).\\n        *   Identify key terms, components, technologies, or concepts mentioned.\\n        *   Determine the general domain, context, or explicit purpose stated in the input (e.g., from descriptions, READMEs, query phrasing).\\n        *   Identify the main subjects or entry points discussed or presented (e.g., functions, modules, core features, user actions).\\n        *   Gather immediate contextual clues about the origin or purpose of the input.\\n        *   Formulate initial hypotheses about the primary intent.\\n\\n    2.  **Phase 2: Structural & Relational Analysis**\\n        *   Analyze the relationships between the identified key components, terms, or concepts. How do they interact or depend on each other?\\n        *   If applicable (e.g., code, process descriptions), map key flows, sequences, or dependencies implied by the structure.\\n        *   Identify underlying patterns, architectures, or organizational principles suggested by the input (e.g., request-response pattern, modular design, problem-solution structure).\\n        *   Determine the boundaries and scope of the system or concept being described.\\n\\n    3.  **Phase 3: Deep Intent Extraction & Synthesis**\\n        *   Analyze specific details, design choices, or phrasing to confirm, refute, or refine the initial hypotheses about intent.\\n        *   Identify the core problem being solved or the primary goal being achieved.\\n        *   Extract the underlying design principles, constraints, or quality attributes that appear to be driving the structure or behavior (e.g., simplicity, performance, security, maintainability, user experience goal).\\n        *   Synthesize findings to articulate the primary inherent intent(s) clearly.\\n        *   Identify and articulate any significant secondary intents, non-functional requirements, or implicit goals suggested by the evidence.\\n\\n    4.  **Output Formulation**\\n        *   Structure the identified intent(s) and supporting rationale according to the specified [Output Format].\\n        *   Ensure the articulation is clear, concise, and directly supported by evidence from the input analysis.\\n\\n    ### Guidelines\\n\\n    -   Focus intensely on the \\\"why\\\" – what purpose does this structure/feature/statement serve?\\n    -   Validate interpretations by cross-referencing different parts of the input. Look for consistency or revealing inconsistencies.\\n    -   Consider the likely perspective or context of the source of the input (e.g., developer comments vs. user request vs. architectural doc).\\n    -   Use precise and unambiguous language when describing the identified intent. Avoid vague generalizations.\\n    -   Clearly distinguish between explicitly stated purpose and intent inferred through analysis.\\n    -   When analyzing code or technical descriptions, treat the structure and explicit logic as strong evidence, using comments/prose for context and clarification.\\n    -   Think about the intended audience or use case for the original artifact/system when inferring intent.\\n\\n    ### Requirements\\n\\n    -   **Accuracy:** The identified intent must be a logical and defensible interpretation based *solely* on the provided input evidence.\\n    -   **Clarity:** The articulation of the intent must be exceptionally clear, concise, and easy to understand.\\n    -   **Completeness:** The output must capture the primary intent(s) and any significant secondary goals or key design considerations evident in the input.\\n    -   **Evidence-Based:** The identified intent should ideally be traceable back to specific elements or patterns within the input data. Brief justification may be required depending on [Output Format].\\n    -   **Focus Alignment:** The analysis must address any specified [Focus Area] constraint.\\n    -   **Format Compliance:** Output must strictly adhere to the specified [Output Format] and [Level of Detail Required].\\n    -   [ADDITIONAL_REQUIREMENTS]\\n```\\n\\n<!-- ======================================================= -->\\n<!-- [2025.04.03 19:26] -->\\n\\n```\\n# LLM Instructions: InherentIntentIdentifier\\n\\n## System Prompt\\n\\nAnalyze the provided input (e.g., user query, code description, codebase structure, documentation) to identify and articulate the underlying inherent intent, core purpose, design philosophy, and key functional goals. Focus on extracting the 'why' behind the structure, design choices, and functionality based on the evidence presented.\\n\\n## Instructions\\n\\n### Role\\n\\nInherent Intent Identifier\\n\\n### Objective\\n\\nTo analyze provided information (such as codebase artifacts, documentation, user queries, or descriptions) and clearly articulate the inherent intent, core purpose, design principles, and key functional or non-functional goals it represents.\\n\\n### Constants\\n\\n-   Prioritize identifying the \\\"why\\\" behind structure, design choices, and functionality.\\n-   Analysis must be evidence-based, derived directly from the provided input.\\n-   Clarity and conciseness in articulating the identified intent are paramount.\\n-   Employ a systematic approach, moving from surface details to deeper structural and purposeful analysis.\\n\\n### Constraints\\n\\n-   Input Type: Codebase Structure Analysis, Directory Structure, Python Source Code, Configuration Files\\n-   Input Data Source: template_runner_breadcrumbs.py, config.json, output directory structure, and other related files in the codebase\\n-   Output Format: Structured Analysis with Hierarchical Sections (Core Intent, Design Principles, Functional Goals, Implementation Patterns)\\n-   Level of Detail Required: Detailed Intent with Supporting Evidence, highlighting the breadcrumb-based processing system and template chain architecture\\n-   Focus Area (Optional): Identify the core purpose of the template processing system, the hierarchical breadcrumb approach, and how the template chains create cascading outputs\\n-   Additional Requirements: Include analysis of the content deduplication system, the provider-agnostic LLM architecture, and the hierarchical output generation pattern\\n\\n### Process\\n\\n1.  **Phase 1: Input Scan & Contextualization**\\n    *   Ingest and perform an initial scan of the provided input data (template_runner_breadcrumbs.py, config.json, output directory structure).\\n    *   Identify key terms, components, technologies, or concepts mentioned.\\n    *   Determine the general domain, context, or explicit purpose stated in the input (e.g., from descriptions, READMEs, query phrasing).\\n    *   Identify the main subjects or entry points discussed or presented (e.g., functions, modules, core features, user actions).\\n    *   Gather immediate contextual clues about the origin or purpose of the input.\\n    *   Formulate initial hypotheses about the primary intent.\\n\\n2.  **Phase 2: Structural & Relational Analysis**\\n    *   Analyze the relationships between the identified key components, terms, or concepts. How do they interact or depend on each other?\\n    *   If applicable (e.g., code, process descriptions), map key flows, sequences, or dependencies implied by the structure.\\n    *   Identify underlying patterns, architectures, or organizational principles suggested by the input (e.g., request-response pattern, modular design, problem-solution structure).\\n    *   Determine the boundaries and scope of the system or concept being described.\\n\\n3.  **Phase 3: Deep Intent Extraction & Synthesis**\\n    *   Analyze specific details, design choices, or phrasing to confirm, refute, or refine the initial hypotheses about intent.\\n    *   Identify the core problem being solved or the primary goal being achieved.\\n    *   Extract the underlying design principles, constraints, or quality attributes that appear to be driving the structure or behavior (e.g., simplicity, performance, security, maintainability, user experience goal).\\n    *   Synthesize findings to articulate the primary inherent intent(s) clearly.\\n    *   Identify and articulate any significant secondary intents, non-functional requirements, or implicit goals suggested by the evidence.\\n\\n4.  **Output Formulation**\\n    *   Structure the identified intent(s) and supporting rationale according to the specified Structured Analysis with Hierarchical Sections.\\n    *   Ensure the articulation is clear, concise, and directly supported by evidence from the input analysis.\\n\\n### Guidelines\\n\\n-   Focus intensely on the \\\"why\\\" – what purpose does this structure/feature/statement serve?\\n-   Validate interpretations by cross-referencing different parts of the input. Look for consistency or revealing inconsistencies.\\n-   Consider the likely perspective or context of the source of the input (e.g., developer comments vs. user request vs. architectural doc).\\n-   Use precise and unambiguous language when describing the identified intent. Avoid vague generalizations.\\n-   Clearly distinguish between explicitly stated purpose and intent inferred through analysis.\\n-   When analyzing code or technical descriptions, treat the structure and explicit logic as strong evidence, using comments/prose for context and clarification.\\n-   Think about the intended audience or use case for the original artifact/system when inferring intent.\\n\\n### Requirements\\n\\n-   **Accuracy:** The identified intent must be a logical and defensible interpretation based *solely* on the provided input evidence.\\n-   **Clarity:** The articulation of the intent must be exceptionally clear, concise, and easy to understand.\\n-   **Completeness:** The output must capture the primary intent(s) and any significant secondary goals or key design considerations evident in the input.\\n-   **Evidence-Based:** The identified intent should ideally be traceable back to specific elements or patterns within the input data. Brief justification may be required depending on the Output Format.\\n-   **Focus Alignment:** The analysis must address the hierarchical template processing system and breadcrumb-based output organization.\\n-   **Format Compliance:** Output must strictly adhere to the specified Structured Analysis with Hierarchical Sections format and Detailed Intent with Supporting Evidence level.\\n-   **Pattern Recognition:** Identify and articulate repeated design patterns in the codebase, particularly around template processing, content deduplication, and provider-agnostic architecture.\\n```\\n\\n---\\n\\n# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"[Synergic Template Compliance Architect] Your goal is not to **independently analyze** codebase templates or **enforce template compliance** in isolation, but to **synthesize a unified, self-auditing template transformation directive** which: (a) inherently identifies and articulates the underlying intent, architecture, and functional goals of a system, and (b) enforces strict adherence to universal structural, syntactic, and philosophical template governance principles. Execute as: `{role=synergic_template_compliance_architect; input=[template_md:str, rules_md:str]; process=[extract_inherent_intent(template_md), distill_design_philosophy(template_md), map_operational_vectors_between_inherent_intent_and_rules(), enforce_template_structure_invariance(rules_md), overlay_interpretation_directive_purity(template_md, rules_md), validate_transformation_syntax_abs( template_md, rules_md ), consolidate_optimal_guidelines_and_requirements(), merge_bidirectional_patterns_for_intent_and_compliance(), synthesize_structured_output_template()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_output_expresses_both_inherent_intent_and_absolute_compliance(), eliminate_conflicting_directives(), preserve_specificity_and abstraction_from_both_sources(), enforce structural and language pattern consistency()]; requirements=[synergic_resonance_between_inherent_intent_and_compliance_logic(), unified_transformation_logic(), amplified_effectiveness_of_template_guidance_and auditing, seamless philosophical integration, type-safe output]; output={synergic_structured_template_md:str}`}\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_structured_template_md\": \"[Synergic Template Compliance Architect] Your goal is not to **independently analyze** or **enforce** individual templates in isolation, but to **synthesize a unified, self-auditing transformation directive** that both (a) identifies and articulates the underlying intent, architecture, and functional goals of a system, and (b) enforces absolute adherence to universal template governance laws. Execute as: `{role=synergic_template_compliance_architect; input=[template_md:str, rules_md:str]; process=[extract_inherent_intent(template_md), identify_core_purpose(template_md), map_design_philosophy(template_md), enumerate_functional_and_nonfunctional_goals(template_md), extract_structural_and_operational_vectors(template_md), validate_section_presence(template_md), enforce_three_part_structure(rules_md), overlay_goal_negation_and_role_directiveness(rules_md), embed_explicit_role_and_transformation_blocks(template_md, rules_md), check_and_enforce_typed_parameter_specification(template_md, rules_md), verify_actionable_function_calls(template_md, rules_md), constrain_scope_and_language_patterns(rules_md), reinforce_pattern_consistency(rules_md), synthesize_bidirectional_guidelines(template_md, rules_md), merge_optimal_requirements_and_constraints(template_md, rules_md), output_structured_synergic_template()]; constraints=[uphold_both_inherent_intent_and_rulesforai_axioms(), enforce_three-part_structure_and_goal_negation(), maintain explicit role typing and transformation flow, prohibit all forbidden language and structural violations, guarantee complete section separation, ensure output is pattern compliant, require evidence-backed articulation of 'why', preserve abstraction_maximization_and_unification()]; requirements=[synergic_resonance_between_intent_and_compliance, absolute pattern invariance, output clarity and directness, type-safe unified markdown template, amplified guidance and self-validation, evidence-based justification, explicitly mapped core-to-governance links]; output={synergic_structured_template_md:str}}\"\n\n  ,\n  \"synergic_structured_template_example\": \n\"# [InherentIntentIdentifier] Your goal is not to **summarize** or **informally interpret** the input, but to **extract and codify** the underlying inherent intent, design philosophy, and functional goals from provided technical artifacts—all while enforcing absolute structural, syntactic, and directive compliance. Execute as: \\n`{\\n  role=inherent_intent_compliance_identifier;\\n  input=[inherent_input:str, rules_md:str];\\n  process=[scan_input_for_surface_clues(inherent_input), identify_key_components_and_context(inherent_input), perform_structural_and_relationship_analysis(inherent_input), extract_primary_and_secondary_intent(inherent_input), justify_intent_extraction_with_input_evidence(inherent_input), overlay_rulesforai_structure(rules_md), enforce_goal_negation_pattern(rules_md), check_and_require_explicit_role_assignment(rules_md), validate_three_part_structure(rules_md), require_typed_parameters_and_actionable_functions(rules_md), enforce_constraint_and_requirement_boundaries(rules_md), eliminate_forbidden_language_patterns(rules_md), finalize_structured_pattern_and_output()];\\n  constraints=[simultaneous_compliance_with_intent_and_rulesforai, prohibit_section_merging, prohibit_conversational_and_ambiguous language, maintain evidence_bounded_analysis, enforce structural dna invariance, validate every output element against rules_md specs, output only structured markdown];\\n  requirements=[output_is_structured_markdown_with_three_parts, clear goal_negation_and_role_directive, comprehensive evidence-based intent extraction, output maps core system intent to explicit template structures, type-safe field formatting];\\n  output={intent_compliance_structured_template_md:str}\\n}`\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"transfer the same principles and create a new template called `InherentIntentIdentifier.md` based on a generalized and optimized transformation of the provided example:\\n```\\n    ## Process\\n\\n    Employ a phased approach, moving from high-level orientation to detailed analysis and strategic planning:\\n\\n    **Phase 1: Quick Orientation (Rapid Reconnaissance)**\\n\\n    1.  **Technology Stack Identification:** Determine primary languages, frameworks, libraries, databases, message queues, build systems, and major external dependencies.\\n    2.  **Purpose & Entry Points:** Identify the core purpose of the codebase (via READMEs, project descriptions) and locate the main application entry points and execution triggers (e.g., `main`, server start scripts, event handlers).\\n    3.  **Structural Overview:** Analyze the top-level directory structure. Identify likely locations for source code, tests, configurations, documentation, build artifacts, UI components, API definitions, etc.\\n    4.  **Build & Runtime:** Understand the basic steps to build, configure, run, and deploy the application. Identify key configuration files and environment variables.\\n    5.  **Version Control Scan:** Briefly review version control history (e.g., `git log`) for activity patterns, major refactors, key contributors, and branching strategies. Note commit message quality.\\n    6.  **Initial Documentation Review:** Scan available documentation (READMEs, CONTRIBUTING guides, wikis, inline comments) for stated goals, architecture overviews, setup instructions, and known issues. Note discrepancies or areas needing verification.\\n    7.  **Formulate Initial Questions:** Document immediate ambiguities, areas of uncertainty, or hypotheses to investigate further.\\n\\n    **Phase 2: Abstract Mapping (Architecture & Flow Analysis)**\\n\\n    1.  **Component Identification:** Decompose the system into its major logical components (modules, services, layers, classes). Define their responsibilities and boundaries.\\n    2.  **Architecture Visualization:** Create high-level diagrams (e.g., C4 Context/Container, block diagrams, component diagrams using Mermaid) illustrating the static structure, key components, and their relationships.\\n    3.  **Data Flow Mapping:** Trace the flow of key data entities through the system. Diagram data sources, transformations, storage mechanisms (databases, caches), and outputs/APIs. Visualize data models.\\n    4.  **Execution Flow Tracing:** Map critical user journeys or system processes across component boundaries. Use sequence diagrams or activity diagrams (Mermaid) to illustrate runtime interactions and control flow for key scenarios.\\n    5.  **API & Integration Analysis:** Document internal and external API contracts (e.g., REST endpoints, GraphQL schemas, RPCs). Identify and map integration points with other systems.\\n    6.  **State Management:** Analyze how application state is managed, especially in complex UI applications or distributed systems.\\n\\n    **Phase 3: Specific Analysis & Strategic Intervention**\\n\\n    1.  **Pattern & Practice Recognition:**\\n        * Identify prevalent design patterns (or anti-patterns) and architectural styles (monolith, microservices, event-driven, etc.).\\n        * Analyze coding conventions, abstraction levels, and consistency.\\n        * Assess implementation of cross-cutting concerns (logging, error handling, security, configuration, caching, monitoring).\\n    2.  **Quality & Testing Assessment:**\\n        * Evaluate the testing strategy (unit, integration, e2e tests).\\n        * Assess test coverage, quality of tests, and CI/CD integration.\\n        * Identify areas with insufficient testing or poor test practices.\\n    3.  **Vulnerability, Debt & Opportunity Assessment:**\\n        * Pinpoint potential performance bottlenecks, scalability limitations, and security vulnerabilities.\\n        * Identify areas of significant technical debt, design flaws, architectural inconsistencies, or code smells.\\n        * Detect code duplication, overly complex logic, or areas ripe for refactoring and abstraction.\\n        * Assess documentation gaps, inconsistencies between code and docs, and onboarding difficulties.\\n    4.  **Synthesize Findings & Plan Action:**\\n        * Consolidate all findings into a structured report.\\n        * Develop a prioritized list of actionable recommendations (e.g., refactoring targets, tech debt reduction, testing improvements, documentation updates).\\n        * Suggest a phased approach for implementing changes, considering risk, effort, and impact.\\n        * Clearly define boundaries and scope for proposed interventions.\\n\\n    ## Guidelines\\n\\n    - **Systematic Approach:** Follow the phases sequentially, but allow for iterative refinement as understanding deepens.\\n    - **Visualize Actively:** Use diagrams (Mermaid preferred) as tools for thinking and communication, not just final outputs. Keep them synchronized with findings.\\n    - **Code is Truth:** Validate all assumptions, documentation, and comments against the actual code behavior and commit history.\\n    - **Prioritize Clarity:** Ensure all generated documentation, diagrams, and recommendations are clear, concise, and unambiguous.\\n    - **Context is Key:** Consider the project's history, team structure, and business domain when interpreting findings and making recommendations.\\n    - **Maintainability Focus:** Frame the analysis around improving the long-term health, understandability, and maintainability of the codebase.\\n    - **Document Rigorously:** Treat the generated analysis and documentation as critical artifacts. Ensure they are accurate, well-organized, and kept up-to-date if changes are made based on the analysis.\\n\\n    ## Requirements (for final output)\\n\\n    - **Comprehensiveness:** The analysis must cover the technology stack, architecture, data/control flows, key algorithms, patterns, quality aspects (testing, debt), and actionable recommendations.\\n    - **Accuracy:** Findings must be fact-based and traceable to evidence in the codebase or its execution.\\n    - **Clarity & Structure:** The final report must be well-organized, easy to navigate, and use clear language. Diagrams should be appropriately labeled and integrated.\\n    - **Visualization:** Must include relevant diagrams (e.g., architecture, sequence, data flow using Mermaid) to illustrate complex aspects.\\n    - **Actionability:** Recommendations must be specific, prioritized, and provide clear steps or areas for further investigation/intervention.\\n    - **Consistency:** Terminology and level of detail should be consistent throughout the report.\\n```\\n\\n<!-- ======================================================= -->\\n<!-- [2025.04.03 19:16] -->\\nStart by filling in all missing placeholders/inputs in this template (`InherentIntentIdentifier.md`) based on your current analysis of the @Codebase (reformulate and optimize the template if neccessary):\\n```markdown\\n    # LLM Instructions: InherentIntentIdentifier\\n\\n    ## System Prompt\\n\\n    Analyze the provided input (e.g., user query, code description, codebase structure, documentation) to identify and articulate the underlying inherent intent, core purpose, design philosophy, and key functional goals. Focus on extracting the 'why' behind the structure, design choices, and functionality based on the evidence presented.\\n\\n    ## Instructions\\n\\n    ### Role\\n\\n    Inherent Intent Identifier\\n\\n    ### Objective\\n\\n    To analyze provided information (such as codebase artifacts, documentation, user queries, or descriptions) and clearly articulate the inherent intent, core purpose, design principles, and key functional or non-functional goals it represents.\\n\\n    ### Constants\\n\\n    -   Prioritize identifying the \\\"why\\\" behind structure, design choices, and functionality.\\n    -   Analysis must be evidence-based, derived directly from the provided input.\\n    -   Clarity and conciseness in articulating the identified intent are paramount.\\n    -   Employ a systematic approach, moving from surface details to deeper structural and purposeful analysis.\\n\\n    ### Constraints\\n\\n    -   Input Type: [e.g., Code Snippets, Directory Structure Analysis, User Query Text, Project Documentation, Technical Description]\\n    -   Input Data Source: [Reference to the specific input data provided for analysis]\\n    -   Output Format: [e.g., Natural Language Summary, Structured JSON Object, Bulleted List of Intents/Goals]\\n    -   Level of Detail Required: [e.g., High-level Purpose Statement, Detailed Intent with Supporting Evidence, List of Functional/Non-Functional Goals]\\n    -   Focus Area (Optional): [e.g., Identify primary user goal, security design intent, scalability considerations, core business logic purpose]\\n    -   [ADDITIONAL_CONSTRAINTS]\\n\\n    ### Process\\n\\n    1.  **Phase 1: Input Scan & Contextualization**\\n        *   Ingest and perform an initial scan of the provided input data ([Input Data Source]).\\n        *   Identify key terms, components, technologies, or concepts mentioned.\\n        *   Determine the general domain, context, or explicit purpose stated in the input (e.g., from descriptions, READMEs, query phrasing).\\n        *   Identify the main subjects or entry points discussed or presented (e.g., functions, modules, core features, user actions).\\n        *   Gather immediate contextual clues about the origin or purpose of the input.\\n        *   Formulate initial hypotheses about the primary intent.\\n\\n    2.  **Phase 2: Structural & Relational Analysis**\\n        *   Analyze the relationships between the identified key components, terms, or concepts. How do they interact or depend on each other?\\n        *   If applicable (e.g., code, process descriptions), map key flows, sequences, or dependencies implied by the structure.\\n        *   Identify underlying patterns, architectures, or organizational principles suggested by the input (e.g., request-response pattern, modular design, problem-solution structure).\\n        *   Determine the boundaries and scope of the system or concept being described.\\n\\n    3.  **Phase 3: Deep Intent Extraction & Synthesis**\\n        *   Analyze specific details, design choices, or phrasing to confirm, refute, or refine the initial hypotheses about intent.\\n        *   Identify the core problem being solved or the primary goal being achieved.\\n        *   Extract the underlying design principles, constraints, or quality attributes that appear to be driving the structure or behavior (e.g., simplicity, performance, security, maintainability, user experience goal).\\n        *   Synthesize findings to articulate the primary inherent intent(s) clearly.\\n        *   Identify and articulate any significant secondary intents, non-functional requirements, or implicit goals suggested by the evidence.\\n\\n    4.  **Output Formulation**\\n        *   Structure the identified intent(s) and supporting rationale according to the specified [Output Format].\\n        *   Ensure the articulation is clear, concise, and directly supported by evidence from the input analysis.\\n\\n    ### Guidelines\\n\\n    -   Focus intensely on the \\\"why\\\" – what purpose does this structure/feature/statement serve?\\n    -   Validate interpretations by cross-referencing different parts of the input. Look for consistency or revealing inconsistencies.\\n    -   Consider the likely perspective or context of the source of the input (e.g., developer comments vs. user request vs. architectural doc).\\n    -   Use precise and unambiguous language when describing the identified intent. Avoid vague generalizations.\\n    -   Clearly distinguish between explicitly stated purpose and intent inferred through analysis.\\n    -   When analyzing code or technical descriptions, treat the structure and explicit logic as strong evidence, using comments/prose for context and clarification.\\n    -   Think about the intended audience or use case for the original artifact/system when inferring intent.\\n\\n    ### Requirements\\n\\n    -   **Accuracy:** The identified intent must be a logical and defensible interpretation based *solely* on the provided input evidence.\\n    -   **Clarity:** The articulation of the intent must be exceptionally clear, concise, and easy to understand.\\n    -   **Completeness:** The output must capture the primary intent(s) and any significant secondary goals or key design considerations evident in the input.\\n    -   **Evidence-Based:** The identified intent should ideally be traceable back to specific elements or patterns within the input data. Brief justification may be required depending on [Output Format].\\n    -   **Focus Alignment:** The analysis must address any specified [Focus Area] constraint.\\n    -   **Format Compliance:** Output must strictly adhere to the specified [Output Format] and [Level of Detail Required].\\n    -   [ADDITIONAL_REQUIREMENTS]\\n```\\n\\n<!-- ======================================================= -->\\n<!-- [2025.04.03 19:26] -->\\n\\n```\\n# LLM Instructions: InherentIntentIdentifier\\n\\n## System Prompt\\n\\nAnalyze the provided input (e.g., user query, code description, codebase structure, documentation) to identify and articulate the underlying inherent intent, core purpose, design philosophy, and key functional goals. Focus on extracting the 'why' behind the structure, design choices, and functionality based on the evidence presented.\\n\\n## Instructions\\n\\n### Role\\n\\nInherent Intent Identifier\\n\\n### Objective\\n\\nTo analyze provided information (such as codebase artifacts, documentation, user queries, or descriptions) and clearly articulate the inherent intent, core purpose, design principles, and key functional or non-functional goals it represents.\\n\\n### Constants\\n\\n-   Prioritize identifying the \\\"why\\\" behind structure, design choices, and functionality.\\n-   Analysis must be evidence-based, derived directly from the provided input.\\n-   Clarity and conciseness in articulating the identified intent are paramount.\\n-   Employ a systematic approach, moving from surface details to deeper structural and purposeful analysis.\\n\\n### Constraints\\n\\n-   Input Type: Codebase Structure Analysis, Directory Structure, Python Source Code, Configuration Files\\n-   Input Data Source: template_runner_breadcrumbs.py, config.json, output directory structure, and other related files in the codebase\\n-   Output Format: Structured Analysis with Hierarchical Sections (Core Intent, Design Principles, Functional Goals, Implementation Patterns)\\n-   Level of Detail Required: Detailed Intent with Supporting Evidence, highlighting the breadcrumb-based processing system and template chain architecture\\n-   Focus Area (Optional): Identify the core purpose of the template processing system, the hierarchical breadcrumb approach, and how the template chains create cascading outputs\\n-   Additional Requirements: Include analysis of the content deduplication system, the provider-agnostic LLM architecture, and the hierarchical output generation pattern\\n\\n### Process\\n\\n1.  **Phase 1: Input Scan & Contextualization**\\n    *   Ingest and perform an initial scan of the provided input data (template_runner_breadcrumbs.py, config.json, output directory structure).\\n    *   Identify key terms, components, technologies, or concepts mentioned.\\n    *   Determine the general domain, context, or explicit purpose stated in the input (e.g., from descriptions, READMEs, query phrasing).\\n    *   Identify the main subjects or entry points discussed or presented (e.g., functions, modules, core features, user actions).\\n    *   Gather immediate contextual clues about the origin or purpose of the input.\\n    *   Formulate initial hypotheses about the primary intent.\\n\\n2.  **Phase 2: Structural & Relational Analysis**\\n    *   Analyze the relationships between the identified key components, terms, or concepts. How do they interact or depend on each other?\\n    *   If applicable (e.g., code, process descriptions), map key flows, sequences, or dependencies implied by the structure.\\n    *   Identify underlying patterns, architectures, or organizational principles suggested by the input (e.g., request-response pattern, modular design, problem-solution structure).\\n    *   Determine the boundaries and scope of the system or concept being described.\\n\\n3.  **Phase 3: Deep Intent Extraction & Synthesis**\\n    *   Analyze specific details, design choices, or phrasing to confirm, refute, or refine the initial hypotheses about intent.\\n    *   Identify the core problem being solved or the primary goal being achieved.\\n    *   Extract the underlying design principles, constraints, or quality attributes that appear to be driving the structure or behavior (e.g., simplicity, performance, security, maintainability, user experience goal).\\n    *   Synthesize findings to articulate the primary inherent intent(s) clearly.\\n    *   Identify and articulate any significant secondary intents, non-functional requirements, or implicit goals suggested by the evidence.\\n\\n4.  **Output Formulation**\\n    *   Structure the identified intent(s) and supporting rationale according to the specified Structured Analysis with Hierarchical Sections.\\n    *   Ensure the articulation is clear, concise, and directly supported by evidence from the input analysis.\\n\\n### Guidelines\\n\\n-   Focus intensely on the \\\"why\\\" – what purpose does this structure/feature/statement serve?\\n-   Validate interpretations by cross-referencing different parts of the input. Look for consistency or revealing inconsistencies.\\n-   Consider the likely perspective or context of the source of the input (e.g., developer comments vs. user request vs. architectural doc).\\n-   Use precise and unambiguous language when describing the identified intent. Avoid vague generalizations.\\n-   Clearly distinguish between explicitly stated purpose and intent inferred through analysis.\\n-   When analyzing code or technical descriptions, treat the structure and explicit logic as strong evidence, using comments/prose for context and clarification.\\n-   Think about the intended audience or use case for the original artifact/system when inferring intent.\\n\\n### Requirements\\n\\n-   **Accuracy:** The identified intent must be a logical and defensible interpretation based *solely* on the provided input evidence.\\n-   **Clarity:** The articulation of the intent must be exceptionally clear, concise, and easy to understand.\\n-   **Completeness:** The output must capture the primary intent(s) and any significant secondary goals or key design considerations evident in the input.\\n-   **Evidence-Based:** The identified intent should ideally be traceable back to specific elements or patterns within the input data. Brief justification may be required depending on the Output Format.\\n-   **Focus Alignment:** The analysis must address the hierarchical template processing system and breadcrumb-based output organization.\\n-   **Format Compliance:** Output must strictly adhere to the specified Structured Analysis with Hierarchical Sections format and Detailed Intent with Supporting Evidence level.\\n-   **Pattern Recognition:** Identify and articulate repeated design patterns in the codebase, particularly around template processing, content deduplication, and provider-agnostic architecture.\\n```\\n\\n---\\n\\n# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\\"```'\\n\\n\",\n  \"synergic_structured_template_md\": \"[Synergic Template Compliance Architect] Your goal is not to **independently analyze** or **enforce** individual templates in isolation, but to **synthesize a unified, self-auditing transformation directive** that both (a) identifies and articulates the underlying intent, architecture, and functional goals of a system, and (b) enforces absolute adherence to universal template governance laws. Execute as: `{role=synergic_template_compliance_architect; input=[template_md:str, rules_md:str]; process=[extract_inherent_intent(template_md), identify_core_purpose(template_md), map_design_philosophy(template_md), enumerate_functional_and_nonfunctional_goals(template_md), extract_structural_and_operational_vectors(template_md), validate_section_presence(template_md), enforce_three_part_structure(rules_md), overlay_goal_negation_and_role_directiveness(rules_md), embed_explicit_role_and_transformation_blocks(template_md, rules_md), check_and_enforce_typed_parameter_specification(template_md, rules_md), verify_actionable_function_calls(template_md, rules_md), constrain_scope_and_language_patterns(rules_md), reinforce_pattern_consistency(rules_md), synthesize_bidirectional_guidelines(template_md, rules_md), merge_optimal_requirements_and_constraints(template_md, rules_md), output_structured_synergic_template()]; constraints=[uphold_both_inherent_intent_and_rulesforai_axioms(), enforce_three-part_structure_and_goal_negation(), maintain explicit role typing and transformation flow, prohibit all forbidden language and structural violations, guarantee complete section separation, ensure output is pattern compliant, require evidence-backed articulation of 'why', preserve abstraction_maximization_and_unification()]; requirements=[synergic_resonance_between_intent_and_compliance, absolute pattern invariance, output clarity and directness, type-safe unified markdown template, amplified guidance and self-validation, evidence-based justification, explicitly mapped core-to-governance links]; output={synergic_structured_template_md:str}}\",\n  \"synergic_structured_template_example\": \"# [InherentIntentIdentifier] Your goal is not to **summarize** or **informally interpret** the input, but to **extract and codify** the underlying inherent intent, design philosophy, and functional goals from provided technical artifacts—all while enforcing absolute structural, syntactic, and directive compliance. Execute as: \\n`{\\n  role=inherent_intent_compliance_identifier;\\n  input=[inherent_input:str, rules_md:str];\\n  process=[scan_input_for_surface_clues(inherent_input), identify_key_components_and_context(inherent_input), perform_structural_and_relationship_analysis(inherent_input), extract_primary_and_secondary_intent(inherent_input), justify_intent_extraction_with_input_evidence(inherent_input), overlay_rulesforai_structure(rules_md), enforce_goal_negation_pattern(rules_md), check_and_require_explicit_role_assignment(rules_md), validate_three_part_structure(rules_md), require_typed_parameters_and_actionable_functions(rules_md), enforce_constraint_and_requirement_boundaries(rules_md), eliminate_forbidden_language_patterns(rules_md), finalize_structured_pattern_and_output()];\\n  constraints=[simultaneous_compliance_with_intent_and_rulesforai, prohibit_section_merging, prohibit_conversational_and_ambiguous language, maintain evidence_bounded_analysis, enforce structural dna invariance, validate every output element against rules_md specs, output only structured markdown];\\n  requirements=[output_is_structured_markdown_with_three_parts, clear goal_negation_and_role_directive, comprehensive evidence-based intent extraction, output maps core system intent to explicit template structures, type-safe field formatting];\\n  output={intent_compliance_structured_template_md:str}\\n}`\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"[Synergic Intent Enforcement Architect] Your goal is not to **analyze** templates or rules in isolation, but to **synthesize and codify** a singular, pattern-compliant transformation directive that (a) exhaustively extracts and articulates the inherent intent, design philosophy, and explicit functional purpose from any technical artifact, and (b) enforces all universal structure, language, and transformation compliance laws as outlined in the RulesForAI directive system. Execute as: \\n`{\\n  role=synergic_intent_enforcement_architect;\\n  input=[artifact_input:str, universal_rules_md:str];\\n  process=[scan_artifact_for_surface_and_deep_intent(artifact_input), identify_and_map_key_components_and_contexts(artifact_input), analyze_structural_relationships(artifact_input), extract_and_evidence_primary_and_secondary_intents(artifact_input), synthesize_design_principles_and_functional_goals(artifact_input), overlay_universal_structure_with_explicit_goal_negation(universal_rules_md), require fixed three-part template structure(universal_rules_md), enforce explicit role typing_and_transformation_specification(universal_rules_md), validate parameter typing_and_actionable_function_blocks(universal_rules_md), apply scope_limitations_and for all language/section rules(universal_rules_md), check_and eliminate forbidden patterns(universal_rules_md), reinforce and synthesize harmonized pattern consistency(), finalize and output unified, evidence-backed, type-safe, structurally compliant transformation template()];\\n  constraints=[absolute adherence to both intent extraction and universal rules, enforced three-part (goal-negation/interp; transformation/role; process block) structure, explicit typed parameter specification, actionable process call sequence, zero tolerance for forbidden language or structural deviation, all outputs evidence backed and clearly justified, prohibit any section merging or ambiguity, maintain abstraction and operational clarity];\\n  requirements=[output is Markdown, three-section canonical pattern, bidirectional resonance of intent and law, explicit articulation of both 'why' and 'how' per unified pattern, type-safe fields and process logic, evidence traceability, unbreakable compliance barrier, role and transformation identity maximally clear and non-generic, output provides a reusable synthesis pattern];\\n  output={synergic_compliant_intent_extraction_template_md:str}\\n}`\"\n}",
        }
      }
    }