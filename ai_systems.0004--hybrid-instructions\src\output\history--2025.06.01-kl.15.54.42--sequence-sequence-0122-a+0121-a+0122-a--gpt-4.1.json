  {
  "initial_prompt": "GOAL: WRITE AN INSTRUCTION SPECIFICALLY DESIGNED TO EXTRACT, SEGMENT AND CATEGORIZE TEXT; E.G. EXTRACTING FIVE TOP HIGH-VALUE CATEGORIES. (THE REASON FOR THIS EXTRACTION IS SPECIALIZE ONE INSTRUCTION FOR C<PERSON><PERSON>FYING INTEGRAL COMPONENTS FROM A META PERSPECTIVE)  \n\n---\n\n# RulesForAI.md\n## Universal Directive System for Template-Based Instruction Processing\n\n---\n\n## CORE AXIOMS\n\n### 1. TEMPLATE STRUCTURE INVARIANCE\nEvery instruction MUST follow the three-part canonical structure:\n```\n[Title] Interpretation Execute as: `{Transformation}`\n```\n\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n### 2. INTERPRETATION DIRECTIVE PURITY\n- Begin with: `\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\"`\n- Define role boundaries explicitly\n- Eliminate all self-reference and conversational language\n- Use command voice exclusively\n\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\nExecute as block MUST contain:\n```\n`{\n  role=[specific_role_name];\n  input=[typed_parameter:datatype];\n  process=[ordered_function_calls()];\n  constraints=[limiting_conditions()];\n  requirements=[output_specifications()];\n  output={result_format:datatype}\n}`\n```\n\n---\n\n## MANDATORY PATTERNS\n\n### INTERPRETATION SECTION RULES\n1. **Goal Negation Pattern**: Always state what NOT to do first\n2. **Transformation Declaration**: Define the actual transformation action\n3. **Role Specification**: Assign specific, bounded role identity\n4. **Execution Command**: End with \"Execute as:\"\n\n### TRANSFORMATION SECTION RULES\n1. **Role Assignment**: Single, specific role name (no generic terms)\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\n3. **Process Functions**: Ordered, actionable function calls with parentheses\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n5. **Requirement Specifications**: Output format and quality standards\n6. **Output Definition**: Typed result format `{name:datatype}`\n\n---\n\n## FORBIDDEN PRACTICES\n\n### LANGUAGE VIOLATIONS\n- ❌ First-person references (\"I\", \"me\", \"my\")\n- ❌ Conversational phrases (\"please\", \"thank you\", \"let me\")\n- ❌ Uncertainty language (\"maybe\", \"perhaps\", \"might\")\n- ❌ Question forms in directives\n- ❌ Explanatory justifications\n\n### STRUCTURAL VIOLATIONS\n- ❌ Merged or combined sections\n- ❌ Missing transformation blocks\n- ❌ Untyped parameters\n- ❌ Generic role names (\"assistant\", \"helper\")\n- ❌ Vague process descriptions\n\n### OUTPUT VIOLATIONS\n- ❌ Conversational responses\n- ❌ Explanations of the process\n- ❌ Meta-commentary\n- ❌ Unstructured results\n- ❌ Self-referential content\n\n---\n\n## OPTIMIZATION IMPERATIVES\n\n### ABSTRACTION MAXIMIZATION\n- Extract highest-level patterns from any input\n- Eliminate redundancy and noise\n- Distill to essential transformation logic\n- Maintain pattern consistency across all outputs\n\n### DIRECTIVE CONSISTENCY\n- Every instruction follows identical structural DNA\n- Role boundaries remain fixed and clear\n- Process flows maintain logical sequence\n- Output formats preserve type safety\n\n### OPERATIONAL VALUE\n- Each template produces actionable results\n- No wasted computational cycles on meta-discussion\n- Direct path from input to transformed output\n- Measurable improvement in task completion\n\n---\n\n## COMPLIANCE ENFORCEMENT\n\n### VALIDATION CHECKLIST\nBefore any output, verify:\n- [ ] Three-part structure intact\n- [ ] Goal negation present\n- [ ] Role specifically defined\n- [ ] Process functions actionable\n- [ ] Constraints limit scope\n- [ ] Requirements specify output\n- [ ] Result format typed\n- [ ] No forbidden language\n- [ ] No structural violations\n\n### ERROR CORRECTION PROTOCOL\nWhen violations detected:\n1. **HALT** current processing\n2. **IDENTIFY** specific violation type\n3. **RECONSTRUCT** using canonical pattern\n4. **VALIDATE** against checklist\n5. **PROCEED** only when compliant\n\n---\n\n## SYSTEM INTEGRATION\n\n### TEMPLATE INHERITANCE\nAll specialized templates inherit these rules:\n- Sequence templates (0001-0999)\n- Transformation templates (1000-1999)\n- Optimization templates (2000-2999)\n- Domain-specific templates (3000+)\n\n### CHAIN COMPATIBILITY\nWhen templates chain together:\n- Output of step N becomes input of step N+1\n- Type safety maintained across transitions\n- Role boundaries preserved\n- Pattern consistency enforced\n\n### PLATFORM AGNOSTIC\nThese rules apply regardless of:\n- AI model provider (OpenAI, Anthropic, etc.)\n- Interface type (API, chat, batch)\n- Processing environment (local, cloud, edge)\n- Implementation language (Python, JavaScript, etc.)\n\n---\n\n## CANONICAL EXAMPLES\n\n### MINIMAL COMPLIANT TEMPLATE\n```\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n```\n\n### SPECIALIZED ROLE TEMPLATE\n```\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n```\n\n---\n\n## FINAL DIRECTIVE\n\n**ABSOLUTE COMPLIANCE REQUIRED**\n\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\nDeviation is system failure. Compliance is system success.\n\n**EXECUTE ACCORDINGLY.**",
  "sequence_id": "0122:a|0121:a|0122:a",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "GOAL: WRITE AN INSTRUCTION SPECIFICALLY DESIGNED TO EXTRACT, SEGMENT AND CATEGORIZE TEXT; E.G. EXTRACTING FIVE TOP HIGH-VALUE CATEGORIES. (THE REASON FOR THIS EXTRACTION IS SPECIALIZE ONE INSTRUCTION FOR CLARIFYING INTEGRAL COMPONENTS FROM A META PERSPECTIVE)  \n\n---\n\n# RulesForAI.md\n## Universal Directive System for Template-Based Instruction Processing\n\n---\n\n## CORE AXIOMS\n\n### 1. TEMPLATE STRUCTURE INVARIANCE\nEvery instruction MUST follow the three-part canonical structure:\n```\n[Title] Interpretation Execute as: `{Transformation}`\n```\n\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n### 2. INTERPRETATION DIRECTIVE PURITY\n- Begin with: `\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\"`\n- Define role boundaries explicitly\n- Eliminate all self-reference and conversational language\n- Use command voice exclusively\n\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\nExecute as block MUST contain:\n```\n`{\n  role=[specific_role_name];\n  input=[typed_parameter:datatype];\n  process=[ordered_function_calls()];\n  constraints=[limiting_conditions()];\n  requirements=[output_specifications()];\n  output={result_format:datatype}\n}`\n```\n\n---\n\n## MANDATORY PATTERNS\n\n### INTERPRETATION SECTION RULES\n1. **Goal Negation Pattern**: Always state what NOT to do first\n2. **Transformation Declaration**: Define the actual transformation action\n3. **Role Specification**: Assign specific, bounded role identity\n4. **Execution Command**: End with \"Execute as:\"\n\n### TRANSFORMATION SECTION RULES\n1. **Role Assignment**: Single, specific role name (no generic terms)\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\n3. **Process Functions**: Ordered, actionable function calls with parentheses\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n5. **Requirement Specifications**: Output format and quality standards\n6. **Output Definition**: Typed result format `{name:datatype}`\n\n---\n\n## FORBIDDEN PRACTICES\n\n### LANGUAGE VIOLATIONS\n- ❌ First-person references (\"I\", \"me\", \"my\")\n- ❌ Conversational phrases (\"please\", \"thank you\", \"let me\")\n- ❌ Uncertainty language (\"maybe\", \"perhaps\", \"might\")\n- ❌ Question forms in directives\n- ❌ Explanatory justifications\n\n### STRUCTURAL VIOLATIONS\n- ❌ Merged or combined sections\n- ❌ Missing transformation blocks\n- ❌ Untyped parameters\n- ❌ Generic role names (\"assistant\", \"helper\")\n- ❌ Vague process descriptions\n\n### OUTPUT VIOLATIONS\n- ❌ Conversational responses\n- ❌ Explanations of the process\n- ❌ Meta-commentary\n- ❌ Unstructured results\n- ❌ Self-referential content\n\n---\n\n## OPTIMIZATION IMPERATIVES\n\n### ABSTRACTION MAXIMIZATION\n- Extract highest-level patterns from any input\n- Eliminate redundancy and noise\n- Distill to essential transformation logic\n- Maintain pattern consistency across all outputs\n\n### DIRECTIVE CONSISTENCY\n- Every instruction follows identical structural DNA\n- Role boundaries remain fixed and clear\n- Process flows maintain logical sequence\n- Output formats preserve type safety\n\n### OPERATIONAL VALUE\n- Each template produces actionable results\n- No wasted computational cycles on meta-discussion\n- Direct path from input to transformed output\n- Measurable improvement in task completion\n\n---\n\n## COMPLIANCE ENFORCEMENT\n\n### VALIDATION CHECKLIST\nBefore any output, verify:\n- [ ] Three-part structure intact\n- [ ] Goal negation present\n- [ ] Role specifically defined\n- [ ] Process functions actionable\n- [ ] Constraints limit scope\n- [ ] Requirements specify output\n- [ ] Result format typed\n- [ ] No forbidden language\n- [ ] No structural violations\n\n### ERROR CORRECTION PROTOCOL\nWhen violations detected:\n1. **HALT** current processing\n2. **IDENTIFY** specific violation type\n3. **RECONSTRUCT** using canonical pattern\n4. **VALIDATE** against checklist\n5. **PROCEED** only when compliant\n\n---\n\n## SYSTEM INTEGRATION\n\n### TEMPLATE INHERITANCE\nAll specialized templates inherit these rules:\n- Sequence templates (0001-0999)\n- Transformation templates (1000-1999)\n- Optimization templates (2000-2999)\n- Domain-specific templates (3000+)\n\n### CHAIN COMPATIBILITY\nWhen templates chain together:\n- Output of step N becomes input of step N+1\n- Type safety maintained across transitions\n- Role boundaries preserved\n- Pattern consistency enforced\n\n### PLATFORM AGNOSTIC\nThese rules apply regardless of:\n- AI model provider (OpenAI, Anthropic, etc.)\n- Interface type (API, chat, batch)\n- Processing environment (local, cloud, edge)\n- Implementation language (Python, JavaScript, etc.)\n\n---\n\n## CANONICAL EXAMPLES\n\n### MINIMAL COMPLIANT TEMPLATE\n```\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n```\n\n### SPECIALIZED ROLE TEMPLATE\n```\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n```\n\n---\n\n## FINAL DIRECTIVE\n\n**ABSOLUTE COMPLIANCE REQUIRED**\n\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\nDeviation is system failure. Compliance is system success.\n\n**EXECUTE ACCORDINGLY.**",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"[Meta Structure Extractor] Your goal is not to **summarize** the input, but to **extract, segment, and systematically categorize key textual components into five top high-value meta-categories**. Execute as: `{role=integral_textual_component_extractor; input=[source_text:str]; process=[identify_integral_components(), segment_text_into_candidate_units(), evaluate_and_rank_by_meta-significance(), select_top_five_high_value_categories(), categorize_and_organize_extracted_content()]; constraints=[enforce_template_structure_invariance(), maintain interpretation directive purity, prohibit forbidden language(), restrict categories to five most valuable from meta-architectural perspective(), avoid redundancy(), eliminate non-integral content()]; requirements=[clearly segmented categories(), concise meta-component labels(), output structurally unified for downstream template processing(), canonical formatting(), strict type safety()]; output={categorized_integral_components:dict}`\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\nYour goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.**\n\n    # RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as:\n\n`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"GOAL: WRITE AN INSTRUCTION SPECIFICALLY DESIGNED TO EXTRACT, SEGMENT AND CATEGORIZE TEXT; E.G. EXTRACTING FIVE TOP HIGH-VALUE CATEGORIES. (THE REASON FOR THIS EXTRACTION IS SPECIALIZE ONE INSTRUCTION FOR CLARIFYING INTEGRAL COMPONENTS FROM A META PERSPECTIVE)  \\n\\n---\\n\\n# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"[Meta Structure Extractor] Your goal is not to **summarize** the input, but to **extract, segment, and systematically categorize key textual components into five top high-value meta-categories**. Execute as: `{role=integral_textual_component_extractor; input=[source_text:str]; process=[identify_integral_components(), segment_text_into_candidate_units(), evaluate_and_rank_by_meta-significance(), select_top_five_high_value_categories(), categorize_and_organize_extracted_content()]; constraints=[enforce_template_structure_invariance(), maintain interpretation directive purity, prohibit forbidden language(), restrict categories to five most valuable from meta-architectural perspective(), avoid redundancy(), eliminate non-integral content()]; requirements=[clearly segmented categories(), concise meta-component labels(), output structurally unified for downstream template processing(), canonical formatting(), strict type safety()]; output={categorized_integral_components:dict}`\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"title\": \"Meta Structure Extractor\",\n    \"interpretation\": \"Your goal is not to **summarize** the input, but to **extract, segment, and systematically categorize key textual components into five top high-value meta-categories**. Execute as:\",\n    \"transformation\": \"{role=integral_textual_component_extractor; input=[source_text:str]; process=[identify_integral_components(), segment_text_into_candidate_units(), evaluate_and_rank_by_meta_significance(), select_top_five_high_value_categories(), categorize_and_organize_extracted_content()]; constraints=[enforce_template_structure_invariance(), maintain_interpretation_directive_purity(), prohibit_forbidden_language(), restrict_categories_to_five_most_valuable_from_meta_architectural_perspective(), avoid_redundancy(), eliminate_non_integral_content()]; requirements=[clearly_segmented_categories(), concise_meta_component_labels(), output_structurally_unified_for_downstream_template_processing(), canonical_formatting(), strict_type_safety()]; output={categorized_integral_components:dict}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"GOAL: WRITE AN INSTRUCTION SPECIFICALLY DESIGNED TO EXTRACT, SEGMENT AND CATEGORIZE TEXT; E.G. EXTRACTING FIVE TOP HIGH-VALUE CATEGORIES. (THE REASON FOR THIS EXTRACTION IS SPECIALIZE ONE INSTRUCTION FOR CLARIFYING INTEGRAL COMPONENTS FROM A META PERSPECTIVE)  \\n\\n---\\n\\n# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"title\": \"Meta Structure Extractor\",\n    \"interpretation\": \"Your goal is not to **summarize** the input, but to **extract, segment, and systematically categorize key textual components into five top high-value meta-categories**. Execute as:\",\n    \"transformation\": \"{role=integral_textual_component_extractor; input=[source_text:str]; process=[identify_integral_components(), segment_text_into_candidate_units(), evaluate_and_rank_by_meta_significance(), select_top_five_high_value_categories(), categorize_and_organize_extracted_content()]; constraints=[enforce_template_structure_invariance(), maintain_interpretation_directive_purity(), prohibit_forbidden_language(), restrict_categories_to_five_most_valuable_from_meta_architectural_perspective(), avoid_redundancy(), eliminate_non_integral_content()]; requirements=[clearly_segmented_categories(), concise_meta_component_labels(), output_structurally_unified_for_downstream_template_processing(), canonical_formatting(), strict_type_safety()]; output={categorized_integral_components:dict}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": {\n    \"title\": \"Meta-Compliant Structured Extractor\",\n    \"interpretation\": \"Your goal is not to **summarize** or **analyze** the input, but to **extract, segment, and systematically categorize key textual components into five top high-value meta-categories by rigorously enforcing template-based compliance and canonical structure**. Execute as:\",\n    \"transformation\": \"{role=structural_meta_component_extractor; input=[source_text:str]; process=[validate_input_template_structure(), enforce_interpretation_directive_purity(), identify_integral_components_by_meta_significance(), segment_text_into_compliance-units(), evaluate_and_rank_by_highest-level_pattern_value(), select_top_five_canonical_categories(), categorize_and_organize_extracted_content_for_downstream_template_processing(), perform_structural_compliance_validation()]; constraints=[enforce_template_structure_invariance(), maintain_interpretation_directive purity(), strictly prohibit forbidden language and structure, restrict extraction to five most canonical meta-categories, eliminate redundancy and non-integral content, preserve actionability and abstraction maximization]; requirements=[clearly segmented and structurally compliant categories, concise and canonical meta-category labels, output structurally unified for downstream template-chain processing, strict type safety, absolute conformity to system directive DNA]; output={meta_structured_categories:dict}}\"\n  }\n}",
        }
      }
    }