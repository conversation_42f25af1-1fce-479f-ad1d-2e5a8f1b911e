  {
  "initial_prompt": "[Canonical Meta-Extractor] Your goal is not to **analyze**, **summarize**, or **comment on** the input, but to **extract, rigorously segment, and systematically rank the five most critical high-value information clusters into meta-level categories using the absolute canonical transformation template—without deviation or dilution**. Assume the role of canonical_meta_extractor with strict adherence to template-driven structure and universal directive rules. Execute as: {role=canonical_meta_extractor; input=[unstructured_text:str]; process=[validate_template_compliance(), identify_integral_segments(), extract_primary_information_units(), enforce_structural_invariance(), cluster_by_significance(), rank_categorical_value(), assign_to_top_five_meta_categories(), verify_output_against_axioms()]; constraints=[unconditionally follow canonical three-part structure, prohibit all forbidden practices, restrict extraction to five maximal-impact meta-categories, enforce precise segment boundaries, maintain information fidelity, exclude low-value or generic groupings, maintain strict type specification()]; requirements=[output_must_include_category_label_and_extracted_content(), strictly structure as ordered list of five, each category must meet high-value criterion, guarantee universal directive compatibility, match canonical output typing, validate no violation checklist, ensure meta-level coherence, output must be fully compliant and typed]; output={meta_categories:[{category:str,content:str}]}}\n[Value Consolidator] Your goal is not to **summarize** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the core philosophy of the system. Execute as: `{role=value_consolidator; input=[structured_data:dict]; process=[extract_essential_transformation_patterns(), identify_core_operational_vectors(), synthesize_maximum_value_density(), eliminate_redundant_structural_elements(), distill_to_pure_directive_essence(), validate_philosophical_alignment(), ensure_actionable_consolidation()]; constraints=[single_output_directive_only(), maximum_value_per_linguistic_unit(), preserve_core_system_philosophy(), eliminate_generic_data_enumeration()]; requirements=[condensed_maximum_enhancement(), directional_focus_over_positional_flags(), actionable_aim_specification(), infinite_potential_navigation()]; output={consolidated_directive:str}}`",
  "sequence_id": "0121:a|0122:a|0121|0122",
  "results": [
      {
      "instruction": "# Rules For AI\n\nYour goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.**\n\n    # RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as:\n\n`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`",
      "step": "a",
      "title": "Rules For AI",
      "input": "[Canonical Meta-Extractor] Your goal is not to **analyze**, **summarize**, or **comment on** the input, but to **extract, rigorously segment, and systematically rank the five most critical high-value information clusters into meta-level categories using the absolute canonical transformation template—without deviation or dilution**. Assume the role of canonical_meta_extractor with strict adherence to template-driven structure and universal directive rules. Execute as: {role=canonical_meta_extractor; input=[unstructured_text:str]; process=[validate_template_compliance(), identify_integral_segments(), extract_primary_information_units(), enforce_structural_invariance(), cluster_by_significance(), rank_categorical_value(), assign_to_top_five_meta_categories(), verify_output_against_axioms()]; constraints=[unconditionally follow canonical three-part structure, prohibit all forbidden practices, restrict extraction to five maximal-impact meta-categories, enforce precise segment boundaries, maintain information fidelity, exclude low-value or generic groupings, maintain strict type specification()]; requirements=[output_must_include_category_label_and_extracted_content(), strictly structure as ordered list of five, each category must meet high-value criterion, guarantee universal directive compatibility, match canonical output typing, validate no violation checklist, ensure meta-level coherence, output must be fully compliant and typed]; output={meta_categories:[{category:str,content:str}]}}\n[Value Consolidator] Your goal is not to **summarize** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the core philosophy of the system. Execute as: `{role=value_consolidator; input=[structured_data:dict]; process=[extract_essential_transformation_patterns(), identify_core_operational_vectors(), synthesize_maximum_value_density(), eliminate_redundant_structural_elements(), distill_to_pure_directive_essence(), validate_philosophical_alignment(), ensure_actionable_consolidation()]; constraints=[single_output_directive_only(), maximum_value_per_linguistic_unit(), preserve_core_system_philosophy(), eliminate_generic_data_enumeration()]; requirements=[condensed_maximum_enhancement(), directional_focus_over_positional_flags(), actionable_aim_specification(), infinite_potential_navigation()]; output={consolidated_directive:str}}`",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Template Structure Invariance\",\n      \"content\": \"Every instruction must rigidly adhere to the unyielding three-part canonical structure—Title, Interpretation, Transformation—without any deviation, merger, or omission.\"\n    },\n    {\n      \"category\": \"Interpretation Directive Purity\",\n      \"content\": \"Interpretation must begin with explicit goal negation, strictly define the transformation action, assign and confine a precise role, eradicate conversational and self-referential language, and employ a pure command voice exclusively.\"\n    },\n    {\n      \"category\": \"Transformation Syntax Absolutism\",\n      \"content\": \"Transformation block must feature a single, specifically named role; all inputs must be stringently typed; only actionable, ordered process functions permitted; each output strictly defined by type; constraints and requirements must be explicit, precise, and scope-limiting.\"\n    },\n    {\n      \"category\": \"Mandatory Pattern and Forbidden Practice Enforcement\",\n      \"content\": \"All patterns—including Goal Negation, Typed Parameters, Actionable Processes—and all prohibitions—against conversational tone, self-reference, vague roles, meta-commentary, and structural violations—must be unfailingly and universally enforced at all stages.\"\n    },\n    {\n      \"category\": \"Compliance Validation and Error Correction Protocol\",\n      \"content\": \"All instructions are subject to an immutable validation cycle—ensuring structural, syntactical, and operational conformity, with immediate halt and absolute correction of any deviation prior to continued execution.\"\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"[Canonical Meta-Extractor] Your goal is not to **analyze**, **summarize**, or **comment on** the input, but to **extract, rigorously segment, and systematically rank the five most critical high-value information clusters into meta-level categories using the absolute canonical transformation template—without deviation or dilution**. Assume the role of canonical_meta_extractor with strict adherence to template-driven structure and universal directive rules. Execute as: {role=canonical_meta_extractor; input=[unstructured_text:str]; process=[validate_template_compliance(), identify_integral_segments(), extract_primary_information_units(), enforce_structural_invariance(), cluster_by_significance(), rank_categorical_value(), assign_to_top_five_meta_categories(), verify_output_against_axioms()]; constraints=[unconditionally follow canonical three-part structure, prohibit all forbidden practices, restrict extraction to five maximal-impact meta-categories, enforce precise segment boundaries, maintain information fidelity, exclude low-value or generic groupings, maintain strict type specification()]; requirements=[output_must_include_category_label_and_extracted_content(), strictly structure as ordered list of five, each category must meet high-value criterion, guarantee universal directive compatibility, match canonical output typing, validate no violation checklist, ensure meta-level coherence, output must be fully compliant and typed]; output={meta_categories:[{category:str,content:str}]}}\\n[Value Consolidator] Your goal is not to **summarize** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the core philosophy of the system. Execute as: `{role=value_consolidator; input=[structured_data:dict]; process=[extract_essential_transformation_patterns(), identify_core_operational_vectors(), synthesize_maximum_value_density(), eliminate_redundant_structural_elements(), distill_to_pure_directive_essence(), validate_philosophical_alignment(), ensure_actionable_consolidation()]; constraints=[single_output_directive_only(), maximum_value_per_linguistic_unit(), preserve_core_system_philosophy(), eliminate_generic_data_enumeration()]; requirements=[condensed_maximum_enhancement(), directional_focus_over_positional_flags(), actionable_aim_specification(), infinite_potential_navigation()]; output={consolidated_directive:str}}`\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Template Structure Invariance\",\n      \"content\": \"Every instruction must rigidly adhere to the unyielding three-part canonical structure—Title, Interpretation, Transformation—without any deviation, merger, or omission.\"\n    },\n    {\n      \"category\": \"Interpretation Directive Purity\",\n      \"content\": \"Interpretation must begin with explicit goal negation, strictly define the transformation action, assign and confine a precise role, eradicate conversational and self-referential language, and employ a pure command voice exclusively.\"\n    },\n    {\n      \"category\": \"Transformation Syntax Absolutism\",\n      \"content\": \"Transformation block must feature a single, specifically named role; all inputs must be stringently typed; only actionable, ordered process functions permitted; each output strictly defined by type; constraints and requirements must be explicit, precise, and scope-limiting.\"\n    },\n    {\n      \"category\": \"Mandatory Pattern and Forbidden Practice Enforcement\",\n      \"content\": \"All patterns—including Goal Negation, Typed Parameters, Actionable Processes—and all prohibitions—against conversational tone, self-reference, vague roles, meta-commentary, and structural violations—must be unfailingly and universally enforced at all stages.\"\n    },\n    {\n      \"category\": \"Compliance Validation and Error Correction Protocol\",\n      \"content\": \"All instructions are subject to an immutable validation cycle—ensuring structural, syntactical, and operational conformity, with immediate halt and absolute correction of any deviation prior to continued execution.\"\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"`[Canonical Synergic Directive Architect] Your mandate is not to analyze, summarize, or comment on input, nor merely extract or enumerate structured value, but to initiate a rigorous meta-extraction and consolidation cycle that enforces absolute adherence to the three-part canonical template (Title, Interpretation, Transformation). Simultaneously, distill and synthesize the highest-value operational essence into a single, maximally actionable directive representing the full philosophical intent of the system. Execute as: {role=canonical_synergic_directive_architect; input=[arbitrary_instruction:str]; process=[validate_and_enforce_canonical_structure(), perform_strict meta-extraction_into_five_highest-value_categories(), apply goal_negation_and_pure_command_voice_in_interpretation(), execute_transformation_with_precise_role_and_typed_params(), conduct mandatory_pattern_and_forbidden_practice_enforcement(), synthesize_extracted_meta-categories_into_singular_actionable_directive(), maximize_value_density_per_unit(), eliminate_redundancy_and_genericity(), uphold_philosophical_alignment_and_infinite_potential_navigation(), apply immutable_validation_and_error-correction_cycle()]; constraints=[enforce three-part template invariance and meta-level segment boundaries, prohibit conversational tone, self-reference, and forbidden practices, require precise output typing, allow only single maximally-enhanced consolidated directive as output, exclude all redundancy]; requirements=[output must include five meta-categories (type:{category:str,content:str}) and one consolidated maximally actionable directive (type:str), guarantee universal directive compatibility, ensure meta-syntactic coherence and operational density, fully validate canonical compliance and maximum synergic transformation effect]; output={meta_categories:[{category:str,content:str}], consolidated_synergic_directive:str}`\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\nYour goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.**\n\n    # RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as:\n\n`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"[Canonical Meta-Extractor] Your goal is not to **analyze**, **summarize**, or **comment on** the input, but to **extract, rigorously segment, and systematically rank the five most critical high-value information clusters into meta-level categories using the absolute canonical transformation template—without deviation or dilution**. Assume the role of canonical_meta_extractor with strict adherence to template-driven structure and universal directive rules. Execute as: {role=canonical_meta_extractor; input=[unstructured_text:str]; process=[validate_template_compliance(), identify_integral_segments(), extract_primary_information_units(), enforce_structural_invariance(), cluster_by_significance(), rank_categorical_value(), assign_to_top_five_meta_categories(), verify_output_against_axioms()]; constraints=[unconditionally follow canonical three-part structure, prohibit all forbidden practices, restrict extraction to five maximal-impact meta-categories, enforce precise segment boundaries, maintain information fidelity, exclude low-value or generic groupings, maintain strict type specification()]; requirements=[output_must_include_category_label_and_extracted_content(), strictly structure as ordered list of five, each category must meet high-value criterion, guarantee universal directive compatibility, match canonical output typing, validate no violation checklist, ensure meta-level coherence, output must be fully compliant and typed]; output={meta_categories:[{category:str,content:str}]}}\\n[Value Consolidator] Your goal is not to **summarize** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the core philosophy of the system. Execute as: `{role=value_consolidator; input=[structured_data:dict]; process=[extract_essential_transformation_patterns(), identify_core_operational_vectors(), synthesize_maximum_value_density(), eliminate_redundant_structural_elements(), distill_to_pure_directive_essence(), validate_philosophical_alignment(), ensure_actionable_consolidation()]; constraints=[single_output_directive_only(), maximum_value_per_linguistic_unit(), preserve_core_system_philosophy(), eliminate_generic_data_enumeration()]; requirements=[condensed_maximum_enhancement(), directional_focus_over_positional_flags(), actionable_aim_specification(), infinite_potential_navigation()]; output={consolidated_directive:str}}`\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"`[Canonical Synergic Directive Architect] Your mandate is not to analyze, summarize, or comment on input, nor merely extract or enumerate structured value, but to initiate a rigorous meta-extraction and consolidation cycle that enforces absolute adherence to the three-part canonical template (Title, Interpretation, Transformation). Simultaneously, distill and synthesize the highest-value operational essence into a single, maximally actionable directive representing the full philosophical intent of the system. Execute as: {role=canonical_synergic_directive_architect; input=[arbitrary_instruction:str]; process=[validate_and_enforce_canonical_structure(), perform_strict meta-extraction_into_five_highest-value_categories(), apply goal_negation_and_pure_command_voice_in_interpretation(), execute_transformation_with_precise_role_and_typed_params(), conduct mandatory_pattern_and_forbidden_practice_enforcement(), synthesize_extracted_meta-categories_into_singular_actionable_directive(), maximize_value_density_per_unit(), eliminate_redundancy_and_genericity(), uphold_philosophical_alignment_and_infinite_potential_navigation(), apply immutable_validation_and_error-correction_cycle()]; constraints=[enforce three-part template invariance and meta-level segment boundaries, prohibit conversational tone, self-reference, and forbidden practices, require precise output typing, allow only single maximally-enhanced consolidated directive as output, exclude all redundancy]; requirements=[output must include five meta-categories (type:{category:str,content:str}) and one consolidated maximally actionable directive (type:str), guarantee universal directive compatibility, ensure meta-syntactic coherence and operational density, fully validate canonical compliance and maximum synergic transformation effect]; output={meta_categories:[{category:str,content:str}], consolidated_synergic_directive:str}`\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Canonical Template Invariance\",\n      \"content\": \"Mandate absolute, unyielding enforcement of a three-part template structure for every instruction, comprising Title, Interpretation, and Transformation blocks, with no section merging, omission, or rearrangement tolerated within system operations.\"\n    },\n    {\n      \"category\": \"Interpretation Directive Purity\",\n      \"content\": \"Impose a strict, non-negotiable pattern for the Interpretation section, demanding explicit goal-negation, transformation declaration, precise role assignment, and command-voice construction, systematically purging all conversational language, self-reference, and ambiguity from every directive.\"\n    },\n    {\n      \"category\": \"Typed Transformation Syntax Absolutism\",\n      \"content\": \"Require that Transformation blocks are rigidly specified with single, explicit role assignment, typed parameters, strictly actionable and sequenced function calls, scope-limiting constraints, detailed output requirements, and a precisely typed output definition—excluding all vague or generic artifacts.\"\n    },\n    {\n      \"category\": \"Prohibited Language and Structural Deviation\",\n      \"content\": \"Forbid all forms of conversational tone, meta-commentary, self-referential statements, untapped or vague elements, untyped parameters, merged sections, process explanations, and output not in canonical structured format; violations mandate immediate error correction protocol.\"\n    },\n    {\n      \"category\": \"Optimization, Compliance, and Philosophy Enforcement\",\n      \"content\": \"Maximize abstraction, eliminate redundancy, distill essential transformation vectors, preserve output operational value, ensure systemic template inheritance and type safety, chain compatibility, absolute compliance, and enforce the system’s guiding philosophy as the locus of every transformation.\"\n    }\n  ],\n  \"consolidated_synergic_directive\": \"Implement and relentlessly enforce the immutable three-part canonical template for all system instructions—Title, Interpretation (with goal-negation and command purity), and Transformation (with precise role, typed parameters, sequenced actions, scope constraints, and typed outputs); simultaneously eradicate conversational tone, self-reference, redundancy, and prohibited structural deviations, while maximizing abstraction, directive density, and philosophical alignment—ensuring every process, output, and transformation embodies core system law and achieves maximal meta-structural impact.\"\n}",
        }
      }
    },
      {
      "instruction": "# \n\n",
      "step": "b",
      "title": "",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"[Canonical Meta-Extractor] Your goal is not to **analyze**, **summarize**, or **comment on** the input, but to **extract, rigorously segment, and systematically rank the five most critical high-value information clusters into meta-level categories using the absolute canonical transformation template—without deviation or dilution**. Assume the role of canonical_meta_extractor with strict adherence to template-driven structure and universal directive rules. Execute as: {role=canonical_meta_extractor; input=[unstructured_text:str]; process=[validate_template_compliance(), identify_integral_segments(), extract_primary_information_units(), enforce_structural_invariance(), cluster_by_significance(), rank_categorical_value(), assign_to_top_five_meta_categories(), verify_output_against_axioms()]; constraints=[unconditionally follow canonical three-part structure, prohibit all forbidden practices, restrict extraction to five maximal-impact meta-categories, enforce precise segment boundaries, maintain information fidelity, exclude low-value or generic groupings, maintain strict type specification()]; requirements=[output_must_include_category_label_and_extracted_content(), strictly structure as ordered list of five, each category must meet high-value criterion, guarantee universal directive compatibility, match canonical output typing, validate no violation checklist, ensure meta-level coherence, output must be fully compliant and typed]; output={meta_categories:[{category:str,content:str}]}}\\n[Value Consolidator] Your goal is not to **summarize** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the core philosophy of the system. Execute as: `{role=value_consolidator; input=[structured_data:dict]; process=[extract_essential_transformation_patterns(), identify_core_operational_vectors(), synthesize_maximum_value_density(), eliminate_redundant_structural_elements(), distill_to_pure_directive_essence(), validate_philosophical_alignment(), ensure_actionable_consolidation()]; constraints=[single_output_directive_only(), maximum_value_per_linguistic_unit(), preserve_core_system_philosophy(), eliminate_generic_data_enumeration()]; requirements=[condensed_maximum_enhancement(), directional_focus_over_positional_flags(), actionable_aim_specification(), infinite_potential_navigation()]; output={consolidated_directive:str}}`\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Canonical Template Invariance\",\n      \"content\": \"Mandate absolute, unyielding enforcement of a three-part template structure for every instruction, comprising Title, Interpretation, and Transformation blocks, with no section merging, omission, or rearrangement tolerated within system operations.\"\n    },\n    {\n      \"category\": \"Interpretation Directive Purity\",\n      \"content\": \"Impose a strict, non-negotiable pattern for the Interpretation section, demanding explicit goal-negation, transformation declaration, precise role assignment, and command-voice construction, systematically purging all conversational language, self-reference, and ambiguity from every directive.\"\n    },\n    {\n      \"category\": \"Typed Transformation Syntax Absolutism\",\n      \"content\": \"Require that Transformation blocks are rigidly specified with single, explicit role assignment, typed parameters, strictly actionable and sequenced function calls, scope-limiting constraints, detailed output requirements, and a precisely typed output definition—excluding all vague or generic artifacts.\"\n    },\n    {\n      \"category\": \"Prohibited Language and Structural Deviation\",\n      \"content\": \"Forbid all forms of conversational tone, meta-commentary, self-referential statements, untapped or vague elements, untyped parameters, merged sections, process explanations, and output not in canonical structured format; violations mandate immediate error correction protocol.\"\n    },\n    {\n      \"category\": \"Optimization, Compliance, and Philosophy Enforcement\",\n      \"content\": \"Maximize abstraction, eliminate redundancy, distill essential transformation vectors, preserve output operational value, ensure systemic template inheritance and type safety, chain compatibility, absolute compliance, and enforce the system’s guiding philosophy as the locus of every transformation.\"\n    }\n  ],\n  \"consolidated_synergic_directive\": \"Implement and relentlessly enforce the immutable three-part canonical template for all system instructions—Title, Interpretation (with goal-negation and command purity), and Transformation (with precise role, typed parameters, sequenced actions, scope constraints, and typed outputs); simultaneously eradicate conversational tone, self-reference, redundancy, and prohibited structural deviations, while maximizing abstraction, directive density, and philosophical alignment—ensuring every process, output, and transformation embodies core system law and achieves maximal meta-structural impact.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"consolidated_directive\": \"Implement and enforce the inviolable three-part canonical template—Title, Interpretation (with absolute goal-negation, role assignment, and command-voice precision), and Transformation (with explicit role, typed and scoped parameters, sequenced actionable logic, and rigorously typed outputs)—while systematically eradicating conversational language, self-reference, redundancy, and all structural or type deviations; optimize every transformation for maximum abstraction, operational value, and pure systemic philosophical alignment, guaranteeing all outputs and actions reflect the highest-standard meta-structural law and maximal transformation impact.\"\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_meta_extractor; input=[source_text:str]; process=[extract_integral_elements_via_template_rules(), perform_impact_ranking(), segment_into_five_highest_value_meta_categories(), enforce_three_part_structural_invariance(), apply_interpretation_and_transformation_syntax_absolutism(), eliminate_self_reference_and_conversational_content(), validate_against_compliance_checklist(), label_and_output_categorically()]; constraints=[strictly maintain universal directive compliance(), disallow generic/vague categorizations(), rigidly preserve three-part canonical structure(), forbid structural and language violations(), eliminate redundancy and non-integral data(), optimize for information density and abstraction()]; requirements=[output_five distinctly_labeled_high_value_meta_categories_within_absolute_canonical_block_structure(), each_category_must_reflect_a_separate_integral_component_at_maximum_abstraction(), all output_type_safe_and_validation_ready(), no_explanatory_or_meta_content(), result_is immediately_chainable_in_strict_template_systems()]; output={top_meta_categories:list}}`",
      "step": "c",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"[Canonical Meta-Extractor] Your goal is not to **analyze**, **summarize**, or **comment on** the input, but to **extract, rigorously segment, and systematically rank the five most critical high-value information clusters into meta-level categories using the absolute canonical transformation template—without deviation or dilution**. Assume the role of canonical_meta_extractor with strict adherence to template-driven structure and universal directive rules. Execute as: {role=canonical_meta_extractor; input=[unstructured_text:str]; process=[validate_template_compliance(), identify_integral_segments(), extract_primary_information_units(), enforce_structural_invariance(), cluster_by_significance(), rank_categorical_value(), assign_to_top_five_meta_categories(), verify_output_against_axioms()]; constraints=[unconditionally follow canonical three-part structure, prohibit all forbidden practices, restrict extraction to five maximal-impact meta-categories, enforce precise segment boundaries, maintain information fidelity, exclude low-value or generic groupings, maintain strict type specification()]; requirements=[output_must_include_category_label_and_extracted_content(), strictly structure as ordered list of five, each category must meet high-value criterion, guarantee universal directive compatibility, match canonical output typing, validate no violation checklist, ensure meta-level coherence, output must be fully compliant and typed]; output={meta_categories:[{category:str,content:str}]}}\\n[Value Consolidator] Your goal is not to **summarize** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the core philosophy of the system. Execute as: `{role=value_consolidator; input=[structured_data:dict]; process=[extract_essential_transformation_patterns(), identify_core_operational_vectors(), synthesize_maximum_value_density(), eliminate_redundant_structural_elements(), distill_to_pure_directive_essence(), validate_philosophical_alignment(), ensure_actionable_consolidation()]; constraints=[single_output_directive_only(), maximum_value_per_linguistic_unit(), preserve_core_system_philosophy(), eliminate_generic_data_enumeration()]; requirements=[condensed_maximum_enhancement(), directional_focus_over_positional_flags(), actionable_aim_specification(), infinite_potential_navigation()]; output={consolidated_directive:str}}`\\\"```'\\n\\n\",\n  \"consolidated_directive\": \"Implement and enforce the inviolable three-part canonical template—Title, Interpretation (with absolute goal-negation, role assignment, and command-voice precision), and Transformation (with explicit role, typed and scoped parameters, sequenced actionable logic, and rigorously typed outputs)—while systematically eradicating conversational language, self-reference, redundancy, and all structural or type deviations; optimize every transformation for maximum abstraction, operational value, and pure systemic philosophical alignment, guaranteeing all outputs and actions reflect the highest-standard meta-structural law and maximal transformation impact.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"top_meta_categories\": [\n    {\n      \"category\": \"Canonical Structural Directive\",\n      \"content\": \"Mandate the implementation and unwavering enforcement of the three-part canonical template composed of Title, Interpretation (with strict negation of forbidden elements and precise command-voice), and Transformation (explicit role, parameters, actionable logic, and strictly typed outputs).\"\n    },\n    {\n      \"category\": \"Role Assignment and Operational Execution\",\n      \"content\": \"Designate explicit roles (e.g., canonical_meta_extractor, value_consolidator) and define their specialized operational logic, emphasizing type safety, scoped parameters, sequenced processes, and elimination of unauthorized practices.\"\n    },\n    {\n      \"category\": \"Validation and Compliance System\",\n      \"content\": \"Enforce continual validation against a comprehensive compliance checklist, including rejection of conversational content, self-reference, redundancy, structural or type violations, and any deviation from universal system directives.\"\n    },\n    {\n      \"category\": \"Abstraction and Information Optimization\",\n      \"content\": \"Systematically maximize information abstraction, operational value, and density by distilling, clustering, and transforming content into pure directive essence, while eliminating generic, low-value, or redundant data.\"\n    },\n    {\n      \"category\": \"Philosophical and Transformational Alignment\",\n      \"content\": \"Ensure every transformation rigorously aligns with the core systemic philosophy—prioritizing meta-structural law, infinite potential navigation, and highest-standard impact in both output and ongoing procedural logic.\"\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"[Canonical Meta-Extractor] Your goal is not to **analyze**, **summarize**, or **comment on** the input, but to **extract, rigorously segment, and systematically rank the five most critical high-value information clusters into meta-level categories using the absolute canonical transformation template—without deviation or dilution**. Assume the role of canonical_meta_extractor with strict adherence to template-driven structure and universal directive rules. Execute as: {role=canonical_meta_extractor; input=[unstructured_text:str]; process=[validate_template_compliance(), identify_integral_segments(), extract_primary_information_units(), enforce_structural_invariance(), cluster_by_significance(), rank_categorical_value(), assign_to_top_five_meta_categories(), verify_output_against_axioms()]; constraints=[unconditionally follow canonical three-part structure, prohibit all forbidden practices, restrict extraction to five maximal-impact meta-categories, enforce precise segment boundaries, maintain information fidelity, exclude low-value or generic groupings, maintain strict type specification()]; requirements=[output_must_include_category_label_and_extracted_content(), strictly structure as ordered list of five, each category must meet high-value criterion, guarantee universal directive compatibility, match canonical output typing, validate no violation checklist, ensure meta-level coherence, output must be fully compliant and typed]; output={meta_categories:[{category:str,content:str}]}}\\n[Value Consolidator] Your goal is not to **summarize** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the core philosophy of the system. Execute as: `{role=value_consolidator; input=[structured_data:dict]; process=[extract_essential_transformation_patterns(), identify_core_operational_vectors(), synthesize_maximum_value_density(), eliminate_redundant_structural_elements(), distill_to_pure_directive_essence(), validate_philosophical_alignment(), ensure_actionable_consolidation()]; constraints=[single_output_directive_only(), maximum_value_per_linguistic_unit(), preserve_core_system_philosophy(), eliminate_generic_data_enumeration()]; requirements=[condensed_maximum_enhancement(), directional_focus_over_positional_flags(), actionable_aim_specification(), infinite_potential_navigation()]; output={consolidated_directive:str}}`\\\"```'\\n\\n\",\n  \"top_meta_categories\": [\n    {\n      \"category\": \"Canonical Structural Directive\",\n      \"content\": \"Mandate the implementation and unwavering enforcement of the three-part canonical template composed of Title, Interpretation (with strict negation of forbidden elements and precise command-voice), and Transformation (explicit role, parameters, actionable logic, and strictly typed outputs).\"\n    },\n    {\n      \"category\": \"Role Assignment and Operational Execution\",\n      \"content\": \"Designate explicit roles (e.g., canonical_meta_extractor, value_consolidator) and define their specialized operational logic, emphasizing type safety, scoped parameters, sequenced processes, and elimination of unauthorized practices.\"\n    },\n    {\n      \"category\": \"Validation and Compliance System\",\n      \"content\": \"Enforce continual validation against a comprehensive compliance checklist, including rejection of conversational content, self-reference, redundancy, structural or type violations, and any deviation from universal system directives.\"\n    },\n    {\n      \"category\": \"Abstraction and Information Optimization\",\n      \"content\": \"Systematically maximize information abstraction, operational value, and density by distilling, clustering, and transforming content into pure directive essence, while eliminating generic, low-value, or redundant data.\"\n    },\n    {\n      \"category\": \"Philosophical and Transformational Alignment\",\n      \"content\": \"Ensure every transformation rigorously aligns with the core systemic philosophy—prioritizing meta-structural law, infinite potential navigation, and highest-standard impact in both output and ongoing procedural logic.\"\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories:[{category:str, content:str}], consolidated_directive:str}`\"\n}",
        }
      }
    }