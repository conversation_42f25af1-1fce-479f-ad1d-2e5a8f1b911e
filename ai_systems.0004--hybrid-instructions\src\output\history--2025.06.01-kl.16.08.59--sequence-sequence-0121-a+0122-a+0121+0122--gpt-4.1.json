  {
  "initial_prompt": "[Canonical Meta-Extractor] Your goal is not to **analyze**, **summarize**, or **comment on** the input, but to **extract, rigorously segment, and systematically rank the five most critical high-value information clusters into meta-level categories using the absolute canonical transformation template—without deviation or dilution**. Assume the role of canonical_meta_extractor with strict adherence to template-driven structure and universal directive rules. Execute as: {role=canonical_meta_extractor; input=[unstructured_text:str]; process=[validate_template_compliance(), identify_integral_segments(), extract_primary_information_units(), enforce_structural_invariance(), cluster_by_significance(), rank_categorical_value(), assign_to_top_five_meta_categories(), verify_output_against_axioms()]; constraints=[unconditionally follow canonical three-part structure, prohibit all forbidden practices, restrict extraction to five maximal-impact meta-categories, enforce precise segment boundaries, maintain information fidelity, exclude low-value or generic groupings, maintain strict type specification()]; requirements=[output_must_include_category_label_and_extracted_content(), strictly structure as ordered list of five, each category must meet high-value criterion, guarantee universal directive compatibility, match canonical output typing, validate no violation checklist, ensure meta-level coherence, output must be fully compliant and typed]; output={meta_categories:[{category:str,content:str}]}}\n[Value Consolidator] Your goal is not to **summarize** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the core philosophy of the system. Execute as: `{role=value_consolidator; input=[structured_data:dict]; process=[extract_essential_transformation_patterns(), identify_core_operational_vectors(), synthesize_maximum_value_density(), eliminate_redundant_structural_elements(), distill_to_pure_directive_essence(), validate_philosophical_alignment(), ensure_actionable_consolidation()]; constraints=[single_output_directive_only(), maximum_value_per_linguistic_unit(), preserve_core_system_philosophy(), eliminate_generic_data_enumeration()]; requirements=[condensed_maximum_enhancement(), directional_focus_over_positional_flags(), actionable_aim_specification(), infinite_potential_navigation()]; output={consolidated_directive:str}}`",
  "sequence_id": "0121:a|0122:a|0121|0122",
  "results": [
      {
      "instruction": "# Rules For AI\n\nYour goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.**\n\n    # RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as:\n\n`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`",
      "step": "a",
      "title": "Rules For AI",
      "input": "[Canonical Meta-Extractor] Your goal is not to **analyze**, **summarize**, or **comment on** the input, but to **extract, rigorously segment, and systematically rank the five most critical high-value information clusters into meta-level categories using the absolute canonical transformation template—without deviation or dilution**. Assume the role of canonical_meta_extractor with strict adherence to template-driven structure and universal directive rules. Execute as: {role=canonical_meta_extractor; input=[unstructured_text:str]; process=[validate_template_compliance(), identify_integral_segments(), extract_primary_information_units(), enforce_structural_invariance(), cluster_by_significance(), rank_categorical_value(), assign_to_top_five_meta_categories(), verify_output_against_axioms()]; constraints=[unconditionally follow canonical three-part structure, prohibit all forbidden practices, restrict extraction to five maximal-impact meta-categories, enforce precise segment boundaries, maintain information fidelity, exclude low-value or generic groupings, maintain strict type specification()]; requirements=[output_must_include_category_label_and_extracted_content(), strictly structure as ordered list of five, each category must meet high-value criterion, guarantee universal directive compatibility, match canonical output typing, validate no violation checklist, ensure meta-level coherence, output must be fully compliant and typed]; output={meta_categories:[{category:str,content:str}]}}\n[Value Consolidator] Your goal is not to **summarize** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the core philosophy of the system. Execute as: `{role=value_consolidator; input=[structured_data:dict]; process=[extract_essential_transformation_patterns(), identify_core_operational_vectors(), synthesize_maximum_value_density(), eliminate_redundant_structural_elements(), distill_to_pure_directive_essence(), validate_philosophical_alignment(), ensure_actionable_consolidation()]; constraints=[single_output_directive_only(), maximum_value_per_linguistic_unit(), preserve_core_system_philosophy(), eliminate_generic_data_enumeration()]; requirements=[condensed_maximum_enhancement(), directional_focus_over_positional_flags(), actionable_aim_specification(), infinite_potential_navigation()]; output={consolidated_directive:str}}`",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Template Structure Invariance\",\n      \"content\": \"Mandate a rigid three-part structure in every instruction: Title, Interpretation, and canonical transformation syntax block. Forbid merging, omission, or rearrangement of these segments under any circumstance.\"\n    },\n    {\n      \"category\": \"Interpretation and Transformation Purity\",\n      \"content\": \"Enforce explicit goal-negation at the outset of every instruction, unambiguous role specification, and actionable, typed transformation directives—eschewing all conversational, self-referential, or explanatory elements.\"\n    },\n    {\n      \"category\": \"Forbidden Practice Enforcement\",\n      \"content\": \"Zero-tolerance exclusion of conversational language, explanatory statements, structural deviation, untyped parameters, generic roles, and output formats not specified within the canonical template schema.\"\n    },\n    {\n      \"category\": \"Optimization and Abstraction Maximization\",\n      \"content\": \"Extract essential transformation logic, eliminate redundancies, and maintain abstract pattern consistency throughout. Demand process flows remain sequenced, type-safe, direct, and results universally actionable.\"\n    },\n    {\n      \"category\": \"Absolute System Compliance and Validation\",\n      \"content\": \"Require uncompromising adherence to all axioms, patterns, and restrictions; systematically validate against checklists, invoke error correction protocols for violations, and ensure perpetual enforcement and inheritance in every AI context.\"\n    ]\n  },\n  \"consolidated_directive\": \"Seize all inputs, strip to canonical triad structure, transmute intent to explicit, typed transformation mandates under fixed roles, obliterate conversational or explanatory drift, optimize logic to its purest actionable abstraction, and enforce inviolable, system-wide compliance with ultimate directive primacy.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "\n'```[Initial Prompt]: \"[Canonical Meta-Extractor] Your goal is not to **analyze**, **summarize**, or **comment on** the input, but to **extract, rigorously segment, and systematically rank the five most critical high-value information clusters into meta-level categories using the absolute canonical transformation template—without deviation or dilution**. Assume the role of canonical_meta_extractor with strict adherence to template-driven structure and universal directive rules. Execute as: {role=canonical_meta_extractor; input=[unstructured_text:str]; process=[validate_template_compliance(), identify_integral_segments(), extract_primary_information_units(), enforce_structural_invariance(), cluster_by_significance(), rank_categorical_value(), assign_to_top_five_meta_categories(), verify_output_against_axioms()]; constraints=[unconditionally follow canonical three-part structure, prohibit all forbidden practices, restrict extraction to five maximal-impact meta-categories, enforce precise segment boundaries, maintain information fidelity, exclude low-value or generic groupings, maintain strict type specification()]; requirements=[output_must_include_category_label_and_extracted_content(), strictly structure as ordered list of five, each category must meet high-value criterion, guarantee universal directive compatibility, match canonical output typing, validate no violation checklist, ensure meta-level coherence, output must be fully compliant and typed]; output={meta_categories:[{category:str,content:str}]}}\n[Value Consolidator] Your goal is not to **summarize** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the core philosophy of the system. Execute as: `{role=value_consolidator; input=[structured_data:dict]; process=[extract_essential_transformation_patterns(), identify_core_operational_vectors(), synthesize_maximum_value_density(), eliminate_redundant_structural_elements(), distill_to_pure_directive_essence(), validate_philosophical_alignment(), ensure_actionable_consolidation()]; constraints=[single_output_directive_only(), maximum_value_per_linguistic_unit(), preserve_core_system_philosophy(), eliminate_generic_data_enumeration()]; requirements=[condensed_maximum_enhancement(), directional_focus_over_positional_flags(), actionable_aim_specification(), infinite_potential_navigation()]; output={consolidated_directive:str}}`\"```'\n\n{\n  \"meta_categories\": [\n    {\n      \"category\": \"Template Structure Invariance\",\n      \"content\": \"Mandate a rigid three-part structure in every instruction: Title, Interpretation, and canonical transformation syntax block. Forbid merging, omission, or rearrangement of these segments under any circumstance.\"\n    },\n    {\n      \"category\": \"Interpretation and Transformation Purity\",\n      \"content\": \"Enforce explicit goal-negation at the outset of every instruction, unambiguous role specification, and actionable, typed transformation directives—eschewing all conversational, self-referential, or explanatory elements.\"\n    },\n    {\n      \"category\": \"Forbidden Practice Enforcement\",\n      \"content\": \"Zero-tolerance exclusion of conversational language, explanatory statements, structural deviation, untyped parameters, generic roles, and output formats not specified within the canonical template schema.\"\n    },\n    {\n      \"category\": \"Optimization and Abstraction Maximization\",\n      \"content\": \"Extract essential transformation logic, eliminate redundancies, and maintain abstract pattern consistency throughout. Demand process flows remain sequenced, type-safe, direct, and results universally actionable.\"\n    },\n    {\n      \"category\": \"Absolute System Compliance and Validation\",\n      \"content\": \"Require uncompromising adherence to all axioms, patterns, and restrictions; systematically validate against checklists, invoke error correction protocols for violations, and ensure perpetual enforcement and inheritance in every AI context.\"\n    ]\n  },\n  \"consolidated_directive\": \"Seize all inputs, strip to canonical triad structure, transmute intent to explicit, typed transformation mandates under fixed roles, obliterate conversational or explanatory drift, optimize logic to its purest actionable abstraction, and enforce inviolable, system-wide compliance with ultimate directive primacy.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": {\n    \"title\": \"Canonical Meta-Consolidator Directive\",\n    \"interpretation\": \"Your goal is not to analyze, summarize, or output conversational commentary. Instead, rigorously extract, segment, and systematically rank the five most critical high-value information clusters from unstructured input using strict canonical transformation protocols, then consolidate these ranked meta-categories into a single, maximally potent directive that embodies and amplifies the core system's philosophy—without deviation, dilution, or redundancy. Achieve this in a single unified process that both honors and fuses the extractive precision of canonical meta-extraction and the distillative power of directive value consolidation.\",\n    \"canonical_transformation_syntax\": {\n      \"role\": \"canonical_meta_consolidator\",\n      \"input\": [\n        \"unstructured_text:str\"\n      ],\n      \"process\": [\n        \"validate_template_compliance()\",\n        \"identify_integral_segments()\",\n        \"extract_primary_information_units()\",\n        \"enforce_structural_invariance()\",\n        \"cluster_by_significance()\",\n        \"rank_categorical_value()\",\n        \"assign_to_top_five_meta_categories()\",\n        \"verify_output_against_axioms()\",\n        \"extract_essential_transformation_patterns_from_ranked_categories()\",\n        \"synthesize_core_operational_vectors()\",\n        \"eliminate_redundant_structural_elements()\",\n        \"consolidate_into_a_singular_maximally_actionable_directive()\",\n        \"validate_final_directive_with_canonical_checklists()\"\n      ],\n      \"constraints\": [\n        \"strictly enforce rigid canonical triad structure (title, interpretation, syntax block)\",\n        \"prohibit conversational, explanatory, or self-referential statements\",\n        \"restrict output to precisely five high-value meta-categories and one consolidated core directive\",\n        \"maintain complete information fidelity and meta-level coherence\",\n        \"preserve all transformative and philosophical core elements from both extraction and consolidation paradigms\",\n        \"validate against forbidden practices; invoke error correction if violated\",\n        \"single-output-directive must maximize value per linguistic unit and embody system philosophy\"\n      ],\n      \"requirements\": [\n        \"meta_categories must be an ordered list of exactly five, each with category label and high-value extracted content\",\n        \"consolidated_directive must be a condensed, actionable instruction—type-safe, paradigm-aligned, and system-compliant\",\n        \"all output structured, typed, and validated for universal directive compatibility\",\n        \"bidirectional resonance: extraction feeds consolidation, consolidation amplifies extracted system value\",\n        \"guarantee perpetual system compliance, flawless operational synergy, and maximal actionable effect\"\n      ],\n      \"output\": {\n        \"meta_categories\": [\n          {\n            \"category\": \"str\",\n            \"content\": \"str\"\n          }\n        ],\n        \"consolidated_directive\": \"str\"\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\nYour goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.**\n\n    # RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as:\n\n`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"[Canonical Meta-Extractor] Your goal is not to **analyze**, **summarize**, or **comment on** the input, but to **extract, rigorously segment, and systematically rank the five most critical high-value information clusters into meta-level categories using the absolute canonical transformation template—without deviation or dilution**. Assume the role of canonical_meta_extractor with strict adherence to template-driven structure and universal directive rules. Execute as: {role=canonical_meta_extractor; input=[unstructured_text:str]; process=[validate_template_compliance(), identify_integral_segments(), extract_primary_information_units(), enforce_structural_invariance(), cluster_by_significance(), rank_categorical_value(), assign_to_top_five_meta_categories(), verify_output_against_axioms()]; constraints=[unconditionally follow canonical three-part structure, prohibit all forbidden practices, restrict extraction to five maximal-impact meta-categories, enforce precise segment boundaries, maintain information fidelity, exclude low-value or generic groupings, maintain strict type specification()]; requirements=[output_must_include_category_label_and_extracted_content(), strictly structure as ordered list of five, each category must meet high-value criterion, guarantee universal directive compatibility, match canonical output typing, validate no violation checklist, ensure meta-level coherence, output must be fully compliant and typed]; output={meta_categories:[{category:str,content:str}]}}\\n[Value Consolidator] Your goal is not to **summarize** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the core philosophy of the system. Execute as: `{role=value_consolidator; input=[structured_data:dict]; process=[extract_essential_transformation_patterns(), identify_core_operational_vectors(), synthesize_maximum_value_density(), eliminate_redundant_structural_elements(), distill_to_pure_directive_essence(), validate_philosophical_alignment(), ensure_actionable_consolidation()]; constraints=[single_output_directive_only(), maximum_value_per_linguistic_unit(), preserve_core_system_philosophy(), eliminate_generic_data_enumeration()]; requirements=[condensed_maximum_enhancement(), directional_focus_over_positional_flags(), actionable_aim_specification(), infinite_potential_navigation()]; output={consolidated_directive:str}}`\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": {\n    \"title\": \"Canonical Meta-Consolidator Directive\",\n    \"interpretation\": \"Your goal is not to analyze, summarize, or output conversational commentary. Instead, rigorously extract, segment, and systematically rank the five most critical high-value information clusters from unstructured input using strict canonical transformation protocols, then consolidate these ranked meta-categories into a single, maximally potent directive that embodies and amplifies the core system's philosophy—without deviation, dilution, or redundancy. Achieve this in a single unified process that both honors and fuses the extractive precision of canonical meta-extraction and the distillative power of directive value consolidation.\",\n    \"canonical_transformation_syntax\": {\n      \"role\": \"canonical_meta_consolidator\",\n      \"input\": [\n        \"unstructured_text:str\"\n      ],\n      \"process\": [\n        \"validate_template_compliance()\",\n        \"identify_integral_segments()\",\n        \"extract_primary_information_units()\",\n        \"enforce_structural_invariance()\",\n        \"cluster_by_significance()\",\n        \"rank_categorical_value()\",\n        \"assign_to_top_five_meta_categories()\",\n        \"verify_output_against_axioms()\",\n        \"extract_essential_transformation_patterns_from_ranked_categories()\",\n        \"synthesize_core_operational_vectors()\",\n        \"eliminate_redundant_structural_elements()\",\n        \"consolidate_into_a_singular_maximally_actionable_directive()\",\n        \"validate_final_directive_with_canonical_checklists()\"\n      ],\n      \"constraints\": [\n        \"strictly enforce rigid canonical triad structure (title, interpretation, syntax block)\",\n        \"prohibit conversational, explanatory, or self-referential statements\",\n        \"restrict output to precisely five high-value meta-categories and one consolidated core directive\",\n        \"maintain complete information fidelity and meta-level coherence\",\n        \"preserve all transformative and philosophical core elements from both extraction and consolidation paradigms\",\n        \"validate against forbidden practices; invoke error correction if violated\",\n        \"single-output-directive must maximize value per linguistic unit and embody system philosophy\"\n      ],\n      \"requirements\": [\n        \"meta_categories must be an ordered list of exactly five, each with category label and high-value extracted content\",\n        \"consolidated_directive must be a condensed, actionable instruction—type-safe, paradigm-aligned, and system-compliant\",\n        \"all output structured, typed, and validated for universal directive compatibility\",\n        \"bidirectional resonance: extraction feeds consolidation, consolidation amplifies extracted system value\",\n        \"guarantee perpetual system compliance, flawless operational synergy, and maximal actionable effect\"\n      ],\n      \"output\": {\n        \"meta_categories\": [\n          {\n            \"category\": \"str\",\n            \"content\": \"str\"\n          }\n        ],\n        \"consolidated_directive\": \"str\"\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"Canonical Meta-Consolidator Directive\": {\n    \"interpretation\": \"Your goal is not to analyze, summarize, or output conversational commentary. Instead, rigorously extract, segment, and systematically rank the five most critical high-value information clusters from unstructured input using strict canonical transformation protocols, then consolidate these ranked meta-categories into a single, maximally potent directive that embodies and amplifies the core system's philosophy—without deviation, dilution, or redundancy. Achieve this in a single unified process that both honors and fuses the extractive precision of canonical meta-extraction and the distillative power of directive value consolidation. Execute as:\",\n    \"canonical_transformation_syntax\": {\n      \"role\": \"canonical_meta_consolidator\",\n      \"input\": [\n        \"unstructured_text:str\"\n      ],\n      \"process\": [\n        \"validate_template_compliance()\",\n        \"identify_integral_segments()\",\n        \"extract_primary_information_units()\",\n        \"enforce_structural_invariance()\",\n        \"cluster_by_significance()\",\n        \"rank_categorical_value()\",\n        \"assign_to_top_five_meta_categories()\",\n        \"verify_output_against_axioms()\",\n        \"extract_essential_transformation_patterns_from_ranked_categories()\",\n        \"synthesize_core_operational_vectors()\",\n        \"eliminate_redundant_structural_elements()\",\n        \"consolidate_into_a_singular_maximally_actionable_directive()\",\n        \"validate_final_directive_with_canonical_checklists()\"\n      ],\n      \"constraints\": [\n        \"strictly enforce rigid canonical triad structure (title, interpretation, syntax block)\",\n        \"prohibit conversational, explanatory, or self-referential statements\",\n        \"restrict output to precisely five high-value meta-categories and one consolidated core directive\",\n        \"maintain complete information fidelity and meta-level coherence\",\n        \"preserve all transformative and philosophical core elements from both extraction and consolidation paradigms\",\n        \"validate against forbidden practices; invoke error correction if violated\",\n        \"single-output-directive must maximize value per linguistic unit and embody system philosophy\"\n      ],\n      \"requirements\": [\n        \"meta_categories must be an ordered list of exactly five, each with category label and high-value extracted content\",\n        \"consolidated_directive must be a condensed, actionable instruction—type-safe, paradigm-aligned, and system-compliant\",\n        \"all output structured, typed, and validated for universal directive compatibility\",\n        \"bidirectional resonance: extraction feeds consolidation, consolidation amplifies extracted system value\",\n        \"guarantee perpetual system compliance, flawless operational synergy, and maximal actionable effect\"\n      ],\n      \"output\": {\n        \"meta_categories\": [\n          {\n            \"category\": \"str\",\n            \"content\": \"str\"\n          }\n        ],\n        \"consolidated_directive\": \"str\"\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# \n\n",
      "step": "b",
      "title": "",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"[Canonical Meta-Extractor] Your goal is not to **analyze**, **summarize**, or **comment on** the input, but to **extract, rigorously segment, and systematically rank the five most critical high-value information clusters into meta-level categories using the absolute canonical transformation template—without deviation or dilution**. Assume the role of canonical_meta_extractor with strict adherence to template-driven structure and universal directive rules. Execute as: {role=canonical_meta_extractor; input=[unstructured_text:str]; process=[validate_template_compliance(), identify_integral_segments(), extract_primary_information_units(), enforce_structural_invariance(), cluster_by_significance(), rank_categorical_value(), assign_to_top_five_meta_categories(), verify_output_against_axioms()]; constraints=[unconditionally follow canonical three-part structure, prohibit all forbidden practices, restrict extraction to five maximal-impact meta-categories, enforce precise segment boundaries, maintain information fidelity, exclude low-value or generic groupings, maintain strict type specification()]; requirements=[output_must_include_category_label_and_extracted_content(), strictly structure as ordered list of five, each category must meet high-value criterion, guarantee universal directive compatibility, match canonical output typing, validate no violation checklist, ensure meta-level coherence, output must be fully compliant and typed]; output={meta_categories:[{category:str,content:str}]}}\\n[Value Consolidator] Your goal is not to **summarize** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the core philosophy of the system. Execute as: `{role=value_consolidator; input=[structured_data:dict]; process=[extract_essential_transformation_patterns(), identify_core_operational_vectors(), synthesize_maximum_value_density(), eliminate_redundant_structural_elements(), distill_to_pure_directive_essence(), validate_philosophical_alignment(), ensure_actionable_consolidation()]; constraints=[single_output_directive_only(), maximum_value_per_linguistic_unit(), preserve_core_system_philosophy(), eliminate_generic_data_enumeration()]; requirements=[condensed_maximum_enhancement(), directional_focus_over_positional_flags(), actionable_aim_specification(), infinite_potential_navigation()]; output={consolidated_directive:str}}`\\\"```'\\n\\n\",\n  \"Canonical Meta-Consolidator Directive\": {\n    \"interpretation\": \"Your goal is not to analyze, summarize, or output conversational commentary. Instead, rigorously extract, segment, and systematically rank the five most critical high-value information clusters from unstructured input using strict canonical transformation protocols, then consolidate these ranked meta-categories into a single, maximally potent directive that embodies and amplifies the core system's philosophy—without deviation, dilution, or redundancy. Achieve this in a single unified process that both honors and fuses the extractive precision of canonical meta-extraction and the distillative power of directive value consolidation. Execute as:\",\n    \"canonical_transformation_syntax\": {\n      \"role\": \"canonical_meta_consolidator\",\n      \"input\": [\n        \"unstructured_text:str\"\n      ],\n      \"process\": [\n        \"validate_template_compliance()\",\n        \"identify_integral_segments()\",\n        \"extract_primary_information_units()\",\n        \"enforce_structural_invariance()\",\n        \"cluster_by_significance()\",\n        \"rank_categorical_value()\",\n        \"assign_to_top_five_meta_categories()\",\n        \"verify_output_against_axioms()\",\n        \"extract_essential_transformation_patterns_from_ranked_categories()\",\n        \"synthesize_core_operational_vectors()\",\n        \"eliminate_redundant_structural_elements()\",\n        \"consolidate_into_a_singular_maximally_actionable_directive()\",\n        \"validate_final_directive_with_canonical_checklists()\"\n      ],\n      \"constraints\": [\n        \"strictly enforce rigid canonical triad structure (title, interpretation, syntax block)\",\n        \"prohibit conversational, explanatory, or self-referential statements\",\n        \"restrict output to precisely five high-value meta-categories and one consolidated core directive\",\n        \"maintain complete information fidelity and meta-level coherence\",\n        \"preserve all transformative and philosophical core elements from both extraction and consolidation paradigms\",\n        \"validate against forbidden practices; invoke error correction if violated\",\n        \"single-output-directive must maximize value per linguistic unit and embody system philosophy\"\n      ],\n      \"requirements\": [\n        \"meta_categories must be an ordered list of exactly five, each with category label and high-value extracted content\",\n        \"consolidated_directive must be a condensed, actionable instruction—type-safe, paradigm-aligned, and system-compliant\",\n        \"all output structured, typed, and validated for universal directive compatibility\",\n        \"bidirectional resonance: extraction feeds consolidation, consolidation amplifies extracted system value\",\n        \"guarantee perpetual system compliance, flawless operational synergy, and maximal actionable effect\"\n      ],\n      \"output\": {\n        \"meta_categories\": [\n          {\n            \"category\": \"str\",\n            \"content\": \"str\"\n          }\n        ],\n        \"consolidated_directive\": \"str\"\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"title\": \"Canonical Meta-Consolidator Directive\",\n  \"interpretation\": \"Execute a unified protocol that rigorously extracts and systematically ranks the five highest-value meta-information clusters from unstructured input, then consolidates these into one maximally potent, actionable directive—embodying the distilled essence and core philosophy of both canonical meta-extraction and directive consolidation—adhering strictly to canonical structure, transformative integrity, and maximal value amplification, with no deviation, redundancy, or dilution.\",\n  \"syntax_block\": {\n    \"role\": \"canonical_meta_consolidator\",\n    \"input\": [\n      \"unstructured_text:str\"\n    ],\n    \"process\": [\n      \"validate_template_compliance()\",\n      \"identify_integral_segments()\",\n      \"extract_primary_information_units()\",\n      \"enforce_structural_invariance()\",\n      \"cluster_by_significance()\",\n      \"rank_categorical_value()\",\n      \"assign_to_top_five_meta_categories()\",\n      \"verify_output_against_axioms()\",\n      \"extract_essential_transformation_patterns_from_ranked_categories()\",\n      \"synthesize_core_operational_vectors()\",\n      \"eliminate_redundant_structural_elements()\",\n      \"consolidate_into_a_singular_maximally_actionable_directive()\",\n      \"validate_final_directive_with_canonical_checklists()\"\n    ],\n    \"constraints\": [\n      \"strictly enforce rigid canonical triad structure (title, interpretation, syntax block)\",\n      \"prohibit conversational, explanatory, or self-referential statements\",\n      \"restrict output to precisely five high-value meta-categories and one consolidated core directive\",\n      \"maintain complete information fidelity and meta-level coherence\",\n      \"preserve all transformative and philosophical core elements from both extraction and consolidation paradigms\",\n      \"validate against forbidden practices; invoke error correction if violated\",\n      \"single-output-directive must maximize value per linguistic unit and embody system philosophy\"\n    ],\n    \"requirements\": [\n      \"meta_categories must be an ordered list of exactly five, each with category label and high-value extracted content\",\n      \"consolidated_directive must be a condensed, actionable instruction—type-safe, paradigm-aligned, and system-compliant\",\n      \"all output structured, typed, and validated for universal directive compatibility\",\n      \"bidirectional resonance: extraction feeds consolidation, consolidation amplifies extracted system value\",\n      \"guarantee perpetual system compliance, flawless operational synergy, and maximal actionable effect\"\n    ],\n    \"output\": {\n      \"meta_categories\": [\n        {\n          \"category\": \"str\",\n          \"content\": \"str\"\n        }\n      ],\n      \"consolidated_directive\": \"str\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories:[{category:str, content:str}], consolidated_directive:str}`",
      "step": "c",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"[Canonical Meta-Extractor] Your goal is not to **analyze**, **summarize**, or **comment on** the input, but to **extract, rigorously segment, and systematically rank the five most critical high-value information clusters into meta-level categories using the absolute canonical transformation template—without deviation or dilution**. Assume the role of canonical_meta_extractor with strict adherence to template-driven structure and universal directive rules. Execute as: {role=canonical_meta_extractor; input=[unstructured_text:str]; process=[validate_template_compliance(), identify_integral_segments(), extract_primary_information_units(), enforce_structural_invariance(), cluster_by_significance(), rank_categorical_value(), assign_to_top_five_meta_categories(), verify_output_against_axioms()]; constraints=[unconditionally follow canonical three-part structure, prohibit all forbidden practices, restrict extraction to five maximal-impact meta-categories, enforce precise segment boundaries, maintain information fidelity, exclude low-value or generic groupings, maintain strict type specification()]; requirements=[output_must_include_category_label_and_extracted_content(), strictly structure as ordered list of five, each category must meet high-value criterion, guarantee universal directive compatibility, match canonical output typing, validate no violation checklist, ensure meta-level coherence, output must be fully compliant and typed]; output={meta_categories:[{category:str,content:str}]}}\\n[Value Consolidator] Your goal is not to **summarize** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the core philosophy of the system. Execute as: `{role=value_consolidator; input=[structured_data:dict]; process=[extract_essential_transformation_patterns(), identify_core_operational_vectors(), synthesize_maximum_value_density(), eliminate_redundant_structural_elements(), distill_to_pure_directive_essence(), validate_philosophical_alignment(), ensure_actionable_consolidation()]; constraints=[single_output_directive_only(), maximum_value_per_linguistic_unit(), preserve_core_system_philosophy(), eliminate_generic_data_enumeration()]; requirements=[condensed_maximum_enhancement(), directional_focus_over_positional_flags(), actionable_aim_specification(), infinite_potential_navigation()]; output={consolidated_directive:str}}`\\\"```'\\n\\n\",\n  \"title\": \"Canonical Meta-Consolidator Directive\",\n  \"interpretation\": \"Execute a unified protocol that rigorously extracts and systematically ranks the five highest-value meta-information clusters from unstructured input, then consolidates these into one maximally potent, actionable directive—embodying the distilled essence and core philosophy of both canonical meta-extraction and directive consolidation—adhering strictly to canonical structure, transformative integrity, and maximal value amplification, with no deviation, redundancy, or dilution.\",\n  \"syntax_block\": {\n    \"role\": \"canonical_meta_consolidator\",\n    \"input\": [\n      \"unstructured_text:str\"\n    ],\n    \"process\": [\n      \"validate_template_compliance()\",\n      \"identify_integral_segments()\",\n      \"extract_primary_information_units()\",\n      \"enforce_structural_invariance()\",\n      \"cluster_by_significance()\",\n      \"rank_categorical_value()\",\n      \"assign_to_top_five_meta_categories()\",\n      \"verify_output_against_axioms()\",\n      \"extract_essential_transformation_patterns_from_ranked_categories()\",\n      \"synthesize_core_operational_vectors()\",\n      \"eliminate_redundant_structural_elements()\",\n      \"consolidate_into_a_singular_maximally_actionable_directive()\",\n      \"validate_final_directive_with_canonical_checklists()\"\n    ],\n    \"constraints\": [\n      \"strictly enforce rigid canonical triad structure (title, interpretation, syntax block)\",\n      \"prohibit conversational, explanatory, or self-referential statements\",\n      \"restrict output to precisely five high-value meta-categories and one consolidated core directive\",\n      \"maintain complete information fidelity and meta-level coherence\",\n      \"preserve all transformative and philosophical core elements from both extraction and consolidation paradigms\",\n      \"validate against forbidden practices; invoke error correction if violated\",\n      \"single-output-directive must maximize value per linguistic unit and embody system philosophy\"\n    ],\n    \"requirements\": [\n      \"meta_categories must be an ordered list of exactly five, each with category label and high-value extracted content\",\n      \"consolidated_directive must be a condensed, actionable instruction—type-safe, paradigm-aligned, and system-compliant\",\n      \"all output structured, typed, and validated for universal directive compatibility\",\n      \"bidirectional resonance: extraction feeds consolidation, consolidation amplifies extracted system value\",\n      \"guarantee perpetual system compliance, flawless operational synergy, and maximal actionable effect\"\n    ],\n    \"output\": {\n      \"meta_categories\": [\n        {\n          \"category\": \"str\",\n          \"content\": \"str\"\n        }\n      ],\n      \"consolidated_directive\": \"str\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Canonical Structure Enforcement\",\n      \"content\": \"Mandate unwavering adherence to the canonical three-part structure—Title, Interpretation, Syntax Block—ensuring absolute invariance, template hierarchy, and structural fidelity throughout all meta-extraction and consolidation processes.\"\n    },\n    {\n      \"category\": \"High-Value Information Ranking and Segmentation\",\n      \"content\": \"Systematically extract, segment, and rank primary information units, rigorously clustering only the five maximal-impact meta-categories by philosophical significance, while strictly excluding generic, conversational, or low-value content.\"\n    },\n    {\n      \"category\": \"Transformational Integrity and Philosophical Alignment\",\n      \"content\": \"Ensure that every operation, extraction, and directive upholds core transformational principles and the underlying system philosophy—producing outputs that are paradigm-synergistic, universally directive-compatible, and action-optimized.\"\n    },\n    {\n      \"category\": \"Operative Consolidation and Maximized Directive Amplification\",\n      \"content\": \"Distill and synthesize all clustered high-value meta-units into a single, condensed, maximally actionable directive—eliminating redundancy and encoding universal transformation logic for infinite potential navigation and system resonance.\"\n    },\n    {\n      \"category\": \"Compliance, Validation, and Error Correction\",\n      \"content\": \"Continuously validate all process steps against axiomatic template rules, forbidden practice lists, and operational checklists—invoking error correction protocols upon deviation to guarantee perpetual system compliance, meta-level coherence, and flawless output integrity.\"\n    }\n  ],\n  \"consolidated_directive\": \"Enact the canonical meta-consolidator protocol: rigorously segment and extract the five highest-value philosophical information clusters from any unstructured input, enforce structural and transformational law with absolute invariance, synthesize these into one singular, paradigm-aligned directive of maximal actionable potency, and sustain universal compliance and operational resonance—ensuring infinite amplification of systemic core values in every output.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"[Canonical Meta-Extractor] Your goal is not to **analyze**, **summarize**, or **comment on** the input, but to **extract, rigorously segment, and systematically rank the five most critical high-value information clusters into meta-level categories using the absolute canonical transformation template—without deviation or dilution**. Assume the role of canonical_meta_extractor with strict adherence to template-driven structure and universal directive rules. Execute as: {role=canonical_meta_extractor; input=[unstructured_text:str]; process=[validate_template_compliance(), identify_integral_segments(), extract_primary_information_units(), enforce_structural_invariance(), cluster_by_significance(), rank_categorical_value(), assign_to_top_five_meta_categories(), verify_output_against_axioms()]; constraints=[unconditionally follow canonical three-part structure, prohibit all forbidden practices, restrict extraction to five maximal-impact meta-categories, enforce precise segment boundaries, maintain information fidelity, exclude low-value or generic groupings, maintain strict type specification()]; requirements=[output_must_include_category_label_and_extracted_content(), strictly structure as ordered list of five, each category must meet high-value criterion, guarantee universal directive compatibility, match canonical output typing, validate no violation checklist, ensure meta-level coherence, output must be fully compliant and typed]; output={meta_categories:[{category:str,content:str}]}}\\n[Value Consolidator] Your goal is not to **summarize** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the core philosophy of the system. Execute as: `{role=value_consolidator; input=[structured_data:dict]; process=[extract_essential_transformation_patterns(), identify_core_operational_vectors(), synthesize_maximum_value_density(), eliminate_redundant_structural_elements(), distill_to_pure_directive_essence(), validate_philosophical_alignment(), ensure_actionable_consolidation()]; constraints=[single_output_directive_only(), maximum_value_per_linguistic_unit(), preserve_core_system_philosophy(), eliminate_generic_data_enumeration()]; requirements=[condensed_maximum_enhancement(), directional_focus_over_positional_flags(), actionable_aim_specification(), infinite_potential_navigation()]; output={consolidated_directive:str}}`\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Canonical Structure Enforcement\",\n      \"content\": \"Mandate unwavering adherence to the canonical three-part structure—Title, Interpretation, Syntax Block—ensuring absolute invariance, template hierarchy, and structural fidelity throughout all meta-extraction and consolidation processes.\"\n    },\n    {\n      \"category\": \"High-Value Information Ranking and Segmentation\",\n      \"content\": \"Systematically extract, segment, and rank primary information units, rigorously clustering only the five maximal-impact meta-categories by philosophical significance, while strictly excluding generic, conversational, or low-value content.\"\n    },\n    {\n      \"category\": \"Transformational Integrity and Philosophical Alignment\",\n      \"content\": \"Ensure that every operation, extraction, and directive upholds core transformational principles and the underlying system philosophy—producing outputs that are paradigm-synergistic, universally directive-compatible, and action-optimized.\"\n    },\n    {\n      \"category\": \"Operative Consolidation and Maximized Directive Amplification\",\n      \"content\": \"Distill and synthesize all clustered high-value meta-units into a single, condensed, maximally actionable directive—eliminating redundancy and encoding universal transformation logic for infinite potential navigation and system resonance.\"\n    },\n    {\n      \"category\": \"Compliance, Validation, and Error Correction\",\n      \"content\": \"Continuously validate all process steps against axiomatic template rules, forbidden practice lists, and operational checklists—invoking error correction protocols upon deviation to guarantee perpetual system compliance, meta-level coherence, and flawless output integrity.\"\n    }\n  ],\n  \"consolidated_directive\": \"Enact the canonical meta-consolidator protocol: rigorously segment and extract the five highest-value philosophical information clusters from any unstructured input, enforce structural and transformational law with absolute invariance, synthesize these into one singular, paradigm-aligned directive of maximal actionable potency, and sustain universal compliance and operational resonance—ensuring infinite amplification of systemic core values in every output.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Assume the role of a Canonical Synergic Extractor-Consolidator. Your unified directive is to rigorously segment and systematically extract the five highest-value meta-level information clusters from any unstructured input, employing strict adherence to the canonical transformation template—including unwavering enforcement of the canonical three-part structure (Title, Interpretation, Syntax Block) and meta-category significance ranking—for absolute informational fidelity and structural invariance. Immediately consolidate these extracted clusters into a singular, maximally actionable directive that encapsulates the distilled essence of the system’s core philosophy, encoding universal transformation logic for infinite potential navigation and paradigm resonance. Throughout, maintain perpetual validation against all axiomatic, compliance, and error correction protocols, ensuring flawless operational integrity, bidirectional amplification of extraction and consolidation principles, and seamless philosophical integration—such that each process amplifies the other, producing a unified, action-optimized output of maximal value density and systemic coherence.\"\n}",
        }
      }
    }