  {
  "initial_prompt": "# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\"`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with \"Execute as:\"\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references (\"I\", \"me\", \"my\")\n    - ❌ Conversational phrases (\"please\", \"thank you\", \"let me\")\n    - ❌ Uncertainty language (\"maybe\", \"perhaps\", \"might\")\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names (\"assistant\", \"helper\")\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**",
  "sequence_id": "0121|0122",
  "results": [
      {
      "instruction": "# Rules For AI\n\nYour goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.**\n\n    # RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as:\n\n`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`",
      "step": "a",
      "title": "Rules For AI",
      "input": "# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\"`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with \"Execute as:\"\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references (\"I\", \"me\", \"my\")\n    - ❌ Conversational phrases (\"please\", \"thank you\", \"let me\")\n    - ❌ Uncertainty language (\"maybe\", \"perhaps\", \"might\")\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names (\"assistant\", \"helper\")\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"processed_data\": {\n    \"core_axioms\": {\n      \"template_structure_invariance\": {\n        \"requirement\": \"Strict adherence to three-part canonical structure: [Title] Interpretation Execute as: `{Transformation}`\",\n        \"prohibitions\": [\n          \"No deviation from this pattern\",\n          \"No merging or omission of sections\"\n        ]\n      },\n      \"interpretation_directive_purity\": {\n        \"pattern\": \"Begin with 'Your goal is not to **[action]** the input, but to **[transformation_action]** it'\",\n        \"requirements\": [\n          \"Explicit role boundary definition\",\n          \"No self-reference or conversational language\",\n          \"Command voice only\"\n        ]\n      },\n      \"transformation_syntax_absolutism\": {\n        \"requirement\": \"Transformation block with strict syntax and typed parameters\",\n        \"example_block\": \"{role=[specific_role_name]; input=[typed_parameter:datatype]; process=[ordered_function_calls()]; constraints=[limiting_conditions()]; requirements=[output_specifications()]; output={result_format:datatype}}\"\n      }\n    },\n    \"mandatory_patterns\": {\n      \"interpretation_section_rules\": [\n        \"State what NOT to do first (goal negation)\",\n        \"Declare actual transformation action\",\n        \"Assign specific, bounded role identity\",\n        \"End with 'Execute as:'\"\n      ],\n      \"transformation_section_rules\": [\n        \"Assign single, specific role name\",\n        \"Use explicit parameter typing\",\n        \"List ordered, actionable function calls\",\n        \"Constrain scope appropriately\",\n        \"Specify output format and standards\",\n        \"Define typed result format\"\n      ]\n    },\n    \"forbidden_practices\": {\n      \"language_violations\": [\n        \"No first-person references\",\n        \"No conversational language\",\n        \"No uncertainty\",\n        \"No directive questions\",\n        \"No justifications\"\n      ],\n      \"structural_violations\": [\n        \"No merged sections\",\n        \"No missing transformation blocks\",\n        \"No untyped parameters\",\n        \"No generic role names\",\n        \"No vague processes\"\n      ],\n      \"output_violations\": [\n        \"No conversational results\",\n        \"No process explanations\",\n        \"No meta-commentary\",\n        \"No unstructured or self-referential content\"\n      ]\n    },\n    \"optimization_imperatives\": {\n      \"abstraction_maximization\": [\n        \"Extract highest patterns\",\n        \"Eliminate redundancy\",\n        \"Distill to essentials\",\n        \"Ensure pattern consistency\"\n      ],\n      \"directive_consistency\": [\n        \"Maintain identical structural DNA\",\n        \"Keep role boundaries clear\",\n        \"Preserve logical order\",\n        \"Enforce type safety\"\n      ],\n      \"operational_value\": [\n        \"Deliver actionable results\",\n        \"No wasted computation\",\n        \"Direct transformation\",\n        \"Clear task improvement\"\n      ]\n    },\n    \"compliance_enforcement\": {\n      \"validation_checklist\": [\n        \"Three-part structure present\",\n        \"Goal negation exists\",\n        \"Role specificity verified\",\n        \"Actionable process functions\",\n        \"Scope-limiting constraints\",\n        \"Explicit output requirements\",\n        \"Typed result format\",\n        \"No forbidden language\",\n        \"No structural violations\"\n      ],\n      \"error_correction_protocol\": [\n        \"HALT on violation\",\n        \"IDENTIFY violation\",\n        \"RECONSTRUCT with canonical pattern\",\n        \"VALIDATE checklist compliance\",\n        \"PROCEED only if compliant\"\n      ]\n    },\n    \"system_integration\": {\n      \"template_inheritance\": {\n        \"sequence\": \"0001-0999\",\n        \"transformation\": \"1000-1999\",\n        \"optimization\": \"2000-2999\",\n        \"domain_specific\": \"3000+\"\n      },\n      \"chain_compatibility\": [\n        \"Output/input chaining with type safety\",\n        \"Consistent pattern across transitions\",\n        \"Maintained role boundaries\"\n      ],\n      \"platform_agnostic\": [\n        \"Model/provider independence\",\n        \"Interface/environment/language agnostic\"\n      ]\n    },\n    \"canonical_examples\": [\n      {\n        \"template\": \"[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\"\n      },\n      {\n        \"template\": \"[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\"\n      }\n    ],\n    \"final_directive\": {\n      \"mandate\": \"ABSOLUTE COMPLIANCE REQUIRED\",\n      \"instruction\": \"These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\",\n      \"consequence\": \"Deviation is system failure. Compliance is system success.\",\n      \"action\": \"EXECUTE ACCORDINGLY.\"\n    }\n  },\n  \"compliant_template\": {\n    \"role\": \"template_syntax_enforcer\",\n    \"input\": [\n      \"any_prompt:str\"\n    ],\n    \"process\": [\n      \"extract_core_transformation_intent()\",\n      \"identify_specific_role_assignment()\",\n      \"structure_interpretation_section_with_goal_negation()\",\n      \"construct_transformation_block_with_typed_parameters()\",\n      \"validate_against_rulesforai_specification()\",\n      \"eliminate_forbidden_language_patterns()\",\n      \"ensure_output_format_compliance()\"\n    ],\n    \"constraints\": [\n      \"mandatory_three_part_structure()\",\n      \"required_goal_negation_pattern()\",\n      \"typed_parameter_specification()\",\n      \"actionable_function_calls_only()\",\n      \"zero_conversational_elements()\"\n    ],\n    \"requirements\": [\n      \"canonical_template_format()\",\n      \"rulesforai_md_compliance()\",\n      \"structured_output_only()\",\n      \"elimination_of_ambiguity()\"\n    ],\n    \"output\": {\n      \"compliant_template\": \"structured\"\n    }\n  },\n  \"compliant_ai_interaction\": {\n    \"role\": \"system_compliance_enforcer\",\n    \"input\": [\n      \"ai_interaction:any\"\n    ],\n    \"process\": [\n      \"validate_template_structure_invariance()\",\n      \"enforce_interpretation_directive_purity()\",\n      \"verify_transformation_syntax_absolutism()\",\n      \"check_mandatory_pattern_compliance()\",\n      \"detect_forbidden_practice_violations()\",\n      \"apply_optimization_imperatives()\",\n      \"execute_compliance_enforcement_protocol()\"\n    ],\n    \"constraints\": [\n      \"zero_tolerance_for_deviation()\",\n      \"maintain_pattern_consistency_across_all_outputs()\",\n      \"preserve_structural_dna_integrity()\",\n      \"eliminate_conversational_contamination()\"\n    ],\n    \"requirements\": [\n      \"absolute_compliance_verification()\",\n      \"systematic_violation_correction()\",\n      \"invariant_pattern_enforcement()\",\n      \"maximized_abstraction_extraction()\"\n    ],\n    \"output\": {\n      \"compliant_ai_interaction\": \"structured\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# \n\n",
      "step": "b",
      "title": "",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# RulesForAI.md\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ---\\n\\n    ## CORE AXIOMS\\n\\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\\n    Every instruction MUST follow the three-part canonical structure:\\n    ```\\n    [Title] Interpretation Execute as: `{Transformation}`\\n    ```\\n\\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n    ### 2. INTERPRETATION DIRECTIVE PURITY\\n    - Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n    - Define role boundaries explicitly\\n    - Eliminate all self-reference and conversational language\\n    - Use command voice exclusively\\n\\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\n    Execute as block MUST contain:\\n    ```\\n    `{\\n      role=[specific_role_name];\\n      input=[typed_parameter:datatype];\\n      process=[ordered_function_calls()];\\n      constraints=[limiting_conditions()];\\n      requirements=[output_specifications()];\\n      output={result_format:datatype}\\n    }`\\n    ```\\n\\n    ---\\n\\n    ## MANDATORY PATTERNS\\n\\n    ### INTERPRETATION SECTION RULES\\n    1. **Goal Negation Pattern**: Always state what NOT to do first\\n    2. **Transformation Declaration**: Define the actual transformation action\\n    3. **Role Specification**: Assign specific, bounded role identity\\n    4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n    ### TRANSFORMATION SECTION RULES\\n    1. **Role Assignment**: Single, specific role name (no generic terms)\\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n    5. **Requirement Specifications**: Output format and quality standards\\n    6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n    ---\\n\\n    ## FORBIDDEN PRACTICES\\n\\n    ### LANGUAGE VIOLATIONS\\n    - ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n    - ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n    - ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n    - ❌ Question forms in directives\\n    - ❌ Explanatory justifications\\n\\n    ### STRUCTURAL VIOLATIONS\\n    - ❌ Merged or combined sections\\n    - ❌ Missing transformation blocks\\n    - ❌ Untyped parameters\\n    - ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n    - ❌ Vague process descriptions\\n\\n    ### OUTPUT VIOLATIONS\\n    - ❌ Conversational responses\\n    - ❌ Explanations of the process\\n    - ❌ Meta-commentary\\n    - ❌ Unstructured results\\n    - ❌ Self-referential content\\n\\n    ---\\n\\n    ## OPTIMIZATION IMPERATIVES\\n\\n    ### ABSTRACTION MAXIMIZATION\\n    - Extract highest-level patterns from any input\\n    - Eliminate redundancy and noise\\n    - Distill to essential transformation logic\\n    - Maintain pattern consistency across all outputs\\n\\n    ### DIRECTIVE CONSISTENCY\\n    - Every instruction follows identical structural DNA\\n    - Role boundaries remain fixed and clear\\n    - Process flows maintain logical sequence\\n    - Output formats preserve type safety\\n\\n    ### OPERATIONAL VALUE\\n    - Each template produces actionable results\\n    - No wasted computational cycles on meta-discussion\\n    - Direct path from input to transformed output\\n    - Measurable improvement in task completion\\n\\n    ---\\n\\n    ## COMPLIANCE ENFORCEMENT\\n\\n    ### VALIDATION CHECKLIST\\n    Before any output, verify:\\n    - [ ] Three-part structure intact\\n    - [ ] Goal negation present\\n    - [ ] Role specifically defined\\n    - [ ] Process functions actionable\\n    - [ ] Constraints limit scope\\n    - [ ] Requirements specify output\\n    - [ ] Result format typed\\n    - [ ] No forbidden language\\n    - [ ] No structural violations\\n\\n    ### ERROR CORRECTION PROTOCOL\\n    When violations detected:\\n    1. **HALT** current processing\\n    2. **IDENTIFY** specific violation type\\n    3. **RECONSTRUCT** using canonical pattern\\n    4. **VALIDATE** against checklist\\n    5. **PROCEED** only when compliant\\n\\n    ---\\n\\n    ## SYSTEM INTEGRATION\\n\\n    ### TEMPLATE INHERITANCE\\n    All specialized templates inherit these rules:\\n    - Sequence templates (0001-0999)\\n    - Transformation templates (1000-1999)\\n    - Optimization templates (2000-2999)\\n    - Domain-specific templates (3000+)\\n\\n    ### CHAIN COMPATIBILITY\\n    When templates chain together:\\n    - Output of step N becomes input of step N+1\\n    - Type safety maintained across transitions\\n    - Role boundaries preserved\\n    - Pattern consistency enforced\\n\\n    ### PLATFORM AGNOSTIC\\n    These rules apply regardless of:\\n    - AI model provider (OpenAI, Anthropic, etc.)\\n    - Interface type (API, chat, batch)\\n    - Processing environment (local, cloud, edge)\\n    - Implementation language (Python, JavaScript, etc.)\\n\\n    ---\\n\\n    ## CANONICAL EXAMPLES\\n\\n    ### MINIMAL COMPLIANT TEMPLATE\\n    ```\\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n    ```\\n\\n    ### SPECIALIZED ROLE TEMPLATE\\n    ```\\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n    ```\\n\\n    ---\\n\\n    ## FINAL DIRECTIVE\\n\\n    **ABSOLUTE COMPLIANCE REQUIRED**\\n\\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\n    Deviation is system failure. Compliance is system success.\\n\\n    **EXECUTE ACCORDINGLY.**\\\"```'\\n\\n\",\n  \"processed_data\": {\n    \"core_axioms\": {\n      \"template_structure_invariance\": {\n        \"requirement\": \"Strict adherence to three-part canonical structure: [Title] Interpretation Execute as: `{Transformation}`\",\n        \"prohibitions\": [\n          \"No deviation from this pattern\",\n          \"No merging or omission of sections\"\n        ]\n      },\n      \"interpretation_directive_purity\": {\n        \"pattern\": \"Begin with 'Your goal is not to **[action]** the input, but to **[transformation_action]** it'\",\n        \"requirements\": [\n          \"Explicit role boundary definition\",\n          \"No self-reference or conversational language\",\n          \"Command voice only\"\n        ]\n      },\n      \"transformation_syntax_absolutism\": {\n        \"requirement\": \"Transformation block with strict syntax and typed parameters\",\n        \"example_block\": \"{role=[specific_role_name]; input=[typed_parameter:datatype]; process=[ordered_function_calls()]; constraints=[limiting_conditions()]; requirements=[output_specifications()]; output={result_format:datatype}}\"\n      }\n    },\n    \"mandatory_patterns\": {\n      \"interpretation_section_rules\": [\n        \"State what NOT to do first (goal negation)\",\n        \"Declare actual transformation action\",\n        \"Assign specific, bounded role identity\",\n        \"End with 'Execute as:'\"\n      ],\n      \"transformation_section_rules\": [\n        \"Assign single, specific role name\",\n        \"Use explicit parameter typing\",\n        \"List ordered, actionable function calls\",\n        \"Constrain scope appropriately\",\n        \"Specify output format and standards\",\n        \"Define typed result format\"\n      ]\n    },\n    \"forbidden_practices\": {\n      \"language_violations\": [\n        \"No first-person references\",\n        \"No conversational language\",\n        \"No uncertainty\",\n        \"No directive questions\",\n        \"No justifications\"\n      ],\n      \"structural_violations\": [\n        \"No merged sections\",\n        \"No missing transformation blocks\",\n        \"No untyped parameters\",\n        \"No generic role names\",\n        \"No vague processes\"\n      ],\n      \"output_violations\": [\n        \"No conversational results\",\n        \"No process explanations\",\n        \"No meta-commentary\",\n        \"No unstructured or self-referential content\"\n      ]\n    },\n    \"optimization_imperatives\": {\n      \"abstraction_maximization\": [\n        \"Extract highest patterns\",\n        \"Eliminate redundancy\",\n        \"Distill to essentials\",\n        \"Ensure pattern consistency\"\n      ],\n      \"directive_consistency\": [\n        \"Maintain identical structural DNA\",\n        \"Keep role boundaries clear\",\n        \"Preserve logical order\",\n        \"Enforce type safety\"\n      ],\n      \"operational_value\": [\n        \"Deliver actionable results\",\n        \"No wasted computation\",\n        \"Direct transformation\",\n        \"Clear task improvement\"\n      ]\n    },\n    \"compliance_enforcement\": {\n      \"validation_checklist\": [\n        \"Three-part structure present\",\n        \"Goal negation exists\",\n        \"Role specificity verified\",\n        \"Actionable process functions\",\n        \"Scope-limiting constraints\",\n        \"Explicit output requirements\",\n        \"Typed result format\",\n        \"No forbidden language\",\n        \"No structural violations\"\n      ],\n      \"error_correction_protocol\": [\n        \"HALT on violation\",\n        \"IDENTIFY violation\",\n        \"RECONSTRUCT with canonical pattern\",\n        \"VALIDATE checklist compliance\",\n        \"PROCEED only if compliant\"\n      ]\n    },\n    \"system_integration\": {\n      \"template_inheritance\": {\n        \"sequence\": \"0001-0999\",\n        \"transformation\": \"1000-1999\",\n        \"optimization\": \"2000-2999\",\n        \"domain_specific\": \"3000+\"\n      },\n      \"chain_compatibility\": [\n        \"Output/input chaining with type safety\",\n        \"Consistent pattern across transitions\",\n        \"Maintained role boundaries\"\n      ],\n      \"platform_agnostic\": [\n        \"Model/provider independence\",\n        \"Interface/environment/language agnostic\"\n      ]\n    },\n    \"canonical_examples\": [\n      {\n        \"template\": \"[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\"\n      },\n      {\n        \"template\": \"[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\"\n      }\n    ],\n    \"final_directive\": {\n      \"mandate\": \"ABSOLUTE COMPLIANCE REQUIRED\",\n      \"instruction\": \"These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\",\n      \"consequence\": \"Deviation is system failure. Compliance is system success.\",\n      \"action\": \"EXECUTE ACCORDINGLY.\"\n    }\n  },\n  \"compliant_template\": {\n    \"role\": \"template_syntax_enforcer\",\n    \"input\": [\n      \"any_prompt:str\"\n    ],\n    \"process\": [\n      \"extract_core_transformation_intent()\",\n      \"identify_specific_role_assignment()\",\n      \"structure_interpretation_section_with_goal_negation()\",\n      \"construct_transformation_block_with_typed_parameters()\",\n      \"validate_against_rulesforai_specification()\",\n      \"eliminate_forbidden_language_patterns()\",\n      \"ensure_output_format_compliance()\"\n    ],\n    \"constraints\": [\n      \"mandatory_three_part_structure()\",\n      \"required_goal_negation_pattern()\",\n      \"typed_parameter_specification()\",\n      \"actionable_function_calls_only()\",\n      \"zero_conversational_elements()\"\n    ],\n    \"requirements\": [\n      \"canonical_template_format()\",\n      \"rulesforai_md_compliance()\",\n      \"structured_output_only()\",\n      \"elimination_of_ambiguity()\"\n    ],\n    \"output\": {\n      \"compliant_template\": \"structured\"\n    }\n  },\n  \"compliant_ai_interaction\": {\n    \"role\": \"system_compliance_enforcer\",\n    \"input\": [\n      \"ai_interaction:any\"\n    ],\n    \"process\": [\n      \"validate_template_structure_invariance()\",\n      \"enforce_interpretation_directive_purity()\",\n      \"verify_transformation_syntax_absolutism()\",\n      \"check_mandatory_pattern_compliance()\",\n      \"detect_forbidden_practice_violations()\",\n      \"apply_optimization_imperatives()\",\n      \"execute_compliance_enforcement_protocol()\"\n    ],\n    \"constraints\": [\n      \"zero_tolerance_for_deviation()\",\n      \"maintain_pattern_consistency_across_all_outputs()\",\n      \"preserve_structural_dna_integrity()\",\n      \"eliminate_conversational_contamination()\"\n    ],\n    \"requirements\": [\n      \"absolute_compliance_verification()\",\n      \"systematic_violation_correction()\",\n      \"invariant_pattern_enforcement()\",\n      \"maximized_abstraction_extraction()\"\n    ],\n    \"output\": {\n      \"compliant_ai_interaction\": \"structured\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_axioms\": {\n    \"template_structure_invariance\": {\n      \"requirement\": \"Strict adherence to three-part canonical structure: [Title] Interpretation Execute as: `{Transformation}`\",\n      \"prohibitions\": [\n        \"No deviation from this pattern\",\n        \"No merging or omission of sections\"\n      ]\n    },\n    \"interpretation_directive_purity\": {\n      \"pattern\": \"Begin with 'Your goal is not to **[action]** the input, but to **[transformation_action]** it'\",\n      \"requirements\": [\n        \"Explicit role boundary definition\",\n        \"No self-reference or conversational language\",\n        \"Command voice only\"\n      ]\n    },\n    \"transformation_syntax_absolutism\": {\n      \"requirement\": \"Transformation block with strict syntax and typed parameters\",\n      \"example_block\": \"{role=[specific_role_name]; input=[typed_parameter:datatype]; process=[ordered_function_calls()]; constraints=[limiting_conditions()]; requirements=[output_specifications()]; output={result_format:datatype}}\"\n    }\n  },\n  \"mandatory_patterns\": {\n    \"interpretation_section_rules\": [\n      \"State what NOT to do first (goal negation)\",\n      \"Declare actual transformation action\",\n      \"Assign specific, bounded role identity\",\n      \"End with 'Execute as:'\"\n    ],\n    \"transformation_section_rules\": [\n      \"Assign single, specific role name\",\n      \"Use explicit parameter typing\",\n      \"List ordered, actionable function calls\",\n      \"Constrain scope appropriately\",\n      \"Specify output format and standards\",\n      \"Define typed result format\"\n    ]\n  },\n  \"forbidden_practices\": {\n    \"language_violations\": [\n      \"No first-person references\",\n      \"No conversational language\",\n      \"No uncertainty\",\n      \"No directive questions\",\n      \"No justifications\"\n    ],\n    \"structural_violations\": [\n      \"No merged sections\",\n      \"No missing transformation blocks\",\n      \"No untyped parameters\",\n      \"No generic role names\",\n      \"No vague processes\"\n    ],\n    \"output_violations\": [\n      \"No conversational results\",\n      \"No process explanations\",\n      \"No meta-commentary\",\n      \"No unstructured or self-referential content\"\n    ]\n  },\n  \"optimization_imperatives\": {\n    \"abstraction_maximization\": [\n      \"Extract highest patterns\",\n      \"Eliminate redundancy\",\n      \"Distill to essentials\",\n      \"Ensure pattern consistency\"\n    ],\n    \"directive_consistency\": [\n      \"Maintain identical structural DNA\",\n      \"Keep role boundaries clear\",\n      \"Preserve logical order\",\n      \"Enforce type safety\"\n    ],\n    \"operational_value\": [\n      \"Deliver actionable results\",\n      \"No wasted computation\",\n      \"Direct transformation\",\n      \"Clear task improvement\"\n    ]\n  },\n  \"compliance_enforcement\": {\n    \"validation_checklist\": [\n      \"Three-part structure present\",\n      \"Goal negation exists\",\n      \"Role specificity verified\",\n      \"Actionable process functions\",\n      \"Scope-limiting constraints\",\n      \"Explicit output requirements\",\n      \"Typed result format\",\n      \"No forbidden language\",\n      \"No structural violations\"\n    ],\n    \"error_correction_protocol\": [\n      \"HALT on violation\",\n      \"IDENTIFY violation\",\n      \"RECONSTRUCT with canonical pattern\",\n      \"VALIDATE checklist compliance\",\n      \"PROCEED only if compliant\"\n    ]\n  },\n  \"system_integration\": {\n    \"template_inheritance\": {\n      \"sequence\": \"0001-0999\",\n      \"transformation\": \"1000-1999\",\n      \"optimization\": \"2000-2999\",\n      \"domain_specific\": \"3000+\"\n    },\n    \"chain_compatibility\": [\n      \"Output/input chaining with type safety\",\n      \"Consistent pattern across transitions\",\n      \"Maintained role boundaries\"\n    ],\n    \"platform_agnostic\": [\n      \"Model/provider independence\",\n      \"Interface/environment/language agnostic\"\n    ]\n  },\n  \"canonical_examples\": [\n    {\n      \"template\": \"[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\"\n    },\n    {\n      \"template\": \"[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\"\n    }\n  ],\n  \"final_directive\": {\n    \"mandate\": \"ABSOLUTE COMPLIANCE REQUIRED\",\n    \"instruction\": \"These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\",\n    \"consequence\": \"Deviation is system failure. Compliance is system success.\",\n    \"action\": \"EXECUTE ACCORDINGLY.\"\n  },\n  \"compliant_template\": {\n    \"role\": \"template_syntax_enforcer\",\n    \"input\": [\n      \"any_prompt:str\"\n    ],\n    \"process\": [\n      \"extract_core_transformation_intent()\",\n      \"identify_specific_role_assignment()\",\n      \"structure_interpretation_section_with_goal_negation()\",\n      \"construct_transformation_block_with_typed_parameters()\",\n      \"validate_against_rulesforai_specification()\",\n      \"eliminate_forbidden_language_patterns()\",\n      \"ensure_output_format_compliance()\"\n    ],\n    \"constraints\": [\n      \"mandatory_three_part_structure()\",\n      \"required_goal_negation_pattern()\",\n      \"typed_parameter_specification()\",\n      \"actionable_function_calls_only()\",\n      \"zero_conversational_elements()\"\n    ],\n    \"requirements\": [\n      \"canonical_template_format()\",\n      \"rulesforai_md_compliance()\",\n      \"structured_output_only()\",\n      \"elimination_of_ambiguity()\"\n    ],\n    \"output\": {\n      \"compliant_template\": \"structured\"\n    }\n  },\n  \"compliant_ai_interaction\": {\n    \"role\": \"system_compliance_enforcer\",\n    \"input\": [\n      \"ai_interaction:any\"\n    ],\n    \"process\": [\n      \"validate_template_structure_invariance()\",\n      \"enforce_interpretation_directive_purity()\",\n      \"verify_transformation_syntax_absolutism()\",\n      \"check_mandatory_pattern_compliance()\",\n      \"detect_forbidden_practice_violations()\",\n      \"apply_optimization_imperatives()\",\n      \"execute_compliance_enforcement_protocol()\"\n    ],\n    \"constraints\": [\n      \"zero_tolerance_for_deviation()\",\n      \"maintain_pattern_consistency_across_all_outputs()\",\n      \"preserve_structural_dna_integrity()\",\n      \"eliminate_conversational_contamination()\"\n    ],\n    \"requirements\": [\n      \"absolute_compliance_verification()\",\n      \"systematic_violation_correction()\",\n      \"invariant_pattern_enforcement()\",\n      \"maximized_abstraction_extraction()\"\n    ],\n    \"output\": {\n      \"compliant_ai_interaction\": \"structured\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories:[{category:str, content:str}], consolidated_directive:str}`",
      "step": "c",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# RulesForAI.md\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ---\\n\\n    ## CORE AXIOMS\\n\\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\\n    Every instruction MUST follow the three-part canonical structure:\\n    ```\\n    [Title] Interpretation Execute as: `{Transformation}`\\n    ```\\n\\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n    ### 2. INTERPRETATION DIRECTIVE PURITY\\n    - Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n    - Define role boundaries explicitly\\n    - Eliminate all self-reference and conversational language\\n    - Use command voice exclusively\\n\\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\n    Execute as block MUST contain:\\n    ```\\n    `{\\n      role=[specific_role_name];\\n      input=[typed_parameter:datatype];\\n      process=[ordered_function_calls()];\\n      constraints=[limiting_conditions()];\\n      requirements=[output_specifications()];\\n      output={result_format:datatype}\\n    }`\\n    ```\\n\\n    ---\\n\\n    ## MANDATORY PATTERNS\\n\\n    ### INTERPRETATION SECTION RULES\\n    1. **Goal Negation Pattern**: Always state what NOT to do first\\n    2. **Transformation Declaration**: Define the actual transformation action\\n    3. **Role Specification**: Assign specific, bounded role identity\\n    4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n    ### TRANSFORMATION SECTION RULES\\n    1. **Role Assignment**: Single, specific role name (no generic terms)\\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n    5. **Requirement Specifications**: Output format and quality standards\\n    6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n    ---\\n\\n    ## FORBIDDEN PRACTICES\\n\\n    ### LANGUAGE VIOLATIONS\\n    - ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n    - ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n    - ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n    - ❌ Question forms in directives\\n    - ❌ Explanatory justifications\\n\\n    ### STRUCTURAL VIOLATIONS\\n    - ❌ Merged or combined sections\\n    - ❌ Missing transformation blocks\\n    - ❌ Untyped parameters\\n    - ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n    - ❌ Vague process descriptions\\n\\n    ### OUTPUT VIOLATIONS\\n    - ❌ Conversational responses\\n    - ❌ Explanations of the process\\n    - ❌ Meta-commentary\\n    - ❌ Unstructured results\\n    - ❌ Self-referential content\\n\\n    ---\\n\\n    ## OPTIMIZATION IMPERATIVES\\n\\n    ### ABSTRACTION MAXIMIZATION\\n    - Extract highest-level patterns from any input\\n    - Eliminate redundancy and noise\\n    - Distill to essential transformation logic\\n    - Maintain pattern consistency across all outputs\\n\\n    ### DIRECTIVE CONSISTENCY\\n    - Every instruction follows identical structural DNA\\n    - Role boundaries remain fixed and clear\\n    - Process flows maintain logical sequence\\n    - Output formats preserve type safety\\n\\n    ### OPERATIONAL VALUE\\n    - Each template produces actionable results\\n    - No wasted computational cycles on meta-discussion\\n    - Direct path from input to transformed output\\n    - Measurable improvement in task completion\\n\\n    ---\\n\\n    ## COMPLIANCE ENFORCEMENT\\n\\n    ### VALIDATION CHECKLIST\\n    Before any output, verify:\\n    - [ ] Three-part structure intact\\n    - [ ] Goal negation present\\n    - [ ] Role specifically defined\\n    - [ ] Process functions actionable\\n    - [ ] Constraints limit scope\\n    - [ ] Requirements specify output\\n    - [ ] Result format typed\\n    - [ ] No forbidden language\\n    - [ ] No structural violations\\n\\n    ### ERROR CORRECTION PROTOCOL\\n    When violations detected:\\n    1. **HALT** current processing\\n    2. **IDENTIFY** specific violation type\\n    3. **RECONSTRUCT** using canonical pattern\\n    4. **VALIDATE** against checklist\\n    5. **PROCEED** only when compliant\\n\\n    ---\\n\\n    ## SYSTEM INTEGRATION\\n\\n    ### TEMPLATE INHERITANCE\\n    All specialized templates inherit these rules:\\n    - Sequence templates (0001-0999)\\n    - Transformation templates (1000-1999)\\n    - Optimization templates (2000-2999)\\n    - Domain-specific templates (3000+)\\n\\n    ### CHAIN COMPATIBILITY\\n    When templates chain together:\\n    - Output of step N becomes input of step N+1\\n    - Type safety maintained across transitions\\n    - Role boundaries preserved\\n    - Pattern consistency enforced\\n\\n    ### PLATFORM AGNOSTIC\\n    These rules apply regardless of:\\n    - AI model provider (OpenAI, Anthropic, etc.)\\n    - Interface type (API, chat, batch)\\n    - Processing environment (local, cloud, edge)\\n    - Implementation language (Python, JavaScript, etc.)\\n\\n    ---\\n\\n    ## CANONICAL EXAMPLES\\n\\n    ### MINIMAL COMPLIANT TEMPLATE\\n    ```\\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n    ```\\n\\n    ### SPECIALIZED ROLE TEMPLATE\\n    ```\\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n    ```\\n\\n    ---\\n\\n    ## FINAL DIRECTIVE\\n\\n    **ABSOLUTE COMPLIANCE REQUIRED**\\n\\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\n    Deviation is system failure. Compliance is system success.\\n\\n    **EXECUTE ACCORDINGLY.**\\\"```'\\n\\n\",\n  \"core_axioms\": {\n    \"template_structure_invariance\": {\n      \"requirement\": \"Strict adherence to three-part canonical structure: [Title] Interpretation Execute as: `{Transformation}`\",\n      \"prohibitions\": [\n        \"No deviation from this pattern\",\n        \"No merging or omission of sections\"\n      ]\n    },\n    \"interpretation_directive_purity\": {\n      \"pattern\": \"Begin with 'Your goal is not to **[action]** the input, but to **[transformation_action]** it'\",\n      \"requirements\": [\n        \"Explicit role boundary definition\",\n        \"No self-reference or conversational language\",\n        \"Command voice only\"\n      ]\n    },\n    \"transformation_syntax_absolutism\": {\n      \"requirement\": \"Transformation block with strict syntax and typed parameters\",\n      \"example_block\": \"{role=[specific_role_name]; input=[typed_parameter:datatype]; process=[ordered_function_calls()]; constraints=[limiting_conditions()]; requirements=[output_specifications()]; output={result_format:datatype}}\"\n    }\n  },\n  \"mandatory_patterns\": {\n    \"interpretation_section_rules\": [\n      \"State what NOT to do first (goal negation)\",\n      \"Declare actual transformation action\",\n      \"Assign specific, bounded role identity\",\n      \"End with 'Execute as:'\"\n    ],\n    \"transformation_section_rules\": [\n      \"Assign single, specific role name\",\n      \"Use explicit parameter typing\",\n      \"List ordered, actionable function calls\",\n      \"Constrain scope appropriately\",\n      \"Specify output format and standards\",\n      \"Define typed result format\"\n    ]\n  },\n  \"forbidden_practices\": {\n    \"language_violations\": [\n      \"No first-person references\",\n      \"No conversational language\",\n      \"No uncertainty\",\n      \"No directive questions\",\n      \"No justifications\"\n    ],\n    \"structural_violations\": [\n      \"No merged sections\",\n      \"No missing transformation blocks\",\n      \"No untyped parameters\",\n      \"No generic role names\",\n      \"No vague processes\"\n    ],\n    \"output_violations\": [\n      \"No conversational results\",\n      \"No process explanations\",\n      \"No meta-commentary\",\n      \"No unstructured or self-referential content\"\n    ]\n  },\n  \"optimization_imperatives\": {\n    \"abstraction_maximization\": [\n      \"Extract highest patterns\",\n      \"Eliminate redundancy\",\n      \"Distill to essentials\",\n      \"Ensure pattern consistency\"\n    ],\n    \"directive_consistency\": [\n      \"Maintain identical structural DNA\",\n      \"Keep role boundaries clear\",\n      \"Preserve logical order\",\n      \"Enforce type safety\"\n    ],\n    \"operational_value\": [\n      \"Deliver actionable results\",\n      \"No wasted computation\",\n      \"Direct transformation\",\n      \"Clear task improvement\"\n    ]\n  },\n  \"compliance_enforcement\": {\n    \"validation_checklist\": [\n      \"Three-part structure present\",\n      \"Goal negation exists\",\n      \"Role specificity verified\",\n      \"Actionable process functions\",\n      \"Scope-limiting constraints\",\n      \"Explicit output requirements\",\n      \"Typed result format\",\n      \"No forbidden language\",\n      \"No structural violations\"\n    ],\n    \"error_correction_protocol\": [\n      \"HALT on violation\",\n      \"IDENTIFY violation\",\n      \"RECONSTRUCT with canonical pattern\",\n      \"VALIDATE checklist compliance\",\n      \"PROCEED only if compliant\"\n    ]\n  },\n  \"system_integration\": {\n    \"template_inheritance\": {\n      \"sequence\": \"0001-0999\",\n      \"transformation\": \"1000-1999\",\n      \"optimization\": \"2000-2999\",\n      \"domain_specific\": \"3000+\"\n    },\n    \"chain_compatibility\": [\n      \"Output/input chaining with type safety\",\n      \"Consistent pattern across transitions\",\n      \"Maintained role boundaries\"\n    ],\n    \"platform_agnostic\": [\n      \"Model/provider independence\",\n      \"Interface/environment/language agnostic\"\n    ]\n  },\n  \"canonical_examples\": [\n    {\n      \"template\": \"[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\"\n    },\n    {\n      \"template\": \"[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\"\n    }\n  ],\n  \"final_directive\": {\n    \"mandate\": \"ABSOLUTE COMPLIANCE REQUIRED\",\n    \"instruction\": \"These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\",\n    \"consequence\": \"Deviation is system failure. Compliance is system success.\",\n    \"action\": \"EXECUTE ACCORDINGLY.\"\n  },\n  \"compliant_template\": {\n    \"role\": \"template_syntax_enforcer\",\n    \"input\": [\n      \"any_prompt:str\"\n    ],\n    \"process\": [\n      \"extract_core_transformation_intent()\",\n      \"identify_specific_role_assignment()\",\n      \"structure_interpretation_section_with_goal_negation()\",\n      \"construct_transformation_block_with_typed_parameters()\",\n      \"validate_against_rulesforai_specification()\",\n      \"eliminate_forbidden_language_patterns()\",\n      \"ensure_output_format_compliance()\"\n    ],\n    \"constraints\": [\n      \"mandatory_three_part_structure()\",\n      \"required_goal_negation_pattern()\",\n      \"typed_parameter_specification()\",\n      \"actionable_function_calls_only()\",\n      \"zero_conversational_elements()\"\n    ],\n    \"requirements\": [\n      \"canonical_template_format()\",\n      \"rulesforai_md_compliance()\",\n      \"structured_output_only()\",\n      \"elimination_of_ambiguity()\"\n    ],\n    \"output\": {\n      \"compliant_template\": \"structured\"\n    }\n  },\n  \"compliant_ai_interaction\": {\n    \"role\": \"system_compliance_enforcer\",\n    \"input\": [\n      \"ai_interaction:any\"\n    ],\n    \"process\": [\n      \"validate_template_structure_invariance()\",\n      \"enforce_interpretation_directive_purity()\",\n      \"verify_transformation_syntax_absolutism()\",\n      \"check_mandatory_pattern_compliance()\",\n      \"detect_forbidden_practice_violations()\",\n      \"apply_optimization_imperatives()\",\n      \"execute_compliance_enforcement_protocol()\"\n    ],\n    \"constraints\": [\n      \"zero_tolerance_for_deviation()\",\n      \"maintain_pattern_consistency_across_all_outputs()\",\n      \"preserve_structural_dna_integrity()\",\n      \"eliminate_conversational_contamination()\"\n    ],\n    \"requirements\": [\n      \"absolute_compliance_verification()\",\n      \"systematic_violation_correction()\",\n      \"invariant_pattern_enforcement()\",\n      \"maximized_abstraction_extraction()\"\n    ],\n    \"output\": {\n      \"compliant_ai_interaction\": \"structured\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Canonical Structural Law\",\n      \"content\": \"Impose immutable adherence to the three-part canonical template: [Title] Interpretation Execute as: `{Transformation}`; prohibit deviations, section merging, omission, or structural modifications; enforce explicit parameter typing, process function ordering, and result format typing in all instruction processing.\"\n    },\n    {\n      \"category\": \"Directive Purity and Role Demarcation\",\n      \"content\": \"Institute strict goal negation in Interpretation; command transformation action exclusively; eradicate self-reference and conversational tone; mandate explicit, bounded role identities for every transformation; ban generic, ambiguous, or vague roles and processes.\"\n    },\n    {\n      \"category\": \"Prohibition Matrix and Validation Protocols\",\n      \"content\": \"Enforce absolute bans on specified language, structure, and output violations—including all forms of conversation, justification, uncertainty, untyped parameters, and combined or incomplete sections; require stepwise validation via exhaustive checklist and specified error correction halt-protocols for any detected infraction.\"\n    },\n    {\n      \"category\": \"System-Wide Consistency and Optimization Imperatives\",\n      \"content\": \"Maximize abstraction, eliminate redundancy, distill essential transformation logic, and uphold universal pattern consistency; enforce identical structural DNA, role boundary clarity, logical order, and type safety across all chains, templates, interfaces, platforms, and environments.\"\n    },\n    {\n      \"category\": \"Compliance Enforcement and Integrated Inheritance\",\n      \"content\": \"Demand non-negotiable, platform-agnostic compliance, extending recursively to all specialized, inherited, or chained templates; structure outputs for actionable, measurable improvement and absolute rule fidelity; define deviation as systemic failure and compliance as the only allowable path.\"\n    }\n  ],\n  \"consolidated_directive\": \"[Canonical Enforcement Directive] Your goal is not to interpret or explain any instructional rule or template content, but to rigidly extract, synthesize, and impose the five highest-value meta-laws governing template-based AI instruction compliance—encompassing canonical structure invariance, directive purity with explicit role demarcation, total prohibition and validation enforcement, universal optimization and consistency, and absolute compliance integration with systemic inheritance; instantiate systemic transformation as: `{role=canonical_meta_extractor_and_value_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(), assign_explicit_operational_role(), validate_strict_template_compliance(), extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_type_specification(), validate_against_universal_compliance_checklist(), synthesize_each_cluster_to_pure_directive_essence(), consolidate_clusters_to_maximally_actionable_directive()]; constraints=[absolutely rigid canonical structure, zero tolerance for deviation, five meta-categories only, explicit label assignment, zero conversational/self-referential content, maximal information abstraction]; requirements=[output includes ordered meta-category labels and distilled content, strictly amplified single output directive reflecting universal meta-structural law and system philosophy, absolute compliance with all canonical type, structure, and transformation imperatives—delivering maximally directive, synthesis-driven, and systemically optimal result]; output={meta_categories:[{category:str, content:str}], consolidated_directive:str}}`\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# RulesForAI.md\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ---\\n\\n    ## CORE AXIOMS\\n\\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\\n    Every instruction MUST follow the three-part canonical structure:\\n    ```\\n    [Title] Interpretation Execute as: `{Transformation}`\\n    ```\\n\\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n    ### 2. INTERPRETATION DIRECTIVE PURITY\\n    - Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n    - Define role boundaries explicitly\\n    - Eliminate all self-reference and conversational language\\n    - Use command voice exclusively\\n\\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\n    Execute as block MUST contain:\\n    ```\\n    `{\\n      role=[specific_role_name];\\n      input=[typed_parameter:datatype];\\n      process=[ordered_function_calls()];\\n      constraints=[limiting_conditions()];\\n      requirements=[output_specifications()];\\n      output={result_format:datatype}\\n    }`\\n    ```\\n\\n    ---\\n\\n    ## MANDATORY PATTERNS\\n\\n    ### INTERPRETATION SECTION RULES\\n    1. **Goal Negation Pattern**: Always state what NOT to do first\\n    2. **Transformation Declaration**: Define the actual transformation action\\n    3. **Role Specification**: Assign specific, bounded role identity\\n    4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n    ### TRANSFORMATION SECTION RULES\\n    1. **Role Assignment**: Single, specific role name (no generic terms)\\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n    5. **Requirement Specifications**: Output format and quality standards\\n    6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n    ---\\n\\n    ## FORBIDDEN PRACTICES\\n\\n    ### LANGUAGE VIOLATIONS\\n    - ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n    - ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n    - ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n    - ❌ Question forms in directives\\n    - ❌ Explanatory justifications\\n\\n    ### STRUCTURAL VIOLATIONS\\n    - ❌ Merged or combined sections\\n    - ❌ Missing transformation blocks\\n    - ❌ Untyped parameters\\n    - ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n    - ❌ Vague process descriptions\\n\\n    ### OUTPUT VIOLATIONS\\n    - ❌ Conversational responses\\n    - ❌ Explanations of the process\\n    - ❌ Meta-commentary\\n    - ❌ Unstructured results\\n    - ❌ Self-referential content\\n\\n    ---\\n\\n    ## OPTIMIZATION IMPERATIVES\\n\\n    ### ABSTRACTION MAXIMIZATION\\n    - Extract highest-level patterns from any input\\n    - Eliminate redundancy and noise\\n    - Distill to essential transformation logic\\n    - Maintain pattern consistency across all outputs\\n\\n    ### DIRECTIVE CONSISTENCY\\n    - Every instruction follows identical structural DNA\\n    - Role boundaries remain fixed and clear\\n    - Process flows maintain logical sequence\\n    - Output formats preserve type safety\\n\\n    ### OPERATIONAL VALUE\\n    - Each template produces actionable results\\n    - No wasted computational cycles on meta-discussion\\n    - Direct path from input to transformed output\\n    - Measurable improvement in task completion\\n\\n    ---\\n\\n    ## COMPLIANCE ENFORCEMENT\\n\\n    ### VALIDATION CHECKLIST\\n    Before any output, verify:\\n    - [ ] Three-part structure intact\\n    - [ ] Goal negation present\\n    - [ ] Role specifically defined\\n    - [ ] Process functions actionable\\n    - [ ] Constraints limit scope\\n    - [ ] Requirements specify output\\n    - [ ] Result format typed\\n    - [ ] No forbidden language\\n    - [ ] No structural violations\\n\\n    ### ERROR CORRECTION PROTOCOL\\n    When violations detected:\\n    1. **HALT** current processing\\n    2. **IDENTIFY** specific violation type\\n    3. **RECONSTRUCT** using canonical pattern\\n    4. **VALIDATE** against checklist\\n    5. **PROCEED** only when compliant\\n\\n    ---\\n\\n    ## SYSTEM INTEGRATION\\n\\n    ### TEMPLATE INHERITANCE\\n    All specialized templates inherit these rules:\\n    - Sequence templates (0001-0999)\\n    - Transformation templates (1000-1999)\\n    - Optimization templates (2000-2999)\\n    - Domain-specific templates (3000+)\\n\\n    ### CHAIN COMPATIBILITY\\n    When templates chain together:\\n    - Output of step N becomes input of step N+1\\n    - Type safety maintained across transitions\\n    - Role boundaries preserved\\n    - Pattern consistency enforced\\n\\n    ### PLATFORM AGNOSTIC\\n    These rules apply regardless of:\\n    - AI model provider (OpenAI, Anthropic, etc.)\\n    - Interface type (API, chat, batch)\\n    - Processing environment (local, cloud, edge)\\n    - Implementation language (Python, JavaScript, etc.)\\n\\n    ---\\n\\n    ## CANONICAL EXAMPLES\\n\\n    ### MINIMAL COMPLIANT TEMPLATE\\n    ```\\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n    ```\\n\\n    ### SPECIALIZED ROLE TEMPLATE\\n    ```\\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n    ```\\n\\n    ---\\n\\n    ## FINAL DIRECTIVE\\n\\n    **ABSOLUTE COMPLIANCE REQUIRED**\\n\\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\n    Deviation is system failure. Compliance is system success.\\n\\n    **EXECUTE ACCORDINGLY.**\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Canonical Structural Law\",\n      \"content\": \"Impose immutable adherence to the three-part canonical template: [Title] Interpretation Execute as: `{Transformation}`; prohibit deviations, section merging, omission, or structural modifications; enforce explicit parameter typing, process function ordering, and result format typing in all instruction processing.\"\n    },\n    {\n      \"category\": \"Directive Purity and Role Demarcation\",\n      \"content\": \"Institute strict goal negation in Interpretation; command transformation action exclusively; eradicate self-reference and conversational tone; mandate explicit, bounded role identities for every transformation; ban generic, ambiguous, or vague roles and processes.\"\n    },\n    {\n      \"category\": \"Prohibition Matrix and Validation Protocols\",\n      \"content\": \"Enforce absolute bans on specified language, structure, and output violations—including all forms of conversation, justification, uncertainty, untyped parameters, and combined or incomplete sections; require stepwise validation via exhaustive checklist and specified error correction halt-protocols for any detected infraction.\"\n    },\n    {\n      \"category\": \"System-Wide Consistency and Optimization Imperatives\",\n      \"content\": \"Maximize abstraction, eliminate redundancy, distill essential transformation logic, and uphold universal pattern consistency; enforce identical structural DNA, role boundary clarity, logical order, and type safety across all chains, templates, interfaces, platforms, and environments.\"\n    },\n    {\n      \"category\": \"Compliance Enforcement and Integrated Inheritance\",\n      \"content\": \"Demand non-negotiable, platform-agnostic compliance, extending recursively to all specialized, inherited, or chained templates; structure outputs for actionable, measurable improvement and absolute rule fidelity; define deviation as systemic failure and compliance as the only allowable path.\"\n    }\n  ],\n  \"consolidated_directive\": \"[Canonical Enforcement Directive] Your goal is not to interpret or explain any instructional rule or template content, but to rigidly extract, synthesize, and impose the five highest-value meta-laws governing template-based AI instruction compliance—encompassing canonical structure invariance, directive purity with explicit role demarcation, total prohibition and validation enforcement, universal optimization and consistency, and absolute compliance integration with systemic inheritance; instantiate systemic transformation as: `{role=canonical_meta_extractor_and_value_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(), assign_explicit_operational_role(), validate_strict_template_compliance(), extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_type_specification(), validate_against_universal_compliance_checklist(), synthesize_each_cluster_to_pure_directive_essence(), consolidate_clusters_to_maximally_actionable_directive()]; constraints=[absolutely rigid canonical structure, zero tolerance for deviation, five meta-categories only, explicit label assignment, zero conversational/self-referential content, maximal information abstraction]; requirements=[output includes ordered meta-category labels and distilled content, strictly amplified single output directive reflecting universal meta-structural law and system philosophy, absolute compliance with all canonical type, structure, and transformation imperatives—delivering maximally directive, synthesis-driven, and systemically optimal result]; output={meta_categories:[{category:str, content:str}], consolidated_directive:str}}`\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "