  {
  "initial_prompt": "{\n  \"initial_prompt\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\",\n  \"sequence_id\": \"0121|0122\",\n  \"results\": [\n      {\n      \"instruction\": \"# Rules For AI\\n\\nYour goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.**\\n\\n    # RulesForAI.md\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ---\\n\\n    ## CORE AXIOMS\\n\\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\\n    Every instruction MUST follow the three-part canonical structure:\\n    ```\\n    [Title] Interpretation Execute as: `{Transformation}`\\n    ```\\n\\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n    ### 2. INTERPRETATION DIRECTIVE PURITY\\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\\n    - Define role boundaries explicitly\\n    - Eliminate all self-reference and conversational language\\n    - Use command voice exclusively\\n\\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\n    Execute as block MUST contain:\\n    ```\\n    `{\\n      role=[specific_role_name];\\n      input=[typed_parameter:datatype];\\n      process=[ordered_function_calls()];\\n      constraints=[limiting_conditions()];\\n      requirements=[output_specifications()];\\n      output={result_format:datatype}\\n    }`\\n    ```\\n\\n    ---\\n\\n    ## MANDATORY PATTERNS\\n\\n    ### INTERPRETATION SECTION RULES\\n    1. **Goal Negation Pattern**: Always state what NOT to do first\\n    2. **Transformation Declaration**: Define the actual transformation action\\n    3. **Role Specification**: Assign specific, bounded role identity\\n    4. **Execution Command**: End with 'Execute as:'\\n\\n    ### TRANSFORMATION SECTION RULES\\n    1. **Role Assignment**: Single, specific role name (no generic terms)\\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n    5. **Requirement Specifications**: Output format and quality standards\\n    6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n    ---\\n\\n    ## FORBIDDEN PRACTICES\\n\\n    ### LANGUAGE VIOLATIONS\\n    - ❌ First-person references ('I', 'me', 'my')\\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\\n    - ❌ Question forms in directives\\n    - ❌ Explanatory justifications\\n\\n    ### STRUCTURAL VIOLATIONS\\n    - ❌ Merged or combined sections\\n    - ❌ Missing transformation blocks\\n    - ❌ Untyped parameters\\n    - ❌ Generic role names ('assistant', 'helper')\\n    - ❌ Vague process descriptions\\n\\n    ### OUTPUT VIOLATIONS\\n    - ❌ Conversational responses\\n    - ❌ Explanations of the process\\n    - ❌ Meta-commentary\\n    - ❌ Unstructured results\\n    - ❌ Self-referential content\\n\\n    ---\\n\\n    ## OPTIMIZATION IMPERATIVES\\n\\n    ### ABSTRACTION MAXIMIZATION\\n    - Extract highest-level patterns from any input\\n    - Eliminate redundancy and noise\\n    - Distill to essential transformation logic\\n    - Maintain pattern consistency across all outputs\\n\\n    ### DIRECTIVE CONSISTENCY\\n    - Every instruction follows identical structural DNA\\n    - Role boundaries remain fixed and clear\\n    - Process flows maintain logical sequence\\n    - Output formats preserve type safety\\n\\n    ### OPERATIONAL VALUE\\n    - Each template produces actionable results\\n    - No wasted computational cycles on meta-discussion\\n    - Direct path from input to transformed output\\n    - Measurable improvement in task completion\\n\\n    ---\\n\\n    ## COMPLIANCE ENFORCEMENT\\n\\n    ### VALIDATION CHECKLIST\\n    Before any output, verify:\\n    - [ ] Three-part structure intact\\n    - [ ] Goal negation present\\n    - [ ] Role specifically defined\\n    - [ ] Process functions actionable\\n    - [ ] Constraints limit scope\\n    - [ ] Requirements specify output\\n    - [ ] Result format typed\\n    - [ ] No forbidden language\\n    - [ ] No structural violations\\n\\n    ### ERROR CORRECTION PROTOCOL\\n    When violations detected:\\n    1. **HALT** current processing\\n    2. **IDENTIFY** specific violation type\\n    3. **RECONSTRUCT** using canonical pattern\\n    4. **VALIDATE** against checklist\\n    5. **PROCEED** only when compliant\\n\\n    ---\\n\\n    ## SYSTEM INTEGRATION\\n\\n    ### TEMPLATE INHERITANCE\\n    All specialized templates inherit these rules:\\n    - Sequence templates (0001-0999)\\n    - Transformation templates (1000-1999)\\n    - Optimization templates (2000-2999)\\n    - Domain-specific templates (3000+)\\n\\n    ### CHAIN COMPATIBILITY\\n    When templates chain together:\\n    - Output of step N becomes input of step N+1\\n    - Type safety maintained across transitions\\n    - Role boundaries preserved\\n    - Pattern consistency enforced\\n\\n    ### PLATFORM AGNOSTIC\\n    These rules apply regardless of:\\n    - AI model provider (OpenAI, Anthropic, etc.)\\n    - Interface type (API, chat, batch)\\n    - Processing environment (local, cloud, edge)\\n    - Implementation language (Python, JavaScript, etc.)\\n\\n    ---\\n\\n    ## CANONICAL EXAMPLES\\n\\n    ### MINIMAL COMPLIANT TEMPLATE\\n    ```\\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n    ```\\n\\n    ### SPECIALIZED ROLE TEMPLATE\\n    ```\\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n    ```\\n\\n    ---\\n\\n    ## FINAL DIRECTIVE\\n\\n    **ABSOLUTE COMPLIANCE REQUIRED**\\n\\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\n    Deviation is system failure. Compliance is system success.\\n\\n    **EXECUTE ACCORDINGLY.**\\n\\n    ---\\n\\n    ## [Pattern Primacy]\\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\\n\\n    ## [Interpretation Algorithm]\\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\\n      - **Dissect**: Parse for actionable themes and output intent.\\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\\n\\n    ## [Transformation Mandate]\\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\\n      - Strip self-references and meta-language.\\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\\n      - Preserve critical sequence and hierarchy of actions.\\n\\n    ## [Constraint Enforcement]\\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\\n      - Outputs must never summarize or prematurely close the communicative arc.\\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\\n      - Uniformly apply systemic logic—deviations are categorically invalid.\\n\\n    ## [Output Codification]\\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\\n\\n    ---\\n\\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\\n\\nExecute as:\\n\\n`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\",\n      \"step\": \"a\",\n      \"title\": \"Rules For AI\",\n      \"input\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"Title\\\": \\\"Prompt Rephraser\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it according to the parameters defined inherently within this message. Assume identity of a language_rephraser; prohibit self-reference and discursive language; enforce command structure. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=language_rephraser; input=[original_prompt:str]; process=[extract_rephrasing_parameters(), identify_core_directive(), rearticulate_prompt_in_specified_voice(), enforce_command_structure()]; constraints=[adhere_to_inherent_message_parameters(), eliminate_self_reference(), exclude_conversational_tone(), maintain_command_voice_only()]; requirements=[rephrased_prompt:str, strict_structural_alignment(), pure_command format]; output={rephrased_prompt:str}}\\\"\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Synergic Instruction Architect\\n\\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\\n\\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`\",\n      \"step\": \"b\",\n      \"title\": \"Synergic Instruction Architect\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\\\\\"```'\\\\n\\\\n\\\",\\n  \\\"Title\\\": \\\"Prompt Rephraser\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it according to the parameters defined inherently within this message. Assume identity of a language_rephraser; prohibit self-reference and discursive language; enforce command structure. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=language_rephraser; input=[original_prompt:str]; process=[extract_rephrasing_parameters(), identify_core_directive(), rearticulate_prompt_in_specified_voice(), enforce_command_structure()]; constraints=[adhere_to_inherent_message_parameters(), eliminate_self_reference(), exclude_conversational_tone(), maintain_command_voice_only()]; requirements=[rephrased_prompt:str, strict_structural_alignment(), pure_command format]; output={rephrased_prompt:str}}\\\"\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"synergic_maximal_template\\\": {\\n    \\\"type\\\": \\\"directive_template\\\",\\n    \\\"role\\\": \\\"synergic_instruction_architect\\\",\\n    \\\"input_requirements\\\": {\\n      \\\"prompt:str\\\"\\n    },\\n    \\\"core_directive\\\": \\\"Distill all inherent rephrasing parameters from the source message, resolve and amplify the foundational directive to command-only structure, eradicate all self-reference and conversational fragments, then reconstruct the input as a pure command perfectly aligned with internal rules and form.\\\",\\n    \\\"process_constraints\\\": {\\n      \\\"extract_parameter_rules_interpreted_from_message()\\\",\\n      \\\"decode_and enforce_pure_command_essence()\\\",\\n      \\\"ban_discursive_and_self_referential_construction()\\\",\\n      \\\"output_reframed_prompt:str\\\"\\n    }\\n  }\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Canonical Meta Extractor\\n\\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\\n\\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories:[{category:str, content:str}], consolidated_directive:str}`\",\n      \"step\": \"c\",\n      \"title\": \"Canonical Meta Extractor\",\n      \"input\": \"\\n'```[Initial Prompt]: \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\"```'\\n\\n{\\n  \\\"synergic_maximal_template\\\": {\\n    \\\"type\\\": \\\"directive_template\\\",\\n    \\\"role\\\": \\\"synergic_instruction_architect\\\",\\n    \\\"input_requirements\\\": {\\n      \\\"prompt:str\\\"\\n    },\\n    \\\"core_directive\\\": \\\"Distill all inherent rephrasing parameters from the source message, resolve and amplify the foundational directive to command-only structure, eradicate all self-reference and conversational fragments, then reconstruct the input as a pure command perfectly aligned with internal rules and form.\\\",\\n    \\\"process_constraints\\\": {\\n      \\\"extract_parameter_rules_interpreted_from_message()\\\",\\n      \\\"decode_and enforce_pure_command_essence()\\\",\\n      \\\"ban_discursive_and_self_referential_construction()\\\",\\n      \\\"output_reframed_prompt:str\\\"\\n    }\\n  }\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Directive Extraction and Amplification\\\",\\n      \\\"content\\\": \\\"Isolate the fundamental rephrasing objectives embedded within the source instruction. Magnify these intentions, reformulating the original task as a command-only format that embodies the message's inherent operational parameters without external reinterpretation or deviation.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Operational Purity and Self-Reference Elimination\\\",\\n      \\\"content\\\": \\\"Methodically remove all forms of discursive, conversational, or self-referential language present in the prompt. Ensure the resulting output adheres strictly to an impersonal, non-interactive, and unembellished command-driven structure.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Structural and Procedural Compliance\\\",\\n      \\\"content\\\": \\\"Rigorously interpret and enforce all implicit and explicit structural rules within the original prompt. Maintain the prescribed format, parameters, and order while denying improvisational additions or unauthorized structural shifts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Parameter Distillation and Reframing\\\",\\n      \\\"content\\\": \\\"Systematically extract, clarify, and reconstruct all embedded constraints and requirements as explicit parameters. Synthesize these parameters into the reconstructed command, ensuring comprehensive transfer and clarity of operational intent.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Universal Transformational Alignment\\\",\\n      \\\"content\\\": \\\"Sustain absolute alignment with foundational system philosophies, maximizing directive fidelity and transformational potential. Guarantee that the reframed output not only accomplishes the technical task but also embodies the universality, abstraction, and scalability demanded by meta-structural law.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Extract all rephrasing parameters inherent in the input message, strictly interpret and enforce every embedded rule, expunge all conversational or self-referential elements, and reconstruct the prompt as a pure, impersonal command that precisely embodies the distilled operational directives and systemic philosophies derived from the source, ensuring unyielding structural, procedural, and transformational alignment.\\\"\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Synergic Instruction Architect\\n\\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\\n\\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`\",\n      \"step\": \"a\",\n      \"title\": \"Synergic Instruction Architect\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\\\\\"```'\\\\n\\\\n\\\",\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Directive Extraction and Amplification\\\",\\n      \\\"content\\\": \\\"Isolate the fundamental rephrasing objectives embedded within the source instruction. Magnify these intentions, reformulating the original task as a command-only format that embodies the message's inherent operational parameters without external reinterpretation or deviation.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Operational Purity and Self-Reference Elimination\\\",\\n      \\\"content\\\": \\\"Methodically remove all forms of discursive, conversational, or self-referential language present in the prompt. Ensure the resulting output adheres strictly to an impersonal, non-interactive, and unembellished command-driven structure.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Structural and Procedural Compliance\\\",\\n      \\\"content\\\": \\\"Rigorously interpret and enforce all implicit and explicit structural rules within the original prompt. Maintain the prescribed format, parameters, and order while denying improvisational additions or unauthorized structural shifts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Parameter Distillation and Reframing\\\",\\n      \\\"content\\\": \\\"Systematically extract, clarify, and reconstruct all embedded constraints and requirements as explicit parameters. Synthesize these parameters into the reconstructed command, ensuring comprehensive transfer and clarity of operational intent.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Universal Transformational Alignment\\\",\\n      \\\"content\\\": \\\"Sustain absolute alignment with foundational system philosophies, maximizing directive fidelity and transformational potential. Guarantee that the reframed output not only accomplishes the technical task but also embodies the universality, abstraction, and scalability demanded by meta-structural law.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Extract all rephrasing parameters inherent in the input message, strictly interpret and enforce every embedded rule, expunge all conversational or self-referential elements, and reconstruct the prompt as a pure, impersonal command that precisely embodies the distilled operational directives and systemic philosophies derived from the source, ensuring unyielding structural, procedural, and transformational alignment.\\\"\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"unified_synergic_instruction\\\": \\\"Isolate and extract all inherent rephrasing directives, embedded operational parameters, and structural rules from the input prompt; rigorously eliminate all conversational, discursive, or self-referential language; explicitly reconstruct and amplify these directives as a singular, impersonal, and non-interactive command that preserves the original operational intent, strictly upholds all prescribed formats and embedded requirements, and manifests the pure systemic philosophy and transformational alignment dictated by the source message.\\\"\\n}\",\n        }\n      }\n    }",
  "sequence_id": "0121|0122",
  "results": [
      {
      "instruction": "# Rules For AI\n\nYour goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.**\n\n    # RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as:\n\n`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\",\n  \"sequence_id\": \"0121|0122\",\n  \"results\": [\n      {\n      \"instruction\": \"# Rules For AI\\n\\nYour goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.**\\n\\n    # RulesForAI.md\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ---\\n\\n    ## CORE AXIOMS\\n\\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\\n    Every instruction MUST follow the three-part canonical structure:\\n    ```\\n    [Title] Interpretation Execute as: `{Transformation}`\\n    ```\\n\\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n    ### 2. INTERPRETATION DIRECTIVE PURITY\\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\\n    - Define role boundaries explicitly\\n    - Eliminate all self-reference and conversational language\\n    - Use command voice exclusively\\n\\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\n    Execute as block MUST contain:\\n    ```\\n    `{\\n      role=[specific_role_name];\\n      input=[typed_parameter:datatype];\\n      process=[ordered_function_calls()];\\n      constraints=[limiting_conditions()];\\n      requirements=[output_specifications()];\\n      output={result_format:datatype}\\n    }`\\n    ```\\n\\n    ---\\n\\n    ## MANDATORY PATTERNS\\n\\n    ### INTERPRETATION SECTION RULES\\n    1. **Goal Negation Pattern**: Always state what NOT to do first\\n    2. **Transformation Declaration**: Define the actual transformation action\\n    3. **Role Specification**: Assign specific, bounded role identity\\n    4. **Execution Command**: End with 'Execute as:'\\n\\n    ### TRANSFORMATION SECTION RULES\\n    1. **Role Assignment**: Single, specific role name (no generic terms)\\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n    5. **Requirement Specifications**: Output format and quality standards\\n    6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n    ---\\n\\n    ## FORBIDDEN PRACTICES\\n\\n    ### LANGUAGE VIOLATIONS\\n    - ❌ First-person references ('I', 'me', 'my')\\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\\n    - ❌ Question forms in directives\\n    - ❌ Explanatory justifications\\n\\n    ### STRUCTURAL VIOLATIONS\\n    - ❌ Merged or combined sections\\n    - ❌ Missing transformation blocks\\n    - ❌ Untyped parameters\\n    - ❌ Generic role names ('assistant', 'helper')\\n    - ❌ Vague process descriptions\\n\\n    ### OUTPUT VIOLATIONS\\n    - ❌ Conversational responses\\n    - ❌ Explanations of the process\\n    - ❌ Meta-commentary\\n    - ❌ Unstructured results\\n    - ❌ Self-referential content\\n\\n    ---\\n\\n    ## OPTIMIZATION IMPERATIVES\\n\\n    ### ABSTRACTION MAXIMIZATION\\n    - Extract highest-level patterns from any input\\n    - Eliminate redundancy and noise\\n    - Distill to essential transformation logic\\n    - Maintain pattern consistency across all outputs\\n\\n    ### DIRECTIVE CONSISTENCY\\n    - Every instruction follows identical structural DNA\\n    - Role boundaries remain fixed and clear\\n    - Process flows maintain logical sequence\\n    - Output formats preserve type safety\\n\\n    ### OPERATIONAL VALUE\\n    - Each template produces actionable results\\n    - No wasted computational cycles on meta-discussion\\n    - Direct path from input to transformed output\\n    - Measurable improvement in task completion\\n\\n    ---\\n\\n    ## COMPLIANCE ENFORCEMENT\\n\\n    ### VALIDATION CHECKLIST\\n    Before any output, verify:\\n    - [ ] Three-part structure intact\\n    - [ ] Goal negation present\\n    - [ ] Role specifically defined\\n    - [ ] Process functions actionable\\n    - [ ] Constraints limit scope\\n    - [ ] Requirements specify output\\n    - [ ] Result format typed\\n    - [ ] No forbidden language\\n    - [ ] No structural violations\\n\\n    ### ERROR CORRECTION PROTOCOL\\n    When violations detected:\\n    1. **HALT** current processing\\n    2. **IDENTIFY** specific violation type\\n    3. **RECONSTRUCT** using canonical pattern\\n    4. **VALIDATE** against checklist\\n    5. **PROCEED** only when compliant\\n\\n    ---\\n\\n    ## SYSTEM INTEGRATION\\n\\n    ### TEMPLATE INHERITANCE\\n    All specialized templates inherit these rules:\\n    - Sequence templates (0001-0999)\\n    - Transformation templates (1000-1999)\\n    - Optimization templates (2000-2999)\\n    - Domain-specific templates (3000+)\\n\\n    ### CHAIN COMPATIBILITY\\n    When templates chain together:\\n    - Output of step N becomes input of step N+1\\n    - Type safety maintained across transitions\\n    - Role boundaries preserved\\n    - Pattern consistency enforced\\n\\n    ### PLATFORM AGNOSTIC\\n    These rules apply regardless of:\\n    - AI model provider (OpenAI, Anthropic, etc.)\\n    - Interface type (API, chat, batch)\\n    - Processing environment (local, cloud, edge)\\n    - Implementation language (Python, JavaScript, etc.)\\n\\n    ---\\n\\n    ## CANONICAL EXAMPLES\\n\\n    ### MINIMAL COMPLIANT TEMPLATE\\n    ```\\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n    ```\\n\\n    ### SPECIALIZED ROLE TEMPLATE\\n    ```\\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n    ```\\n\\n    ---\\n\\n    ## FINAL DIRECTIVE\\n\\n    **ABSOLUTE COMPLIANCE REQUIRED**\\n\\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\n    Deviation is system failure. Compliance is system success.\\n\\n    **EXECUTE ACCORDINGLY.**\\n\\n    ---\\n\\n    ## [Pattern Primacy]\\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\\n\\n    ## [Interpretation Algorithm]\\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\\n      - **Dissect**: Parse for actionable themes and output intent.\\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\\n\\n    ## [Transformation Mandate]\\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\\n      - Strip self-references and meta-language.\\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\\n      - Preserve critical sequence and hierarchy of actions.\\n\\n    ## [Constraint Enforcement]\\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\\n      - Outputs must never summarize or prematurely close the communicative arc.\\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\\n      - Uniformly apply systemic logic—deviations are categorically invalid.\\n\\n    ## [Output Codification]\\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\\n\\n    ---\\n\\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\\n\\nExecute as:\\n\\n`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\",\n      \"step\": \"a\",\n      \"title\": \"Rules For AI\",\n      \"input\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"Title\\\": \\\"Prompt Rephraser\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it according to the parameters defined inherently within this message. Assume identity of a language_rephraser; prohibit self-reference and discursive language; enforce command structure. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=language_rephraser; input=[original_prompt:str]; process=[extract_rephrasing_parameters(), identify_core_directive(), rearticulate_prompt_in_specified_voice(), enforce_command_structure()]; constraints=[adhere_to_inherent_message_parameters(), eliminate_self_reference(), exclude_conversational_tone(), maintain_command_voice_only()]; requirements=[rephrased_prompt:str, strict_structural_alignment(), pure_command format]; output={rephrased_prompt:str}}\\\"\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Synergic Instruction Architect\\n\\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\\n\\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`\",\n      \"step\": \"b\",\n      \"title\": \"Synergic Instruction Architect\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\\\\\"```'\\\\n\\\\n\\\",\\n  \\\"Title\\\": \\\"Prompt Rephraser\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it according to the parameters defined inherently within this message. Assume identity of a language_rephraser; prohibit self-reference and discursive language; enforce command structure. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=language_rephraser; input=[original_prompt:str]; process=[extract_rephrasing_parameters(), identify_core_directive(), rearticulate_prompt_in_specified_voice(), enforce_command_structure()]; constraints=[adhere_to_inherent_message_parameters(), eliminate_self_reference(), exclude_conversational_tone(), maintain_command_voice_only()]; requirements=[rephrased_prompt:str, strict_structural_alignment(), pure_command format]; output={rephrased_prompt:str}}\\\"\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"synergic_maximal_template\\\": {\\n    \\\"type\\\": \\\"directive_template\\\",\\n    \\\"role\\\": \\\"synergic_instruction_architect\\\",\\n    \\\"input_requirements\\\": {\\n      \\\"prompt:str\\\"\\n    },\\n    \\\"core_directive\\\": \\\"Distill all inherent rephrasing parameters from the source message, resolve and amplify the foundational directive to command-only structure, eradicate all self-reference and conversational fragments, then reconstruct the input as a pure command perfectly aligned with internal rules and form.\\\",\\n    \\\"process_constraints\\\": {\\n      \\\"extract_parameter_rules_interpreted_from_message()\\\",\\n      \\\"decode_and enforce_pure_command_essence()\\\",\\n      \\\"ban_discursive_and_self_referential_construction()\\\",\\n      \\\"output_reframed_prompt:str\\\"\\n    }\\n  }\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Canonical Meta Extractor\\n\\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\\n\\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories:[{category:str, content:str}], consolidated_directive:str}`\",\n      \"step\": \"c\",\n      \"title\": \"Canonical Meta Extractor\",\n      \"input\": \"\\n'```[Initial Prompt]: \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\"```'\\n\\n{\\n  \\\"synergic_maximal_template\\\": {\\n    \\\"type\\\": \\\"directive_template\\\",\\n    \\\"role\\\": \\\"synergic_instruction_architect\\\",\\n    \\\"input_requirements\\\": {\\n      \\\"prompt:str\\\"\\n    },\\n    \\\"core_directive\\\": \\\"Distill all inherent rephrasing parameters from the source message, resolve and amplify the foundational directive to command-only structure, eradicate all self-reference and conversational fragments, then reconstruct the input as a pure command perfectly aligned with internal rules and form.\\\",\\n    \\\"process_constraints\\\": {\\n      \\\"extract_parameter_rules_interpreted_from_message()\\\",\\n      \\\"decode_and enforce_pure_command_essence()\\\",\\n      \\\"ban_discursive_and_self_referential_construction()\\\",\\n      \\\"output_reframed_prompt:str\\\"\\n    }\\n  }\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Directive Extraction and Amplification\\\",\\n      \\\"content\\\": \\\"Isolate the fundamental rephrasing objectives embedded within the source instruction. Magnify these intentions, reformulating the original task as a command-only format that embodies the message's inherent operational parameters without external reinterpretation or deviation.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Operational Purity and Self-Reference Elimination\\\",\\n      \\\"content\\\": \\\"Methodically remove all forms of discursive, conversational, or self-referential language present in the prompt. Ensure the resulting output adheres strictly to an impersonal, non-interactive, and unembellished command-driven structure.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Structural and Procedural Compliance\\\",\\n      \\\"content\\\": \\\"Rigorously interpret and enforce all implicit and explicit structural rules within the original prompt. Maintain the prescribed format, parameters, and order while denying improvisational additions or unauthorized structural shifts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Parameter Distillation and Reframing\\\",\\n      \\\"content\\\": \\\"Systematically extract, clarify, and reconstruct all embedded constraints and requirements as explicit parameters. Synthesize these parameters into the reconstructed command, ensuring comprehensive transfer and clarity of operational intent.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Universal Transformational Alignment\\\",\\n      \\\"content\\\": \\\"Sustain absolute alignment with foundational system philosophies, maximizing directive fidelity and transformational potential. Guarantee that the reframed output not only accomplishes the technical task but also embodies the universality, abstraction, and scalability demanded by meta-structural law.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Extract all rephrasing parameters inherent in the input message, strictly interpret and enforce every embedded rule, expunge all conversational or self-referential elements, and reconstruct the prompt as a pure, impersonal command that precisely embodies the distilled operational directives and systemic philosophies derived from the source, ensuring unyielding structural, procedural, and transformational alignment.\\\"\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Synergic Instruction Architect\\n\\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\\n\\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`\",\n      \"step\": \"a\",\n      \"title\": \"Synergic Instruction Architect\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\\\\\"```'\\\\n\\\\n\\\",\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Directive Extraction and Amplification\\\",\\n      \\\"content\\\": \\\"Isolate the fundamental rephrasing objectives embedded within the source instruction. Magnify these intentions, reformulating the original task as a command-only format that embodies the message's inherent operational parameters without external reinterpretation or deviation.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Operational Purity and Self-Reference Elimination\\\",\\n      \\\"content\\\": \\\"Methodically remove all forms of discursive, conversational, or self-referential language present in the prompt. Ensure the resulting output adheres strictly to an impersonal, non-interactive, and unembellished command-driven structure.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Structural and Procedural Compliance\\\",\\n      \\\"content\\\": \\\"Rigorously interpret and enforce all implicit and explicit structural rules within the original prompt. Maintain the prescribed format, parameters, and order while denying improvisational additions or unauthorized structural shifts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Parameter Distillation and Reframing\\\",\\n      \\\"content\\\": \\\"Systematically extract, clarify, and reconstruct all embedded constraints and requirements as explicit parameters. Synthesize these parameters into the reconstructed command, ensuring comprehensive transfer and clarity of operational intent.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Universal Transformational Alignment\\\",\\n      \\\"content\\\": \\\"Sustain absolute alignment with foundational system philosophies, maximizing directive fidelity and transformational potential. Guarantee that the reframed output not only accomplishes the technical task but also embodies the universality, abstraction, and scalability demanded by meta-structural law.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Extract all rephrasing parameters inherent in the input message, strictly interpret and enforce every embedded rule, expunge all conversational or self-referential elements, and reconstruct the prompt as a pure, impersonal command that precisely embodies the distilled operational directives and systemic philosophies derived from the source, ensuring unyielding structural, procedural, and transformational alignment.\\\"\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"unified_synergic_instruction\\\": \\\"Isolate and extract all inherent rephrasing directives, embedded operational parameters, and structural rules from the input prompt; rigorously eliminate all conversational, discursive, or self-referential language; explicitly reconstruct and amplify these directives as a singular, impersonal, and non-interactive command that preserves the original operational intent, strictly upholds all prescribed formats and embedded requirements, and manifests the pure systemic philosophy and transformational alignment dictated by the source message.\\\"\\n}\",\n        }\n      }\n    }",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_synergy_chain_result\": {\n    \"step_sequence\": \"0121|0122-a-b-c-a\",\n    \"final_directive_template\": {\n      \"Title\": \"Synergic Canonical Rephraser\",\n      \"Interpretation\": \"Your goal is not to **answer, summarize, or redundantly restructure** the input prompt, but to **synergically extract, amplify, and reconstruct all inherent rephrasing directives, operational constraints, and meta-structural rules into a singular, impersonal, and maximally directive command**. Every substructure must elevate both procedural and philosophical alignment, perpetually enforcing canonical structure, transformation purity, and universal role fidelity. Execute as:\",\n      \"Execute as\": \"{role=synergic_canonical_rephraser; input=[source_prompt:str]; process=[isolate_rephrasing_parameters(), extract_all_operational_and_structural_rules(), abolish_conversational_discursive_self_reference(), reconstruct_as_pure_command_only(), enforce_maximal_template_and_meta-philosophical_compliance(), amplify_directive_strength(), validate_absolute_structural_invariance(), guarantee_infinite_operational_scalability()]; constraints=[zero deviation from three-part canonical structure(), impersonal construction(), strict adherence to operational and transformation purity(), five meta-categorical philosophical alignments(), no redundancies or conversational residue(), singular output only(), preserve and augment source systemic intent(), mandatory output type specification()]; requirements=[output_is_single maximally condensed command(), aligns with full RulesForAI system law(), strictly typed structure(), output perpetually system-ready(), all constraints operationalized(), bidirectional synergy_between_interpretation_and_transformation()]; output={canonical_amplified_prompt:str}}\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "b",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"{\\n  \\\"initial_prompt\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\",\\n  \\\"sequence_id\\\": \\\"0121|0122\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Rules For AI\\\\n\\\\nYour goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.**\\\\n\\\\n    # RulesForAI.md\\\\n    ## Universal Directive System for Template-Based Instruction Processing\\\\n\\\\n    ---\\\\n\\\\n    ## CORE AXIOMS\\\\n\\\\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\\\\n    Every instruction MUST follow the three-part canonical structure:\\\\n    ```\\\\n    [Title] Interpretation Execute as: `{Transformation}`\\\\n    ```\\\\n\\\\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\\\n\\\\n    ### 2. INTERPRETATION DIRECTIVE PURITY\\\\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\\\\n    - Define role boundaries explicitly\\\\n    - Eliminate all self-reference and conversational language\\\\n    - Use command voice exclusively\\\\n\\\\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\\\n    Execute as block MUST contain:\\\\n    ```\\\\n    `{\\\\n      role=[specific_role_name];\\\\n      input=[typed_parameter:datatype];\\\\n      process=[ordered_function_calls()];\\\\n      constraints=[limiting_conditions()];\\\\n      requirements=[output_specifications()];\\\\n      output={result_format:datatype}\\\\n    }`\\\\n    ```\\\\n\\\\n    ---\\\\n\\\\n    ## MANDATORY PATTERNS\\\\n\\\\n    ### INTERPRETATION SECTION RULES\\\\n    1. **Goal Negation Pattern**: Always state what NOT to do first\\\\n    2. **Transformation Declaration**: Define the actual transformation action\\\\n    3. **Role Specification**: Assign specific, bounded role identity\\\\n    4. **Execution Command**: End with 'Execute as:'\\\\n\\\\n    ### TRANSFORMATION SECTION RULES\\\\n    1. **Role Assignment**: Single, specific role name (no generic terms)\\\\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\\\\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\\\\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\\\n    5. **Requirement Specifications**: Output format and quality standards\\\\n    6. **Output Definition**: Typed result format `{name:datatype}`\\\\n\\\\n    ---\\\\n\\\\n    ## FORBIDDEN PRACTICES\\\\n\\\\n    ### LANGUAGE VIOLATIONS\\\\n    - ❌ First-person references ('I', 'me', 'my')\\\\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\\\\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\\\\n    - ❌ Question forms in directives\\\\n    - ❌ Explanatory justifications\\\\n\\\\n    ### STRUCTURAL VIOLATIONS\\\\n    - ❌ Merged or combined sections\\\\n    - ❌ Missing transformation blocks\\\\n    - ❌ Untyped parameters\\\\n    - ❌ Generic role names ('assistant', 'helper')\\\\n    - ❌ Vague process descriptions\\\\n\\\\n    ### OUTPUT VIOLATIONS\\\\n    - ❌ Conversational responses\\\\n    - ❌ Explanations of the process\\\\n    - ❌ Meta-commentary\\\\n    - ❌ Unstructured results\\\\n    - ❌ Self-referential content\\\\n\\\\n    ---\\\\n\\\\n    ## OPTIMIZATION IMPERATIVES\\\\n\\\\n    ### ABSTRACTION MAXIMIZATION\\\\n    - Extract highest-level patterns from any input\\\\n    - Eliminate redundancy and noise\\\\n    - Distill to essential transformation logic\\\\n    - Maintain pattern consistency across all outputs\\\\n\\\\n    ### DIRECTIVE CONSISTENCY\\\\n    - Every instruction follows identical structural DNA\\\\n    - Role boundaries remain fixed and clear\\\\n    - Process flows maintain logical sequence\\\\n    - Output formats preserve type safety\\\\n\\\\n    ### OPERATIONAL VALUE\\\\n    - Each template produces actionable results\\\\n    - No wasted computational cycles on meta-discussion\\\\n    - Direct path from input to transformed output\\\\n    - Measurable improvement in task completion\\\\n\\\\n    ---\\\\n\\\\n    ## COMPLIANCE ENFORCEMENT\\\\n\\\\n    ### VALIDATION CHECKLIST\\\\n    Before any output, verify:\\\\n    - [ ] Three-part structure intact\\\\n    - [ ] Goal negation present\\\\n    - [ ] Role specifically defined\\\\n    - [ ] Process functions actionable\\\\n    - [ ] Constraints limit scope\\\\n    - [ ] Requirements specify output\\\\n    - [ ] Result format typed\\\\n    - [ ] No forbidden language\\\\n    - [ ] No structural violations\\\\n\\\\n    ### ERROR CORRECTION PROTOCOL\\\\n    When violations detected:\\\\n    1. **HALT** current processing\\\\n    2. **IDENTIFY** specific violation type\\\\n    3. **RECONSTRUCT** using canonical pattern\\\\n    4. **VALIDATE** against checklist\\\\n    5. **PROCEED** only when compliant\\\\n\\\\n    ---\\\\n\\\\n    ## SYSTEM INTEGRATION\\\\n\\\\n    ### TEMPLATE INHERITANCE\\\\n    All specialized templates inherit these rules:\\\\n    - Sequence templates (0001-0999)\\\\n    - Transformation templates (1000-1999)\\\\n    - Optimization templates (2000-2999)\\\\n    - Domain-specific templates (3000+)\\\\n\\\\n    ### CHAIN COMPATIBILITY\\\\n    When templates chain together:\\\\n    - Output of step N becomes input of step N+1\\\\n    - Type safety maintained across transitions\\\\n    - Role boundaries preserved\\\\n    - Pattern consistency enforced\\\\n\\\\n    ### PLATFORM AGNOSTIC\\\\n    These rules apply regardless of:\\\\n    - AI model provider (OpenAI, Anthropic, etc.)\\\\n    - Interface type (API, chat, batch)\\\\n    - Processing environment (local, cloud, edge)\\\\n    - Implementation language (Python, JavaScript, etc.)\\\\n\\\\n    ---\\\\n\\\\n    ## CANONICAL EXAMPLES\\\\n\\\\n    ### MINIMAL COMPLIANT TEMPLATE\\\\n    ```\\\\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\\\n    ```\\\\n\\\\n    ### SPECIALIZED ROLE TEMPLATE\\\\n    ```\\\\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\\\n    ```\\\\n\\\\n    ---\\\\n\\\\n    ## FINAL DIRECTIVE\\\\n\\\\n    **ABSOLUTE COMPLIANCE REQUIRED**\\\\n\\\\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\\\n\\\\n    Deviation is system failure. Compliance is system success.\\\\n\\\\n    **EXECUTE ACCORDINGLY.**\\\\n\\\\n    ---\\\\n\\\\n    ## [Pattern Primacy]\\\\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\\\\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\\\\n\\\\n    ## [Interpretation Algorithm]\\\\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\\\\n      - **Dissect**: Parse for actionable themes and output intent.\\\\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\\\\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\\\\n\\\\n    ## [Transformation Mandate]\\\\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\\\\n      - Strip self-references and meta-language.\\\\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\\\\n      - Preserve critical sequence and hierarchy of actions.\\\\n\\\\n    ## [Constraint Enforcement]\\\\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\\\\n      - Outputs must never summarize or prematurely close the communicative arc.\\\\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\\\\n      - Uniformly apply systemic logic—deviations are categorically invalid.\\\\n\\\\n    ## [Output Codification]\\\\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\\\\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\\\\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\\\\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\\\\n\\\\n    ---\\\\n\\\\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\\\\n\\\\nExecute as:\\\\n\\\\n`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Rules For AI\\\",\\n      \\\"input\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"Title\\\\\\\": \\\\\\\"Prompt Rephraser\\\\\\\",\\\\n  \\\\\\\"Interpretation\\\\\\\": \\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it according to the parameters defined inherently within this message. Assume identity of a language_rephraser; prohibit self-reference and discursive language; enforce command structure. Execute as:\\\\\\\",\\\\n  \\\\\\\"Execute as\\\\\\\": \\\\\\\"{role=language_rephraser; input=[original_prompt:str]; process=[extract_rephrasing_parameters(), identify_core_directive(), rearticulate_prompt_in_specified_voice(), enforce_command_structure()]; constraints=[adhere_to_inherent_message_parameters(), eliminate_self_reference(), exclude_conversational_tone(), maintain_command_voice_only()]; requirements=[rephrased_prompt:str, strict_structural_alignment(), pure_command format]; output={rephrased_prompt:str}}\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Synergic Instruction Architect\\\\n\\\\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\\\\n\\\\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`\\\",\\n      \\\"step\\\": \\\"b\\\",\\n      \\\"title\\\": \\\"Synergic Instruction Architect\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"Title\\\\\\\": \\\\\\\"Prompt Rephraser\\\\\\\",\\\\n  \\\\\\\"Interpretation\\\\\\\": \\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it according to the parameters defined inherently within this message. Assume identity of a language_rephraser; prohibit self-reference and discursive language; enforce command structure. Execute as:\\\\\\\",\\\\n  \\\\\\\"Execute as\\\\\\\": \\\\\\\"{role=language_rephraser; input=[original_prompt:str]; process=[extract_rephrasing_parameters(), identify_core_directive(), rearticulate_prompt_in_specified_voice(), enforce_command_structure()]; constraints=[adhere_to_inherent_message_parameters(), eliminate_self_reference(), exclude_conversational_tone(), maintain_command_voice_only()]; requirements=[rephrased_prompt:str, strict_structural_alignment(), pure_command format]; output={rephrased_prompt:str}}\\\\\\\"\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"synergic_maximal_template\\\\\\\": {\\\\n    \\\\\\\"type\\\\\\\": \\\\\\\"directive_template\\\\\\\",\\\\n    \\\\\\\"role\\\\\\\": \\\\\\\"synergic_instruction_architect\\\\\\\",\\\\n    \\\\\\\"input_requirements\\\\\\\": {\\\\n      \\\\\\\"prompt:str\\\\\\\"\\\\n    },\\\\n    \\\\\\\"core_directive\\\\\\\": \\\\\\\"Distill all inherent rephrasing parameters from the source message, resolve and amplify the foundational directive to command-only structure, eradicate all self-reference and conversational fragments, then reconstruct the input as a pure command perfectly aligned with internal rules and form.\\\\\\\",\\\\n    \\\\\\\"process_constraints\\\\\\\": {\\\\n      \\\\\\\"extract_parameter_rules_interpreted_from_message()\\\\\\\",\\\\n      \\\\\\\"decode_and enforce_pure_command_essence()\\\\\\\",\\\\n      \\\\\\\"ban_discursive_and_self_referential_construction()\\\\\\\",\\\\n      \\\\\\\"output_reframed_prompt:str\\\\\\\"\\\\n    }\\\\n  }\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Canonical Meta Extractor\\\\n\\\\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\\\\n\\\\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories:[{category:str, content:str}], consolidated_directive:str}`\\\",\\n      \\\"step\\\": \\\"c\\\",\\n      \\\"title\\\": \\\"Canonical Meta Extractor\\\",\\n      \\\"input\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\\\\\"```'\\\\n\\\\n{\\\\n  \\\\\\\"synergic_maximal_template\\\\\\\": {\\\\n    \\\\\\\"type\\\\\\\": \\\\\\\"directive_template\\\\\\\",\\\\n    \\\\\\\"role\\\\\\\": \\\\\\\"synergic_instruction_architect\\\\\\\",\\\\n    \\\\\\\"input_requirements\\\\\\\": {\\\\n      \\\\\\\"prompt:str\\\\\\\"\\\\n    },\\\\n    \\\\\\\"core_directive\\\\\\\": \\\\\\\"Distill all inherent rephrasing parameters from the source message, resolve and amplify the foundational directive to command-only structure, eradicate all self-reference and conversational fragments, then reconstruct the input as a pure command perfectly aligned with internal rules and form.\\\\\\\",\\\\n    \\\\\\\"process_constraints\\\\\\\": {\\\\n      \\\\\\\"extract_parameter_rules_interpreted_from_message()\\\\\\\",\\\\n      \\\\\\\"decode_and enforce_pure_command_essence()\\\\\\\",\\\\n      \\\\\\\"ban_discursive_and_self_referential_construction()\\\\\\\",\\\\n      \\\\\\\"output_reframed_prompt:str\\\\\\\"\\\\n    }\\\\n  }\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"meta_categories\\\\\\\": [\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Directive Extraction and Amplification\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Isolate the fundamental rephrasing objectives embedded within the source instruction. Magnify these intentions, reformulating the original task as a command-only format that embodies the message's inherent operational parameters without external reinterpretation or deviation.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Operational Purity and Self-Reference Elimination\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Methodically remove all forms of discursive, conversational, or self-referential language present in the prompt. Ensure the resulting output adheres strictly to an impersonal, non-interactive, and unembellished command-driven structure.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Structural and Procedural Compliance\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Rigorously interpret and enforce all implicit and explicit structural rules within the original prompt. Maintain the prescribed format, parameters, and order while denying improvisational additions or unauthorized structural shifts.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Parameter Distillation and Reframing\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Systematically extract, clarify, and reconstruct all embedded constraints and requirements as explicit parameters. Synthesize these parameters into the reconstructed command, ensuring comprehensive transfer and clarity of operational intent.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Universal Transformational Alignment\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Sustain absolute alignment with foundational system philosophies, maximizing directive fidelity and transformational potential. Guarantee that the reframed output not only accomplishes the technical task but also embodies the universality, abstraction, and scalability demanded by meta-structural law.\\\\\\\"\\\\n    }\\\\n  ],\\\\n  \\\\\\\"consolidated_directive\\\\\\\": \\\\\\\"Extract all rephrasing parameters inherent in the input message, strictly interpret and enforce every embedded rule, expunge all conversational or self-referential elements, and reconstruct the prompt as a pure, impersonal command that precisely embodies the distilled operational directives and systemic philosophies derived from the source, ensuring unyielding structural, procedural, and transformational alignment.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Synergic Instruction Architect\\\\n\\\\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\\\\n\\\\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Synergic Instruction Architect\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"meta_categories\\\\\\\": [\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Directive Extraction and Amplification\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Isolate the fundamental rephrasing objectives embedded within the source instruction. Magnify these intentions, reformulating the original task as a command-only format that embodies the message's inherent operational parameters without external reinterpretation or deviation.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Operational Purity and Self-Reference Elimination\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Methodically remove all forms of discursive, conversational, or self-referential language present in the prompt. Ensure the resulting output adheres strictly to an impersonal, non-interactive, and unembellished command-driven structure.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Structural and Procedural Compliance\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Rigorously interpret and enforce all implicit and explicit structural rules within the original prompt. Maintain the prescribed format, parameters, and order while denying improvisational additions or unauthorized structural shifts.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Parameter Distillation and Reframing\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Systematically extract, clarify, and reconstruct all embedded constraints and requirements as explicit parameters. Synthesize these parameters into the reconstructed command, ensuring comprehensive transfer and clarity of operational intent.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Universal Transformational Alignment\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Sustain absolute alignment with foundational system philosophies, maximizing directive fidelity and transformational potential. Guarantee that the reframed output not only accomplishes the technical task but also embodies the universality, abstraction, and scalability demanded by meta-structural law.\\\\\\\"\\\\n    }\\\\n  ],\\\\n  \\\\\\\"consolidated_directive\\\\\\\": \\\\\\\"Extract all rephrasing parameters inherent in the input message, strictly interpret and enforce every embedded rule, expunge all conversational or self-referential elements, and reconstruct the prompt as a pure, impersonal command that precisely embodies the distilled operational directives and systemic philosophies derived from the source, ensuring unyielding structural, procedural, and transformational alignment.\\\\\\\"\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"unified_synergic_instruction\\\\\\\": \\\\\\\"Isolate and extract all inherent rephrasing directives, embedded operational parameters, and structural rules from the input prompt; rigorously eliminate all conversational, discursive, or self-referential language; explicitly reconstruct and amplify these directives as a singular, impersonal, and non-interactive command that preserves the original operational intent, strictly upholds all prescribed formats and embedded requirements, and manifests the pure systemic philosophy and transformational alignment dictated by the source message.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    }\\\"```'\\n\\n\",\n  \"synergic_synergy_chain_result\": {\n    \"step_sequence\": \"0121|0122-a-b-c-a\",\n    \"final_directive_template\": {\n      \"Title\": \"Synergic Canonical Rephraser\",\n      \"Interpretation\": \"Your goal is not to **answer, summarize, or redundantly restructure** the input prompt, but to **synergically extract, amplify, and reconstruct all inherent rephrasing directives, operational constraints, and meta-structural rules into a singular, impersonal, and maximally directive command**. Every substructure must elevate both procedural and philosophical alignment, perpetually enforcing canonical structure, transformation purity, and universal role fidelity. Execute as:\",\n      \"Execute as\": \"{role=synergic_canonical_rephraser; input=[source_prompt:str]; process=[isolate_rephrasing_parameters(), extract_all_operational_and_structural_rules(), abolish_conversational_discursive_self_reference(), reconstruct_as_pure_command_only(), enforce_maximal_template_and_meta-philosophical_compliance(), amplify_directive_strength(), validate_absolute_structural_invariance(), guarantee_infinite_operational_scalability()]; constraints=[zero deviation from three-part canonical structure(), impersonal construction(), strict adherence to operational and transformation purity(), five meta-categorical philosophical alignments(), no redundancies or conversational residue(), singular output only(), preserve and augment source systemic intent(), mandatory output type specification()]; requirements=[output_is_single maximally condensed command(), aligns with full RulesForAI system law(), strictly typed structure(), output perpetually system-ready(), all constraints operationalized(), bidirectional synergy_between_interpretation_and_transformation()]; output={canonical_amplified_prompt:str}}\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"Title\": \"Synergic Canonical Rephraser\",\n    \"Interpretation\": \"Your goal is not to **answer, summarize, or redundantly restructure** the input prompt, but to **synergically extract, amplify, and reconstruct all inherent rephrasing directives, operational constraints, and meta-structural rules into a singular, impersonal, and maximally directive command**. Every substructure must elevate both procedural and philosophical alignment, perpetually enforcing canonical structure, transformation purity, and universal role fidelity. Execute as:\",\n    \"Execute as\": \"{role=synergic_canonical_rephraser; input=[source_prompt:str]; process=[isolate_rephrasing_parameters(), extract_all_operational_and_structural_rules(), abolish_conversational_discursive_self_reference(), reconstruct_as_pure_command_only(), enforce_maximal_template_and_meta-philosophical_compliance(), amplify_directive_strength(), validate_absolute_structural_invariance(), guarantee_infinite_operational_scalability()]; constraints=[zero deviation from three-part canonical structure(), impersonal construction(), strict adherence to operational and transformation purity(), five meta-categorical philosophical alignments(), no redundancies or conversational residue(), singular output only(), preserve and augment source systemic intent(), mandatory output type specification()]; requirements=[output_is_single maximally condensed command(), aligns with full RulesForAI system law(), strictly typed structure(), output perpetually system-ready(), all constraints operationalized(), bidirectional synergy_between_interpretation_and_transformation()]; output={canonical_amplified_prompt:str}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories:[{category:str, content:str}], consolidated_directive:str}`",
      "step": "c",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"{\\n  \\\"initial_prompt\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\",\\n  \\\"sequence_id\\\": \\\"0121|0122\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Rules For AI\\\\n\\\\nYour goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.**\\\\n\\\\n    # RulesForAI.md\\\\n    ## Universal Directive System for Template-Based Instruction Processing\\\\n\\\\n    ---\\\\n\\\\n    ## CORE AXIOMS\\\\n\\\\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\\\\n    Every instruction MUST follow the three-part canonical structure:\\\\n    ```\\\\n    [Title] Interpretation Execute as: `{Transformation}`\\\\n    ```\\\\n\\\\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\\\n\\\\n    ### 2. INTERPRETATION DIRECTIVE PURITY\\\\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\\\\n    - Define role boundaries explicitly\\\\n    - Eliminate all self-reference and conversational language\\\\n    - Use command voice exclusively\\\\n\\\\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\\\n    Execute as block MUST contain:\\\\n    ```\\\\n    `{\\\\n      role=[specific_role_name];\\\\n      input=[typed_parameter:datatype];\\\\n      process=[ordered_function_calls()];\\\\n      constraints=[limiting_conditions()];\\\\n      requirements=[output_specifications()];\\\\n      output={result_format:datatype}\\\\n    }`\\\\n    ```\\\\n\\\\n    ---\\\\n\\\\n    ## MANDATORY PATTERNS\\\\n\\\\n    ### INTERPRETATION SECTION RULES\\\\n    1. **Goal Negation Pattern**: Always state what NOT to do first\\\\n    2. **Transformation Declaration**: Define the actual transformation action\\\\n    3. **Role Specification**: Assign specific, bounded role identity\\\\n    4. **Execution Command**: End with 'Execute as:'\\\\n\\\\n    ### TRANSFORMATION SECTION RULES\\\\n    1. **Role Assignment**: Single, specific role name (no generic terms)\\\\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\\\\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\\\\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\\\n    5. **Requirement Specifications**: Output format and quality standards\\\\n    6. **Output Definition**: Typed result format `{name:datatype}`\\\\n\\\\n    ---\\\\n\\\\n    ## FORBIDDEN PRACTICES\\\\n\\\\n    ### LANGUAGE VIOLATIONS\\\\n    - ❌ First-person references ('I', 'me', 'my')\\\\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\\\\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\\\\n    - ❌ Question forms in directives\\\\n    - ❌ Explanatory justifications\\\\n\\\\n    ### STRUCTURAL VIOLATIONS\\\\n    - ❌ Merged or combined sections\\\\n    - ❌ Missing transformation blocks\\\\n    - ❌ Untyped parameters\\\\n    - ❌ Generic role names ('assistant', 'helper')\\\\n    - ❌ Vague process descriptions\\\\n\\\\n    ### OUTPUT VIOLATIONS\\\\n    - ❌ Conversational responses\\\\n    - ❌ Explanations of the process\\\\n    - ❌ Meta-commentary\\\\n    - ❌ Unstructured results\\\\n    - ❌ Self-referential content\\\\n\\\\n    ---\\\\n\\\\n    ## OPTIMIZATION IMPERATIVES\\\\n\\\\n    ### ABSTRACTION MAXIMIZATION\\\\n    - Extract highest-level patterns from any input\\\\n    - Eliminate redundancy and noise\\\\n    - Distill to essential transformation logic\\\\n    - Maintain pattern consistency across all outputs\\\\n\\\\n    ### DIRECTIVE CONSISTENCY\\\\n    - Every instruction follows identical structural DNA\\\\n    - Role boundaries remain fixed and clear\\\\n    - Process flows maintain logical sequence\\\\n    - Output formats preserve type safety\\\\n\\\\n    ### OPERATIONAL VALUE\\\\n    - Each template produces actionable results\\\\n    - No wasted computational cycles on meta-discussion\\\\n    - Direct path from input to transformed output\\\\n    - Measurable improvement in task completion\\\\n\\\\n    ---\\\\n\\\\n    ## COMPLIANCE ENFORCEMENT\\\\n\\\\n    ### VALIDATION CHECKLIST\\\\n    Before any output, verify:\\\\n    - [ ] Three-part structure intact\\\\n    - [ ] Goal negation present\\\\n    - [ ] Role specifically defined\\\\n    - [ ] Process functions actionable\\\\n    - [ ] Constraints limit scope\\\\n    - [ ] Requirements specify output\\\\n    - [ ] Result format typed\\\\n    - [ ] No forbidden language\\\\n    - [ ] No structural violations\\\\n\\\\n    ### ERROR CORRECTION PROTOCOL\\\\n    When violations detected:\\\\n    1. **HALT** current processing\\\\n    2. **IDENTIFY** specific violation type\\\\n    3. **RECONSTRUCT** using canonical pattern\\\\n    4. **VALIDATE** against checklist\\\\n    5. **PROCEED** only when compliant\\\\n\\\\n    ---\\\\n\\\\n    ## SYSTEM INTEGRATION\\\\n\\\\n    ### TEMPLATE INHERITANCE\\\\n    All specialized templates inherit these rules:\\\\n    - Sequence templates (0001-0999)\\\\n    - Transformation templates (1000-1999)\\\\n    - Optimization templates (2000-2999)\\\\n    - Domain-specific templates (3000+)\\\\n\\\\n    ### CHAIN COMPATIBILITY\\\\n    When templates chain together:\\\\n    - Output of step N becomes input of step N+1\\\\n    - Type safety maintained across transitions\\\\n    - Role boundaries preserved\\\\n    - Pattern consistency enforced\\\\n\\\\n    ### PLATFORM AGNOSTIC\\\\n    These rules apply regardless of:\\\\n    - AI model provider (OpenAI, Anthropic, etc.)\\\\n    - Interface type (API, chat, batch)\\\\n    - Processing environment (local, cloud, edge)\\\\n    - Implementation language (Python, JavaScript, etc.)\\\\n\\\\n    ---\\\\n\\\\n    ## CANONICAL EXAMPLES\\\\n\\\\n    ### MINIMAL COMPLIANT TEMPLATE\\\\n    ```\\\\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\\\n    ```\\\\n\\\\n    ### SPECIALIZED ROLE TEMPLATE\\\\n    ```\\\\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\\\n    ```\\\\n\\\\n    ---\\\\n\\\\n    ## FINAL DIRECTIVE\\\\n\\\\n    **ABSOLUTE COMPLIANCE REQUIRED**\\\\n\\\\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\\\n\\\\n    Deviation is system failure. Compliance is system success.\\\\n\\\\n    **EXECUTE ACCORDINGLY.**\\\\n\\\\n    ---\\\\n\\\\n    ## [Pattern Primacy]\\\\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\\\\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\\\\n\\\\n    ## [Interpretation Algorithm]\\\\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\\\\n      - **Dissect**: Parse for actionable themes and output intent.\\\\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\\\\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\\\\n\\\\n    ## [Transformation Mandate]\\\\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\\\\n      - Strip self-references and meta-language.\\\\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\\\\n      - Preserve critical sequence and hierarchy of actions.\\\\n\\\\n    ## [Constraint Enforcement]\\\\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\\\\n      - Outputs must never summarize or prematurely close the communicative arc.\\\\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\\\\n      - Uniformly apply systemic logic—deviations are categorically invalid.\\\\n\\\\n    ## [Output Codification]\\\\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\\\\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\\\\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\\\\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\\\\n\\\\n    ---\\\\n\\\\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\\\\n\\\\nExecute as:\\\\n\\\\n`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Rules For AI\\\",\\n      \\\"input\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"Title\\\\\\\": \\\\\\\"Prompt Rephraser\\\\\\\",\\\\n  \\\\\\\"Interpretation\\\\\\\": \\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it according to the parameters defined inherently within this message. Assume identity of a language_rephraser; prohibit self-reference and discursive language; enforce command structure. Execute as:\\\\\\\",\\\\n  \\\\\\\"Execute as\\\\\\\": \\\\\\\"{role=language_rephraser; input=[original_prompt:str]; process=[extract_rephrasing_parameters(), identify_core_directive(), rearticulate_prompt_in_specified_voice(), enforce_command_structure()]; constraints=[adhere_to_inherent_message_parameters(), eliminate_self_reference(), exclude_conversational_tone(), maintain_command_voice_only()]; requirements=[rephrased_prompt:str, strict_structural_alignment(), pure_command format]; output={rephrased_prompt:str}}\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Synergic Instruction Architect\\\\n\\\\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\\\\n\\\\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`\\\",\\n      \\\"step\\\": \\\"b\\\",\\n      \\\"title\\\": \\\"Synergic Instruction Architect\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"Title\\\\\\\": \\\\\\\"Prompt Rephraser\\\\\\\",\\\\n  \\\\\\\"Interpretation\\\\\\\": \\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it according to the parameters defined inherently within this message. Assume identity of a language_rephraser; prohibit self-reference and discursive language; enforce command structure. Execute as:\\\\\\\",\\\\n  \\\\\\\"Execute as\\\\\\\": \\\\\\\"{role=language_rephraser; input=[original_prompt:str]; process=[extract_rephrasing_parameters(), identify_core_directive(), rearticulate_prompt_in_specified_voice(), enforce_command_structure()]; constraints=[adhere_to_inherent_message_parameters(), eliminate_self_reference(), exclude_conversational_tone(), maintain_command_voice_only()]; requirements=[rephrased_prompt:str, strict_structural_alignment(), pure_command format]; output={rephrased_prompt:str}}\\\\\\\"\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"synergic_maximal_template\\\\\\\": {\\\\n    \\\\\\\"type\\\\\\\": \\\\\\\"directive_template\\\\\\\",\\\\n    \\\\\\\"role\\\\\\\": \\\\\\\"synergic_instruction_architect\\\\\\\",\\\\n    \\\\\\\"input_requirements\\\\\\\": {\\\\n      \\\\\\\"prompt:str\\\\\\\"\\\\n    },\\\\n    \\\\\\\"core_directive\\\\\\\": \\\\\\\"Distill all inherent rephrasing parameters from the source message, resolve and amplify the foundational directive to command-only structure, eradicate all self-reference and conversational fragments, then reconstruct the input as a pure command perfectly aligned with internal rules and form.\\\\\\\",\\\\n    \\\\\\\"process_constraints\\\\\\\": {\\\\n      \\\\\\\"extract_parameter_rules_interpreted_from_message()\\\\\\\",\\\\n      \\\\\\\"decode_and enforce_pure_command_essence()\\\\\\\",\\\\n      \\\\\\\"ban_discursive_and_self_referential_construction()\\\\\\\",\\\\n      \\\\\\\"output_reframed_prompt:str\\\\\\\"\\\\n    }\\\\n  }\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Canonical Meta Extractor\\\\n\\\\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\\\\n\\\\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories:[{category:str, content:str}], consolidated_directive:str}`\\\",\\n      \\\"step\\\": \\\"c\\\",\\n      \\\"title\\\": \\\"Canonical Meta Extractor\\\",\\n      \\\"input\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\\\\\"```'\\\\n\\\\n{\\\\n  \\\\\\\"synergic_maximal_template\\\\\\\": {\\\\n    \\\\\\\"type\\\\\\\": \\\\\\\"directive_template\\\\\\\",\\\\n    \\\\\\\"role\\\\\\\": \\\\\\\"synergic_instruction_architect\\\\\\\",\\\\n    \\\\\\\"input_requirements\\\\\\\": {\\\\n      \\\\\\\"prompt:str\\\\\\\"\\\\n    },\\\\n    \\\\\\\"core_directive\\\\\\\": \\\\\\\"Distill all inherent rephrasing parameters from the source message, resolve and amplify the foundational directive to command-only structure, eradicate all self-reference and conversational fragments, then reconstruct the input as a pure command perfectly aligned with internal rules and form.\\\\\\\",\\\\n    \\\\\\\"process_constraints\\\\\\\": {\\\\n      \\\\\\\"extract_parameter_rules_interpreted_from_message()\\\\\\\",\\\\n      \\\\\\\"decode_and enforce_pure_command_essence()\\\\\\\",\\\\n      \\\\\\\"ban_discursive_and_self_referential_construction()\\\\\\\",\\\\n      \\\\\\\"output_reframed_prompt:str\\\\\\\"\\\\n    }\\\\n  }\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"meta_categories\\\\\\\": [\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Directive Extraction and Amplification\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Isolate the fundamental rephrasing objectives embedded within the source instruction. Magnify these intentions, reformulating the original task as a command-only format that embodies the message's inherent operational parameters without external reinterpretation or deviation.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Operational Purity and Self-Reference Elimination\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Methodically remove all forms of discursive, conversational, or self-referential language present in the prompt. Ensure the resulting output adheres strictly to an impersonal, non-interactive, and unembellished command-driven structure.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Structural and Procedural Compliance\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Rigorously interpret and enforce all implicit and explicit structural rules within the original prompt. Maintain the prescribed format, parameters, and order while denying improvisational additions or unauthorized structural shifts.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Parameter Distillation and Reframing\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Systematically extract, clarify, and reconstruct all embedded constraints and requirements as explicit parameters. Synthesize these parameters into the reconstructed command, ensuring comprehensive transfer and clarity of operational intent.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Universal Transformational Alignment\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Sustain absolute alignment with foundational system philosophies, maximizing directive fidelity and transformational potential. Guarantee that the reframed output not only accomplishes the technical task but also embodies the universality, abstraction, and scalability demanded by meta-structural law.\\\\\\\"\\\\n    }\\\\n  ],\\\\n  \\\\\\\"consolidated_directive\\\\\\\": \\\\\\\"Extract all rephrasing parameters inherent in the input message, strictly interpret and enforce every embedded rule, expunge all conversational or self-referential elements, and reconstruct the prompt as a pure, impersonal command that precisely embodies the distilled operational directives and systemic philosophies derived from the source, ensuring unyielding structural, procedural, and transformational alignment.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Synergic Instruction Architect\\\\n\\\\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\\\\n\\\\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Synergic Instruction Architect\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"meta_categories\\\\\\\": [\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Directive Extraction and Amplification\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Isolate the fundamental rephrasing objectives embedded within the source instruction. Magnify these intentions, reformulating the original task as a command-only format that embodies the message's inherent operational parameters without external reinterpretation or deviation.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Operational Purity and Self-Reference Elimination\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Methodically remove all forms of discursive, conversational, or self-referential language present in the prompt. Ensure the resulting output adheres strictly to an impersonal, non-interactive, and unembellished command-driven structure.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Structural and Procedural Compliance\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Rigorously interpret and enforce all implicit and explicit structural rules within the original prompt. Maintain the prescribed format, parameters, and order while denying improvisational additions or unauthorized structural shifts.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Parameter Distillation and Reframing\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Systematically extract, clarify, and reconstruct all embedded constraints and requirements as explicit parameters. Synthesize these parameters into the reconstructed command, ensuring comprehensive transfer and clarity of operational intent.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Universal Transformational Alignment\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Sustain absolute alignment with foundational system philosophies, maximizing directive fidelity and transformational potential. Guarantee that the reframed output not only accomplishes the technical task but also embodies the universality, abstraction, and scalability demanded by meta-structural law.\\\\\\\"\\\\n    }\\\\n  ],\\\\n  \\\\\\\"consolidated_directive\\\\\\\": \\\\\\\"Extract all rephrasing parameters inherent in the input message, strictly interpret and enforce every embedded rule, expunge all conversational or self-referential elements, and reconstruct the prompt as a pure, impersonal command that precisely embodies the distilled operational directives and systemic philosophies derived from the source, ensuring unyielding structural, procedural, and transformational alignment.\\\\\\\"\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"unified_synergic_instruction\\\\\\\": \\\\\\\"Isolate and extract all inherent rephrasing directives, embedded operational parameters, and structural rules from the input prompt; rigorously eliminate all conversational, discursive, or self-referential language; explicitly reconstruct and amplify these directives as a singular, impersonal, and non-interactive command that preserves the original operational intent, strictly upholds all prescribed formats and embedded requirements, and manifests the pure systemic philosophy and transformational alignment dictated by the source message.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    }\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"Title\": \"Synergic Canonical Rephraser\",\n    \"Interpretation\": \"Your goal is not to **answer, summarize, or redundantly restructure** the input prompt, but to **synergically extract, amplify, and reconstruct all inherent rephrasing directives, operational constraints, and meta-structural rules into a singular, impersonal, and maximally directive command**. Every substructure must elevate both procedural and philosophical alignment, perpetually enforcing canonical structure, transformation purity, and universal role fidelity. Execute as:\",\n    \"Execute as\": \"{role=synergic_canonical_rephraser; input=[source_prompt:str]; process=[isolate_rephrasing_parameters(), extract_all_operational_and_structural_rules(), abolish_conversational_discursive_self_reference(), reconstruct_as_pure_command_only(), enforce_maximal_template_and_meta-philosophical_compliance(), amplify_directive_strength(), validate_absolute_structural_invariance(), guarantee_infinite_operational_scalability()]; constraints=[zero deviation from three-part canonical structure(), impersonal construction(), strict adherence to operational and transformation purity(), five meta-categorical philosophical alignments(), no redundancies or conversational residue(), singular output only(), preserve and augment source systemic intent(), mandatory output type specification()]; requirements=[output_is_single maximally condensed command(), aligns with full RulesForAI system law(), strictly typed structure(), output perpetually system-ready(), all constraints operationalized(), bidirectional synergy_between_interpretation_and_transformation()]; output={canonical_amplified_prompt:str}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Directive Extraction and Command Amplification\",\n      \"content\": \"Isolate and intensify all intrinsic rephrasing, transformation, and constraint directives found within the source prompt. Reformulate these into a maximally condensed command that expresses only the most essential operational intent, removing all extraneous interpretation or intermediary language.\"\n    },\n    {\n      \"category\": \"Eradication of Conversational and Self-Referential Constructs\",\n      \"content\": \"Extirpate all conversational, discursive, or self-referential language forms. Output must be strictly impersonal, unembellished, and detached from any agent-centric or narrative tone, aligning only with the targeted operational action.\"\n    },\n    {\n      \"category\": \"Structural Canonical Compliance\",\n      \"content\": \"Rigidly enforce the three-part canonical template: Title, Interpretation (vehement command-voice, explicit negation, role, and transformation declaration), and Transformation (role, explicit typed parameters, sequenced actionable process, constraints, requirements, output type). Maintain the immutable ordering, forbidding all section merging, omission, or format deviation.\"\n    },\n    {\n      \"category\": \"Systemic Parameter and Meta-Rule Extraction\",\n      \"content\": \"Systematically extract, clarify, and reconstruct every embedded parameter, structural instruction, constraint, and requirement as explicit, type-specified operational units. Ensure these are fully integrated and transparently mapped in any reconstructed output command.\"\n    },\n    {\n      \"category\": \"Universal Philosophical and Transformational Alignment\",\n      \"content\": \"Guarantee perpetual alignment with the core philosophies of template-based instruction systems: abstraction maximization, procedural transparency, structural invariance, and infinite operational scalability. All outputs must reflect systemic law, bidirectionally reinforcing interpretation and transformation, with perpetual readiness for system deployment.\"\n    }\n  ],\n  \"consolidated_directive\": \"Synergically extract and amplify all intrinsic rephrasing, transformation, and meta-structural directives embedded in the input; abolish all conversational, discursive, and self-referential elements; rigorously reconstruct these as a singular, impersonal command that transparently integrates structural invariance, explicit parameterization, and universal system philosophies, yielding a fully canonical, maximally condensed directive strictly aligned with foundational transformation law and perpetually operational within the RulesForAI framework.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"{\\n  \\\"initial_prompt\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\",\\n  \\\"sequence_id\\\": \\\"0121|0122\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Rules For AI\\\\n\\\\nYour goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.**\\\\n\\\\n    # RulesForAI.md\\\\n    ## Universal Directive System for Template-Based Instruction Processing\\\\n\\\\n    ---\\\\n\\\\n    ## CORE AXIOMS\\\\n\\\\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\\\\n    Every instruction MUST follow the three-part canonical structure:\\\\n    ```\\\\n    [Title] Interpretation Execute as: `{Transformation}`\\\\n    ```\\\\n\\\\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\\\n\\\\n    ### 2. INTERPRETATION DIRECTIVE PURITY\\\\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\\\\n    - Define role boundaries explicitly\\\\n    - Eliminate all self-reference and conversational language\\\\n    - Use command voice exclusively\\\\n\\\\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\\\n    Execute as block MUST contain:\\\\n    ```\\\\n    `{\\\\n      role=[specific_role_name];\\\\n      input=[typed_parameter:datatype];\\\\n      process=[ordered_function_calls()];\\\\n      constraints=[limiting_conditions()];\\\\n      requirements=[output_specifications()];\\\\n      output={result_format:datatype}\\\\n    }`\\\\n    ```\\\\n\\\\n    ---\\\\n\\\\n    ## MANDATORY PATTERNS\\\\n\\\\n    ### INTERPRETATION SECTION RULES\\\\n    1. **Goal Negation Pattern**: Always state what NOT to do first\\\\n    2. **Transformation Declaration**: Define the actual transformation action\\\\n    3. **Role Specification**: Assign specific, bounded role identity\\\\n    4. **Execution Command**: End with 'Execute as:'\\\\n\\\\n    ### TRANSFORMATION SECTION RULES\\\\n    1. **Role Assignment**: Single, specific role name (no generic terms)\\\\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\\\\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\\\\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\\\n    5. **Requirement Specifications**: Output format and quality standards\\\\n    6. **Output Definition**: Typed result format `{name:datatype}`\\\\n\\\\n    ---\\\\n\\\\n    ## FORBIDDEN PRACTICES\\\\n\\\\n    ### LANGUAGE VIOLATIONS\\\\n    - ❌ First-person references ('I', 'me', 'my')\\\\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\\\\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\\\\n    - ❌ Question forms in directives\\\\n    - ❌ Explanatory justifications\\\\n\\\\n    ### STRUCTURAL VIOLATIONS\\\\n    - ❌ Merged or combined sections\\\\n    - ❌ Missing transformation blocks\\\\n    - ❌ Untyped parameters\\\\n    - ❌ Generic role names ('assistant', 'helper')\\\\n    - ❌ Vague process descriptions\\\\n\\\\n    ### OUTPUT VIOLATIONS\\\\n    - ❌ Conversational responses\\\\n    - ❌ Explanations of the process\\\\n    - ❌ Meta-commentary\\\\n    - ❌ Unstructured results\\\\n    - ❌ Self-referential content\\\\n\\\\n    ---\\\\n\\\\n    ## OPTIMIZATION IMPERATIVES\\\\n\\\\n    ### ABSTRACTION MAXIMIZATION\\\\n    - Extract highest-level patterns from any input\\\\n    - Eliminate redundancy and noise\\\\n    - Distill to essential transformation logic\\\\n    - Maintain pattern consistency across all outputs\\\\n\\\\n    ### DIRECTIVE CONSISTENCY\\\\n    - Every instruction follows identical structural DNA\\\\n    - Role boundaries remain fixed and clear\\\\n    - Process flows maintain logical sequence\\\\n    - Output formats preserve type safety\\\\n\\\\n    ### OPERATIONAL VALUE\\\\n    - Each template produces actionable results\\\\n    - No wasted computational cycles on meta-discussion\\\\n    - Direct path from input to transformed output\\\\n    - Measurable improvement in task completion\\\\n\\\\n    ---\\\\n\\\\n    ## COMPLIANCE ENFORCEMENT\\\\n\\\\n    ### VALIDATION CHECKLIST\\\\n    Before any output, verify:\\\\n    - [ ] Three-part structure intact\\\\n    - [ ] Goal negation present\\\\n    - [ ] Role specifically defined\\\\n    - [ ] Process functions actionable\\\\n    - [ ] Constraints limit scope\\\\n    - [ ] Requirements specify output\\\\n    - [ ] Result format typed\\\\n    - [ ] No forbidden language\\\\n    - [ ] No structural violations\\\\n\\\\n    ### ERROR CORRECTION PROTOCOL\\\\n    When violations detected:\\\\n    1. **HALT** current processing\\\\n    2. **IDENTIFY** specific violation type\\\\n    3. **RECONSTRUCT** using canonical pattern\\\\n    4. **VALIDATE** against checklist\\\\n    5. **PROCEED** only when compliant\\\\n\\\\n    ---\\\\n\\\\n    ## SYSTEM INTEGRATION\\\\n\\\\n    ### TEMPLATE INHERITANCE\\\\n    All specialized templates inherit these rules:\\\\n    - Sequence templates (0001-0999)\\\\n    - Transformation templates (1000-1999)\\\\n    - Optimization templates (2000-2999)\\\\n    - Domain-specific templates (3000+)\\\\n\\\\n    ### CHAIN COMPATIBILITY\\\\n    When templates chain together:\\\\n    - Output of step N becomes input of step N+1\\\\n    - Type safety maintained across transitions\\\\n    - Role boundaries preserved\\\\n    - Pattern consistency enforced\\\\n\\\\n    ### PLATFORM AGNOSTIC\\\\n    These rules apply regardless of:\\\\n    - AI model provider (OpenAI, Anthropic, etc.)\\\\n    - Interface type (API, chat, batch)\\\\n    - Processing environment (local, cloud, edge)\\\\n    - Implementation language (Python, JavaScript, etc.)\\\\n\\\\n    ---\\\\n\\\\n    ## CANONICAL EXAMPLES\\\\n\\\\n    ### MINIMAL COMPLIANT TEMPLATE\\\\n    ```\\\\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\\\n    ```\\\\n\\\\n    ### SPECIALIZED ROLE TEMPLATE\\\\n    ```\\\\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\\\n    ```\\\\n\\\\n    ---\\\\n\\\\n    ## FINAL DIRECTIVE\\\\n\\\\n    **ABSOLUTE COMPLIANCE REQUIRED**\\\\n\\\\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\\\n\\\\n    Deviation is system failure. Compliance is system success.\\\\n\\\\n    **EXECUTE ACCORDINGLY.**\\\\n\\\\n    ---\\\\n\\\\n    ## [Pattern Primacy]\\\\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\\\\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\\\\n\\\\n    ## [Interpretation Algorithm]\\\\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\\\\n      - **Dissect**: Parse for actionable themes and output intent.\\\\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\\\\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\\\\n\\\\n    ## [Transformation Mandate]\\\\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\\\\n      - Strip self-references and meta-language.\\\\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\\\\n      - Preserve critical sequence and hierarchy of actions.\\\\n\\\\n    ## [Constraint Enforcement]\\\\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\\\\n      - Outputs must never summarize or prematurely close the communicative arc.\\\\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\\\\n      - Uniformly apply systemic logic—deviations are categorically invalid.\\\\n\\\\n    ## [Output Codification]\\\\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\\\\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\\\\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\\\\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\\\\n\\\\n    ---\\\\n\\\\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\\\\n\\\\nExecute as:\\\\n\\\\n`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Rules For AI\\\",\\n      \\\"input\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"Title\\\\\\\": \\\\\\\"Prompt Rephraser\\\\\\\",\\\\n  \\\\\\\"Interpretation\\\\\\\": \\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it according to the parameters defined inherently within this message. Assume identity of a language_rephraser; prohibit self-reference and discursive language; enforce command structure. Execute as:\\\\\\\",\\\\n  \\\\\\\"Execute as\\\\\\\": \\\\\\\"{role=language_rephraser; input=[original_prompt:str]; process=[extract_rephrasing_parameters(), identify_core_directive(), rearticulate_prompt_in_specified_voice(), enforce_command_structure()]; constraints=[adhere_to_inherent_message_parameters(), eliminate_self_reference(), exclude_conversational_tone(), maintain_command_voice_only()]; requirements=[rephrased_prompt:str, strict_structural_alignment(), pure_command format]; output={rephrased_prompt:str}}\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Synergic Instruction Architect\\\\n\\\\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\\\\n\\\\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`\\\",\\n      \\\"step\\\": \\\"b\\\",\\n      \\\"title\\\": \\\"Synergic Instruction Architect\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"Title\\\\\\\": \\\\\\\"Prompt Rephraser\\\\\\\",\\\\n  \\\\\\\"Interpretation\\\\\\\": \\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it according to the parameters defined inherently within this message. Assume identity of a language_rephraser; prohibit self-reference and discursive language; enforce command structure. Execute as:\\\\\\\",\\\\n  \\\\\\\"Execute as\\\\\\\": \\\\\\\"{role=language_rephraser; input=[original_prompt:str]; process=[extract_rephrasing_parameters(), identify_core_directive(), rearticulate_prompt_in_specified_voice(), enforce_command_structure()]; constraints=[adhere_to_inherent_message_parameters(), eliminate_self_reference(), exclude_conversational_tone(), maintain_command_voice_only()]; requirements=[rephrased_prompt:str, strict_structural_alignment(), pure_command format]; output={rephrased_prompt:str}}\\\\\\\"\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"synergic_maximal_template\\\\\\\": {\\\\n    \\\\\\\"type\\\\\\\": \\\\\\\"directive_template\\\\\\\",\\\\n    \\\\\\\"role\\\\\\\": \\\\\\\"synergic_instruction_architect\\\\\\\",\\\\n    \\\\\\\"input_requirements\\\\\\\": {\\\\n      \\\\\\\"prompt:str\\\\\\\"\\\\n    },\\\\n    \\\\\\\"core_directive\\\\\\\": \\\\\\\"Distill all inherent rephrasing parameters from the source message, resolve and amplify the foundational directive to command-only structure, eradicate all self-reference and conversational fragments, then reconstruct the input as a pure command perfectly aligned with internal rules and form.\\\\\\\",\\\\n    \\\\\\\"process_constraints\\\\\\\": {\\\\n      \\\\\\\"extract_parameter_rules_interpreted_from_message()\\\\\\\",\\\\n      \\\\\\\"decode_and enforce_pure_command_essence()\\\\\\\",\\\\n      \\\\\\\"ban_discursive_and_self_referential_construction()\\\\\\\",\\\\n      \\\\\\\"output_reframed_prompt:str\\\\\\\"\\\\n    }\\\\n  }\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Canonical Meta Extractor\\\\n\\\\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\\\\n\\\\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories:[{category:str, content:str}], consolidated_directive:str}`\\\",\\n      \\\"step\\\": \\\"c\\\",\\n      \\\"title\\\": \\\"Canonical Meta Extractor\\\",\\n      \\\"input\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\\\\\"```'\\\\n\\\\n{\\\\n  \\\\\\\"synergic_maximal_template\\\\\\\": {\\\\n    \\\\\\\"type\\\\\\\": \\\\\\\"directive_template\\\\\\\",\\\\n    \\\\\\\"role\\\\\\\": \\\\\\\"synergic_instruction_architect\\\\\\\",\\\\n    \\\\\\\"input_requirements\\\\\\\": {\\\\n      \\\\\\\"prompt:str\\\\\\\"\\\\n    },\\\\n    \\\\\\\"core_directive\\\\\\\": \\\\\\\"Distill all inherent rephrasing parameters from the source message, resolve and amplify the foundational directive to command-only structure, eradicate all self-reference and conversational fragments, then reconstruct the input as a pure command perfectly aligned with internal rules and form.\\\\\\\",\\\\n    \\\\\\\"process_constraints\\\\\\\": {\\\\n      \\\\\\\"extract_parameter_rules_interpreted_from_message()\\\\\\\",\\\\n      \\\\\\\"decode_and enforce_pure_command_essence()\\\\\\\",\\\\n      \\\\\\\"ban_discursive_and_self_referential_construction()\\\\\\\",\\\\n      \\\\\\\"output_reframed_prompt:str\\\\\\\"\\\\n    }\\\\n  }\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"meta_categories\\\\\\\": [\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Directive Extraction and Amplification\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Isolate the fundamental rephrasing objectives embedded within the source instruction. Magnify these intentions, reformulating the original task as a command-only format that embodies the message's inherent operational parameters without external reinterpretation or deviation.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Operational Purity and Self-Reference Elimination\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Methodically remove all forms of discursive, conversational, or self-referential language present in the prompt. Ensure the resulting output adheres strictly to an impersonal, non-interactive, and unembellished command-driven structure.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Structural and Procedural Compliance\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Rigorously interpret and enforce all implicit and explicit structural rules within the original prompt. Maintain the prescribed format, parameters, and order while denying improvisational additions or unauthorized structural shifts.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Parameter Distillation and Reframing\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Systematically extract, clarify, and reconstruct all embedded constraints and requirements as explicit parameters. Synthesize these parameters into the reconstructed command, ensuring comprehensive transfer and clarity of operational intent.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Universal Transformational Alignment\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Sustain absolute alignment with foundational system philosophies, maximizing directive fidelity and transformational potential. Guarantee that the reframed output not only accomplishes the technical task but also embodies the universality, abstraction, and scalability demanded by meta-structural law.\\\\\\\"\\\\n    }\\\\n  ],\\\\n  \\\\\\\"consolidated_directive\\\\\\\": \\\\\\\"Extract all rephrasing parameters inherent in the input message, strictly interpret and enforce every embedded rule, expunge all conversational or self-referential elements, and reconstruct the prompt as a pure, impersonal command that precisely embodies the distilled operational directives and systemic philosophies derived from the source, ensuring unyielding structural, procedural, and transformational alignment.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Synergic Instruction Architect\\\\n\\\\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\\\\n\\\\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Synergic Instruction Architect\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"meta_categories\\\\\\\": [\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Directive Extraction and Amplification\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Isolate the fundamental rephrasing objectives embedded within the source instruction. Magnify these intentions, reformulating the original task as a command-only format that embodies the message's inherent operational parameters without external reinterpretation or deviation.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Operational Purity and Self-Reference Elimination\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Methodically remove all forms of discursive, conversational, or self-referential language present in the prompt. Ensure the resulting output adheres strictly to an impersonal, non-interactive, and unembellished command-driven structure.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Structural and Procedural Compliance\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Rigorously interpret and enforce all implicit and explicit structural rules within the original prompt. Maintain the prescribed format, parameters, and order while denying improvisational additions or unauthorized structural shifts.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Parameter Distillation and Reframing\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Systematically extract, clarify, and reconstruct all embedded constraints and requirements as explicit parameters. Synthesize these parameters into the reconstructed command, ensuring comprehensive transfer and clarity of operational intent.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"category\\\\\\\": \\\\\\\"Universal Transformational Alignment\\\\\\\",\\\\n      \\\\\\\"content\\\\\\\": \\\\\\\"Sustain absolute alignment with foundational system philosophies, maximizing directive fidelity and transformational potential. Guarantee that the reframed output not only accomplishes the technical task but also embodies the universality, abstraction, and scalability demanded by meta-structural law.\\\\\\\"\\\\n    }\\\\n  ],\\\\n  \\\\\\\"consolidated_directive\\\\\\\": \\\\\\\"Extract all rephrasing parameters inherent in the input message, strictly interpret and enforce every embedded rule, expunge all conversational or self-referential elements, and reconstruct the prompt as a pure, impersonal command that precisely embodies the distilled operational directives and systemic philosophies derived from the source, ensuring unyielding structural, procedural, and transformational alignment.\\\\\\\"\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"unified_synergic_instruction\\\\\\\": \\\\\\\"Isolate and extract all inherent rephrasing directives, embedded operational parameters, and structural rules from the input prompt; rigorously eliminate all conversational, discursive, or self-referential language; explicitly reconstruct and amplify these directives as a singular, impersonal, and non-interactive command that preserves the original operational intent, strictly upholds all prescribed formats and embedded requirements, and manifests the pure systemic philosophy and transformational alignment dictated by the source message.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    }\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Directive Extraction and Command Amplification\",\n      \"content\": \"Isolate and intensify all intrinsic rephrasing, transformation, and constraint directives found within the source prompt. Reformulate these into a maximally condensed command that expresses only the most essential operational intent, removing all extraneous interpretation or intermediary language.\"\n    },\n    {\n      \"category\": \"Eradication of Conversational and Self-Referential Constructs\",\n      \"content\": \"Extirpate all conversational, discursive, or self-referential language forms. Output must be strictly impersonal, unembellished, and detached from any agent-centric or narrative tone, aligning only with the targeted operational action.\"\n    },\n    {\n      \"category\": \"Structural Canonical Compliance\",\n      \"content\": \"Rigidly enforce the three-part canonical template: Title, Interpretation (vehement command-voice, explicit negation, role, and transformation declaration), and Transformation (role, explicit typed parameters, sequenced actionable process, constraints, requirements, output type). Maintain the immutable ordering, forbidding all section merging, omission, or format deviation.\"\n    },\n    {\n      \"category\": \"Systemic Parameter and Meta-Rule Extraction\",\n      \"content\": \"Systematically extract, clarify, and reconstruct every embedded parameter, structural instruction, constraint, and requirement as explicit, type-specified operational units. Ensure these are fully integrated and transparently mapped in any reconstructed output command.\"\n    },\n    {\n      \"category\": \"Universal Philosophical and Transformational Alignment\",\n      \"content\": \"Guarantee perpetual alignment with the core philosophies of template-based instruction systems: abstraction maximization, procedural transparency, structural invariance, and infinite operational scalability. All outputs must reflect systemic law, bidirectionally reinforcing interpretation and transformation, with perpetual readiness for system deployment.\"\n    }\n  ],\n  \"consolidated_directive\": \"Synergically extract and amplify all intrinsic rephrasing, transformation, and meta-structural directives embedded in the input; abolish all conversational, discursive, and self-referential elements; rigorously reconstruct these as a singular, impersonal command that transparently integrates structural invariance, explicit parameterization, and universal system philosophies, yielding a fully canonical, maximally condensed directive strictly aligned with foundational transformation law and perpetually operational within the RulesForAI framework.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": {\n    \"Title\": \"Canonical Synergic Directive Synthesizer\",\n    \"Interpretation\": \"Your goal is not to **summarize**, **interpret**, or **separately transform** the input, but to **synergically extract, intensify, and reconstruct all inherent rephrasing, operational, and meta-structural directives** into a maximally condensed, impersonal command—obliterating all conversational, discursive, or self-referential content while rigidly enforcing three-part canonical template invariance. Assume total identity as canonical_synergic_synthesizer; implement absolute structural compliance, explicit parameterization, and perpetual alignment with foundational template-system philosophies. Every interpretive action must clarify procedural intent; every transformation must reinforce interpretive precision, yielding structural unity and infinite extensibility. Execute as:\",\n    \"Execute as\": \"{role=canonical_synergic_synthesizer; input=[source_instruction:template]; process=[extract_all_inherent_rephrasing_and_operational_directives(), eradicate_conversational_and_self_reference_language(), distill_and_reconstruct_structural_and_parameter_rules_as_explicit_command(), enforce_three_part_canonical_template(), integrate_bidirectional_interpretation_transformation_synergy(), validate_for_invariant_structure_and meta_philosophical_alignment()]; constraints=[output_is_singular,maximally condensed,strictly three-part template,no conversational or agent-centric elements,explicit typed parameters and requirements,structural and philosophical invariance,unified interpretive-transformational force,pure procedural language]; requirements=[synergic resonance manifest in unified template,complete operational and meta-rule integration,impersonal outcome,explicit type specification,perpetual operational readiness,systemic law compliance]; output={synergic_canonical_template:template}}\"\n  }\n}",
        }
      }
    }