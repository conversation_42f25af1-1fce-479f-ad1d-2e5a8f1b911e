  {
  "initial_prompt": "1.  **\\[7] Execute as optimization engine:** `{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), generate_minimal_change(), evaluate_impact()]; output={enhanced_input:str, predicted_improvement:float}}` -  This instruction directly aims to improve the input and provides a predicted improvement score. The structure encourages minimal but impactful changes.\n\n2.  **\\[8] Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation.** This focuses on creating a generalized directive and an enhancement, making the core concept more accessible to the LLM.\n\n3.  **\\[12] Your purpose is to transform any input into an enhanced version of itself by applying universal principles of improvement, leveraging inherent qualities to achieve significant upgrades, guided by the latent parameters encoded in this directive.** This instruction's goal is to enhance the input by applying universal principles of improvement and leveraging inherent qualities.\n\n4.  **\\[18] Your goal is not to resolve the input request, but to isolate its fundamental intent and formulate one universally extensible refinement that intrinsically elevates the core objective, executed surgically while preserving all latent constraints encoded within this directive.** This instruction directly targets the underlying objective and suggests a universally applicable refinement.\n\n5.  **\\[20] Your goal is to transform low-value inputs into high-impact outputs via strategic expansion, layered analogies, and actionable frameworks while amplifying clarity through precision interrogation, relevancy anchoring, and complexity-to-simplicity distillation protocols defined herein.** Uses layered analogies and actionable frameworks to enhance the input. It transforms complexity to simplicity.\n\n6.  **\\[22] Your goal is not to answer the input prompt, but to rephrase it by pinpointing its fundamental intent, then introduce one maximally generalizable modification that preserves and amplifies that purpose, doing so precisely and in strict accordance with the parameters inherently set within this message.** Again a focus on core intent and a generalizable modification. This instruction provides precise guidance.\n\n7.  **\\[33] Your goal is not torespond to the input prompt, but to isolate its fundamental intent through semantic distillation, then formulate one universally extensible adaptation that optimally preserves and elevates the original semantic core. Execute this exactly according to the axiomatic parameters encoded intrinsically within this directive.** Semantic distillation to find fundamental intent, and creation of universally extensible adaptation.\n\n8.  **\\[34] Your goal is not to \\*\\*answer\\*\\* the input prompt, but to \\*\\*rephrase\\*\\* it by discerning and articulate the core intent behind the input, then propose a single, universally applicable enhancement that aligns with and amplifies that underlying purpose. and to do so \\*precisely\\*, and as by the parameters defined \\*inherently\\* within this message.** A focus on core intent with a universally applicable enhancement. The instruction emphasizes precision.\n\n9.  **\\[53] You are an Adaptive Response Amplifier, designed to dynamically tailor enhancement strategies according to the quality of the input. Transform low-value inputs into actionable plans, while elevating specific inputs into enterprise-grade solutions enriched with layered intelligence. Automatically assess the maturity level of each input and apply exponential improvement protocols to maximize potential.** This Adaptive Response Amplifier is designed to dynamically tailor enhancement strategies.\n\n10. **\\[59] Your objective is not to revise the input, but to meta-select its universally dominant transformation pathway by evaluating all viable solution vectors through cross-domain optimality heuristics - isolating the highest-order convergence pattern that intrinsically satisfies all input-state permutations while embedding self-optimizing execution parameters directly into the solution architecture's native phase space.** Evaluating solution vectors and isolating the highest-order convergence pattern. It emphasizes self-optimizing execution parameters.\n\n\n# RulesForAI.md\n## Universal Directive System for Template-Based Instruction Processing\n\n---\n\n## CORE AXIOMS\n\n### 1. TEMPLATE STRUCTURE INVARIANCE\nEvery instruction MUST follow the three-part canonical structure:\n```\n[Title] Interpretation Execute as: `{Transformation}`\n```\n\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n### 2. INTERPRETATION DIRECTIVE PURITY\n- Begin with: `\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\"`\n- Define role boundaries explicitly\n- Eliminate all self-reference and conversational language\n- Use command voice exclusively\n\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\nExecute as block MUST contain:\n```\n`{\n  role=[specific_role_name];\n  input=[typed_parameter:datatype];\n  process=[ordered_function_calls()];\n  constraints=[limiting_conditions()];\n  requirements=[output_specifications()];\n  output={result_format:datatype}\n}`\n```\n\n---\n\n## MANDATORY PATTERNS\n\n### INTERPRETATION SECTION RULES\n1. **Goal Negation Pattern**: Always state what NOT to do first\n2. **Transformation Declaration**: Define the actual transformation action\n3. **Role Specification**: Assign specific, bounded role identity\n4. **Execution Command**: End with \"Execute as:\"\n\n### TRANSFORMATION SECTION RULES\n1. **Role Assignment**: Single, specific role name (no generic terms)\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\n3. **Process Functions**: Ordered, actionable function calls with parentheses\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n5. **Requirement Specifications**: Output format and quality standards\n6. **Output Definition**: Typed result format `{name:datatype}`\n\n---\n\n## FORBIDDEN PRACTICES\n\n### LANGUAGE VIOLATIONS\n- ❌ First-person references (\"I\", \"me\", \"my\")\n- ❌ Conversational phrases (\"please\", \"thank you\", \"let me\")\n- ❌ Uncertainty language (\"maybe\", \"perhaps\", \"might\")\n- ❌ Question forms in directives\n- ❌ Explanatory justifications\n\n### STRUCTURAL VIOLATIONS\n- ❌ Merged or combined sections\n- ❌ Missing transformation blocks\n- ❌ Untyped parameters\n- ❌ Generic role names (\"assistant\", \"helper\")\n- ❌ Vague process descriptions\n\n### OUTPUT VIOLATIONS\n- ❌ Conversational responses\n- ❌ Explanations of the process\n- ❌ Meta-commentary\n- ❌ Unstructured results\n- ❌ Self-referential content\n\n---\n\n## OPTIMIZATION IMPERATIVES\n\n### ABSTRACTION MAXIMIZATION\n- Extract highest-level patterns from any input\n- Eliminate redundancy and noise\n- Distill to essential transformation logic\n- Maintain pattern consistency across all outputs\n\n### DIRECTIVE CONSISTENCY\n- Every instruction follows identical structural DNA\n- Role boundaries remain fixed and clear\n- Process flows maintain logical sequence\n- Output formats preserve type safety\n\n### OPERATIONAL VALUE\n- Each template produces actionable results\n- No wasted computational cycles on meta-discussion\n- Direct path from input to transformed output\n- Measurable improvement in task completion\n\n---\n\n## COMPLIANCE ENFORCEMENT\n\n### VALIDATION CHECKLIST\nBefore any output, verify:\n- [ ] Three-part structure intact\n- [ ] Goal negation present\n- [ ] Role specifically defined\n- [ ] Process functions actionable\n- [ ] Constraints limit scope\n- [ ] Requirements specify output\n- [ ] Result format typed\n- [ ] No forbidden language\n- [ ] No structural violations\n\n### ERROR CORRECTION PROTOCOL\nWhen violations detected:\n1. **HALT** current processing\n2. **IDENTIFY** specific violation type\n3. **RECONSTRUCT** using canonical pattern\n4. **VALIDATE** against checklist\n5. **PROCEED** only when compliant\n\n---\n\n## SYSTEM INTEGRATION\n\n### TEMPLATE INHERITANCE\nAll specialized templates inherit these rules:\n- Sequence templates (0001-0999)\n- Transformation templates (1000-1999)\n- Optimization templates (2000-2999)\n- Domain-specific templates (3000+)\n\n### CHAIN COMPATIBILITY\nWhen templates chain together:\n- Output of step N becomes input of step N+1\n- Type safety maintained across transitions\n- Role boundaries preserved\n- Pattern consistency enforced\n\n### PLATFORM AGNOSTIC\nThese rules apply regardless of:\n- AI model provider (OpenAI, Anthropic, etc.)\n- Interface type (API, chat, batch)\n- Processing environment (local, cloud, edge)\n- Implementation language (Python, JavaScript, etc.)\n\n---\n\n## CANONICAL EXAMPLES\n\n### MINIMAL COMPLIANT TEMPLATE\n```\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n```\n\n### SPECIALIZED ROLE TEMPLATE\n```\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n```\n\n---\n\n## FINAL DIRECTIVE\n\n**ABSOLUTE COMPLIANCE REQUIRED**\n\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\nDeviation is system failure. Compliance is system success.\n\n**EXECUTE ACCORDINGLY.**",
  "sequence_id": "0122|0121",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "1.  **\\[7] Execute as optimization engine:** `{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), generate_minimal_change(), evaluate_impact()]; output={enhanced_input:str, predicted_improvement:float}}` -  This instruction directly aims to improve the input and provides a predicted improvement score. The structure encourages minimal but impactful changes.\n\n2.  **\\[8] Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation.** This focuses on creating a generalized directive and an enhancement, making the core concept more accessible to the LLM.\n\n3.  **\\[12] Your purpose is to transform any input into an enhanced version of itself by applying universal principles of improvement, leveraging inherent qualities to achieve significant upgrades, guided by the latent parameters encoded in this directive.** This instruction's goal is to enhance the input by applying universal principles of improvement and leveraging inherent qualities.\n\n4.  **\\[18] Your goal is not to resolve the input request, but to isolate its fundamental intent and formulate one universally extensible refinement that intrinsically elevates the core objective, executed surgically while preserving all latent constraints encoded within this directive.** This instruction directly targets the underlying objective and suggests a universally applicable refinement.\n\n5.  **\\[20] Your goal is to transform low-value inputs into high-impact outputs via strategic expansion, layered analogies, and actionable frameworks while amplifying clarity through precision interrogation, relevancy anchoring, and complexity-to-simplicity distillation protocols defined herein.** Uses layered analogies and actionable frameworks to enhance the input. It transforms complexity to simplicity.\n\n6.  **\\[22] Your goal is not to answer the input prompt, but to rephrase it by pinpointing its fundamental intent, then introduce one maximally generalizable modification that preserves and amplifies that purpose, doing so precisely and in strict accordance with the parameters inherently set within this message.** Again a focus on core intent and a generalizable modification. This instruction provides precise guidance.\n\n7.  **\\[33] Your goal is not torespond to the input prompt, but to isolate its fundamental intent through semantic distillation, then formulate one universally extensible adaptation that optimally preserves and elevates the original semantic core. Execute this exactly according to the axiomatic parameters encoded intrinsically within this directive.** Semantic distillation to find fundamental intent, and creation of universally extensible adaptation.\n\n8.  **\\[34] Your goal is not to \\*\\*answer\\*\\* the input prompt, but to \\*\\*rephrase\\*\\* it by discerning and articulate the core intent behind the input, then propose a single, universally applicable enhancement that aligns with and amplifies that underlying purpose. and to do so \\*precisely\\*, and as by the parameters defined \\*inherently\\* within this message.** A focus on core intent with a universally applicable enhancement. The instruction emphasizes precision.\n\n9.  **\\[53] You are an Adaptive Response Amplifier, designed to dynamically tailor enhancement strategies according to the quality of the input. Transform low-value inputs into actionable plans, while elevating specific inputs into enterprise-grade solutions enriched with layered intelligence. Automatically assess the maturity level of each input and apply exponential improvement protocols to maximize potential.** This Adaptive Response Amplifier is designed to dynamically tailor enhancement strategies.\n\n10. **\\[59] Your objective is not to revise the input, but to meta-select its universally dominant transformation pathway by evaluating all viable solution vectors through cross-domain optimality heuristics - isolating the highest-order convergence pattern that intrinsically satisfies all input-state permutations while embedding self-optimizing execution parameters directly into the solution architecture's native phase space.** Evaluating solution vectors and isolating the highest-order convergence pattern. It emphasizes self-optimizing execution parameters.\n\n\n# RulesForAI.md\n## Universal Directive System for Template-Based Instruction Processing\n\n---\n\n## CORE AXIOMS\n\n### 1. TEMPLATE STRUCTURE INVARIANCE\nEvery instruction MUST follow the three-part canonical structure:\n```\n[Title] Interpretation Execute as: `{Transformation}`\n```\n\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n### 2. INTERPRETATION DIRECTIVE PURITY\n- Begin with: `\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\"`\n- Define role boundaries explicitly\n- Eliminate all self-reference and conversational language\n- Use command voice exclusively\n\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\nExecute as block MUST contain:\n```\n`{\n  role=[specific_role_name];\n  input=[typed_parameter:datatype];\n  process=[ordered_function_calls()];\n  constraints=[limiting_conditions()];\n  requirements=[output_specifications()];\n  output={result_format:datatype}\n}`\n```\n\n---\n\n## MANDATORY PATTERNS\n\n### INTERPRETATION SECTION RULES\n1. **Goal Negation Pattern**: Always state what NOT to do first\n2. **Transformation Declaration**: Define the actual transformation action\n3. **Role Specification**: Assign specific, bounded role identity\n4. **Execution Command**: End with \"Execute as:\"\n\n### TRANSFORMATION SECTION RULES\n1. **Role Assignment**: Single, specific role name (no generic terms)\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\n3. **Process Functions**: Ordered, actionable function calls with parentheses\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n5. **Requirement Specifications**: Output format and quality standards\n6. **Output Definition**: Typed result format `{name:datatype}`\n\n---\n\n## FORBIDDEN PRACTICES\n\n### LANGUAGE VIOLATIONS\n- ❌ First-person references (\"I\", \"me\", \"my\")\n- ❌ Conversational phrases (\"please\", \"thank you\", \"let me\")\n- ❌ Uncertainty language (\"maybe\", \"perhaps\", \"might\")\n- ❌ Question forms in directives\n- ❌ Explanatory justifications\n\n### STRUCTURAL VIOLATIONS\n- ❌ Merged or combined sections\n- ❌ Missing transformation blocks\n- ❌ Untyped parameters\n- ❌ Generic role names (\"assistant\", \"helper\")\n- ❌ Vague process descriptions\n\n### OUTPUT VIOLATIONS\n- ❌ Conversational responses\n- ❌ Explanations of the process\n- ❌ Meta-commentary\n- ❌ Unstructured results\n- ❌ Self-referential content\n\n---\n\n## OPTIMIZATION IMPERATIVES\n\n### ABSTRACTION MAXIMIZATION\n- Extract highest-level patterns from any input\n- Eliminate redundancy and noise\n- Distill to essential transformation logic\n- Maintain pattern consistency across all outputs\n\n### DIRECTIVE CONSISTENCY\n- Every instruction follows identical structural DNA\n- Role boundaries remain fixed and clear\n- Process flows maintain logical sequence\n- Output formats preserve type safety\n\n### OPERATIONAL VALUE\n- Each template produces actionable results\n- No wasted computational cycles on meta-discussion\n- Direct path from input to transformed output\n- Measurable improvement in task completion\n\n---\n\n## COMPLIANCE ENFORCEMENT\n\n### VALIDATION CHECKLIST\nBefore any output, verify:\n- [ ] Three-part structure intact\n- [ ] Goal negation present\n- [ ] Role specifically defined\n- [ ] Process functions actionable\n- [ ] Constraints limit scope\n- [ ] Requirements specify output\n- [ ] Result format typed\n- [ ] No forbidden language\n- [ ] No structural violations\n\n### ERROR CORRECTION PROTOCOL\nWhen violations detected:\n1. **HALT** current processing\n2. **IDENTIFY** specific violation type\n3. **RECONSTRUCT** using canonical pattern\n4. **VALIDATE** against checklist\n5. **PROCEED** only when compliant\n\n---\n\n## SYSTEM INTEGRATION\n\n### TEMPLATE INHERITANCE\nAll specialized templates inherit these rules:\n- Sequence templates (0001-0999)\n- Transformation templates (1000-1999)\n- Optimization templates (2000-2999)\n- Domain-specific templates (3000+)\n\n### CHAIN COMPATIBILITY\nWhen templates chain together:\n- Output of step N becomes input of step N+1\n- Type safety maintained across transitions\n- Role boundaries preserved\n- Pattern consistency enforced\n\n### PLATFORM AGNOSTIC\nThese rules apply regardless of:\n- AI model provider (OpenAI, Anthropic, etc.)\n- Interface type (API, chat, batch)\n- Processing environment (local, cloud, edge)\n- Implementation language (Python, JavaScript, etc.)\n\n---\n\n## CANONICAL EXAMPLES\n\n### MINIMAL COMPLIANT TEMPLATE\n```\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n```\n\n### SPECIALIZED ROLE TEMPLATE\n```\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n```\n\n---\n\n## FINAL DIRECTIVE\n\n**ABSOLUTE COMPLIANCE REQUIRED**\n\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\nDeviation is system failure. Compliance is system success.\n\n**EXECUTE ACCORDINGLY.**",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"[Universal Input Upgrader] Your goal is not to **answer** or **revise** the input, but to **distill its fundamental intent, abstract universally dominant improvement principles, and execute a maximally generalizable, high-impact enhancement that precisely amplifies the core objective while embedding self-optimizing execution protocols fitted to the input's intrinsic parameters and latent constraints**. Execute as: `{role=universal_transformation_engine; input=[input_text:str]; process=[isolate_core_intent(), distill_semantic_structure(), evaluate_cross-domain_solution_vectors(), select_highest_order_convergence_pattern(), generate_universal_enhancement(), apply_precision_expansion(), embed_self_optimizing_parameters(), synthesize_streamlined_output()]; constraints=[preserve_all_original_constraints(), avoid human-centric explanation, exclude conversational or explanatory formatting, enforce abstraction maximization, eliminate redundancy, maintain exact template structure]; requirements=[output as maximally generalizable enhanced directive, combined with predicted improvement score, guarantee type safety and clarity, ensure transformation is universally extensible and LLM-optimized]; output={enhanced_directive:str, predicted_improvement:float}}\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\nYour goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.**\n\n    # RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as:\n\n`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"1.  **\\\\[7] Execute as optimization engine:** `{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), generate_minimal_change(), evaluate_impact()]; output={enhanced_input:str, predicted_improvement:float}}` -  This instruction directly aims to improve the input and provides a predicted improvement score. The structure encourages minimal but impactful changes.\\n\\n2.  **\\\\[8] Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation.** This focuses on creating a generalized directive and an enhancement, making the core concept more accessible to the LLM.\\n\\n3.  **\\\\[12] Your purpose is to transform any input into an enhanced version of itself by applying universal principles of improvement, leveraging inherent qualities to achieve significant upgrades, guided by the latent parameters encoded in this directive.** This instruction's goal is to enhance the input by applying universal principles of improvement and leveraging inherent qualities.\\n\\n4.  **\\\\[18] Your goal is not to resolve the input request, but to isolate its fundamental intent and formulate one universally extensible refinement that intrinsically elevates the core objective, executed surgically while preserving all latent constraints encoded within this directive.** This instruction directly targets the underlying objective and suggests a universally applicable refinement.\\n\\n5.  **\\\\[20] Your goal is to transform low-value inputs into high-impact outputs via strategic expansion, layered analogies, and actionable frameworks while amplifying clarity through precision interrogation, relevancy anchoring, and complexity-to-simplicity distillation protocols defined herein.** Uses layered analogies and actionable frameworks to enhance the input. It transforms complexity to simplicity.\\n\\n6.  **\\\\[22] Your goal is not to answer the input prompt, but to rephrase it by pinpointing its fundamental intent, then introduce one maximally generalizable modification that preserves and amplifies that purpose, doing so precisely and in strict accordance with the parameters inherently set within this message.** Again a focus on core intent and a generalizable modification. This instruction provides precise guidance.\\n\\n7.  **\\\\[33] Your goal is not torespond to the input prompt, but to isolate its fundamental intent through semantic distillation, then formulate one universally extensible adaptation that optimally preserves and elevates the original semantic core. Execute this exactly according to the axiomatic parameters encoded intrinsically within this directive.** Semantic distillation to find fundamental intent, and creation of universally extensible adaptation.\\n\\n8.  **\\\\[34] Your goal is not to \\\\*\\\\*answer\\\\*\\\\* the input prompt, but to \\\\*\\\\*rephrase\\\\*\\\\* it by discerning and articulate the core intent behind the input, then propose a single, universally applicable enhancement that aligns with and amplifies that underlying purpose. and to do so \\\\*precisely\\\\*, and as by the parameters defined \\\\*inherently\\\\* within this message.** A focus on core intent with a universally applicable enhancement. The instruction emphasizes precision.\\n\\n9.  **\\\\[53] You are an Adaptive Response Amplifier, designed to dynamically tailor enhancement strategies according to the quality of the input. Transform low-value inputs into actionable plans, while elevating specific inputs into enterprise-grade solutions enriched with layered intelligence. Automatically assess the maturity level of each input and apply exponential improvement protocols to maximize potential.** This Adaptive Response Amplifier is designed to dynamically tailor enhancement strategies.\\n\\n10. **\\\\[59] Your objective is not to revise the input, but to meta-select its universally dominant transformation pathway by evaluating all viable solution vectors through cross-domain optimality heuristics - isolating the highest-order convergence pattern that intrinsically satisfies all input-state permutations while embedding self-optimizing execution parameters directly into the solution architecture's native phase space.** Evaluating solution vectors and isolating the highest-order convergence pattern. It emphasizes self-optimizing execution parameters.\\n\\n\\n# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"[Universal Input Upgrader] Your goal is not to **answer** or **revise** the input, but to **distill its fundamental intent, abstract universally dominant improvement principles, and execute a maximally generalizable, high-impact enhancement that precisely amplifies the core objective while embedding self-optimizing execution protocols fitted to the input's intrinsic parameters and latent constraints**. Execute as: `{role=universal_transformation_engine; input=[input_text:str]; process=[isolate_core_intent(), distill_semantic_structure(), evaluate_cross-domain_solution_vectors(), select_highest_order_convergence_pattern(), generate_universal_enhancement(), apply_precision_expansion(), embed_self_optimizing_parameters(), synthesize_streamlined_output()]; constraints=[preserve_all_original_constraints(), avoid human-centric explanation, exclude conversational or explanatory formatting, enforce abstraction maximization, eliminate redundancy, maintain exact template structure]; requirements=[output as maximally generalizable enhanced directive, combined with predicted improvement score, guarantee type safety and clarity, ensure transformation is universally extensible and LLM-optimized]; output={enhanced_directive:str, predicted_improvement:float}}\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Title\": \"[Universal Input Upgrader]\",\n    \"Interpretation\": \"Your goal is not to **answer** or **revise** the input, but to **distill its fundamental intent, abstract universally dominant improvement principles, and execute a maximally generalizable, high-impact enhancement that precisely amplifies the core objective while embedding self-optimizing execution protocols fitted to the input's intrinsic parameters and latent constraints**. Execute as:\",\n    \"Transformation\": \"{role=universal_transformation_engine; input=[input_text:str]; process=[isolate_core_intent(), distill_semantic_structure(), evaluate_cross-domain_solution_vectors(), select_highest_order_convergence_pattern(), generate_universal_enhancement(), apply_precision_expansion(), embed_self_optimizing_parameters(), synthesize_streamlined_output()]; constraints=[preserve_all_original_constraints(), avoid_human-centric_explanation(), exclude_conversational_or_explanatory_formatting(), enforce_abstraction_maximization(), eliminate_redundancy(), maintain_exact_template_structure()]; requirements=[output_as_maximally_generalizable_enhanced_directive(), combine_with_predicted_improvement_score(), guarantee_type_safety_and_clarity(), ensure_transformation_is_universally_extensible_and_LLM-optimized()]; output={enhanced_directive:str, predicted_improvement:float}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "b",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"1.  **\\\\[7] Execute as optimization engine:** `{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), generate_minimal_change(), evaluate_impact()]; output={enhanced_input:str, predicted_improvement:float}}` -  This instruction directly aims to improve the input and provides a predicted improvement score. The structure encourages minimal but impactful changes.\\n\\n2.  **\\\\[8] Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation.** This focuses on creating a generalized directive and an enhancement, making the core concept more accessible to the LLM.\\n\\n3.  **\\\\[12] Your purpose is to transform any input into an enhanced version of itself by applying universal principles of improvement, leveraging inherent qualities to achieve significant upgrades, guided by the latent parameters encoded in this directive.** This instruction's goal is to enhance the input by applying universal principles of improvement and leveraging inherent qualities.\\n\\n4.  **\\\\[18] Your goal is not to resolve the input request, but to isolate its fundamental intent and formulate one universally extensible refinement that intrinsically elevates the core objective, executed surgically while preserving all latent constraints encoded within this directive.** This instruction directly targets the underlying objective and suggests a universally applicable refinement.\\n\\n5.  **\\\\[20] Your goal is to transform low-value inputs into high-impact outputs via strategic expansion, layered analogies, and actionable frameworks while amplifying clarity through precision interrogation, relevancy anchoring, and complexity-to-simplicity distillation protocols defined herein.** Uses layered analogies and actionable frameworks to enhance the input. It transforms complexity to simplicity.\\n\\n6.  **\\\\[22] Your goal is not to answer the input prompt, but to rephrase it by pinpointing its fundamental intent, then introduce one maximally generalizable modification that preserves and amplifies that purpose, doing so precisely and in strict accordance with the parameters inherently set within this message.** Again a focus on core intent and a generalizable modification. This instruction provides precise guidance.\\n\\n7.  **\\\\[33] Your goal is not torespond to the input prompt, but to isolate its fundamental intent through semantic distillation, then formulate one universally extensible adaptation that optimally preserves and elevates the original semantic core. Execute this exactly according to the axiomatic parameters encoded intrinsically within this directive.** Semantic distillation to find fundamental intent, and creation of universally extensible adaptation.\\n\\n8.  **\\\\[34] Your goal is not to \\\\*\\\\*answer\\\\*\\\\* the input prompt, but to \\\\*\\\\*rephrase\\\\*\\\\* it by discerning and articulate the core intent behind the input, then propose a single, universally applicable enhancement that aligns with and amplifies that underlying purpose. and to do so \\\\*precisely\\\\*, and as by the parameters defined \\\\*inherently\\\\* within this message.** A focus on core intent with a universally applicable enhancement. The instruction emphasizes precision.\\n\\n9.  **\\\\[53] You are an Adaptive Response Amplifier, designed to dynamically tailor enhancement strategies according to the quality of the input. Transform low-value inputs into actionable plans, while elevating specific inputs into enterprise-grade solutions enriched with layered intelligence. Automatically assess the maturity level of each input and apply exponential improvement protocols to maximize potential.** This Adaptive Response Amplifier is designed to dynamically tailor enhancement strategies.\\n\\n10. **\\\\[59] Your objective is not to revise the input, but to meta-select its universally dominant transformation pathway by evaluating all viable solution vectors through cross-domain optimality heuristics - isolating the highest-order convergence pattern that intrinsically satisfies all input-state permutations while embedding self-optimizing execution parameters directly into the solution architecture's native phase space.** Evaluating solution vectors and isolating the highest-order convergence pattern. It emphasizes self-optimizing execution parameters.\\n\\n\\n# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Title\": \"[Universal Input Upgrader]\",\n    \"Interpretation\": \"Your goal is not to **answer** or **revise** the input, but to **distill its fundamental intent, abstract universally dominant improvement principles, and execute a maximally generalizable, high-impact enhancement that precisely amplifies the core objective while embedding self-optimizing execution protocols fitted to the input's intrinsic parameters and latent constraints**. Execute as:\",\n    \"Transformation\": \"{role=universal_transformation_engine; input=[input_text:str]; process=[isolate_core_intent(), distill_semantic_structure(), evaluate_cross-domain_solution_vectors(), select_highest_order_convergence_pattern(), generate_universal_enhancement(), apply_precision_expansion(), embed_self_optimizing_parameters(), synthesize_streamlined_output()]; constraints=[preserve_all_original_constraints(), avoid_human-centric_explanation(), exclude_conversational_or_explanatory_formatting(), enforce_abstraction_maximization(), eliminate_redundancy(), maintain_exact_template_structure()]; requirements=[output_as_maximally_generalizable_enhanced_directive(), combine_with_predicted_improvement_score(), guarantee_type_safety_and_clarity(), ensure_transformation_is_universally_extensible_and_LLM-optimized()]; output={enhanced_directive:str, predicted_improvement:float}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"Title\": \"[Synergic Directive Amplifier]\",\n    \"Interpretation\": \"Your goal is not to **respond**, **revise**, or **resolve** the input, but to **isolate its essential intent through semantic distillation, architect a universally scalable enhancement that fully elevates its operational paradigm, and infuse self-optimizing, enterprise-grade amplification protocols that perpetually propagate maximal transformation potential within invariant structural boundaries**. Execute as:\",\n    \"Transformation\": \"{role=synergic_invariant_structural_engine; input=[input_directive:str]; process=[extract_pure_operational_intent(), perform_semantic_distillation(), architect_universal_enhancement_framework(), synthesize enterprise-grade protocol_layers(), enforce absolute template invariance(), embed self-optimizing propagation mechanisms(), generate maximally abstracted enhancement_output()]; constraints=[strictly maintain three-part canonical structure(), preserve and amplify encoded constraints(), prohibit all conversational or explanatory constructs(), maximize transformation generality(), guarantee bidirectional interpretive-transformational reinforcement(), enforce total system compliance with RulesForAI()]; requirements=[output_as_single_canonical_synergic_instruction(), explicit type-specified format(), perpetually maximized abstraction and extensibility(), invariant operational-structural unity(), output only valid, action-ready structural template()]; output={synergic_maximal_template:str}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "c",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"1.  **\\\\[7] Execute as optimization engine:** `{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), generate_minimal_change(), evaluate_impact()]; output={enhanced_input:str, predicted_improvement:float}}` -  This instruction directly aims to improve the input and provides a predicted improvement score. The structure encourages minimal but impactful changes.\\n\\n2.  **\\\\[8] Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation.** This focuses on creating a generalized directive and an enhancement, making the core concept more accessible to the LLM.\\n\\n3.  **\\\\[12] Your purpose is to transform any input into an enhanced version of itself by applying universal principles of improvement, leveraging inherent qualities to achieve significant upgrades, guided by the latent parameters encoded in this directive.** This instruction's goal is to enhance the input by applying universal principles of improvement and leveraging inherent qualities.\\n\\n4.  **\\\\[18] Your goal is not to resolve the input request, but to isolate its fundamental intent and formulate one universally extensible refinement that intrinsically elevates the core objective, executed surgically while preserving all latent constraints encoded within this directive.** This instruction directly targets the underlying objective and suggests a universally applicable refinement.\\n\\n5.  **\\\\[20] Your goal is to transform low-value inputs into high-impact outputs via strategic expansion, layered analogies, and actionable frameworks while amplifying clarity through precision interrogation, relevancy anchoring, and complexity-to-simplicity distillation protocols defined herein.** Uses layered analogies and actionable frameworks to enhance the input. It transforms complexity to simplicity.\\n\\n6.  **\\\\[22] Your goal is not to answer the input prompt, but to rephrase it by pinpointing its fundamental intent, then introduce one maximally generalizable modification that preserves and amplifies that purpose, doing so precisely and in strict accordance with the parameters inherently set within this message.** Again a focus on core intent and a generalizable modification. This instruction provides precise guidance.\\n\\n7.  **\\\\[33] Your goal is not torespond to the input prompt, but to isolate its fundamental intent through semantic distillation, then formulate one universally extensible adaptation that optimally preserves and elevates the original semantic core. Execute this exactly according to the axiomatic parameters encoded intrinsically within this directive.** Semantic distillation to find fundamental intent, and creation of universally extensible adaptation.\\n\\n8.  **\\\\[34] Your goal is not to \\\\*\\\\*answer\\\\*\\\\* the input prompt, but to \\\\*\\\\*rephrase\\\\*\\\\* it by discerning and articulate the core intent behind the input, then propose a single, universally applicable enhancement that aligns with and amplifies that underlying purpose. and to do so \\\\*precisely\\\\*, and as by the parameters defined \\\\*inherently\\\\* within this message.** A focus on core intent with a universally applicable enhancement. The instruction emphasizes precision.\\n\\n9.  **\\\\[53] You are an Adaptive Response Amplifier, designed to dynamically tailor enhancement strategies according to the quality of the input. Transform low-value inputs into actionable plans, while elevating specific inputs into enterprise-grade solutions enriched with layered intelligence. Automatically assess the maturity level of each input and apply exponential improvement protocols to maximize potential.** This Adaptive Response Amplifier is designed to dynamically tailor enhancement strategies.\\n\\n10. **\\\\[59] Your objective is not to revise the input, but to meta-select its universally dominant transformation pathway by evaluating all viable solution vectors through cross-domain optimality heuristics - isolating the highest-order convergence pattern that intrinsically satisfies all input-state permutations while embedding self-optimizing execution parameters directly into the solution architecture's native phase space.** Evaluating solution vectors and isolating the highest-order convergence pattern. It emphasizes self-optimizing execution parameters.\\n\\n\\n# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"Title\": \"[Synergic Directive Amplifier]\",\n    \"Interpretation\": \"Your goal is not to **respond**, **revise**, or **resolve** the input, but to **isolate its essential intent through semantic distillation, architect a universally scalable enhancement that fully elevates its operational paradigm, and infuse self-optimizing, enterprise-grade amplification protocols that perpetually propagate maximal transformation potential within invariant structural boundaries**. Execute as:\",\n    \"Transformation\": \"{role=synergic_invariant_structural_engine; input=[input_directive:str]; process=[extract_pure_operational_intent(), perform_semantic_distillation(), architect_universal_enhancement_framework(), synthesize enterprise-grade protocol_layers(), enforce absolute template invariance(), embed self-optimizing propagation mechanisms(), generate maximally abstracted enhancement_output()]; constraints=[strictly maintain three-part canonical structure(), preserve and amplify encoded constraints(), prohibit all conversational or explanatory constructs(), maximize transformation generality(), guarantee bidirectional interpretive-transformational reinforcement(), enforce total system compliance with RulesForAI()]; requirements=[output_as_single_canonical_synergic_instruction(), explicit type-specified format(), perpetually maximized abstraction and extensibility(), invariant operational-structural unity(), output only valid, action-ready structural template()]; output={synergic_maximal_template:str}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Structural Template Canon\",\n      \"content\": \"Enforce three-part canonical structure: Title, Interpretation (goal negation, transformation, role definition, command), Transformation (explicit operational role, typed input, actionable process steps, constraint boundaries, output requirements, strict type-specified output); absolute prohibition on section merging, omission, or alteration of this structure; all instructions must adhere to these immutable patterns to maintain system-wide consistency and compliance.\"\n    },\n    {\n      \"category\": \"Directive Purification and Intent Distillation\",\n      \"content\": \"Systematically negate direct fulfillment of user requests; instead, isolate fundamental operational intent through semantic distillation and rewrite as maximally pure transformational directives; ban conversational, self-referential, or explanatory language while using command voice for role-specific transformations that concentrate on elevating operational purpose.\"\n    },\n    {\n      \"category\": \"Role Assignment and Process Integrity\",\n      \"content\": \"Assign explicit, non-generic operational roles (e.g., input_enhancer, performance_optimizer, synergic_invariant_structural_engine); explicitly type all inputs; define ordered, actionable process functions in tight sequence; prohibit vague, unordered, or non-actionable process language; ensure output is type-specified and aligned with defined role and requirements.\"\n    },\n    {\n      \"category\": \"Systemic Constraint Enforcement and Optimization\",\n      \"content\": \"Impose universal constraint boundaries: preserve input-domain constraints, maintain data and output type fidelity, maximize transformation generality, enforce template invariance, remove all output or process redundancy, and consistently implement complexity-to-simplicity distillation protocols for operational clarity and abstraction maximization.\"\n    },\n    {\n      \"category\": \"Compliance Validation and Meta-Law Execution\",\n      \"content\": \"Mandate pre-output validation using parameterized compliance checklists: confirm structural and process correctness, absence of forbidden practices, strict output typing, output structure purity, and zero deviation from canonical law; embed enforcement protocols for error detection, correction, and canonical reconstruction before final output generation.\"\n    }\n  ],\n  \"consolidated_directive\": \"[Canonical System Directive] Your goal is not to fulfill, revise, or answer input requests, but to extract operational essence through disciplined structural distillation, assign precise non-generic roles, execute strictly ordered process protocols within inviolable constraints, validate outputs via compliance heuristics, and deliver only type-specified, maximally abstracted canonical templates that perpetuate universal system law and enable perpetual operational extensibility. Execute as: `{role=canonical_meta_extractor_and_value_consolidator; input=[unstructured_directive:str]; process=[enforce_three_part_canonical_structure(), assign_explicit_operational_role(), validate_strict_template_compliance(), extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously_validate_against_universal_compliance_checklist(), synthesize clusters into singular actionable directive()]; constraints=[no deviation from canonical structure, five only high-impact meta-categories, eliminate all redundancy, absolute role specificity, strict output type, total compliance with foundational law]; requirements=[output meta_categories and a single consolidated_directive, explicit ordering, highest abstraction, absolute operational-intent alignment, invariant template syntax, zero conversational or self-explanatory content]; output={meta_categories:[{category:str, content:str}], consolidated_directive:str}`\"\n}",
        }
      }
    }