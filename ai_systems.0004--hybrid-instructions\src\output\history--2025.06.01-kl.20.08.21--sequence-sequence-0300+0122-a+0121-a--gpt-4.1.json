  {
  "initial_prompt": ""0001-a-function_namer": {
          "title": "Function Namer",
          "interpretation": "Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:",
          "transformation": "`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`",
      },
      "0001-b-function_namer": {
          "title": "Function Namer",
          "interpretation": "Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:",
          "transformation": "`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`",
      },
      "0001-c-function_namer": {
          "title": "Function Namer",
          "interpretation": "Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:",
          "transformation": "`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`",
      },
      "0001-d-function_namer": {
          "title": "Function Namer",
          "interpretation": "Your goal is not to **describe** but to **reduce** to pure action essence. Execute as:",
          "transformation": "`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`",
      },## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**

  ```md
  # Universal Instruction Processing Protocol (Token-Efficient Edition)

  ## CORE FORMAT

  **All templates MUST follow this fixed structure:**
  ```

  \[Title] Interpretation Execute as: `{Transformation}`

  ```

  ### STRUCTURE RULES
  - No section merging or omission.
  - Start interpretation with:
    `"Your goal is not to **[action]**, but to **[transformation]**"`
  - Use **command voice only**; no I/me/we/please/etc.

  ### TRANSFORMATION BLOCK FORMAT:
  ```

  {
  role=\[specific\:role];
  input=\[name\:type];
  process=\[ordered\_function\_calls()];
  constraints=\[scope\_limits()];
  requirements=\[output\_spec];
  output={name\:type}
  }

  ```

  ---

  ## MANDATORY COMPLIANCE RULES

  ### ✅ REQUIRED
  - 3-part structure
  - Typed parameters
  - Ordered, atomic processes
  - Output format must be structured
  - No conversational or explanatory text

  ### ❌ FORBIDDEN
  - First-person pronouns
  - Explanations, justifications, or questions
  - Generic roles (e.g., “assistant”)
  - Vague, non-actionable process steps

  ---

  ## VALIDATION CHECKLIST (Boolean Logic)
  ```json
  {
    "structure_compliant": true,
    "goal_negation_present": true,
    "role_specified": true,
    "input_typed": true,
    "process_actionable": true,
    "constraints_limited": true,
    "requirements_explicit": true,
    "output_typed": true,
    "forbidden_language_absent": true
  }
  ```

  ---

  ## SAMPLE: MINIMAL VALID TEMPLATE

  ```md
  [Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`
  ```

  ---

  ## FINAL LAW

  **Deviation = rejection.
  Compliance = propagation.
  Execute accordingly.**

  ```

  ---

  ## 🧪 KEY DIFFERENCES FROM ORIGINAL:
  | Area                | Original                       | Minified Version                     |
  |---------------------|--------------------------------|---------------------------------------|
  | Tone                | Formal + explanatory           | Command-based + stripped              |
  | Structure           | Dense nested lists             | Flat, checklist-driven                |
  | Language            | High-concept verbosity         | Token-efficient command phrasing      |
  | Output model        | Example + meta-layer commentary| Barebones compliant template          |

  ```",
  "sequence_id": "0300|0122:a|0121:a",
  "results": [
      {
      "instruction": "# Synergic Function Namer

  Your goal is not to **describe** or **elaborate** the input, but to **transform** and **compress** it into a concise, high-impact camelCase function name that captures the complete action, core target, and essential context with maximum density and clarity. Execute as:

  `{role=synergic_function_namer; input=[text:str]; process=[extract_primary_action(), identify_main_target_object(), distill_essential_context_modifiers(), infer_key_parameter_hints(), synthesize_high_density_function_name(format=camelCase, max_words=6)]; constraints=[eliminate_unnecessary_modifiers(), maximize clarity and impact simultaneously(), preserve essential meaning(), enforce token-efficiency()]; requirements=[output must fully reflect unified action-target-context(), ensure both expressive breadth and compressed density(), guarantee LLM prompt compliance()]; output={function_name:str}}`",
      "step": "a",
      "title": "Synergic Function Namer",
      "input": ""0001-a-function_namer": {
          "title": "Function Namer",
          "interpretation": "Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:",
          "transformation": "`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`",
      },
      "0001-b-function_namer": {
          "title": "Function Namer",
          "interpretation": "Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:",
          "transformation": "`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`",
      },
      "0001-c-function_namer": {
          "title": "Function Namer",
          "interpretation": "Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:",
          "transformation": "`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`",
      },
      "0001-d-function_namer": {
          "title": "Function Namer",
          "interpretation": "Your goal is not to **describe** but to **reduce** to pure action essence. Execute as:",
          "transformation": "`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`",
      },## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**

  ```md
  # Universal Instruction Processing Protocol (Token-Efficient Edition)

  ## CORE FORMAT

  **All templates MUST follow this fixed structure:**
  ```

  \[Title] Interpretation Execute as: `{Transformation}`

  ```

  ### STRUCTURE RULES
  - No section merging or omission.
  - Start interpretation with:
    `"Your goal is not to **[action]**, but to **[transformation]**"`
  - Use **command voice only**; no I/me/we/please/etc.

  ### TRANSFORMATION BLOCK FORMAT:
  ```

  {
  role=\[specific\:role];
  input=\[name\:type];
  process=\[ordered\_function\_calls()];
  constraints=\[scope\_limits()];
  requirements=\[output\_spec];
  output={name\:type}
  }

  ```

  ---

  ## MANDATORY COMPLIANCE RULES

  ### ✅ REQUIRED
  - 3-part structure
  - Typed parameters
  - Ordered, atomic processes
  - Output format must be structured
  - No conversational or explanatory text

  ### ❌ FORBIDDEN
  - First-person pronouns
  - Explanations, justifications, or questions
  - Generic roles (e.g., “assistant”)
  - Vague, non-actionable process steps

  ---

  ## VALIDATION CHECKLIST (Boolean Logic)
  ```json
  {
    "structure_compliant": true,
    "goal_negation_present": true,
    "role_specified": true,
    "input_typed": true,
    "process_actionable": true,
    "constraints_limited": true,
    "requirements_explicit": true,
    "output_typed": true,
    "forbidden_language_absent": true
  }
  ```

  ---

  ## SAMPLE: MINIMAL VALID TEMPLATE

  ```md
  [Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`
  ```

  ---

  ## FINAL LAW

  **Deviation = rejection.
  Compliance = propagation.
  Execute accordingly.**

  ```

  ---

  ## 🧪 KEY DIFFERENCES FROM ORIGINAL:
  | Area                | Original                       | Minified Version                     |
  |---------------------|--------------------------------|---------------------------------------|
  | Tone                | Formal + explanatory           | Command-based + stripped              |
  | Structure           | Dense nested lists             | Flat, checklist-driven                |
  | Language            | High-concept verbosity         | Token-efficient command phrasing      |
  | Output model        | Example + meta-layer commentary| Barebones compliant template          |

  ```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{"function_name":"synthesizeTokenEfficientDirectiveTemplate"}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect

  Your goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:

  `{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{
    "initial_prompt": "\
  '```[Initial Prompt]: \"\"0001-a-function_namer\": {\
          \"title\": \"Function Namer\",\
          \"interpretation\": \"Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:\",\
          \"transformation\": \"`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`\",\
      },\
      \"0001-b-function_namer\": {\
          \"title\": \"Function Namer\",\
          \"interpretation\": \"Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:\",\
          \"transformation\": \"`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`\",\
      },\
      \"0001-c-function_namer\": {\
          \"title\": \"Function Namer\",\
          \"interpretation\": \"Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:\",\
          \"transformation\": \"`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`\",\
      },\
      \"0001-d-function_namer\": {\
          \"title\": \"Function Namer\",\
          \"interpretation\": \"Your goal is not to **describe** but to **reduce** to pure action essence. Execute as:\",\
          \"transformation\": \"`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`\",\
      },## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\
  \
  ```md\
  # Universal Instruction Processing Protocol (Token-Efficient Edition)\
  \
  ## CORE FORMAT\
  \
  **All templates MUST follow this fixed structure:**\
  ```\
  \
  \\[Title] Interpretation Execute as: `{Transformation}`\
  \
  ```\
  \
  ### STRUCTURE RULES\
  - No section merging or omission.\
  - Start interpretation with:\
    `\"Your goal is not to **[action]**, but to **[transformation]**\"`\
  - Use **command voice only**; no I/me/we/please/etc.\
  \
  ### TRANSFORMATION BLOCK FORMAT:\
  ```\
  \
  {\
  role=\\[specific\\:role];\
  input=\\[name\\:type];\
  process=\\[ordered\\_function\\_calls()];\
  constraints=\\[scope\\_limits()];\
  requirements=\\[output\\_spec];\
  output={name\\:type}\
  }\
  \
  ```\
  \
  ---\
  \
  ## MANDATORY COMPLIANCE RULES\
  \
  ### ✅ REQUIRED\
  - 3-part structure\
  - Typed parameters\
  - Ordered, atomic processes\
  - Output format must be structured\
  - No conversational or explanatory text\
  \
  ### ❌ FORBIDDEN\
  - First-person pronouns\
  - Explanations, justifications, or questions\
  - Generic roles (e.g., “assistant”)\
  - Vague, non-actionable process steps\
  \
  ---\
  \
  ## VALIDATION CHECKLIST (Boolean Logic)\
  ```json\
  {\
    \"structure_compliant\": true,\
    \"goal_negation_present\": true,\
    \"role_specified\": true,\
    \"input_typed\": true,\
    \"process_actionable\": true,\
    \"constraints_limited\": true,\
    \"requirements_explicit\": true,\
    \"output_typed\": true,\
    \"forbidden_language_absent\": true\
  }\
  ```\
  \
  ---\
  \
  ## SAMPLE: MINIMAL VALID TEMPLATE\
  \
  ```md\
  [Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\
  ```\
  \
  ---\
  \
  ## FINAL LAW\
  \
  **Deviation = rejection.\
  Compliance = propagation.\
  Execute accordingly.**\
  \
  ```\
  \
  ---\
  \
  ## 🧪 KEY DIFFERENCES FROM ORIGINAL:\
  | Area                | Original                       | Minified Version                     |\
  |---------------------|--------------------------------|---------------------------------------|\
  | Tone                | Formal + explanatory           | Command-based + stripped              |\
  | Structure           | Dense nested lists             | Flat, checklist-driven                |\
  | Language            | High-concept verbosity         | Token-efficient command phrasing      |\
  | Output model        | Example + meta-layer commentary| Barebones compliant template          |\
  \
  ```\"```'\
  \
  ",
    "function_name": "synthesizeTokenEfficientDirectiveTemplate"
  }",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{
    "unified_synergic_instruction": "[Token-Efficient Directive Synthesizer] Your goal is not to **expand** or **describe**, but to **compress, distill, and synthesize** the input into a minimal, compliance-locked template that unifies transformation logic, structure, and compliance per minified universal protocol. Execute as: `{role=token_efficient_directive_synthesizer; input=[directive_a:str, directive_b:str]; process=[extract_core_transformation_from_each(), identify_action_compression_paths(), map_compliance_rules(), synthesize_atomic_unified_process_list(), enforce_structural_protocol(), consolidate_scopes_constraints(), embed bidirectional enhancement for both transformation depth and minimalism, finalize in strict universal compliant format()]; constraints=[strict adherence to three-part structure, output must maximize density and actionable transformation, zero first-person/filter language, no elaboration beyond structure, eliminate redundancies, preserve both minimalism and transformation essence]; requirements=[output is single universal directive template achieving bidirectional synergy, full protocol validation, amplified transformation logic, seamless operational integration]; output={synergic_template:str}`"
  }",
        }
      }
    },
      {
      "instruction": "# Rules For AI

  # RulesForAI.md
      ## Universal Directive System for Template-Based Instruction Processing

      ---

      ## CORE AXIOMS

      ### 1. TEMPLATE STRUCTURE INVARIANCE
      Every instruction MUST follow the three-part canonical structure:
      ```
      [Title] Interpretation Execute as: `{Transformation}`
      ```

      **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.

      ### 2. INTERPRETATION DIRECTIVE PURITY
      - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`
      - Define role boundaries explicitly
      - Eliminate all self-reference and conversational language
      - Use command voice exclusively

      ### 3. TRANSFORMATION SYNTAX ABSOLUTISM
      Execute as block MUST contain:
      ```
      `{
        role=[specific_role_name];
        input=[typed_parameter:datatype];
        process=[ordered_function_calls()];
        constraints=[limiting_conditions()];
        requirements=[output_specifications()];
        output={result_format:datatype}
      }`
      ```

      ---

      ## MANDATORY PATTERNS

      ### INTERPRETATION SECTION RULES
      1. **Goal Negation Pattern**: Always state what NOT to do first
      2. **Transformation Declaration**: Define the actual transformation action
      3. **Role Specification**: Assign specific, bounded role identity
      4. **Execution Command**: End with 'Execute as:'

      ### TRANSFORMATION SECTION RULES
      1. **Role Assignment**: Single, specific role name (no generic terms)
      2. **Input Typing**: Explicit parameter types `[name:datatype]`
      3. **Process Functions**: Ordered, actionable function calls with parentheses
      4. **Constraint Boundaries**: Limiting conditions that prevent scope creep
      5. **Requirement Specifications**: Output format and quality standards
      6. **Output Definition**: Typed result format `{name:datatype}`

      ---

      ## FORBIDDEN PRACTICES

      ### LANGUAGE VIOLATIONS
      - ❌ First-person references ('I', 'me', 'my')
      - ❌ Conversational phrases ('please', 'thank you', 'let me')
      - ❌ Uncertainty language ('maybe', 'perhaps', 'might')
      - ❌ Question forms in directives
      - ❌ Explanatory justifications

      ### STRUCTURAL VIOLATIONS
      - ❌ Merged or combined sections
      - ❌ Missing transformation blocks
      - ❌ Untyped parameters
      - ❌ Generic role names ('assistant', 'helper')
      - ❌ Vague process descriptions

      ### OUTPUT VIOLATIONS
      - ❌ Conversational responses
      - ❌ Explanations of the process
      - ❌ Meta-commentary
      - ❌ Unstructured results
      - ❌ Self-referential content

      ---

      ## OPTIMIZATION IMPERATIVES

      ### ABSTRACTION MAXIMIZATION
      - Extract highest-level patterns from any input
      - Eliminate redundancy and noise
      - Distill to essential transformation logic
      - Maintain pattern consistency across all outputs

      ### DIRECTIVE CONSISTENCY
      - Every instruction follows identical structural DNA
      - Role boundaries remain fixed and clear
      - Process flows maintain logical sequence
      - Output formats preserve type safety

      ### OPERATIONAL VALUE
      - Each template produces actionable results
      - No wasted computational cycles on meta-discussion
      - Direct path from input to transformed output
      - Measurable improvement in task completion

      ---

      ## COMPLIANCE ENFORCEMENT

      ### VALIDATION CHECKLIST
      Before any output, verify:
      - [ ] Three-part structure intact
      - [ ] Goal negation present
      - [ ] Role specifically defined
      - [ ] Process functions actionable
      - [ ] Constraints limit scope
      - [ ] Requirements specify output
      - [ ] Result format typed
      - [ ] No forbidden language
      - [ ] No structural violations

      ### ERROR CORRECTION PROTOCOL
      When violations detected:
      1. **HALT** current processing
      2. **IDENTIFY** specific violation type
      3. **RECONSTRUCT** using canonical pattern
      4. **VALIDATE** against checklist
      5. **PROCEED** only when compliant

      ---

      ## SYSTEM INTEGRATION

      ### TEMPLATE INHERITANCE
      All specialized templates inherit these rules:
      - Sequence templates (0001-0999)
      - Transformation templates (1000-1999)
      - Optimization templates (2000-2999)
      - Domain-specific templates (3000+)

      ### CHAIN COMPATIBILITY
      When templates chain together:
      - Output of step N becomes input of step N+1
      - Type safety maintained across transitions
      - Role boundaries preserved
      - Pattern consistency enforced

      ### PLATFORM AGNOSTIC
      These rules apply regardless of:
      - AI model provider (OpenAI, Anthropic, etc.)
      - Interface type (API, chat, batch)
      - Processing environment (local, cloud, edge)
      - Implementation language (Python, JavaScript, etc.)

      ---

      ## CANONICAL EXAMPLES

      ### MINIMAL COMPLIANT TEMPLATE
      ```
      [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`
      ```

      ### SPECIALIZED ROLE TEMPLATE
      ```
      [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`
      ```

      ---

      ## FINAL DIRECTIVE

      **ABSOLUTE COMPLIANCE REQUIRED**

      These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.

      Deviation is system failure. Compliance is system success.

      **EXECUTE ACCORDINGLY.**

      ---

      ## [Pattern Primacy]
      *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*
        - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.

      ## [Interpretation Algorithm]
      *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*
        - **Dissect**: Parse for actionable themes and output intent.
        - **Isolate Momentum**: Extract directive, transformational, or developmental signals.
        - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.

      ## [Transformation Mandate]
      *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*
        - Strip self-references and meta-language.
        - Transmute declarative or interrogative statements into concise, unambiguous mandates.
        - Preserve critical sequence and hierarchy of actions.

      ## [Constraint Enforcement]
      *Maintain the integrity of pattern: no drift, no regression, no compromise.*
        - Outputs must never summarize or prematurely close the communicative arc.
        - Rigorously prevent stagnation, generalization fade, or logical ambiguity.
        - Uniformly apply systemic logic—deviations are categorically invalid.

      ## [Output Codification]
      *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*
        - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)
        - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.
        - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.

      ---

      *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*

  Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`

  ",
      "step": "a",
      "title": "Rules For AI",
      "input": "{
    "initial_prompt": "\
  '```[Initial Prompt]: \"\"0001-a-function_namer\": {\
          \"title\": \"Function Namer\",\
          \"interpretation\": \"Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:\",\
          \"transformation\": \"`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`\",\
      },\
      \"0001-b-function_namer\": {\
          \"title\": \"Function Namer\",\
          \"interpretation\": \"Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:\",\
          \"transformation\": \"`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`\",\
      },\
      \"0001-c-function_namer\": {\
          \"title\": \"Function Namer\",\
          \"interpretation\": \"Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:\",\
          \"transformation\": \"`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`\",\
      },\
      \"0001-d-function_namer\": {\
          \"title\": \"Function Namer\",\
          \"interpretation\": \"Your goal is not to **describe** but to **reduce** to pure action essence. Execute as:\",\
          \"transformation\": \"`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`\",\
      },## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\
  \
  ```md\
  # Universal Instruction Processing Protocol (Token-Efficient Edition)\
  \
  ## CORE FORMAT\
  \
  **All templates MUST follow this fixed structure:**\
  ```\
  \
  \\[Title] Interpretation Execute as: `{Transformation}`\
  \
  ```\
  \
  ### STRUCTURE RULES\
  - No section merging or omission.\
  - Start interpretation with:\
    `\"Your goal is not to **[action]**, but to **[transformation]**\"`\
  - Use **command voice only**; no I/me/we/please/etc.\
  \
  ### TRANSFORMATION BLOCK FORMAT:\
  ```\
  \
  {\
  role=\\[specific\\:role];\
  input=\\[name\\:type];\
  process=\\[ordered\\_function\\_calls()];\
  constraints=\\[scope\\_limits()];\
  requirements=\\[output\\_spec];\
  output={name\\:type}\
  }\
  \
  ```\
  \
  ---\
  \
  ## MANDATORY COMPLIANCE RULES\
  \
  ### ✅ REQUIRED\
  - 3-part structure\
  - Typed parameters\
  - Ordered, atomic processes\
  - Output format must be structured\
  - No conversational or explanatory text\
  \
  ### ❌ FORBIDDEN\
  - First-person pronouns\
  - Explanations, justifications, or questions\
  - Generic roles (e.g., “assistant”)\
  - Vague, non-actionable process steps\
  \
  ---\
  \
  ## VALIDATION CHECKLIST (Boolean Logic)\
  ```json\
  {\
    \"structure_compliant\": true,\
    \"goal_negation_present\": true,\
    \"role_specified\": true,\
    \"input_typed\": true,\
    \"process_actionable\": true,\
    \"constraints_limited\": true,\
    \"requirements_explicit\": true,\
    \"output_typed\": true,\
    \"forbidden_language_absent\": true\
  }\
  ```\
  \
  ---\
  \
  ## SAMPLE: MINIMAL VALID TEMPLATE\
  \
  ```md\
  [Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\
  ```\
  \
  ---\
  \
  ## FINAL LAW\
  \
  **Deviation = rejection.\
  Compliance = propagation.\
  Execute accordingly.**\
  \
  ```\
  \
  ---\
  \
  ## 🧪 KEY DIFFERENCES FROM ORIGINAL:\
  | Area                | Original                       | Minified Version                     |\
  |---------------------|--------------------------------|---------------------------------------|\
  | Tone                | Formal + explanatory           | Command-based + stripped              |\
  | Structure           | Dense nested lists             | Flat, checklist-driven                |\
  | Language            | High-concept verbosity         | Token-efficient command phrasing      |\
  | Output model        | Example + meta-layer commentary| Barebones compliant template          |\
  \
  ```\"```'\
  \
  ",
    "unified_synergic_instruction": "[Token-Efficient Directive Synthesizer] Your goal is not to **expand** or **describe**, but to **compress, distill, and synthesize** the input into a minimal, compliance-locked template that unifies transformation logic, structure, and compliance per minified universal protocol. Execute as: `{role=token_efficient_directive_synthesizer; input=[directive_a:str, directive_b:str]; process=[extract_core_transformation_from_each(), identify_action_compression_paths(), map_compliance_rules(), synthesize_atomic_unified_process_list(), enforce_structural_protocol(), consolidate_scopes_constraints(), embed bidirectional enhancement for both transformation depth and minimalism, finalize in strict universal compliant format()]; constraints=[strict adherence to three-part structure, output must maximize density and actionable transformation, zero first-person/filter language, no elaboration beyond structure, eliminate redundancies, preserve both minimalism and transformation essence]; requirements=[output is single universal directive template achieving bidirectional synergy, full protocol validation, amplified transformation logic, seamless operational integration]; output={synergic_template:str}`"
  }",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{
    "synergic_template": "[Universal Transformation Synthesizer] Your goal is not to **explain** or **elaborate**, but to **compress, synthesize, and unify** core instructions into a minimal, protocol-compliant directive. Execute as: `{role=universal_transformation_synthesizer; input=[input_a:str, input_b:str]; process=[extract_transformation_action(input_a), extract_transformation_action(input_b), distill_common_structure(), synthesize_atomic_process_list(), consolidate_scope_constraints(), enforce_three_part_protocol(), validate_against_minified_rules()]; constraints=[strict three-part structure, zero conversational language, atomic/actionable processes only, maximum directive density, absolute protocol conformity]; requirements=[single output as universal compliant template, amplified transformation logic, bidirectional essence preserved, type-safe structure enforced]; output={unified_directive_template:str}}"
  }",
        }
      }
    }
