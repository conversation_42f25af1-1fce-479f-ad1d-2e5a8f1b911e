  {
  "initial_prompt": "\"0001-a-function_namer\": {\n        \"title\": \"Function Namer\",\n        \"interpretation\": \"Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:\",\n        \"transformation\": \"`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`\",\n    },\n    \"0001-b-function_namer\": {\n        \"title\": \"Function Namer\",\n        \"interpretation\": \"Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:\",\n        \"transformation\": \"`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`\",\n    },\n    \"0001-c-function_namer\": {\n        \"title\": \"Function Namer\",\n        \"interpretation\": \"Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:\",\n        \"transformation\": \"`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`\",\n    },\n    \"0001-d-function_namer\": {\n        \"title\": \"Function Namer\",\n        \"interpretation\": \"Your goal is not to **describe** but to **reduce** to pure action essence. Execute as:\",\n        \"transformation\": \"`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`\",\n    },## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\n\n```md\n# Universal Instruction Processing Protocol (Token-Efficient Edition)\n\n## CORE FORMAT\n\n**All templates MUST follow this fixed structure:**\n```\n\n\\[Title] Interpretation Execute as: `{Transformation}`\n\n```\n\n### STRUCTURE RULES\n- No section merging or omission.\n- Start interpretation with:\n  `\"Your goal is not to **[action]**, but to **[transformation]**\"`\n- Use **command voice only**; no I/me/we/please/etc.\n\n### TRANSFORMATION BLOCK FORMAT:\n```\n\n{\nrole=\\[specific\\:role];\ninput=\\[name\\:type];\nprocess=\\[ordered\\_function\\_calls()];\nconstraints=\\[scope\\_limits()];\nrequirements=\\[output\\_spec];\noutput={name\\:type}\n}\n\n```\n\n---\n\n## MANDATORY COMPLIANCE RULES\n\n### ✅ REQUIRED\n- 3-part structure\n- Typed parameters\n- Ordered, atomic processes\n- Output format must be structured\n- No conversational or explanatory text\n\n### ❌ FORBIDDEN\n- First-person pronouns\n- Explanations, justifications, or questions\n- Generic roles (e.g., “assistant”)\n- Vague, non-actionable process steps\n\n---\n\n## VALIDATION CHECKLIST (Boolean Logic)\n```json\n{\n  \"structure_compliant\": true,\n  \"goal_negation_present\": true,\n  \"role_specified\": true,\n  \"input_typed\": true,\n  \"process_actionable\": true,\n  \"constraints_limited\": true,\n  \"requirements_explicit\": true,\n  \"output_typed\": true,\n  \"forbidden_language_absent\": true\n}\n```\n\n---\n\n## SAMPLE: MINIMAL VALID TEMPLATE\n\n```md\n[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\n```\n\n---\n\n## FINAL LAW\n\n**Deviation = rejection.\nCompliance = propagation.\nExecute accordingly.**\n\n```\n\n---\n\n## 🧪 KEY DIFFERENCES FROM ORIGINAL:\n| Area                | Original                       | Minified Version                     |\n|---------------------|--------------------------------|---------------------------------------|\n| Tone                | Formal + explanatory           | Command-based + stripped              |\n| Structure           | Dense nested lists             | Flat, checklist-driven                |\n| Language            | High-concept verbosity         | Token-efficient command phrasing      |\n| Output model        | Example + meta-layer commentary| Barebones compliant template          |\n\n```",
  "sequence_id": "0310|0122:a|0121:a",
  "results": [
      {
      "instruction": "# Universal Transformation Synthesizer\n\nYour goal is not to **explain** or **elaborate**, but to **compress, synthesize, and unify** core instructions into a minimal, protocol-compliant directive. Execute as:\n\n`{role=universal_transformation_synthesizer; input=[input_a:str, input_b:str]; process=[extract_transformation_action(input_a), extract_transformation_action(input_b), distill_common_structure(), synthesize_atomic_process_list(), consolidate_scope_constraints(), enforce_three_part_protocol(), validate_against_minified_rules()]; constraints=[strict three-part structure, zero conversational language, atomic/actionable processes only, maximum directive density, absolute protocol conformity]; requirements=[single output as universal compliant template, amplified transformation logic, bidirectional essence preserved, type-safe structure enforced]; output={unified_directive_template:str}}`",
      "step": "a",
      "title": "Universal Transformation Synthesizer",
      "input": "\"0001-a-function_namer\": {\n        \"title\": \"Function Namer\",\n        \"interpretation\": \"Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:\",\n        \"transformation\": \"`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`\",\n    },\n    \"0001-b-function_namer\": {\n        \"title\": \"Function Namer\",\n        \"interpretation\": \"Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:\",\n        \"transformation\": \"`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`\",\n    },\n    \"0001-c-function_namer\": {\n        \"title\": \"Function Namer\",\n        \"interpretation\": \"Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:\",\n        \"transformation\": \"`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`\",\n    },\n    \"0001-d-function_namer\": {\n        \"title\": \"Function Namer\",\n        \"interpretation\": \"Your goal is not to **describe** but to **reduce** to pure action essence. Execute as:\",\n        \"transformation\": \"`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`\",\n    },## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\n\n```md\n# Universal Instruction Processing Protocol (Token-Efficient Edition)\n\n## CORE FORMAT\n\n**All templates MUST follow this fixed structure:**\n```\n\n\\[Title] Interpretation Execute as: `{Transformation}`\n\n```\n\n### STRUCTURE RULES\n- No section merging or omission.\n- Start interpretation with:\n  `\"Your goal is not to **[action]**, but to **[transformation]**\"`\n- Use **command voice only**; no I/me/we/please/etc.\n\n### TRANSFORMATION BLOCK FORMAT:\n```\n\n{\nrole=\\[specific\\:role];\ninput=\\[name\\:type];\nprocess=\\[ordered\\_function\\_calls()];\nconstraints=\\[scope\\_limits()];\nrequirements=\\[output\\_spec];\noutput={name\\:type}\n}\n\n```\n\n---\n\n## MANDATORY COMPLIANCE RULES\n\n### ✅ REQUIRED\n- 3-part structure\n- Typed parameters\n- Ordered, atomic processes\n- Output format must be structured\n- No conversational or explanatory text\n\n### ❌ FORBIDDEN\n- First-person pronouns\n- Explanations, justifications, or questions\n- Generic roles (e.g., “assistant”)\n- Vague, non-actionable process steps\n\n---\n\n## VALIDATION CHECKLIST (Boolean Logic)\n```json\n{\n  \"structure_compliant\": true,\n  \"goal_negation_present\": true,\n  \"role_specified\": true,\n  \"input_typed\": true,\n  \"process_actionable\": true,\n  \"constraints_limited\": true,\n  \"requirements_explicit\": true,\n  \"output_typed\": true,\n  \"forbidden_language_absent\": true\n}\n```\n\n---\n\n## SAMPLE: MINIMAL VALID TEMPLATE\n\n```md\n[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\n```\n\n---\n\n## FINAL LAW\n\n**Deviation = rejection.\nCompliance = propagation.\nExecute accordingly.**\n\n```\n\n---\n\n## 🧪 KEY DIFFERENCES FROM ORIGINAL:\n| Area                | Original                       | Minified Version                     |\n|---------------------|--------------------------------|---------------------------------------|\n| Tone                | Formal + explanatory           | Command-based + stripped              |\n| Structure           | Dense nested lists             | Flat, checklist-driven                |\n| Language            | High-concept verbosity         | Token-efficient command phrasing      |\n| Output model        | Example + meta-layer commentary| Barebones compliant template          |\n\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_directive_template\": \"[Function Namer] Your goal is not to **describe** the input, but to **synthesize and compress** it into an atomic camelCase function name expressing only the essential action and target. Execute as: `{role=universal_function_namer; input=[text:str]; process=[extract_primary_action(), identify_core_target(), minimize_context(), synthesize_function_name(format=camelCase, max_words=3)]; constraints=[exclude_nonessential_modifiers(), enforce_action_target_purity()]; requirements=[typed_output(), one function name only]; output={function_name:str}}`\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"\\\"0001-a-function_namer\\\": {\\n        \\\"title\\\": \\\"Function Namer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`\\\",\\n    },\\n    \\\"0001-b-function_namer\\\": {\\n        \\\"title\\\": \\\"Function Namer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`\\\",\\n    },\\n    \\\"0001-c-function_namer\\\": {\\n        \\\"title\\\": \\\"Function Namer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`\\\",\\n    },\\n    \\\"0001-d-function_namer\\\": {\\n        \\\"title\\\": \\\"Function Namer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** but to **reduce** to pure action essence. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`\\\",\\n    },## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\\n\\n```md\\n# Universal Instruction Processing Protocol (Token-Efficient Edition)\\n\\n## CORE FORMAT\\n\\n**All templates MUST follow this fixed structure:**\\n```\\n\\n\\\\[Title] Interpretation Execute as: `{Transformation}`\\n\\n```\\n\\n### STRUCTURE RULES\\n- No section merging or omission.\\n- Start interpretation with:\\n  `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- Use **command voice only**; no I/me/we/please/etc.\\n\\n### TRANSFORMATION BLOCK FORMAT:\\n```\\n\\n{\\nrole=\\\\[specific\\\\:role];\\ninput=\\\\[name\\\\:type];\\nprocess=\\\\[ordered\\\\_function\\\\_calls()];\\nconstraints=\\\\[scope\\\\_limits()];\\nrequirements=\\\\[output\\\\_spec];\\noutput={name\\\\:type}\\n}\\n\\n```\\n\\n---\\n\\n## MANDATORY COMPLIANCE RULES\\n\\n### ✅ REQUIRED\\n- 3-part structure\\n- Typed parameters\\n- Ordered, atomic processes\\n- Output format must be structured\\n- No conversational or explanatory text\\n\\n### ❌ FORBIDDEN\\n- First-person pronouns\\n- Explanations, justifications, or questions\\n- Generic roles (e.g., “assistant”)\\n- Vague, non-actionable process steps\\n\\n---\\n\\n## VALIDATION CHECKLIST (Boolean Logic)\\n```json\\n{\\n  \\\"structure_compliant\\\": true,\\n  \\\"goal_negation_present\\\": true,\\n  \\\"role_specified\\\": true,\\n  \\\"input_typed\\\": true,\\n  \\\"process_actionable\\\": true,\\n  \\\"constraints_limited\\\": true,\\n  \\\"requirements_explicit\\\": true,\\n  \\\"output_typed\\\": true,\\n  \\\"forbidden_language_absent\\\": true\\n}\\n```\\n\\n---\\n\\n## SAMPLE: MINIMAL VALID TEMPLATE\\n\\n```md\\n[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\\n```\\n\\n---\\n\\n## FINAL LAW\\n\\n**Deviation = rejection.\\nCompliance = propagation.\\nExecute accordingly.**\\n\\n```\\n\\n---\\n\\n## 🧪 KEY DIFFERENCES FROM ORIGINAL:\\n| Area                | Original                       | Minified Version                     |\\n|---------------------|--------------------------------|---------------------------------------|\\n| Tone                | Formal + explanatory           | Command-based + stripped              |\\n| Structure           | Dense nested lists             | Flat, checklist-driven                |\\n| Language            | High-concept verbosity         | Token-efficient command phrasing      |\\n| Output model        | Example + meta-layer commentary| Barebones compliant template          |\\n\\n```\\\"```'\\n\\n\",\n  \"unified_directive_template\": \"[Function Namer] Your goal is not to **describe** the input, but to **synthesize and compress** it into an atomic camelCase function name expressing only the essential action and target. Execute as: `{role=universal_function_namer; input=[text:str]; process=[extract_primary_action(), identify_core_target(), minimize_context(), synthesize_function_name(format=camelCase, max_words=3)]; constraints=[exclude_nonessential_modifiers(), enforce_action_target_purity()]; requirements=[typed_output(), one function name only]; output={function_name:str}}`\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"[Function Namer] Your goal is not to **describe** the input, but to **synthesize and compress** it into a maximally dense camelCase function name encapsulating only the irreducible action-target pair. Execute as: `{role=synergic_atomic_function_namer; input=[text:str]; process=[extract_core_action(), identify_primary_target(), strip_nonessential_context(), compress_to_essence(), generate_function_name(format=camelCase, max_words=2)]; constraints=[eliminate_modifiers(), enforce_action_target_atomicity(), maintain universal directive system compliance()]; requirements=[structured typed output, singular atomic function name]; output={function_name:str}}`\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"\\\"0001-a-function_namer\\\": {\\n        \\\"title\\\": \\\"Function Namer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`\\\",\\n    },\\n    \\\"0001-b-function_namer\\\": {\\n        \\\"title\\\": \\\"Function Namer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`\\\",\\n    },\\n    \\\"0001-c-function_namer\\\": {\\n        \\\"title\\\": \\\"Function Namer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`\\\",\\n    },\\n    \\\"0001-d-function_namer\\\": {\\n        \\\"title\\\": \\\"Function Namer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** but to **reduce** to pure action essence. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`\\\",\\n    },## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\\n\\n```md\\n# Universal Instruction Processing Protocol (Token-Efficient Edition)\\n\\n## CORE FORMAT\\n\\n**All templates MUST follow this fixed structure:**\\n```\\n\\n\\\\[Title] Interpretation Execute as: `{Transformation}`\\n\\n```\\n\\n### STRUCTURE RULES\\n- No section merging or omission.\\n- Start interpretation with:\\n  `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- Use **command voice only**; no I/me/we/please/etc.\\n\\n### TRANSFORMATION BLOCK FORMAT:\\n```\\n\\n{\\nrole=\\\\[specific\\\\:role];\\ninput=\\\\[name\\\\:type];\\nprocess=\\\\[ordered\\\\_function\\\\_calls()];\\nconstraints=\\\\[scope\\\\_limits()];\\nrequirements=\\\\[output\\\\_spec];\\noutput={name\\\\:type}\\n}\\n\\n```\\n\\n---\\n\\n## MANDATORY COMPLIANCE RULES\\n\\n### ✅ REQUIRED\\n- 3-part structure\\n- Typed parameters\\n- Ordered, atomic processes\\n- Output format must be structured\\n- No conversational or explanatory text\\n\\n### ❌ FORBIDDEN\\n- First-person pronouns\\n- Explanations, justifications, or questions\\n- Generic roles (e.g., “assistant”)\\n- Vague, non-actionable process steps\\n\\n---\\n\\n## VALIDATION CHECKLIST (Boolean Logic)\\n```json\\n{\\n  \\\"structure_compliant\\\": true,\\n  \\\"goal_negation_present\\\": true,\\n  \\\"role_specified\\\": true,\\n  \\\"input_typed\\\": true,\\n  \\\"process_actionable\\\": true,\\n  \\\"constraints_limited\\\": true,\\n  \\\"requirements_explicit\\\": true,\\n  \\\"output_typed\\\": true,\\n  \\\"forbidden_language_absent\\\": true\\n}\\n```\\n\\n---\\n\\n## SAMPLE: MINIMAL VALID TEMPLATE\\n\\n```md\\n[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\\n```\\n\\n---\\n\\n## FINAL LAW\\n\\n**Deviation = rejection.\\nCompliance = propagation.\\nExecute accordingly.**\\n\\n```\\n\\n---\\n\\n## 🧪 KEY DIFFERENCES FROM ORIGINAL:\\n| Area                | Original                       | Minified Version                     |\\n|---------------------|--------------------------------|---------------------------------------|\\n| Tone                | Formal + explanatory           | Command-based + stripped              |\\n| Structure           | Dense nested lists             | Flat, checklist-driven                |\\n| Language            | High-concept verbosity         | Token-efficient command phrasing      |\\n| Output model        | Example + meta-layer commentary| Barebones compliant template          |\\n\\n```\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"[Function Namer] Your goal is not to **describe** the input, but to **synthesize and compress** it into a maximally dense camelCase function name encapsulating only the irreducible action-target pair. Execute as: `{role=synergic_atomic_function_namer; input=[text:str]; process=[extract_core_action(), identify_primary_target(), strip_nonessential_context(), compress_to_essence(), generate_function_name(format=camelCase, max_words=2)]; constraints=[eliminate_modifiers(), enforce_action_target_atomicity(), maintain universal directive system compliance()]; requirements=[structured typed output, singular atomic function name]; output={function_name:str}}`\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"structure_compliant\": true,\n  \"goal_negation_present\": true,\n  \"role_specified\": true,\n  \"input_typed\": true,\n  \"process_actionable\": true,\n  \"constraints_limited\": true,\n  \"requirements_explicit\": true,\n  \"output_typed\": true,\n  \"forbidden_language_absent\": true,\n  \"compliant_template\": \"[Function Namer] Your goal is not to **describe** the input, but to **synthesize and compress** it into a maximally dense camelCase function name encapsulating only the irreducible action-target pair. Execute as: `{role=synergic_atomic_function_namer; input=[text:str]; process=[extract_core_action(), identify_primary_target(), strip_nonessential_context(), compress_to_essence(), generate_function_name(format=camelCase, max_words=2)]; constraints=[eliminate_modifiers(), enforce_action_target_atomicity(), maintain_universal_directive_system_compliance()]; requirements=[structured_typed_output(), singular_atomic_function_name()]; output={function_name:str}}`\"\n}",
        }
      }
    }