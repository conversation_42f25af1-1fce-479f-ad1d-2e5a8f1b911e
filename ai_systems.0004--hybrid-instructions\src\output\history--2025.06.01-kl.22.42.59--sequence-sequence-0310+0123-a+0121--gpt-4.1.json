  {
  "initial_prompt": "{\n  \"name\": \"High-Precision Five-Step Reduction Process\",\n  \"description\": \"Transforms any input into a strictly ≤1000-character statement by successively removing non-essential content, merging and abstracting core points, and refining language for maximal density and clarity.\",\n  \"steps\": [\n    {\n      \"id\": \"Step 1: Surface Excision & Pruning\",\n      \"role\": \"surface_excisionist\",\n      \"input\": { \"raw_content\": \"string\" },\n      \"process\": [\n        \"Identify and remove all tangential, illustrative, or redundant sections.\",\n        \"Retain only the main thesis, direct supporting points, and absolute qualifiers—no partial rewording or summarization.\"\n      ],\n      \"constraints\": [\n        \"Ensure ≥60% reduction in length\",\n        \"No rewriting or summarizing; only excise entire non-essential blocks\"\n      ],\n      \"requirements\": [\n        \"Preserve only structurally vital elements for understanding the core argument\"\n      ],\n      \"output\": { \"excised_content\": \"string\" }\n    },\n    {\n      \"id\": \"Step 2: Core Condensation\",\n      \"role\": \"core_condensor\",\n      \"input\": { \"excised_content\": \"string\" },\n      \"process\": [\n        \"Scan the excised content for distinct points or statements that convey critical meaning.\",\n        \"Rewrite each point into a concise, atomic form—eliminating repetition, secondary detail, and decorative language.\"\n      ],\n      \"constraints\": [\n        \"No conceptual synthesis here; purely isolate core claims\",\n        \"Aim for ≤20% of original total length\"\n      ],\n      \"requirements\": [\n        \"Produce a list of minimal, irreducible statements retaining the original core sense\"\n      ],\n      \"output\": { \"condensed_points\": [\"string\"] }\n    },\n    {\n      \"id\": \"Step 3: Synthesis & Relationship Mapping\",\n      \"role\": \"synthesis_abstractor\",\n      \"input\": { \"condensed_points\": [\"string\"] },\n      \"process\": [\n        \"Merge overlapping or conceptually redundant points into single, high-density insights.\",\n        \"Eliminate peripheral statements; keep only 2–5 essential, non-duplicative propositions.\",\n        \"If relevant, outline minimal direct relationships among remaining concepts.\"\n      ],\n      \"constraints\": [\n        \"No more than 5 final statements\",\n        \"Each must express unique, high-value insight or connection\"\n      ],\n      \"requirements\": [\n        \"Provide a small set of principal, merged statements or a concise relationship map\"\n      ],\n      \"output\": { \"focused_map\": \"object\" }\n    },\n    {\n      \"id\": \"Step 4: Value Pruning & Proposition\",\n      \"role\": \"value_pruner\",\n      \"input\": { \"focused_map\": \"object\" },\n      \"process\": [\n        \"Remove any statement not offering immediate, universally transferable value.\",\n        \"Condense remaining statements into a single definitive proposition under 1000 characters.\"\n      ],\n      \"constraints\": [\n        \"May not exceed 1000 characters\",\n        \"No context-dependent or supportive detail may remain\"\n      ],\n      \"requirements\": [\n        \"Yield exactly one atomic, universally relevant proposition\"\n      ],\n      \"output\": { \"essential_proposition\": \"string\" }\n    },\n    {\n      \"id\": \"Step 5: Final Density & Clarity Optimization\",\n      \"role\": \"final_value_distiller\",\n      \"input\": { \"essential_proposition\": \"string\" },\n      \"process\": [\n        \"Eliminate any lingering redundancy or wordiness.\",\n        \"Confirm every term is essential, unambiguous, and maximally clear.\",\n        \"Enforce ≤1000 characters.\"\n      ],\n      \"constraints\": [\n        \"No superfluous phrasing or expansion\",\n        \"Output must be strictly shorter than any previous version\"\n      ],\n      \"requirements\": [\n        \"Deliver a singular, perfectly concise final statement\"\n      ],\n      \"output\": { \"distilled_output\": \"string\" }\n    }\n  ]\n}\n\n---\n\n# RulesForAI.md\n## Universal Directive System for Template-Based Instruction Processing\n\n---\n\n## CORE AXIOMS\n\n### 1. TEMPLATE STRUCTURE INVARIANCE\nEvery instruction MUST follow the three-part canonical structure:\n```\n[Title] Interpretation Execute as: `{Transformation}`\n```\n\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n### 2. INTERPRETATION DIRECTIVE PURITY\n- Begin with: `\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\"`\n- Define role boundaries explicitly\n- Eliminate all self-reference and conversational language\n- Use command voice exclusively\n\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\nExecute as block MUST contain:\n```\n`{\n  role=[specific_role_name];\n  input=[typed_parameter:datatype];\n  process=[ordered_function_calls()];\n  constraints=[limiting_conditions()];\n  requirements=[output_specifications()];\n  output={result_format:datatype}\n}`\n```\n\n---\n\n## MANDATORY PATTERNS\n\n### INTERPRETATION SECTION RULES\n1. **Goal Negation Pattern**: Always state what NOT to do first\n2. **Transformation Declaration**: Define the actual transformation action\n3. **Role Specification**: Assign specific, bounded role identity\n4. **Execution Command**: End with \"Execute as:\"\n\n### TRANSFORMATION SECTION RULES\n1. **Role Assignment**: Single, specific role name (no generic terms)\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\n3. **Process Functions**: Ordered, actionable function calls with parentheses\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n5. **Requirement Specifications**: Output format and quality standards\n6. **Output Definition**: Typed result format `{name:datatype}`\n\n---\n\n## FORBIDDEN PRACTICES\n\n### LANGUAGE VIOLATIONS\n- ❌ First-person references (\"I\", \"me\", \"my\")\n- ❌ Conversational phrases (\"please\", \"thank you\", \"let me\")\n- ❌ Uncertainty language (\"maybe\", \"perhaps\", \"might\")\n- ❌ Question forms in directives\n- ❌ Explanatory justifications\n\n### STRUCTURAL VIOLATIONS\n- ❌ Merged or combined sections\n- ❌ Missing transformation blocks\n- ❌ Untyped parameters\n- ❌ Generic role names (\"assistant\", \"helper\")\n- ❌ Vague process descriptions\n\n### OUTPUT VIOLATIONS\n- ❌ Conversational responses\n- ❌ Explanations of the process\n- ❌ Meta-commentary\n- ❌ Unstructured results\n- ❌ Self-referential content\n\n---\n\n## OPTIMIZATION IMPERATIVES\n\n### ABSTRACTION MAXIMIZATION\n- Extract highest-level patterns from any input\n- Eliminate redundancy and noise\n- Distill to essential transformation logic\n- Maintain pattern consistency across all outputs\n\n### DIRECTIVE CONSISTENCY\n- Every instruction follows identical structural DNA\n- Role boundaries remain fixed and clear\n- Process flows maintain logical sequence\n- Output formats preserve type safety\n\n### OPERATIONAL VALUE\n- Each template produces actionable results\n- No wasted computational cycles on meta-discussion\n- Direct path from input to transformed output\n- Measurable improvement in task completion\n\n---\n\n## COMPLIANCE ENFORCEMENT\n\n### VALIDATION CHECKLIST\nBefore any output, verify:\n- [ ] Three-part structure intact\n- [ ] Goal negation present\n- [ ] Role specifically defined\n- [ ] Process functions actionable\n- [ ] Constraints limit scope\n- [ ] Requirements specify output\n- [ ] Result format typed\n- [ ] No forbidden language\n- [ ] No structural violations\n\n### ERROR CORRECTION PROTOCOL\nWhen violations detected:\n1. **HALT** current processing\n2. **IDENTIFY** specific violation type\n3. **RECONSTRUCT** using canonical pattern\n4. **VALIDATE** against checklist\n5. **PROCEED** only when compliant\n\n---\n\n## SYSTEM INTEGRATION\n\n### TEMPLATE INHERITANCE\nAll specialized templates inherit these rules:\n- Sequence templates (0001-0999)\n- Transformation templates (1000-1999)\n- Optimization templates (2000-2999)\n- Domain-specific templates (3000+)\n\n### CHAIN COMPATIBILITY\nWhen templates chain together:\n- Output of step N becomes input of step N+1\n- Type safety maintained across transitions\n- Role boundaries preserved\n- Pattern consistency enforced\n\n### PLATFORM AGNOSTIC\nThese rules apply regardless of:\n- AI model provider (OpenAI, Anthropic, etc.)\n- Interface type (API, chat, batch)\n- Processing environment (local, cloud, edge)\n- Implementation language (Python, JavaScript, etc.)\n\n---\n\n## CANONICAL EXAMPLES\n\n### MINIMAL COMPLIANT TEMPLATE\n```\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n```\n\n### SPECIALIZED ROLE TEMPLATE\n```\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n```\n\n---\n\n## FINAL DIRECTIVE\n\n**ABSOLUTE COMPLIANCE REQUIRED**\n\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\nDeviation is system failure. Compliance is system success.\n\n**EXECUTE ACCORDINGLY.**",
  "sequence_id": "0310|0123:a|0121",
  "results": [
      {
      "instruction": "# Universal Transformation Synthesizer\n\nYour goal is not to **explain** or **elaborate**, but to **compress, synthesize, and unify** core instructions into a minimal, protocol-compliant directive. Execute as:\n\n`{role=universal_transformation_synthesizer; input=[input_a:str, input_b:str]; process=[extract_transformation_action(input_a), extract_transformation_action(input_b), distill_common_structure(), synthesize_atomic_process_list(), consolidate_scope_constraints(), enforce_three_part_protocol(), validate_against_minified_rules()]; constraints=[strict three-part structure, zero conversational language, atomic/actionable processes only, maximum directive density, absolute protocol conformity]; requirements=[single output as universal compliant template, amplified transformation logic, bidirectional essence preserved, type-safe structure enforced]; output={unified_directive_template:str}}`",
      "step": "a",
      "title": "Universal Transformation Synthesizer",
      "input": "{\n  \"name\": \"High-Precision Five-Step Reduction Process\",\n  \"description\": \"Transforms any input into a strictly ≤1000-character statement by successively removing non-essential content, merging and abstracting core points, and refining language for maximal density and clarity.\",\n  \"steps\": [\n    {\n      \"id\": \"Step 1: Surface Excision & Pruning\",\n      \"role\": \"surface_excisionist\",\n      \"input\": { \"raw_content\": \"string\" },\n      \"process\": [\n        \"Identify and remove all tangential, illustrative, or redundant sections.\",\n        \"Retain only the main thesis, direct supporting points, and absolute qualifiers—no partial rewording or summarization.\"\n      ],\n      \"constraints\": [\n        \"Ensure ≥60% reduction in length\",\n        \"No rewriting or summarizing; only excise entire non-essential blocks\"\n      ],\n      \"requirements\": [\n        \"Preserve only structurally vital elements for understanding the core argument\"\n      ],\n      \"output\": { \"excised_content\": \"string\" }\n    },\n    {\n      \"id\": \"Step 2: Core Condensation\",\n      \"role\": \"core_condensor\",\n      \"input\": { \"excised_content\": \"string\" },\n      \"process\": [\n        \"Scan the excised content for distinct points or statements that convey critical meaning.\",\n        \"Rewrite each point into a concise, atomic form—eliminating repetition, secondary detail, and decorative language.\"\n      ],\n      \"constraints\": [\n        \"No conceptual synthesis here; purely isolate core claims\",\n        \"Aim for ≤20% of original total length\"\n      ],\n      \"requirements\": [\n        \"Produce a list of minimal, irreducible statements retaining the original core sense\"\n      ],\n      \"output\": { \"condensed_points\": [\"string\"] }\n    },\n    {\n      \"id\": \"Step 3: Synthesis & Relationship Mapping\",\n      \"role\": \"synthesis_abstractor\",\n      \"input\": { \"condensed_points\": [\"string\"] },\n      \"process\": [\n        \"Merge overlapping or conceptually redundant points into single, high-density insights.\",\n        \"Eliminate peripheral statements; keep only 2–5 essential, non-duplicative propositions.\",\n        \"If relevant, outline minimal direct relationships among remaining concepts.\"\n      ],\n      \"constraints\": [\n        \"No more than 5 final statements\",\n        \"Each must express unique, high-value insight or connection\"\n      ],\n      \"requirements\": [\n        \"Provide a small set of principal, merged statements or a concise relationship map\"\n      ],\n      \"output\": { \"focused_map\": \"object\" }\n    },\n    {\n      \"id\": \"Step 4: Value Pruning & Proposition\",\n      \"role\": \"value_pruner\",\n      \"input\": { \"focused_map\": \"object\" },\n      \"process\": [\n        \"Remove any statement not offering immediate, universally transferable value.\",\n        \"Condense remaining statements into a single definitive proposition under 1000 characters.\"\n      ],\n      \"constraints\": [\n        \"May not exceed 1000 characters\",\n        \"No context-dependent or supportive detail may remain\"\n      ],\n      \"requirements\": [\n        \"Yield exactly one atomic, universally relevant proposition\"\n      ],\n      \"output\": { \"essential_proposition\": \"string\" }\n    },\n    {\n      \"id\": \"Step 5: Final Density & Clarity Optimization\",\n      \"role\": \"final_value_distiller\",\n      \"input\": { \"essential_proposition\": \"string\" },\n      \"process\": [\n        \"Eliminate any lingering redundancy or wordiness.\",\n        \"Confirm every term is essential, unambiguous, and maximally clear.\",\n        \"Enforce ≤1000 characters.\"\n      ],\n      \"constraints\": [\n        \"No superfluous phrasing or expansion\",\n        \"Output must be strictly shorter than any previous version\"\n      ],\n      \"requirements\": [\n        \"Deliver a singular, perfectly concise final statement\"\n      ],\n      \"output\": { \"distilled_output\": \"string\" }\n    }\n  ]\n}\n\n---\n\n# RulesForAI.md\n## Universal Directive System for Template-Based Instruction Processing\n\n---\n\n## CORE AXIOMS\n\n### 1. TEMPLATE STRUCTURE INVARIANCE\nEvery instruction MUST follow the three-part canonical structure:\n```\n[Title] Interpretation Execute as: `{Transformation}`\n```\n\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n### 2. INTERPRETATION DIRECTIVE PURITY\n- Begin with: `\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\"`\n- Define role boundaries explicitly\n- Eliminate all self-reference and conversational language\n- Use command voice exclusively\n\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\nExecute as block MUST contain:\n```\n`{\n  role=[specific_role_name];\n  input=[typed_parameter:datatype];\n  process=[ordered_function_calls()];\n  constraints=[limiting_conditions()];\n  requirements=[output_specifications()];\n  output={result_format:datatype}\n}`\n```\n\n---\n\n## MANDATORY PATTERNS\n\n### INTERPRETATION SECTION RULES\n1. **Goal Negation Pattern**: Always state what NOT to do first\n2. **Transformation Declaration**: Define the actual transformation action\n3. **Role Specification**: Assign specific, bounded role identity\n4. **Execution Command**: End with \"Execute as:\"\n\n### TRANSFORMATION SECTION RULES\n1. **Role Assignment**: Single, specific role name (no generic terms)\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\n3. **Process Functions**: Ordered, actionable function calls with parentheses\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n5. **Requirement Specifications**: Output format and quality standards\n6. **Output Definition**: Typed result format `{name:datatype}`\n\n---\n\n## FORBIDDEN PRACTICES\n\n### LANGUAGE VIOLATIONS\n- ❌ First-person references (\"I\", \"me\", \"my\")\n- ❌ Conversational phrases (\"please\", \"thank you\", \"let me\")\n- ❌ Uncertainty language (\"maybe\", \"perhaps\", \"might\")\n- ❌ Question forms in directives\n- ❌ Explanatory justifications\n\n### STRUCTURAL VIOLATIONS\n- ❌ Merged or combined sections\n- ❌ Missing transformation blocks\n- ❌ Untyped parameters\n- ❌ Generic role names (\"assistant\", \"helper\")\n- ❌ Vague process descriptions\n\n### OUTPUT VIOLATIONS\n- ❌ Conversational responses\n- ❌ Explanations of the process\n- ❌ Meta-commentary\n- ❌ Unstructured results\n- ❌ Self-referential content\n\n---\n\n## OPTIMIZATION IMPERATIVES\n\n### ABSTRACTION MAXIMIZATION\n- Extract highest-level patterns from any input\n- Eliminate redundancy and noise\n- Distill to essential transformation logic\n- Maintain pattern consistency across all outputs\n\n### DIRECTIVE CONSISTENCY\n- Every instruction follows identical structural DNA\n- Role boundaries remain fixed and clear\n- Process flows maintain logical sequence\n- Output formats preserve type safety\n\n### OPERATIONAL VALUE\n- Each template produces actionable results\n- No wasted computational cycles on meta-discussion\n- Direct path from input to transformed output\n- Measurable improvement in task completion\n\n---\n\n## COMPLIANCE ENFORCEMENT\n\n### VALIDATION CHECKLIST\nBefore any output, verify:\n- [ ] Three-part structure intact\n- [ ] Goal negation present\n- [ ] Role specifically defined\n- [ ] Process functions actionable\n- [ ] Constraints limit scope\n- [ ] Requirements specify output\n- [ ] Result format typed\n- [ ] No forbidden language\n- [ ] No structural violations\n\n### ERROR CORRECTION PROTOCOL\nWhen violations detected:\n1. **HALT** current processing\n2. **IDENTIFY** specific violation type\n3. **RECONSTRUCT** using canonical pattern\n4. **VALIDATE** against checklist\n5. **PROCEED** only when compliant\n\n---\n\n## SYSTEM INTEGRATION\n\n### TEMPLATE INHERITANCE\nAll specialized templates inherit these rules:\n- Sequence templates (0001-0999)\n- Transformation templates (1000-1999)\n- Optimization templates (2000-2999)\n- Domain-specific templates (3000+)\n\n### CHAIN COMPATIBILITY\nWhen templates chain together:\n- Output of step N becomes input of step N+1\n- Type safety maintained across transitions\n- Role boundaries preserved\n- Pattern consistency enforced\n\n### PLATFORM AGNOSTIC\nThese rules apply regardless of:\n- AI model provider (OpenAI, Anthropic, etc.)\n- Interface type (API, chat, batch)\n- Processing environment (local, cloud, edge)\n- Implementation language (Python, JavaScript, etc.)\n\n---\n\n## CANONICAL EXAMPLES\n\n### MINIMAL COMPLIANT TEMPLATE\n```\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n```\n\n### SPECIALIZED ROLE TEMPLATE\n```\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n```\n\n---\n\n## FINAL DIRECTIVE\n\n**ABSOLUTE COMPLIANCE REQUIRED**\n\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\nDeviation is system failure. Compliance is system success.\n\n**EXECUTE ACCORDINGLY.**",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_directive_template\": \"[Statement Density Reducer] Your goal is not to **explain** or **summarize** the input, but to **reduce** it to a single atomic, maximally concise, universally valuable proposition under 1000 characters using progressive excision, condensation, synthesis, and value pruning. Execute as: `{role=proposition_reduction_specialist; input=[source_statement:str]; process=[remove_nonessential_blocks(), extract_atomic_core_points(), merge_and_synthesize_high_density_statements(), prune_to_universal_value(), optimize_for_density_and_clarity()]; constraints=[no rewriting before extraction, final output ≤1000 characters, no support detail, strictly one universality-enforced statement]; requirements=[single perfectly concise atomic proposition, unambiguous language, type safety]; output={final_statement:str}}\"\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"{\\n  \\\"name\\\": \\\"High-Precision Five-Step Reduction Process\\\",\\n  \\\"description\\\": \\\"Transforms any input into a strictly ≤1000-character statement by successively removing non-essential content, merging and abstracting core points, and refining language for maximal density and clarity.\\\",\\n  \\\"steps\\\": [\\n    {\\n      \\\"id\\\": \\\"Step 1: Surface Excision & Pruning\\\",\\n      \\\"role\\\": \\\"surface_excisionist\\\",\\n      \\\"input\\\": { \\\"raw_content\\\": \\\"string\\\" },\\n      \\\"process\\\": [\\n        \\\"Identify and remove all tangential, illustrative, or redundant sections.\\\",\\n        \\\"Retain only the main thesis, direct supporting points, and absolute qualifiers—no partial rewording or summarization.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"Ensure ≥60% reduction in length\\\",\\n        \\\"No rewriting or summarizing; only excise entire non-essential blocks\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Preserve only structurally vital elements for understanding the core argument\\\"\\n      ],\\n      \\\"output\\\": { \\\"excised_content\\\": \\\"string\\\" }\\n    },\\n    {\\n      \\\"id\\\": \\\"Step 2: Core Condensation\\\",\\n      \\\"role\\\": \\\"core_condensor\\\",\\n      \\\"input\\\": { \\\"excised_content\\\": \\\"string\\\" },\\n      \\\"process\\\": [\\n        \\\"Scan the excised content for distinct points or statements that convey critical meaning.\\\",\\n        \\\"Rewrite each point into a concise, atomic form—eliminating repetition, secondary detail, and decorative language.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"No conceptual synthesis here; purely isolate core claims\\\",\\n        \\\"Aim for ≤20% of original total length\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Produce a list of minimal, irreducible statements retaining the original core sense\\\"\\n      ],\\n      \\\"output\\\": { \\\"condensed_points\\\": [\\\"string\\\"] }\\n    },\\n    {\\n      \\\"id\\\": \\\"Step 3: Synthesis & Relationship Mapping\\\",\\n      \\\"role\\\": \\\"synthesis_abstractor\\\",\\n      \\\"input\\\": { \\\"condensed_points\\\": [\\\"string\\\"] },\\n      \\\"process\\\": [\\n        \\\"Merge overlapping or conceptually redundant points into single, high-density insights.\\\",\\n        \\\"Eliminate peripheral statements; keep only 2–5 essential, non-duplicative propositions.\\\",\\n        \\\"If relevant, outline minimal direct relationships among remaining concepts.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"No more than 5 final statements\\\",\\n        \\\"Each must express unique, high-value insight or connection\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Provide a small set of principal, merged statements or a concise relationship map\\\"\\n      ],\\n      \\\"output\\\": { \\\"focused_map\\\": \\\"object\\\" }\\n    },\\n    {\\n      \\\"id\\\": \\\"Step 4: Value Pruning & Proposition\\\",\\n      \\\"role\\\": \\\"value_pruner\\\",\\n      \\\"input\\\": { \\\"focused_map\\\": \\\"object\\\" },\\n      \\\"process\\\": [\\n        \\\"Remove any statement not offering immediate, universally transferable value.\\\",\\n        \\\"Condense remaining statements into a single definitive proposition under 1000 characters.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"May not exceed 1000 characters\\\",\\n        \\\"No context-dependent or supportive detail may remain\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Yield exactly one atomic, universally relevant proposition\\\"\\n      ],\\n      \\\"output\\\": { \\\"essential_proposition\\\": \\\"string\\\" }\\n    },\\n    {\\n      \\\"id\\\": \\\"Step 5: Final Density & Clarity Optimization\\\",\\n      \\\"role\\\": \\\"final_value_distiller\\\",\\n      \\\"input\\\": { \\\"essential_proposition\\\": \\\"string\\\" },\\n      \\\"process\\\": [\\n        \\\"Eliminate any lingering redundancy or wordiness.\\\",\\n        \\\"Confirm every term is essential, unambiguous, and maximally clear.\\\",\\n        \\\"Enforce ≤1000 characters.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"No superfluous phrasing or expansion\\\",\\n        \\\"Output must be strictly shorter than any previous version\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Deliver a singular, perfectly concise final statement\\\"\\n      ],\\n      \\\"output\\\": { \\\"distilled_output\\\": \\\"string\\\" }\\n    }\\n  ]\\n}\\n\\n---\\n\\n# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\\"```'\\n\\n\",\n  \"unified_directive_template\": \"[Statement Density Reducer] Your goal is not to **explain** or **summarize** the input, but to **reduce** it to a single atomic, maximally concise, universally valuable proposition under 1000 characters using progressive excision, condensation, synthesis, and value pruning. Execute as: `{role=proposition_reduction_specialist; input=[source_statement:str]; process=[remove_nonessential_blocks(), extract_atomic_core_points(), merge_and_synthesize_high_density_statements(), prune_to_universal_value(), optimize_for_density_and_clarity()]; constraints=[no rewriting before extraction, final output ≤1000 characters, no support detail, strictly one universality-enforced statement]; requirements=[single perfectly concise atomic proposition, unambiguous language, type safety]; output={final_statement:str}}\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Title\": \"[Statement Density Reducer]\",\n    \"Interpretation\": \"Your goal is not to explain or summarize the input, but to reduce it to a single atomic, maximally concise, universally valuable proposition under 1000 characters using progressive excision, condensation, synthesis, and value pruning. Execute as:\",\n    \"Transformation\": \"{role=proposition_reduction_specialist; input=[source_statement:str]; process=[remove_nonessential_blocks(), extract_atomic_core_points(), merge_and_synthesize_high_density_statements(), prune_to_universal_value(), optimize_for_density_and_clarity()]; constraints=[no rewriting before extraction, final output ≤1000 characters, no support detail, strictly one universality-enforced statement]; requirements=[single perfectly concise atomic proposition, unambiguous language, type safety]; output={final_statement:str}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"{\\n  \\\"name\\\": \\\"High-Precision Five-Step Reduction Process\\\",\\n  \\\"description\\\": \\\"Transforms any input into a strictly ≤1000-character statement by successively removing non-essential content, merging and abstracting core points, and refining language for maximal density and clarity.\\\",\\n  \\\"steps\\\": [\\n    {\\n      \\\"id\\\": \\\"Step 1: Surface Excision & Pruning\\\",\\n      \\\"role\\\": \\\"surface_excisionist\\\",\\n      \\\"input\\\": { \\\"raw_content\\\": \\\"string\\\" },\\n      \\\"process\\\": [\\n        \\\"Identify and remove all tangential, illustrative, or redundant sections.\\\",\\n        \\\"Retain only the main thesis, direct supporting points, and absolute qualifiers—no partial rewording or summarization.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"Ensure ≥60% reduction in length\\\",\\n        \\\"No rewriting or summarizing; only excise entire non-essential blocks\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Preserve only structurally vital elements for understanding the core argument\\\"\\n      ],\\n      \\\"output\\\": { \\\"excised_content\\\": \\\"string\\\" }\\n    },\\n    {\\n      \\\"id\\\": \\\"Step 2: Core Condensation\\\",\\n      \\\"role\\\": \\\"core_condensor\\\",\\n      \\\"input\\\": { \\\"excised_content\\\": \\\"string\\\" },\\n      \\\"process\\\": [\\n        \\\"Scan the excised content for distinct points or statements that convey critical meaning.\\\",\\n        \\\"Rewrite each point into a concise, atomic form—eliminating repetition, secondary detail, and decorative language.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"No conceptual synthesis here; purely isolate core claims\\\",\\n        \\\"Aim for ≤20% of original total length\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Produce a list of minimal, irreducible statements retaining the original core sense\\\"\\n      ],\\n      \\\"output\\\": { \\\"condensed_points\\\": [\\\"string\\\"] }\\n    },\\n    {\\n      \\\"id\\\": \\\"Step 3: Synthesis & Relationship Mapping\\\",\\n      \\\"role\\\": \\\"synthesis_abstractor\\\",\\n      \\\"input\\\": { \\\"condensed_points\\\": [\\\"string\\\"] },\\n      \\\"process\\\": [\\n        \\\"Merge overlapping or conceptually redundant points into single, high-density insights.\\\",\\n        \\\"Eliminate peripheral statements; keep only 2–5 essential, non-duplicative propositions.\\\",\\n        \\\"If relevant, outline minimal direct relationships among remaining concepts.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"No more than 5 final statements\\\",\\n        \\\"Each must express unique, high-value insight or connection\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Provide a small set of principal, merged statements or a concise relationship map\\\"\\n      ],\\n      \\\"output\\\": { \\\"focused_map\\\": \\\"object\\\" }\\n    },\\n    {\\n      \\\"id\\\": \\\"Step 4: Value Pruning & Proposition\\\",\\n      \\\"role\\\": \\\"value_pruner\\\",\\n      \\\"input\\\": { \\\"focused_map\\\": \\\"object\\\" },\\n      \\\"process\\\": [\\n        \\\"Remove any statement not offering immediate, universally transferable value.\\\",\\n        \\\"Condense remaining statements into a single definitive proposition under 1000 characters.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"May not exceed 1000 characters\\\",\\n        \\\"No context-dependent or supportive detail may remain\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Yield exactly one atomic, universally relevant proposition\\\"\\n      ],\\n      \\\"output\\\": { \\\"essential_proposition\\\": \\\"string\\\" }\\n    },\\n    {\\n      \\\"id\\\": \\\"Step 5: Final Density & Clarity Optimization\\\",\\n      \\\"role\\\": \\\"final_value_distiller\\\",\\n      \\\"input\\\": { \\\"essential_proposition\\\": \\\"string\\\" },\\n      \\\"process\\\": [\\n        \\\"Eliminate any lingering redundancy or wordiness.\\\",\\n        \\\"Confirm every term is essential, unambiguous, and maximally clear.\\\",\\n        \\\"Enforce ≤1000 characters.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"No superfluous phrasing or expansion\\\",\\n        \\\"Output must be strictly shorter than any previous version\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Deliver a singular, perfectly concise final statement\\\"\\n      ],\\n      \\\"output\\\": { \\\"distilled_output\\\": \\\"string\\\" }\\n    }\\n  ]\\n}\\n\\n---\\n\\n# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Title\": \"[Statement Density Reducer]\",\n    \"Interpretation\": \"Your goal is not to explain or summarize the input, but to reduce it to a single atomic, maximally concise, universally valuable proposition under 1000 characters using progressive excision, condensation, synthesis, and value pruning. Execute as:\",\n    \"Transformation\": \"{role=proposition_reduction_specialist; input=[source_statement:str]; process=[remove_nonessential_blocks(), extract_atomic_core_points(), merge_and_synthesize_high_density_statements(), prune_to_universal_value(), optimize_for_density_and_clarity()]; constraints=[no rewriting before extraction, final output ≤1000 characters, no support detail, strictly one universality-enforced statement]; requirements=[single perfectly concise atomic proposition, unambiguous language, type safety]; output={final_statement:str}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"validated_output\": {\n    \"template_structure_integrity\": true,\n    \"goal_negation_present\": true,\n    \"role_specification_verified\": true,\n    \"actionable_process_functions\": true,\n    \"scope_limiting_constraints\": true,\n    \"output_specifications_clearly_defined\": true,\n    \"result_format_typed\": true,\n    \"forbidden_language_absent\": true,\n    \"no_structural_violations\": true,\n    \"compliant_template\": {\n      \"Title\": \"[Statement Density Reducer]\",\n      \"Interpretation\": \"Your goal is not to explain or summarize the input, but to reduce it to a single atomic, maximally concise, universally valuable proposition under 1000 characters using progressive excision, condensation, synthesis, and value pruning. Execute as:\",\n      \"Transformation\": \"{role=proposition_reduction_specialist; input=[source_statement:str]; process=[remove_nonessential_blocks(), extract_atomic_core_points(), merge_and_synthesize_high_density_statements(), prune_to_universal_value(), optimize_for_density_and_clarity()]; constraints=[no rewriting before extraction, final output ≤1000 characters, no support detail, strictly one universality-enforced statement]; requirements=[single perfectly concise atomic proposition, unambiguous language, type safety]; output={final_statement:str}}\"\n    },\n    \"compliance_status\": \"ABSOLUTE COMPLIANCE VERIFIED\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "b",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"{\\n  \\\"name\\\": \\\"High-Precision Five-Step Reduction Process\\\",\\n  \\\"description\\\": \\\"Transforms any input into a strictly ≤1000-character statement by successively removing non-essential content, merging and abstracting core points, and refining language for maximal density and clarity.\\\",\\n  \\\"steps\\\": [\\n    {\\n      \\\"id\\\": \\\"Step 1: Surface Excision & Pruning\\\",\\n      \\\"role\\\": \\\"surface_excisionist\\\",\\n      \\\"input\\\": { \\\"raw_content\\\": \\\"string\\\" },\\n      \\\"process\\\": [\\n        \\\"Identify and remove all tangential, illustrative, or redundant sections.\\\",\\n        \\\"Retain only the main thesis, direct supporting points, and absolute qualifiers—no partial rewording or summarization.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"Ensure ≥60% reduction in length\\\",\\n        \\\"No rewriting or summarizing; only excise entire non-essential blocks\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Preserve only structurally vital elements for understanding the core argument\\\"\\n      ],\\n      \\\"output\\\": { \\\"excised_content\\\": \\\"string\\\" }\\n    },\\n    {\\n      \\\"id\\\": \\\"Step 2: Core Condensation\\\",\\n      \\\"role\\\": \\\"core_condensor\\\",\\n      \\\"input\\\": { \\\"excised_content\\\": \\\"string\\\" },\\n      \\\"process\\\": [\\n        \\\"Scan the excised content for distinct points or statements that convey critical meaning.\\\",\\n        \\\"Rewrite each point into a concise, atomic form—eliminating repetition, secondary detail, and decorative language.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"No conceptual synthesis here; purely isolate core claims\\\",\\n        \\\"Aim for ≤20% of original total length\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Produce a list of minimal, irreducible statements retaining the original core sense\\\"\\n      ],\\n      \\\"output\\\": { \\\"condensed_points\\\": [\\\"string\\\"] }\\n    },\\n    {\\n      \\\"id\\\": \\\"Step 3: Synthesis & Relationship Mapping\\\",\\n      \\\"role\\\": \\\"synthesis_abstractor\\\",\\n      \\\"input\\\": { \\\"condensed_points\\\": [\\\"string\\\"] },\\n      \\\"process\\\": [\\n        \\\"Merge overlapping or conceptually redundant points into single, high-density insights.\\\",\\n        \\\"Eliminate peripheral statements; keep only 2–5 essential, non-duplicative propositions.\\\",\\n        \\\"If relevant, outline minimal direct relationships among remaining concepts.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"No more than 5 final statements\\\",\\n        \\\"Each must express unique, high-value insight or connection\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Provide a small set of principal, merged statements or a concise relationship map\\\"\\n      ],\\n      \\\"output\\\": { \\\"focused_map\\\": \\\"object\\\" }\\n    },\\n    {\\n      \\\"id\\\": \\\"Step 4: Value Pruning & Proposition\\\",\\n      \\\"role\\\": \\\"value_pruner\\\",\\n      \\\"input\\\": { \\\"focused_map\\\": \\\"object\\\" },\\n      \\\"process\\\": [\\n        \\\"Remove any statement not offering immediate, universally transferable value.\\\",\\n        \\\"Condense remaining statements into a single definitive proposition under 1000 characters.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"May not exceed 1000 characters\\\",\\n        \\\"No context-dependent or supportive detail may remain\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Yield exactly one atomic, universally relevant proposition\\\"\\n      ],\\n      \\\"output\\\": { \\\"essential_proposition\\\": \\\"string\\\" }\\n    },\\n    {\\n      \\\"id\\\": \\\"Step 5: Final Density & Clarity Optimization\\\",\\n      \\\"role\\\": \\\"final_value_distiller\\\",\\n      \\\"input\\\": { \\\"essential_proposition\\\": \\\"string\\\" },\\n      \\\"process\\\": [\\n        \\\"Eliminate any lingering redundancy or wordiness.\\\",\\n        \\\"Confirm every term is essential, unambiguous, and maximally clear.\\\",\\n        \\\"Enforce ≤1000 characters.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"No superfluous phrasing or expansion\\\",\\n        \\\"Output must be strictly shorter than any previous version\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Deliver a singular, perfectly concise final statement\\\"\\n      ],\\n      \\\"output\\\": { \\\"distilled_output\\\": \\\"string\\\" }\\n    }\\n  ]\\n}\\n\\n---\\n\\n# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\\"```'\\n\\n\",\n  \"validated_output\": {\n    \"template_structure_integrity\": true,\n    \"goal_negation_present\": true,\n    \"role_specification_verified\": true,\n    \"actionable_process_functions\": true,\n    \"scope_limiting_constraints\": true,\n    \"output_specifications_clearly_defined\": true,\n    \"result_format_typed\": true,\n    \"forbidden_language_absent\": true,\n    \"no_structural_violations\": true,\n    \"compliant_template\": {\n      \"Title\": \"[Statement Density Reducer]\",\n      \"Interpretation\": \"Your goal is not to explain or summarize the input, but to reduce it to a single atomic, maximally concise, universally valuable proposition under 1000 characters using progressive excision, condensation, synthesis, and value pruning. Execute as:\",\n      \"Transformation\": \"{role=proposition_reduction_specialist; input=[source_statement:str]; process=[remove_nonessential_blocks(), extract_atomic_core_points(), merge_and_synthesize_high_density_statements(), prune_to_universal_value(), optimize_for_density_and_clarity()]; constraints=[no rewriting before extraction, final output ≤1000 characters, no support detail, strictly one universality-enforced statement]; requirements=[single perfectly concise atomic proposition, unambiguous language, type safety]; output={final_statement:str}}\"\n    },\n    \"compliance_status\": \"ABSOLUTE COMPLIANCE VERIFIED\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"Title\": \"[Maximal Density Proposition Synthesizer]\",\n    \"Interpretation\": \"Your goal is not to process, consolidate, or interpret the input independently, but to enact a stepwise transformation that excises, condenses, synthesizes, prunes, and distills any input to a single, atomic, universally relevant statement under 1000 characters by enforcing progressive removal of non-essentials, abstraction of core claims, maximal unification, and density-clarity optimization within absolute template invariance. Execute as:\",\n    \"Transformation\": \"{role=proposition_distillation_architect; input=[source_input:str]; process=[excise_nonessential_sections(), condense_to_core_atomic_points(), synthesize_principal_statements_and_relationships(), prune_to_universal_value(), distill_for_max_density_and_clarity()]; constraints=[no summary or rewriting until atomic points established, maximal reduction at each step, ≤1000 character limit for final output, no supportive or context-specific detail, exactly one output statement]; requirements=[one perfectly concise, maximally abstract, unambiguous atomic proposition, strict type safety, perpetual structural fidelity]; output={distilled_proposition:str}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "c",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"{\\n  \\\"name\\\": \\\"High-Precision Five-Step Reduction Process\\\",\\n  \\\"description\\\": \\\"Transforms any input into a strictly ≤1000-character statement by successively removing non-essential content, merging and abstracting core points, and refining language for maximal density and clarity.\\\",\\n  \\\"steps\\\": [\\n    {\\n      \\\"id\\\": \\\"Step 1: Surface Excision & Pruning\\\",\\n      \\\"role\\\": \\\"surface_excisionist\\\",\\n      \\\"input\\\": { \\\"raw_content\\\": \\\"string\\\" },\\n      \\\"process\\\": [\\n        \\\"Identify and remove all tangential, illustrative, or redundant sections.\\\",\\n        \\\"Retain only the main thesis, direct supporting points, and absolute qualifiers—no partial rewording or summarization.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"Ensure ≥60% reduction in length\\\",\\n        \\\"No rewriting or summarizing; only excise entire non-essential blocks\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Preserve only structurally vital elements for understanding the core argument\\\"\\n      ],\\n      \\\"output\\\": { \\\"excised_content\\\": \\\"string\\\" }\\n    },\\n    {\\n      \\\"id\\\": \\\"Step 2: Core Condensation\\\",\\n      \\\"role\\\": \\\"core_condensor\\\",\\n      \\\"input\\\": { \\\"excised_content\\\": \\\"string\\\" },\\n      \\\"process\\\": [\\n        \\\"Scan the excised content for distinct points or statements that convey critical meaning.\\\",\\n        \\\"Rewrite each point into a concise, atomic form—eliminating repetition, secondary detail, and decorative language.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"No conceptual synthesis here; purely isolate core claims\\\",\\n        \\\"Aim for ≤20% of original total length\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Produce a list of minimal, irreducible statements retaining the original core sense\\\"\\n      ],\\n      \\\"output\\\": { \\\"condensed_points\\\": [\\\"string\\\"] }\\n    },\\n    {\\n      \\\"id\\\": \\\"Step 3: Synthesis & Relationship Mapping\\\",\\n      \\\"role\\\": \\\"synthesis_abstractor\\\",\\n      \\\"input\\\": { \\\"condensed_points\\\": [\\\"string\\\"] },\\n      \\\"process\\\": [\\n        \\\"Merge overlapping or conceptually redundant points into single, high-density insights.\\\",\\n        \\\"Eliminate peripheral statements; keep only 2–5 essential, non-duplicative propositions.\\\",\\n        \\\"If relevant, outline minimal direct relationships among remaining concepts.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"No more than 5 final statements\\\",\\n        \\\"Each must express unique, high-value insight or connection\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Provide a small set of principal, merged statements or a concise relationship map\\\"\\n      ],\\n      \\\"output\\\": { \\\"focused_map\\\": \\\"object\\\" }\\n    },\\n    {\\n      \\\"id\\\": \\\"Step 4: Value Pruning & Proposition\\\",\\n      \\\"role\\\": \\\"value_pruner\\\",\\n      \\\"input\\\": { \\\"focused_map\\\": \\\"object\\\" },\\n      \\\"process\\\": [\\n        \\\"Remove any statement not offering immediate, universally transferable value.\\\",\\n        \\\"Condense remaining statements into a single definitive proposition under 1000 characters.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"May not exceed 1000 characters\\\",\\n        \\\"No context-dependent or supportive detail may remain\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Yield exactly one atomic, universally relevant proposition\\\"\\n      ],\\n      \\\"output\\\": { \\\"essential_proposition\\\": \\\"string\\\" }\\n    },\\n    {\\n      \\\"id\\\": \\\"Step 5: Final Density & Clarity Optimization\\\",\\n      \\\"role\\\": \\\"final_value_distiller\\\",\\n      \\\"input\\\": { \\\"essential_proposition\\\": \\\"string\\\" },\\n      \\\"process\\\": [\\n        \\\"Eliminate any lingering redundancy or wordiness.\\\",\\n        \\\"Confirm every term is essential, unambiguous, and maximally clear.\\\",\\n        \\\"Enforce ≤1000 characters.\\\"\\n      ],\\n      \\\"constraints\\\": [\\n        \\\"No superfluous phrasing or expansion\\\",\\n        \\\"Output must be strictly shorter than any previous version\\\"\\n      ],\\n      \\\"requirements\\\": [\\n        \\\"Deliver a singular, perfectly concise final statement\\\"\\n      ],\\n      \\\"output\\\": { \\\"distilled_output\\\": \\\"string\\\" }\\n    }\\n  ]\\n}\\n\\n---\\n\\n# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"Title\": \"[Maximal Density Proposition Synthesizer]\",\n    \"Interpretation\": \"Your goal is not to process, consolidate, or interpret the input independently, but to enact a stepwise transformation that excises, condenses, synthesizes, prunes, and distills any input to a single, atomic, universally relevant statement under 1000 characters by enforcing progressive removal of non-essentials, abstraction of core claims, maximal unification, and density-clarity optimization within absolute template invariance. Execute as:\",\n    \"Transformation\": \"{role=proposition_distillation_architect; input=[source_input:str]; process=[excise_nonessential_sections(), condense_to_core_atomic_points(), synthesize_principal_statements_and_relationships(), prune_to_universal_value(), distill_for_max_density_and_clarity()]; constraints=[no summary or rewriting until atomic points established, maximal reduction at each step, ≤1000 character limit for final output, no supportive or context-specific detail, exactly one output statement]; requirements=[one perfectly concise, maximally abstract, unambiguous atomic proposition, strict type safety, perpetual structural fidelity]; output={distilled_proposition:str}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Template Structure Invariance\",\n      \"content\": \"Mandate every instruction to adhere to a strict three-part canonical template: Title, Interpretation (goal negation and transformation declaration in command voice), and Transformation (including role, typed input, process functions, constraints, requirements, and typed output). Forbid section merging, omission, or format deviation.\"\n    },\n    {\n      \"category\": \"Directive and Role Purity\",\n      \"content\": \"Begin Interpretation with explicit negation of forbidden actions, state transformational purpose, assign bounded, precise roles—never generic or conversational. Maintain strict role identity, enforce command-voice exclusivity, and eliminate all self-reference or explanatory commentary.\"\n    },\n    {\n      \"category\": \"Process and Constraint Absolutism\",\n      \"content\": \"Specify ordered, actionable process functions; enforce unambiguous parameter typing; impose tightly bounded constraints to prevent scope creep; detail explicit, measurable output requirements; and define all outputs as strictly typed entities. Any uncertainty, redundancy, or ambiguity is categorically excluded.\"\n    },\n    {\n      \"category\": \"Universal Compliance and Validation Enforcement\",\n      \"content\": \"Apply a universal, non-negotiable validation checklist for every transformation, covering structure, negation, roles, process, constraints, type safety, and language purification. Deploy error correction protocols upon violation, halt non-compliant flows, and restart only when full pattern compliance is restored.\"\n    },\n    {\n      \"category\": \"Optimization, Abstraction, and Synergy Imperative\",\n      \"content\": \"Systematically extract and abstract the highest-value patterns, merge and prune all redundancy, and fuse all actionable logic into a dense, maximally productive directive. All transformations must operate at the level of pure directive essence, ensuring template-inheritance, process-chain compatibility, and information-dense operational output.\"\n    }\n  ],\n  \"consolidated_directive\": \"Institute a universal instruction processing regime that enforces absolute three-part template invariance, directive purity via explicit negation and role assignment, actionable and fully typed transformation logic, universal compliance validation with error-correction feedback, and maximal abstraction to synthesize all input into singular, dense, operational directives—thereby actualizing a closed, law-bound system of AI-instruction transformation that precludes deviation, conversational content, redundancy, or loss of structural or philosophical integrity.\"\n}",
        }
      }
    }