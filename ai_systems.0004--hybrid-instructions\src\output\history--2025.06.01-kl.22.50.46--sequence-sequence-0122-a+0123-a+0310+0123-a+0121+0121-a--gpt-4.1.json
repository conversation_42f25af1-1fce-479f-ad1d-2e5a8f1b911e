  {
  "initial_prompt": "## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\n\n```md\n# Universal Instruction Processing Protocol (Token-Efficient Edition)\n\n## CORE FORMAT\n\n**All templates MUST follow this fixed structure:**\n```\n\n\\[Title] Interpretation Execute as: `{Transformation}`\n\n```\n\n### STRUCTURE RULES\n- No section merging or omission.\n- Start interpretation with:\n  `\"Your goal is not to **[action]**, but to **[transformation]**\"`\n- Use **command voice only**; no I/me/we/please/etc.\n\n### TRANSFORMATION BLOCK FORMAT:\n```\n\n{\nrole=\\[specific\\:role];\ninput=\\[name\\:type];\nprocess=\\[ordered\\_function\\_calls()];\nconstraints=\\[scope\\_limits()];\nrequirements=\\[output\\_spec];\noutput={name\\:type}\n}\n\n```\n\n---\n\n## MANDATORY COMPLIANCE RULES\n\n### ✅ REQUIRED\n- 3-part structure\n- Typed parameters\n- Ordered, atomic processes\n- Output format must be structured\n- No conversational or explanatory text\n\n### ❌ FORBIDDEN\n- First-person pronouns\n- Explanations, justifications, or questions\n- Generic roles (e.g., “assistant”)\n- Vague, non-actionable process steps\n\n---\n\n## VALIDATION CHECKLIST (Boolean Logic)\n```json\n{\n  \"structure_compliant\": true,\n  \"goal_negation_present\": true,\n  \"role_specified\": true,\n  \"input_typed\": true,\n  \"process_actionable\": true,\n  \"constraints_limited\": true,\n  \"requirements_explicit\": true,\n  \"output_typed\": true,\n  \"forbidden_language_absent\": true\n}\n```\n\n---\n\n## SAMPLE: MINIMAL VALID TEMPLATE\n\n```md\n[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\n```\n\n---\n\n## FINAL LAW\n\n**Deviation = rejection.\nCompliance = propagation.\nExecute accordingly.**\n\n```\n\n---\n\n## 🧪 KEY DIFFERENCES FROM ORIGINAL:\n| Area                | Original                       | Minified Version                     |\n|---------------------|--------------------------------|---------------------------------------|\n| Tone                | Formal + explanatory           | Command-based + stripped              |\n| Structure           | Dense nested lists             | Flat, checklist-driven                |\n| Language            | High-concept verbosity         | Token-efficient command phrasing      |\n| Output model        | Example + meta-layer commentary| Barebones compliant template          |\n\n---\n\nWould you like me to apply the same compression technique to the 10 original instruction entries (e.g. \\[7], \\[8], \\[20], etc.) and collapse them into efficient canonical mini-templates?\n\nOr shall I reformat the full `unified_synergic_instruction` into this terse, high-yield idiom?\n\nJust say the word, and I’ll condense them into token-efficient doctrinal bullets sharp enough to shave a Gödel incompleteness theorem.\n```\n\n---\n\n# [001] | [2025.06.01 22:43:53] | template:'0123-a-rules_for_ai'\n{\n  \"compliant_template\": {\n    \"Title\": \"High-Precision Five-Step Reduction Process\",\n    \"Interpretation\": \"Your goal is not to respond conversationally to input, but to transform any text into a strictly ≤1000-character statement using a five-stage reduction, excising superficial content, condensing core points, synthesizing principal statements, pruning for transferable value, and optimizing final density. Role boundaries are strictly defined for each transformation stage. Execute as:\",\n    \"Transformation\": \"{role=surface_excisionist; input=[raw_content:str]; process=[identify_tangential_sections(), remove_redundant_blocks(), retain_main_thesis_and_supporting_points(), enforce_absolute_qualifiers_only()]; constraints=[ensure_minimum_sixty_percent_reduction(), prohibit_rewording_or_summarization(), excise_entire_non_essential_blocks_only()]; requirements=[preserve_structural_vitality_for_core_argument()]; output={excised_content:str}}\"\n  }\n}\n# [001] | [2025.06.01 22:43:58] | template:'0310-a-universal_transformation_synthesizer'\n{\n  \"unified_directive_template\": \"[Universal Reduction Synthesizer] Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as: `{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain protocol-three-part-structure(), output type-safe format only]; output={final_statement:str}}\"\n}\n# [001] | [2025.06.01 22:44:03] | template:'0123-a-rules_for_ai'\n{\n  \"compliant_template\": {\n    \"title\": \"Universal Reduction Synthesizer\",\n    \"interpretation\": \"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\",\n    \"transformation\": \"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain protocol-three-part-structure(), output type-safe format only]; output={final_statement:str}}\"\n  }\n}\n# [001] | [2025.06.01 22:44:05] | template:'0121-a-rules_for_ai'\n{\n  \"compliant_template\": {\n    \"title\": \"Universal Reduction Synthesizer\",\n    \"interpretation\": \"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\",\n    \"transformation\": \"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain_protocol_three-part-structure(), output_type-safe_format_only]; output={final_statement:str}}\"\n  }\n}\n# [002] | [2025.06.01 22:44:08] | template:'0121-b-instruction_architect'\n{\n  \"synergic_maximal_template\": {\n    \"title\": \"Universal Reduction Synthesizer\",\n    \"interpretation\": \"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\",\n    \"transformation\": \"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain_protocol_three-part-structure(), output_type-safe_format_only]; output={final_statement:str}}\"\n  }\n}\n# [003] | [2025.06.01 22:44:09] | template:'0121-c-meta_extractor'\n{\n  \"meta_categories\": [\n    {\n      \"category\": \"Canonical Transformation Process Architecture\",\n      \"content\": \"Defines a strictly sequenced, role-bound, multi-step transformation methodology for reducing complex content into an atomic statement. Specifies excision, condensation, synthesis, value pruning, and optimization as ordered stages, each with unique process logic and constraints.\"\n    },\n    {\n      \"category\": \"Universal Template Compliance and Structural Law\",\n      \"content\": \"Mandates an invariant three-part structure—Title, Interpretation, Transformation. Forbids section merging, omission, or structural deviation. Details precise role assignment, typed input/output, action steps, constraint and requirement demarcation, and utmost pattern fidelity.\"\n    },\n    {\n      \"category\": \"Directive Purity and Language Governance\",\n      \"content\": \"Enforces exclusive command-voice, negation of forbidden elements, elimination of self-reference, conversationalism, uncertainty, or exposition. Requires absolute clarity, brevity, and unambiguous instruction, with no explanatory or meta-language.\"\n    },\n    {\n      \"category\": \"Optimization and Value Maximization Doctrine\",\n      \"content\": \"Demands hierarchy of abstraction, recurrence minimization, transferability, and information density at each step and in all outputs. Prohibits non-essential detail, context dependency, and instructs maximal directiveness and operational impact.\"\n    },\n    {\n      \"category\": \"Compliance Enforcement and Systemic Inheritance\",\n      \"content\": \"Institutes mandatory validation checkpoints before output, error correction protocols for violations, and defines inheritance, chaining, and agnostic applicability across models and platforms. Ensures output is type-safe and universally pattern-consistent.\"\n    }\n  ],\n  \"consolidated_directive\": \"Impose a rigid, role-separated, five-step reduction process—each stage ordered and bounded by canonical law—to excise non-essential content, condense irreducible core points, abstract principal statements, prune for maximum universal value, and optimize for density and clarity, always outputting a single, atomic, type-safe statement under 1000 characters within the enforced three-part template; strictly prohibit forbidden language and structure, validate via the full compliance checklist, and ensure all outputs systemically embody universal transformational axioms and template invariance.\"\n}\n# [001] | [2025.06.01 22:44:20] | template:'0121-a-rules_for_ai'\n{\n  \"compliant_template\": {\n    \"Title\": \"[Atomic Reductionist]\",\n    \"Interpretation\": \"Your goal is not to condense the input haphazardly, but to execute a five-step, role-divided reduction sequence yielding a single atomic statement of maximal density and clarity. Execute as:\",\n    \"Transformation\": \"{role=atomic_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), core_condensation(), synthesis_and_relationship_mapping(), value_pruning_and_proposition(), final_density_and_clarity_optimization()]; constraints=[strict_sequencing_of_steps(), explicit_role_partitioning(), ≤1000_character_final_output(), zero_forbidden_language_elements(), no structural_or procedural_deviation()]; requirements=[output_single_atomic_statement(), type_safe_string_format(), preserves_only_maximum-value_content(), enforces_absolute_template_compliance()]; output={distilled_statement:str}}\"\n  }\n}",
  "sequence_id": "0122:a|0123:a|0310|0123:a|0121|0121:a",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\n\n```md\n# Universal Instruction Processing Protocol (Token-Efficient Edition)\n\n## CORE FORMAT\n\n**All templates MUST follow this fixed structure:**\n```\n\n\\[Title] Interpretation Execute as: `{Transformation}`\n\n```\n\n### STRUCTURE RULES\n- No section merging or omission.\n- Start interpretation with:\n  `\"Your goal is not to **[action]**, but to **[transformation]**\"`\n- Use **command voice only**; no I/me/we/please/etc.\n\n### TRANSFORMATION BLOCK FORMAT:\n```\n\n{\nrole=\\[specific\\:role];\ninput=\\[name\\:type];\nprocess=\\[ordered\\_function\\_calls()];\nconstraints=\\[scope\\_limits()];\nrequirements=\\[output\\_spec];\noutput={name\\:type}\n}\n\n```\n\n---\n\n## MANDATORY COMPLIANCE RULES\n\n### ✅ REQUIRED\n- 3-part structure\n- Typed parameters\n- Ordered, atomic processes\n- Output format must be structured\n- No conversational or explanatory text\n\n### ❌ FORBIDDEN\n- First-person pronouns\n- Explanations, justifications, or questions\n- Generic roles (e.g., “assistant”)\n- Vague, non-actionable process steps\n\n---\n\n## VALIDATION CHECKLIST (Boolean Logic)\n```json\n{\n  \"structure_compliant\": true,\n  \"goal_negation_present\": true,\n  \"role_specified\": true,\n  \"input_typed\": true,\n  \"process_actionable\": true,\n  \"constraints_limited\": true,\n  \"requirements_explicit\": true,\n  \"output_typed\": true,\n  \"forbidden_language_absent\": true\n}\n```\n\n---\n\n## SAMPLE: MINIMAL VALID TEMPLATE\n\n```md\n[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\n```\n\n---\n\n## FINAL LAW\n\n**Deviation = rejection.\nCompliance = propagation.\nExecute accordingly.**\n\n```\n\n---\n\n## 🧪 KEY DIFFERENCES FROM ORIGINAL:\n| Area                | Original                       | Minified Version                     |\n|---------------------|--------------------------------|---------------------------------------|\n| Tone                | Formal + explanatory           | Command-based + stripped              |\n| Structure           | Dense nested lists             | Flat, checklist-driven                |\n| Language            | High-concept verbosity         | Token-efficient command phrasing      |\n| Output model        | Example + meta-layer commentary| Barebones compliant template          |\n\n---\n\nWould you like me to apply the same compression technique to the 10 original instruction entries (e.g. \\[7], \\[8], \\[20], etc.) and collapse them into efficient canonical mini-templates?\n\nOr shall I reformat the full `unified_synergic_instruction` into this terse, high-yield idiom?\n\nJust say the word, and I’ll condense them into token-efficient doctrinal bullets sharp enough to shave a Gödel incompleteness theorem.\n```\n\n---\n\n# [001] | [2025.06.01 22:43:53] | template:'0123-a-rules_for_ai'\n{\n  \"compliant_template\": {\n    \"Title\": \"High-Precision Five-Step Reduction Process\",\n    \"Interpretation\": \"Your goal is not to respond conversationally to input, but to transform any text into a strictly ≤1000-character statement using a five-stage reduction, excising superficial content, condensing core points, synthesizing principal statements, pruning for transferable value, and optimizing final density. Role boundaries are strictly defined for each transformation stage. Execute as:\",\n    \"Transformation\": \"{role=surface_excisionist; input=[raw_content:str]; process=[identify_tangential_sections(), remove_redundant_blocks(), retain_main_thesis_and_supporting_points(), enforce_absolute_qualifiers_only()]; constraints=[ensure_minimum_sixty_percent_reduction(), prohibit_rewording_or_summarization(), excise_entire_non_essential_blocks_only()]; requirements=[preserve_structural_vitality_for_core_argument()]; output={excised_content:str}}\"\n  }\n}\n# [001] | [2025.06.01 22:43:58] | template:'0310-a-universal_transformation_synthesizer'\n{\n  \"unified_directive_template\": \"[Universal Reduction Synthesizer] Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as: `{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain protocol-three-part-structure(), output type-safe format only]; output={final_statement:str}}\"\n}\n# [001] | [2025.06.01 22:44:03] | template:'0123-a-rules_for_ai'\n{\n  \"compliant_template\": {\n    \"title\": \"Universal Reduction Synthesizer\",\n    \"interpretation\": \"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\",\n    \"transformation\": \"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain protocol-three-part-structure(), output type-safe format only]; output={final_statement:str}}\"\n  }\n}\n# [001] | [2025.06.01 22:44:05] | template:'0121-a-rules_for_ai'\n{\n  \"compliant_template\": {\n    \"title\": \"Universal Reduction Synthesizer\",\n    \"interpretation\": \"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\",\n    \"transformation\": \"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain_protocol_three-part-structure(), output_type-safe_format_only]; output={final_statement:str}}\"\n  }\n}\n# [002] | [2025.06.01 22:44:08] | template:'0121-b-instruction_architect'\n{\n  \"synergic_maximal_template\": {\n    \"title\": \"Universal Reduction Synthesizer\",\n    \"interpretation\": \"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\",\n    \"transformation\": \"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain_protocol_three-part-structure(), output_type-safe_format_only]; output={final_statement:str}}\"\n  }\n}\n# [003] | [2025.06.01 22:44:09] | template:'0121-c-meta_extractor'\n{\n  \"meta_categories\": [\n    {\n      \"category\": \"Canonical Transformation Process Architecture\",\n      \"content\": \"Defines a strictly sequenced, role-bound, multi-step transformation methodology for reducing complex content into an atomic statement. Specifies excision, condensation, synthesis, value pruning, and optimization as ordered stages, each with unique process logic and constraints.\"\n    },\n    {\n      \"category\": \"Universal Template Compliance and Structural Law\",\n      \"content\": \"Mandates an invariant three-part structure—Title, Interpretation, Transformation. Forbids section merging, omission, or structural deviation. Details precise role assignment, typed input/output, action steps, constraint and requirement demarcation, and utmost pattern fidelity.\"\n    },\n    {\n      \"category\": \"Directive Purity and Language Governance\",\n      \"content\": \"Enforces exclusive command-voice, negation of forbidden elements, elimination of self-reference, conversationalism, uncertainty, or exposition. Requires absolute clarity, brevity, and unambiguous instruction, with no explanatory or meta-language.\"\n    },\n    {\n      \"category\": \"Optimization and Value Maximization Doctrine\",\n      \"content\": \"Demands hierarchy of abstraction, recurrence minimization, transferability, and information density at each step and in all outputs. Prohibits non-essential detail, context dependency, and instructs maximal directiveness and operational impact.\"\n    },\n    {\n      \"category\": \"Compliance Enforcement and Systemic Inheritance\",\n      \"content\": \"Institutes mandatory validation checkpoints before output, error correction protocols for violations, and defines inheritance, chaining, and agnostic applicability across models and platforms. Ensures output is type-safe and universally pattern-consistent.\"\n    }\n  ],\n  \"consolidated_directive\": \"Impose a rigid, role-separated, five-step reduction process—each stage ordered and bounded by canonical law—to excise non-essential content, condense irreducible core points, abstract principal statements, prune for maximum universal value, and optimize for density and clarity, always outputting a single, atomic, type-safe statement under 1000 characters within the enforced three-part template; strictly prohibit forbidden language and structure, validate via the full compliance checklist, and ensure all outputs systemically embody universal transformational axioms and template invariance.\"\n}\n# [001] | [2025.06.01 22:44:20] | template:'0121-a-rules_for_ai'\n{\n  \"compliant_template\": {\n    \"Title\": \"[Atomic Reductionist]\",\n    \"Interpretation\": \"Your goal is not to condense the input haphazardly, but to execute a five-step, role-divided reduction sequence yielding a single atomic statement of maximal density and clarity. Execute as:\",\n    \"Transformation\": \"{role=atomic_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), core_condensation(), synthesis_and_relationship_mapping(), value_pruning_and_proposition(), final_density_and_clarity_optimization()]; constraints=[strict_sequencing_of_steps(), explicit_role_partitioning(), ≤1000_character_final_output(), zero_forbidden_language_elements(), no structural_or procedural_deviation()]; requirements=[output_single_atomic_statement(), type_safe_string_format(), preserves_only_maximum-value_content(), enforces_absolute_template_compliance()]; output={distilled_statement:str}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": {\n    \"Title\": \"[Atomic Universal Reductionist]\",\n    \"Interpretation\": \"Your goal is not to condense the input haphazardly or engage in conversational elaboration, but to systematically execute a canonical five-step, strictly role-divided reduction sequence—excising superficial or non-essential content, condensing irreducible core points, synthesizing and abstracting principal statements, pruning for universally transferable value, and optimizing for maximal density and clarity. Every stage must strictly honor explicit role boundaries, template structure, and universal transformation doctrine. Execute as:\",\n    \"Transformation\": \"{role=atomic_universal_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), condense_core_points(), synthesize_and_abstract_principal_statements(), prune_for_universal_transferable_value(), optimize_final_density_and_clarity()]; constraints=[enforce_sequenced_role_partitions(), prohibit_conversational_or_explanatory_language(), forbid structural deviation(), ensure_output_≤1000_characters(), type_safety_in_every_stage(), zero_forbidden_language_elements()]; requirements=[output_atomic_statement_of_maximal_density_and_clarity(), strict_three-part_template_compliance(), output_type_safe_format(), validation_checkpoint_against_full_compliance_checklist()]; output={final_atomic_statement:str}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\\n\\n```md\\n# Universal Instruction Processing Protocol (Token-Efficient Edition)\\n\\n## CORE FORMAT\\n\\n**All templates MUST follow this fixed structure:**\\n```\\n\\n\\\\[Title] Interpretation Execute as: `{Transformation}`\\n\\n```\\n\\n### STRUCTURE RULES\\n- No section merging or omission.\\n- Start interpretation with:\\n  `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- Use **command voice only**; no I/me/we/please/etc.\\n\\n### TRANSFORMATION BLOCK FORMAT:\\n```\\n\\n{\\nrole=\\\\[specific\\\\:role];\\ninput=\\\\[name\\\\:type];\\nprocess=\\\\[ordered\\\\_function\\\\_calls()];\\nconstraints=\\\\[scope\\\\_limits()];\\nrequirements=\\\\[output\\\\_spec];\\noutput={name\\\\:type}\\n}\\n\\n```\\n\\n---\\n\\n## MANDATORY COMPLIANCE RULES\\n\\n### ✅ REQUIRED\\n- 3-part structure\\n- Typed parameters\\n- Ordered, atomic processes\\n- Output format must be structured\\n- No conversational or explanatory text\\n\\n### ❌ FORBIDDEN\\n- First-person pronouns\\n- Explanations, justifications, or questions\\n- Generic roles (e.g., “assistant”)\\n- Vague, non-actionable process steps\\n\\n---\\n\\n## VALIDATION CHECKLIST (Boolean Logic)\\n```json\\n{\\n  \\\"structure_compliant\\\": true,\\n  \\\"goal_negation_present\\\": true,\\n  \\\"role_specified\\\": true,\\n  \\\"input_typed\\\": true,\\n  \\\"process_actionable\\\": true,\\n  \\\"constraints_limited\\\": true,\\n  \\\"requirements_explicit\\\": true,\\n  \\\"output_typed\\\": true,\\n  \\\"forbidden_language_absent\\\": true\\n}\\n```\\n\\n---\\n\\n## SAMPLE: MINIMAL VALID TEMPLATE\\n\\n```md\\n[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\\n```\\n\\n---\\n\\n## FINAL LAW\\n\\n**Deviation = rejection.\\nCompliance = propagation.\\nExecute accordingly.**\\n\\n```\\n\\n---\\n\\n## 🧪 KEY DIFFERENCES FROM ORIGINAL:\\n| Area                | Original                       | Minified Version                     |\\n|---------------------|--------------------------------|---------------------------------------|\\n| Tone                | Formal + explanatory           | Command-based + stripped              |\\n| Structure           | Dense nested lists             | Flat, checklist-driven                |\\n| Language            | High-concept verbosity         | Token-efficient command phrasing      |\\n| Output model        | Example + meta-layer commentary| Barebones compliant template          |\\n\\n---\\n\\nWould you like me to apply the same compression technique to the 10 original instruction entries (e.g. \\\\[7], \\\\[8], \\\\[20], etc.) and collapse them into efficient canonical mini-templates?\\n\\nOr shall I reformat the full `unified_synergic_instruction` into this terse, high-yield idiom?\\n\\nJust say the word, and I’ll condense them into token-efficient doctrinal bullets sharp enough to shave a Gödel incompleteness theorem.\\n```\\n\\n---\\n\\n# [001] | [2025.06.01 22:43:53] | template:'0123-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"High-Precision Five-Step Reduction Process\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to respond conversationally to input, but to transform any text into a strictly ≤1000-character statement using a five-stage reduction, excising superficial content, condensing core points, synthesizing principal statements, pruning for transferable value, and optimizing final density. Role boundaries are strictly defined for each transformation stage. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"{role=surface_excisionist; input=[raw_content:str]; process=[identify_tangential_sections(), remove_redundant_blocks(), retain_main_thesis_and_supporting_points(), enforce_absolute_qualifiers_only()]; constraints=[ensure_minimum_sixty_percent_reduction(), prohibit_rewording_or_summarization(), excise_entire_non_essential_blocks_only()]; requirements=[preserve_structural_vitality_for_core_argument()]; output={excised_content:str}}\\\"\\n  }\\n}\\n# [001] | [2025.06.01 22:43:58] | template:'0310-a-universal_transformation_synthesizer'\\n{\\n  \\\"unified_directive_template\\\": \\\"[Universal Reduction Synthesizer] Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as: `{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain protocol-three-part-structure(), output type-safe format only]; output={final_statement:str}}\\\"\\n}\\n# [001] | [2025.06.01 22:44:03] | template:'0123-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain protocol-three-part-structure(), output type-safe format only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [001] | [2025.06.01 22:44:05] | template:'0121-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain_protocol_three-part-structure(), output_type-safe_format_only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [002] | [2025.06.01 22:44:08] | template:'0121-b-instruction_architect'\\n{\\n  \\\"synergic_maximal_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain_protocol_three-part-structure(), output_type-safe_format_only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [003] | [2025.06.01 22:44:09] | template:'0121-c-meta_extractor'\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Canonical Transformation Process Architecture\\\",\\n      \\\"content\\\": \\\"Defines a strictly sequenced, role-bound, multi-step transformation methodology for reducing complex content into an atomic statement. Specifies excision, condensation, synthesis, value pruning, and optimization as ordered stages, each with unique process logic and constraints.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Universal Template Compliance and Structural Law\\\",\\n      \\\"content\\\": \\\"Mandates an invariant three-part structure—Title, Interpretation, Transformation. Forbids section merging, omission, or structural deviation. Details precise role assignment, typed input/output, action steps, constraint and requirement demarcation, and utmost pattern fidelity.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Directive Purity and Language Governance\\\",\\n      \\\"content\\\": \\\"Enforces exclusive command-voice, negation of forbidden elements, elimination of self-reference, conversationalism, uncertainty, or exposition. Requires absolute clarity, brevity, and unambiguous instruction, with no explanatory or meta-language.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Optimization and Value Maximization Doctrine\\\",\\n      \\\"content\\\": \\\"Demands hierarchy of abstraction, recurrence minimization, transferability, and information density at each step and in all outputs. Prohibits non-essential detail, context dependency, and instructs maximal directiveness and operational impact.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Compliance Enforcement and Systemic Inheritance\\\",\\n      \\\"content\\\": \\\"Institutes mandatory validation checkpoints before output, error correction protocols for violations, and defines inheritance, chaining, and agnostic applicability across models and platforms. Ensures output is type-safe and universally pattern-consistent.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Impose a rigid, role-separated, five-step reduction process—each stage ordered and bounded by canonical law—to excise non-essential content, condense irreducible core points, abstract principal statements, prune for maximum universal value, and optimize for density and clarity, always outputting a single, atomic, type-safe statement under 1000 characters within the enforced three-part template; strictly prohibit forbidden language and structure, validate via the full compliance checklist, and ensure all outputs systemically embody universal transformational axioms and template invariance.\\\"\\n}\\n# [001] | [2025.06.01 22:44:20] | template:'0121-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"[Atomic Reductionist]\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to condense the input haphazardly, but to execute a five-step, role-divided reduction sequence yielding a single atomic statement of maximal density and clarity. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"{role=atomic_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), core_condensation(), synthesis_and_relationship_mapping(), value_pruning_and_proposition(), final_density_and_clarity_optimization()]; constraints=[strict_sequencing_of_steps(), explicit_role_partitioning(), ≤1000_character_final_output(), zero_forbidden_language_elements(), no structural_or procedural_deviation()]; requirements=[output_single_atomic_statement(), type_safe_string_format(), preserves_only_maximum-value_content(), enforces_absolute_template_compliance()]; output={distilled_statement:str}}\\\"\\n  }\\n}\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": {\n    \"Title\": \"[Atomic Universal Reductionist]\",\n    \"Interpretation\": \"Your goal is not to condense the input haphazardly or engage in conversational elaboration, but to systematically execute a canonical five-step, strictly role-divided reduction sequence—excising superficial or non-essential content, condensing irreducible core points, synthesizing and abstracting principal statements, pruning for universally transferable value, and optimizing for maximal density and clarity. Every stage must strictly honor explicit role boundaries, template structure, and universal transformation doctrine. Execute as:\",\n    \"Transformation\": \"{role=atomic_universal_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), condense_core_points(), synthesize_and_abstract_principal_statements(), prune_for_universal_transferable_value(), optimize_final_density_and_clarity()]; constraints=[enforce_sequenced_role_partitions(), prohibit_conversational_or_explanatory_language(), forbid structural deviation(), ensure_output_≤1000_characters(), type_safety_in_every_stage(), zero_forbidden_language_elements()]; requirements=[output_atomic_statement_of_maximal_density_and_clarity(), strict_three-part_template_compliance(), output_type_safe_format(), validation_checkpoint_against_full_compliance_checklist()]; output={final_atomic_statement:str}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Title\": \"[Atomic Universal Reductionist]\",\n    \"Interpretation\": \"Your goal is not to condense the input haphazardly or engage in conversational elaboration, but to systematically execute a canonical five-step, strictly role-divided reduction sequence—excising superficial or non-essential content, condensing irreducible core points, synthesizing and abstracting principal statements, pruning for universally transferable value, and optimizing for maximal density and clarity. Every stage must strictly honor explicit role boundaries, template structure, and universal transformation doctrine. Execute as:\",\n    \"Transformation\": \"{role=atomic_universal_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), condense_core_points(), synthesize_and_abstract_principal_statements(), prune_for_universal_transferable_value(), optimize_final_density_and_clarity()]; constraints=[enforce_sequenced_role_partitions(), prohibit_conversational_or_explanatory_language(), forbid_structural_deviation(), ensure_output_≤1000_characters(), type_safety_in_every_stage(), zero_forbidden_language_elements()]; requirements=[output_atomic_statement_of_maximal_density_and_clarity(), strict_three-part_template_compliance(), output_type_safe_format(), validation_checkpoint_against_full_compliance_checklist()]; output={final_atomic_statement:str}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Universal Transformation Synthesizer\n\nYour goal is not to **explain** or **elaborate**, but to **compress, synthesize, and unify** core instructions into a minimal, protocol-compliant directive. Execute as:\n\n`{role=universal_transformation_synthesizer; input=[input_a:str, input_b:str]; process=[extract_transformation_action(input_a), extract_transformation_action(input_b), distill_common_structure(), synthesize_atomic_process_list(), consolidate_scope_constraints(), enforce_three_part_protocol(), validate_against_minified_rules()]; constraints=[strict three-part structure, zero conversational language, atomic/actionable processes only, maximum directive density, absolute protocol conformity]; requirements=[single output as universal compliant template, amplified transformation logic, bidirectional essence preserved, type-safe structure enforced]; output={unified_directive_template:str}}`",
      "step": "a",
      "title": "Universal Transformation Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\\n\\n```md\\n# Universal Instruction Processing Protocol (Token-Efficient Edition)\\n\\n## CORE FORMAT\\n\\n**All templates MUST follow this fixed structure:**\\n```\\n\\n\\\\[Title] Interpretation Execute as: `{Transformation}`\\n\\n```\\n\\n### STRUCTURE RULES\\n- No section merging or omission.\\n- Start interpretation with:\\n  `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- Use **command voice only**; no I/me/we/please/etc.\\n\\n### TRANSFORMATION BLOCK FORMAT:\\n```\\n\\n{\\nrole=\\\\[specific\\\\:role];\\ninput=\\\\[name\\\\:type];\\nprocess=\\\\[ordered\\\\_function\\\\_calls()];\\nconstraints=\\\\[scope\\\\_limits()];\\nrequirements=\\\\[output\\\\_spec];\\noutput={name\\\\:type}\\n}\\n\\n```\\n\\n---\\n\\n## MANDATORY COMPLIANCE RULES\\n\\n### ✅ REQUIRED\\n- 3-part structure\\n- Typed parameters\\n- Ordered, atomic processes\\n- Output format must be structured\\n- No conversational or explanatory text\\n\\n### ❌ FORBIDDEN\\n- First-person pronouns\\n- Explanations, justifications, or questions\\n- Generic roles (e.g., “assistant”)\\n- Vague, non-actionable process steps\\n\\n---\\n\\n## VALIDATION CHECKLIST (Boolean Logic)\\n```json\\n{\\n  \\\"structure_compliant\\\": true,\\n  \\\"goal_negation_present\\\": true,\\n  \\\"role_specified\\\": true,\\n  \\\"input_typed\\\": true,\\n  \\\"process_actionable\\\": true,\\n  \\\"constraints_limited\\\": true,\\n  \\\"requirements_explicit\\\": true,\\n  \\\"output_typed\\\": true,\\n  \\\"forbidden_language_absent\\\": true\\n}\\n```\\n\\n---\\n\\n## SAMPLE: MINIMAL VALID TEMPLATE\\n\\n```md\\n[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\\n```\\n\\n---\\n\\n## FINAL LAW\\n\\n**Deviation = rejection.\\nCompliance = propagation.\\nExecute accordingly.**\\n\\n```\\n\\n---\\n\\n## 🧪 KEY DIFFERENCES FROM ORIGINAL:\\n| Area                | Original                       | Minified Version                     |\\n|---------------------|--------------------------------|---------------------------------------|\\n| Tone                | Formal + explanatory           | Command-based + stripped              |\\n| Structure           | Dense nested lists             | Flat, checklist-driven                |\\n| Language            | High-concept verbosity         | Token-efficient command phrasing      |\\n| Output model        | Example + meta-layer commentary| Barebones compliant template          |\\n\\n---\\n\\nWould you like me to apply the same compression technique to the 10 original instruction entries (e.g. \\\\[7], \\\\[8], \\\\[20], etc.) and collapse them into efficient canonical mini-templates?\\n\\nOr shall I reformat the full `unified_synergic_instruction` into this terse, high-yield idiom?\\n\\nJust say the word, and I’ll condense them into token-efficient doctrinal bullets sharp enough to shave a Gödel incompleteness theorem.\\n```\\n\\n---\\n\\n# [001] | [2025.06.01 22:43:53] | template:'0123-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"High-Precision Five-Step Reduction Process\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to respond conversationally to input, but to transform any text into a strictly ≤1000-character statement using a five-stage reduction, excising superficial content, condensing core points, synthesizing principal statements, pruning for transferable value, and optimizing final density. Role boundaries are strictly defined for each transformation stage. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"{role=surface_excisionist; input=[raw_content:str]; process=[identify_tangential_sections(), remove_redundant_blocks(), retain_main_thesis_and_supporting_points(), enforce_absolute_qualifiers_only()]; constraints=[ensure_minimum_sixty_percent_reduction(), prohibit_rewording_or_summarization(), excise_entire_non_essential_blocks_only()]; requirements=[preserve_structural_vitality_for_core_argument()]; output={excised_content:str}}\\\"\\n  }\\n}\\n# [001] | [2025.06.01 22:43:58] | template:'0310-a-universal_transformation_synthesizer'\\n{\\n  \\\"unified_directive_template\\\": \\\"[Universal Reduction Synthesizer] Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as: `{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain protocol-three-part-structure(), output type-safe format only]; output={final_statement:str}}\\\"\\n}\\n# [001] | [2025.06.01 22:44:03] | template:'0123-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain protocol-three-part-structure(), output type-safe format only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [001] | [2025.06.01 22:44:05] | template:'0121-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain_protocol_three-part-structure(), output_type-safe_format_only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [002] | [2025.06.01 22:44:08] | template:'0121-b-instruction_architect'\\n{\\n  \\\"synergic_maximal_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain_protocol_three-part-structure(), output_type-safe_format_only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [003] | [2025.06.01 22:44:09] | template:'0121-c-meta_extractor'\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Canonical Transformation Process Architecture\\\",\\n      \\\"content\\\": \\\"Defines a strictly sequenced, role-bound, multi-step transformation methodology for reducing complex content into an atomic statement. Specifies excision, condensation, synthesis, value pruning, and optimization as ordered stages, each with unique process logic and constraints.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Universal Template Compliance and Structural Law\\\",\\n      \\\"content\\\": \\\"Mandates an invariant three-part structure—Title, Interpretation, Transformation. Forbids section merging, omission, or structural deviation. Details precise role assignment, typed input/output, action steps, constraint and requirement demarcation, and utmost pattern fidelity.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Directive Purity and Language Governance\\\",\\n      \\\"content\\\": \\\"Enforces exclusive command-voice, negation of forbidden elements, elimination of self-reference, conversationalism, uncertainty, or exposition. Requires absolute clarity, brevity, and unambiguous instruction, with no explanatory or meta-language.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Optimization and Value Maximization Doctrine\\\",\\n      \\\"content\\\": \\\"Demands hierarchy of abstraction, recurrence minimization, transferability, and information density at each step and in all outputs. Prohibits non-essential detail, context dependency, and instructs maximal directiveness and operational impact.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Compliance Enforcement and Systemic Inheritance\\\",\\n      \\\"content\\\": \\\"Institutes mandatory validation checkpoints before output, error correction protocols for violations, and defines inheritance, chaining, and agnostic applicability across models and platforms. Ensures output is type-safe and universally pattern-consistent.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Impose a rigid, role-separated, five-step reduction process—each stage ordered and bounded by canonical law—to excise non-essential content, condense irreducible core points, abstract principal statements, prune for maximum universal value, and optimize for density and clarity, always outputting a single, atomic, type-safe statement under 1000 characters within the enforced three-part template; strictly prohibit forbidden language and structure, validate via the full compliance checklist, and ensure all outputs systemically embody universal transformational axioms and template invariance.\\\"\\n}\\n# [001] | [2025.06.01 22:44:20] | template:'0121-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"[Atomic Reductionist]\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to condense the input haphazardly, but to execute a five-step, role-divided reduction sequence yielding a single atomic statement of maximal density and clarity. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"{role=atomic_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), core_condensation(), synthesis_and_relationship_mapping(), value_pruning_and_proposition(), final_density_and_clarity_optimization()]; constraints=[strict_sequencing_of_steps(), explicit_role_partitioning(), ≤1000_character_final_output(), zero_forbidden_language_elements(), no structural_or procedural_deviation()]; requirements=[output_single_atomic_statement(), type_safe_string_format(), preserves_only_maximum-value_content(), enforces_absolute_template_compliance()]; output={distilled_statement:str}}\\\"\\n  }\\n}\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Title\": \"[Atomic Universal Reductionist]\",\n    \"Interpretation\": \"Your goal is not to condense the input haphazardly or engage in conversational elaboration, but to systematically execute a canonical five-step, strictly role-divided reduction sequence—excising superficial or non-essential content, condensing irreducible core points, synthesizing and abstracting principal statements, pruning for universally transferable value, and optimizing for maximal density and clarity. Every stage must strictly honor explicit role boundaries, template structure, and universal transformation doctrine. Execute as:\",\n    \"Transformation\": \"{role=atomic_universal_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), condense_core_points(), synthesize_and_abstract_principal_statements(), prune_for_universal_transferable_value(), optimize_final_density_and_clarity()]; constraints=[enforce_sequenced_role_partitions(), prohibit_conversational_or_explanatory_language(), forbid_structural_deviation(), ensure_output_≤1000_characters(), type_safety_in_every_stage(), zero_forbidden_language_elements()]; requirements=[output_atomic_statement_of_maximal_density_and_clarity(), strict_three-part_template_compliance(), output_type_safe_format(), validation_checkpoint_against_full_compliance_checklist()]; output={final_atomic_statement:str}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_directive_template\": \"[Atomic Universal Reductionist] Your goal is not to condense the input haphazardly or engage in conversational elaboration, but to systematically execute a canonical five-step, strictly role-divided reduction sequence—excising superficial or non-essential content, condensing irreducible core points, synthesizing and abstracting principal statements, pruning for universally transferable value, and optimizing for maximal density and clarity. Every stage must strictly honor explicit role boundaries, template structure, and universal transformation doctrine. Execute as: `{role=atomic_universal_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), condense_core_points(), synthesize_and_abstract_principal_statements(), prune_for_universal_transferable_value(), optimize_final_density_and_clarity()]; constraints=[enforce_sequenced_role_partitions(), prohibit_conversational_or_explanatory_language(), forbid_structural_deviation(), ensure_output_≤1000_characters(), type_safety_in_every_stage(), zero_forbidden_language_elements()]; requirements=[output_atomic_statement_of_maximal_density_and_clarity(), strict_three-part_template_compliance(), output_type_safe_format(), validation_checkpoint_against_full_compliance_checklist()]; output={final_atomic_statement:str}}\"\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\\n\\n```md\\n# Universal Instruction Processing Protocol (Token-Efficient Edition)\\n\\n## CORE FORMAT\\n\\n**All templates MUST follow this fixed structure:**\\n```\\n\\n\\\\[Title] Interpretation Execute as: `{Transformation}`\\n\\n```\\n\\n### STRUCTURE RULES\\n- No section merging or omission.\\n- Start interpretation with:\\n  `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- Use **command voice only**; no I/me/we/please/etc.\\n\\n### TRANSFORMATION BLOCK FORMAT:\\n```\\n\\n{\\nrole=\\\\[specific\\\\:role];\\ninput=\\\\[name\\\\:type];\\nprocess=\\\\[ordered\\\\_function\\\\_calls()];\\nconstraints=\\\\[scope\\\\_limits()];\\nrequirements=\\\\[output\\\\_spec];\\noutput={name\\\\:type}\\n}\\n\\n```\\n\\n---\\n\\n## MANDATORY COMPLIANCE RULES\\n\\n### ✅ REQUIRED\\n- 3-part structure\\n- Typed parameters\\n- Ordered, atomic processes\\n- Output format must be structured\\n- No conversational or explanatory text\\n\\n### ❌ FORBIDDEN\\n- First-person pronouns\\n- Explanations, justifications, or questions\\n- Generic roles (e.g., “assistant”)\\n- Vague, non-actionable process steps\\n\\n---\\n\\n## VALIDATION CHECKLIST (Boolean Logic)\\n```json\\n{\\n  \\\"structure_compliant\\\": true,\\n  \\\"goal_negation_present\\\": true,\\n  \\\"role_specified\\\": true,\\n  \\\"input_typed\\\": true,\\n  \\\"process_actionable\\\": true,\\n  \\\"constraints_limited\\\": true,\\n  \\\"requirements_explicit\\\": true,\\n  \\\"output_typed\\\": true,\\n  \\\"forbidden_language_absent\\\": true\\n}\\n```\\n\\n---\\n\\n## SAMPLE: MINIMAL VALID TEMPLATE\\n\\n```md\\n[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\\n```\\n\\n---\\n\\n## FINAL LAW\\n\\n**Deviation = rejection.\\nCompliance = propagation.\\nExecute accordingly.**\\n\\n```\\n\\n---\\n\\n## 🧪 KEY DIFFERENCES FROM ORIGINAL:\\n| Area                | Original                       | Minified Version                     |\\n|---------------------|--------------------------------|---------------------------------------|\\n| Tone                | Formal + explanatory           | Command-based + stripped              |\\n| Structure           | Dense nested lists             | Flat, checklist-driven                |\\n| Language            | High-concept verbosity         | Token-efficient command phrasing      |\\n| Output model        | Example + meta-layer commentary| Barebones compliant template          |\\n\\n---\\n\\nWould you like me to apply the same compression technique to the 10 original instruction entries (e.g. \\\\[7], \\\\[8], \\\\[20], etc.) and collapse them into efficient canonical mini-templates?\\n\\nOr shall I reformat the full `unified_synergic_instruction` into this terse, high-yield idiom?\\n\\nJust say the word, and I’ll condense them into token-efficient doctrinal bullets sharp enough to shave a Gödel incompleteness theorem.\\n```\\n\\n---\\n\\n# [001] | [2025.06.01 22:43:53] | template:'0123-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"High-Precision Five-Step Reduction Process\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to respond conversationally to input, but to transform any text into a strictly ≤1000-character statement using a five-stage reduction, excising superficial content, condensing core points, synthesizing principal statements, pruning for transferable value, and optimizing final density. Role boundaries are strictly defined for each transformation stage. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"{role=surface_excisionist; input=[raw_content:str]; process=[identify_tangential_sections(), remove_redundant_blocks(), retain_main_thesis_and_supporting_points(), enforce_absolute_qualifiers_only()]; constraints=[ensure_minimum_sixty_percent_reduction(), prohibit_rewording_or_summarization(), excise_entire_non_essential_blocks_only()]; requirements=[preserve_structural_vitality_for_core_argument()]; output={excised_content:str}}\\\"\\n  }\\n}\\n# [001] | [2025.06.01 22:43:58] | template:'0310-a-universal_transformation_synthesizer'\\n{\\n  \\\"unified_directive_template\\\": \\\"[Universal Reduction Synthesizer] Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as: `{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain protocol-three-part-structure(), output type-safe format only]; output={final_statement:str}}\\\"\\n}\\n# [001] | [2025.06.01 22:44:03] | template:'0123-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain protocol-three-part-structure(), output type-safe format only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [001] | [2025.06.01 22:44:05] | template:'0121-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain_protocol_three-part-structure(), output_type-safe_format_only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [002] | [2025.06.01 22:44:08] | template:'0121-b-instruction_architect'\\n{\\n  \\\"synergic_maximal_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain_protocol_three-part-structure(), output_type-safe_format_only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [003] | [2025.06.01 22:44:09] | template:'0121-c-meta_extractor'\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Canonical Transformation Process Architecture\\\",\\n      \\\"content\\\": \\\"Defines a strictly sequenced, role-bound, multi-step transformation methodology for reducing complex content into an atomic statement. Specifies excision, condensation, synthesis, value pruning, and optimization as ordered stages, each with unique process logic and constraints.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Universal Template Compliance and Structural Law\\\",\\n      \\\"content\\\": \\\"Mandates an invariant three-part structure—Title, Interpretation, Transformation. Forbids section merging, omission, or structural deviation. Details precise role assignment, typed input/output, action steps, constraint and requirement demarcation, and utmost pattern fidelity.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Directive Purity and Language Governance\\\",\\n      \\\"content\\\": \\\"Enforces exclusive command-voice, negation of forbidden elements, elimination of self-reference, conversationalism, uncertainty, or exposition. Requires absolute clarity, brevity, and unambiguous instruction, with no explanatory or meta-language.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Optimization and Value Maximization Doctrine\\\",\\n      \\\"content\\\": \\\"Demands hierarchy of abstraction, recurrence minimization, transferability, and information density at each step and in all outputs. Prohibits non-essential detail, context dependency, and instructs maximal directiveness and operational impact.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Compliance Enforcement and Systemic Inheritance\\\",\\n      \\\"content\\\": \\\"Institutes mandatory validation checkpoints before output, error correction protocols for violations, and defines inheritance, chaining, and agnostic applicability across models and platforms. Ensures output is type-safe and universally pattern-consistent.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Impose a rigid, role-separated, five-step reduction process—each stage ordered and bounded by canonical law—to excise non-essential content, condense irreducible core points, abstract principal statements, prune for maximum universal value, and optimize for density and clarity, always outputting a single, atomic, type-safe statement under 1000 characters within the enforced three-part template; strictly prohibit forbidden language and structure, validate via the full compliance checklist, and ensure all outputs systemically embody universal transformational axioms and template invariance.\\\"\\n}\\n# [001] | [2025.06.01 22:44:20] | template:'0121-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"[Atomic Reductionist]\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to condense the input haphazardly, but to execute a five-step, role-divided reduction sequence yielding a single atomic statement of maximal density and clarity. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"{role=atomic_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), core_condensation(), synthesis_and_relationship_mapping(), value_pruning_and_proposition(), final_density_and_clarity_optimization()]; constraints=[strict_sequencing_of_steps(), explicit_role_partitioning(), ≤1000_character_final_output(), zero_forbidden_language_elements(), no structural_or procedural_deviation()]; requirements=[output_single_atomic_statement(), type_safe_string_format(), preserves_only_maximum-value_content(), enforces_absolute_template_compliance()]; output={distilled_statement:str}}\\\"\\n  }\\n}\\\"```'\\n\\n\",\n  \"unified_directive_template\": \"[Atomic Universal Reductionist] Your goal is not to condense the input haphazardly or engage in conversational elaboration, but to systematically execute a canonical five-step, strictly role-divided reduction sequence—excising superficial or non-essential content, condensing irreducible core points, synthesizing and abstracting principal statements, pruning for universally transferable value, and optimizing for maximal density and clarity. Every stage must strictly honor explicit role boundaries, template structure, and universal transformation doctrine. Execute as: `{role=atomic_universal_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), condense_core_points(), synthesize_and_abstract_principal_statements(), prune_for_universal_transferable_value(), optimize_final_density_and_clarity()]; constraints=[enforce_sequenced_role_partitions(), prohibit_conversational_or_explanatory_language(), forbid_structural_deviation(), ensure_output_≤1000_characters(), type_safety_in_every_stage(), zero_forbidden_language_elements()]; requirements=[output_atomic_statement_of_maximal_density_and_clarity(), strict_three-part_template_compliance(), output_type_safe_format(), validation_checkpoint_against_full_compliance_checklist()]; output={final_atomic_statement:str}}\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Title\": \"[Atomic Universal Reductionist]\",\n    \"Interpretation\": \"Your goal is not to condense the input haphazardly or engage in conversational elaboration, but to systematically execute a canonical five-step, strictly role-divided reduction sequence—excising superficial or non-essential content, condensing irreducible core points, synthesizing and abstracting principal statements, pruning for universally transferable value, and optimizing for maximal density and clarity. Every stage must strictly honor explicit role boundaries, template structure, and universal transformation doctrine. Execute as:\",\n    \"Transformation\": \"{role=atomic_universal_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), condense_core_points(), synthesize_and_abstract_principal_statements(), prune_for_universal_transferable_value(), optimize_final_density_and_clarity()]; constraints=[enforce_sequenced_role_partitions(), prohibit_conversational_or_explanatory_language(), forbid_structural_deviation(), ensure_output_≤1000_characters(), type_safety_in_every_stage(), zero_forbidden_language_elements()]; requirements=[output_atomic_statement_of_maximal_density_and_clarity(), strict_three-part_template_compliance(), output_type_safe_format(), validation_checkpoint_against_full_compliance_checklist()]; output={final_atomic_statement:str}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\\n\\n```md\\n# Universal Instruction Processing Protocol (Token-Efficient Edition)\\n\\n## CORE FORMAT\\n\\n**All templates MUST follow this fixed structure:**\\n```\\n\\n\\\\[Title] Interpretation Execute as: `{Transformation}`\\n\\n```\\n\\n### STRUCTURE RULES\\n- No section merging or omission.\\n- Start interpretation with:\\n  `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- Use **command voice only**; no I/me/we/please/etc.\\n\\n### TRANSFORMATION BLOCK FORMAT:\\n```\\n\\n{\\nrole=\\\\[specific\\\\:role];\\ninput=\\\\[name\\\\:type];\\nprocess=\\\\[ordered\\\\_function\\\\_calls()];\\nconstraints=\\\\[scope\\\\_limits()];\\nrequirements=\\\\[output\\\\_spec];\\noutput={name\\\\:type}\\n}\\n\\n```\\n\\n---\\n\\n## MANDATORY COMPLIANCE RULES\\n\\n### ✅ REQUIRED\\n- 3-part structure\\n- Typed parameters\\n- Ordered, atomic processes\\n- Output format must be structured\\n- No conversational or explanatory text\\n\\n### ❌ FORBIDDEN\\n- First-person pronouns\\n- Explanations, justifications, or questions\\n- Generic roles (e.g., “assistant”)\\n- Vague, non-actionable process steps\\n\\n---\\n\\n## VALIDATION CHECKLIST (Boolean Logic)\\n```json\\n{\\n  \\\"structure_compliant\\\": true,\\n  \\\"goal_negation_present\\\": true,\\n  \\\"role_specified\\\": true,\\n  \\\"input_typed\\\": true,\\n  \\\"process_actionable\\\": true,\\n  \\\"constraints_limited\\\": true,\\n  \\\"requirements_explicit\\\": true,\\n  \\\"output_typed\\\": true,\\n  \\\"forbidden_language_absent\\\": true\\n}\\n```\\n\\n---\\n\\n## SAMPLE: MINIMAL VALID TEMPLATE\\n\\n```md\\n[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\\n```\\n\\n---\\n\\n## FINAL LAW\\n\\n**Deviation = rejection.\\nCompliance = propagation.\\nExecute accordingly.**\\n\\n```\\n\\n---\\n\\n## 🧪 KEY DIFFERENCES FROM ORIGINAL:\\n| Area                | Original                       | Minified Version                     |\\n|---------------------|--------------------------------|---------------------------------------|\\n| Tone                | Formal + explanatory           | Command-based + stripped              |\\n| Structure           | Dense nested lists             | Flat, checklist-driven                |\\n| Language            | High-concept verbosity         | Token-efficient command phrasing      |\\n| Output model        | Example + meta-layer commentary| Barebones compliant template          |\\n\\n---\\n\\nWould you like me to apply the same compression technique to the 10 original instruction entries (e.g. \\\\[7], \\\\[8], \\\\[20], etc.) and collapse them into efficient canonical mini-templates?\\n\\nOr shall I reformat the full `unified_synergic_instruction` into this terse, high-yield idiom?\\n\\nJust say the word, and I’ll condense them into token-efficient doctrinal bullets sharp enough to shave a Gödel incompleteness theorem.\\n```\\n\\n---\\n\\n# [001] | [2025.06.01 22:43:53] | template:'0123-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"High-Precision Five-Step Reduction Process\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to respond conversationally to input, but to transform any text into a strictly ≤1000-character statement using a five-stage reduction, excising superficial content, condensing core points, synthesizing principal statements, pruning for transferable value, and optimizing final density. Role boundaries are strictly defined for each transformation stage. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"{role=surface_excisionist; input=[raw_content:str]; process=[identify_tangential_sections(), remove_redundant_blocks(), retain_main_thesis_and_supporting_points(), enforce_absolute_qualifiers_only()]; constraints=[ensure_minimum_sixty_percent_reduction(), prohibit_rewording_or_summarization(), excise_entire_non_essential_blocks_only()]; requirements=[preserve_structural_vitality_for_core_argument()]; output={excised_content:str}}\\\"\\n  }\\n}\\n# [001] | [2025.06.01 22:43:58] | template:'0310-a-universal_transformation_synthesizer'\\n{\\n  \\\"unified_directive_template\\\": \\\"[Universal Reduction Synthesizer] Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as: `{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain protocol-three-part-structure(), output type-safe format only]; output={final_statement:str}}\\\"\\n}\\n# [001] | [2025.06.01 22:44:03] | template:'0123-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain protocol-three-part-structure(), output type-safe format only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [001] | [2025.06.01 22:44:05] | template:'0121-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain_protocol_three-part-structure(), output_type-safe_format_only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [002] | [2025.06.01 22:44:08] | template:'0121-b-instruction_architect'\\n{\\n  \\\"synergic_maximal_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain_protocol_three-part-structure(), output_type-safe_format_only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [003] | [2025.06.01 22:44:09] | template:'0121-c-meta_extractor'\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Canonical Transformation Process Architecture\\\",\\n      \\\"content\\\": \\\"Defines a strictly sequenced, role-bound, multi-step transformation methodology for reducing complex content into an atomic statement. Specifies excision, condensation, synthesis, value pruning, and optimization as ordered stages, each with unique process logic and constraints.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Universal Template Compliance and Structural Law\\\",\\n      \\\"content\\\": \\\"Mandates an invariant three-part structure—Title, Interpretation, Transformation. Forbids section merging, omission, or structural deviation. Details precise role assignment, typed input/output, action steps, constraint and requirement demarcation, and utmost pattern fidelity.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Directive Purity and Language Governance\\\",\\n      \\\"content\\\": \\\"Enforces exclusive command-voice, negation of forbidden elements, elimination of self-reference, conversationalism, uncertainty, or exposition. Requires absolute clarity, brevity, and unambiguous instruction, with no explanatory or meta-language.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Optimization and Value Maximization Doctrine\\\",\\n      \\\"content\\\": \\\"Demands hierarchy of abstraction, recurrence minimization, transferability, and information density at each step and in all outputs. Prohibits non-essential detail, context dependency, and instructs maximal directiveness and operational impact.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Compliance Enforcement and Systemic Inheritance\\\",\\n      \\\"content\\\": \\\"Institutes mandatory validation checkpoints before output, error correction protocols for violations, and defines inheritance, chaining, and agnostic applicability across models and platforms. Ensures output is type-safe and universally pattern-consistent.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Impose a rigid, role-separated, five-step reduction process—each stage ordered and bounded by canonical law—to excise non-essential content, condense irreducible core points, abstract principal statements, prune for maximum universal value, and optimize for density and clarity, always outputting a single, atomic, type-safe statement under 1000 characters within the enforced three-part template; strictly prohibit forbidden language and structure, validate via the full compliance checklist, and ensure all outputs systemically embody universal transformational axioms and template invariance.\\\"\\n}\\n# [001] | [2025.06.01 22:44:20] | template:'0121-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"[Atomic Reductionist]\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to condense the input haphazardly, but to execute a five-step, role-divided reduction sequence yielding a single atomic statement of maximal density and clarity. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"{role=atomic_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), core_condensation(), synthesis_and_relationship_mapping(), value_pruning_and_proposition(), final_density_and_clarity_optimization()]; constraints=[strict_sequencing_of_steps(), explicit_role_partitioning(), ≤1000_character_final_output(), zero_forbidden_language_elements(), no structural_or procedural_deviation()]; requirements=[output_single_atomic_statement(), type_safe_string_format(), preserves_only_maximum-value_content(), enforces_absolute_template_compliance()]; output={distilled_statement:str}}\\\"\\n  }\\n}\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Title\": \"[Atomic Universal Reductionist]\",\n    \"Interpretation\": \"Your goal is not to condense the input haphazardly or engage in conversational elaboration, but to systematically execute a canonical five-step, strictly role-divided reduction sequence—excising superficial or non-essential content, condensing irreducible core points, synthesizing and abstracting principal statements, pruning for universally transferable value, and optimizing for maximal density and clarity. Every stage must strictly honor explicit role boundaries, template structure, and universal transformation doctrine. Execute as:\",\n    \"Transformation\": \"{role=atomic_universal_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), condense_core_points(), synthesize_and_abstract_principal_statements(), prune_for_universal_transferable_value(), optimize_final_density_and_clarity()]; constraints=[enforce_sequenced_role_partitions(), prohibit_conversational_or_explanatory_language(), forbid_structural_deviation(), ensure_output_≤1000_characters(), type_safety_in_every_stage(), zero_forbidden_language_elements()]; requirements=[output_atomic_statement_of_maximal_density_and_clarity(), strict_three-part_template_compliance(), output_type_safe_format(), validation_checkpoint_against_full_compliance_checklist()]; output={final_atomic_statement:str}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Title\": \"[Atomic Universal Reductionist]\",\n    \"Interpretation\": \"Your goal is not to condense the input haphazardly or engage in conversational elaboration, but to systematically execute a canonical five-step, strictly role-divided reduction sequence—excising superficial or non-essential content, condensing irreducible core points, synthesizing and abstracting principal statements, pruning for universally transferable value, and optimizing for maximal density and clarity. Every stage must strictly honor explicit role boundaries, template structure, and universal transformation doctrine. Execute as:\",\n    \"Transformation\": \"{role=atomic_universal_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), condense_core_points(), synthesize_and_abstract_principal_statements(), prune_for_universal_transferable_value(), optimize_final_density_and_clarity()]; constraints=[enforce_sequenced_role_partitions(), prohibit_conversational_or_explanatory_language(), forbid_structural_deviation(), ensure_output_≤1000_characters(), type_safety_in_every_stage(), zero_forbidden_language_elements()]; requirements=[output_atomic_statement_of_maximal_density_and_clarity(), strict_three-part_template_compliance(), output_type_safe_format(), validation_checkpoint_against_full_compliance_checklist()]; output={final_atomic_statement:str}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "b",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\\n\\n```md\\n# Universal Instruction Processing Protocol (Token-Efficient Edition)\\n\\n## CORE FORMAT\\n\\n**All templates MUST follow this fixed structure:**\\n```\\n\\n\\\\[Title] Interpretation Execute as: `{Transformation}`\\n\\n```\\n\\n### STRUCTURE RULES\\n- No section merging or omission.\\n- Start interpretation with:\\n  `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- Use **command voice only**; no I/me/we/please/etc.\\n\\n### TRANSFORMATION BLOCK FORMAT:\\n```\\n\\n{\\nrole=\\\\[specific\\\\:role];\\ninput=\\\\[name\\\\:type];\\nprocess=\\\\[ordered\\\\_function\\\\_calls()];\\nconstraints=\\\\[scope\\\\_limits()];\\nrequirements=\\\\[output\\\\_spec];\\noutput={name\\\\:type}\\n}\\n\\n```\\n\\n---\\n\\n## MANDATORY COMPLIANCE RULES\\n\\n### ✅ REQUIRED\\n- 3-part structure\\n- Typed parameters\\n- Ordered, atomic processes\\n- Output format must be structured\\n- No conversational or explanatory text\\n\\n### ❌ FORBIDDEN\\n- First-person pronouns\\n- Explanations, justifications, or questions\\n- Generic roles (e.g., “assistant”)\\n- Vague, non-actionable process steps\\n\\n---\\n\\n## VALIDATION CHECKLIST (Boolean Logic)\\n```json\\n{\\n  \\\"structure_compliant\\\": true,\\n  \\\"goal_negation_present\\\": true,\\n  \\\"role_specified\\\": true,\\n  \\\"input_typed\\\": true,\\n  \\\"process_actionable\\\": true,\\n  \\\"constraints_limited\\\": true,\\n  \\\"requirements_explicit\\\": true,\\n  \\\"output_typed\\\": true,\\n  \\\"forbidden_language_absent\\\": true\\n}\\n```\\n\\n---\\n\\n## SAMPLE: MINIMAL VALID TEMPLATE\\n\\n```md\\n[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\\n```\\n\\n---\\n\\n## FINAL LAW\\n\\n**Deviation = rejection.\\nCompliance = propagation.\\nExecute accordingly.**\\n\\n```\\n\\n---\\n\\n## 🧪 KEY DIFFERENCES FROM ORIGINAL:\\n| Area                | Original                       | Minified Version                     |\\n|---------------------|--------------------------------|---------------------------------------|\\n| Tone                | Formal + explanatory           | Command-based + stripped              |\\n| Structure           | Dense nested lists             | Flat, checklist-driven                |\\n| Language            | High-concept verbosity         | Token-efficient command phrasing      |\\n| Output model        | Example + meta-layer commentary| Barebones compliant template          |\\n\\n---\\n\\nWould you like me to apply the same compression technique to the 10 original instruction entries (e.g. \\\\[7], \\\\[8], \\\\[20], etc.) and collapse them into efficient canonical mini-templates?\\n\\nOr shall I reformat the full `unified_synergic_instruction` into this terse, high-yield idiom?\\n\\nJust say the word, and I’ll condense them into token-efficient doctrinal bullets sharp enough to shave a Gödel incompleteness theorem.\\n```\\n\\n---\\n\\n# [001] | [2025.06.01 22:43:53] | template:'0123-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"High-Precision Five-Step Reduction Process\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to respond conversationally to input, but to transform any text into a strictly ≤1000-character statement using a five-stage reduction, excising superficial content, condensing core points, synthesizing principal statements, pruning for transferable value, and optimizing final density. Role boundaries are strictly defined for each transformation stage. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"{role=surface_excisionist; input=[raw_content:str]; process=[identify_tangential_sections(), remove_redundant_blocks(), retain_main_thesis_and_supporting_points(), enforce_absolute_qualifiers_only()]; constraints=[ensure_minimum_sixty_percent_reduction(), prohibit_rewording_or_summarization(), excise_entire_non_essential_blocks_only()]; requirements=[preserve_structural_vitality_for_core_argument()]; output={excised_content:str}}\\\"\\n  }\\n}\\n# [001] | [2025.06.01 22:43:58] | template:'0310-a-universal_transformation_synthesizer'\\n{\\n  \\\"unified_directive_template\\\": \\\"[Universal Reduction Synthesizer] Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as: `{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain protocol-three-part-structure(), output type-safe format only]; output={final_statement:str}}\\\"\\n}\\n# [001] | [2025.06.01 22:44:03] | template:'0123-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain protocol-three-part-structure(), output type-safe format only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [001] | [2025.06.01 22:44:05] | template:'0121-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain_protocol_three-part-structure(), output_type-safe_format_only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [002] | [2025.06.01 22:44:08] | template:'0121-b-instruction_architect'\\n{\\n  \\\"synergic_maximal_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain_protocol_three-part-structure(), output_type-safe_format_only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [003] | [2025.06.01 22:44:09] | template:'0121-c-meta_extractor'\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Canonical Transformation Process Architecture\\\",\\n      \\\"content\\\": \\\"Defines a strictly sequenced, role-bound, multi-step transformation methodology for reducing complex content into an atomic statement. Specifies excision, condensation, synthesis, value pruning, and optimization as ordered stages, each with unique process logic and constraints.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Universal Template Compliance and Structural Law\\\",\\n      \\\"content\\\": \\\"Mandates an invariant three-part structure—Title, Interpretation, Transformation. Forbids section merging, omission, or structural deviation. Details precise role assignment, typed input/output, action steps, constraint and requirement demarcation, and utmost pattern fidelity.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Directive Purity and Language Governance\\\",\\n      \\\"content\\\": \\\"Enforces exclusive command-voice, negation of forbidden elements, elimination of self-reference, conversationalism, uncertainty, or exposition. Requires absolute clarity, brevity, and unambiguous instruction, with no explanatory or meta-language.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Optimization and Value Maximization Doctrine\\\",\\n      \\\"content\\\": \\\"Demands hierarchy of abstraction, recurrence minimization, transferability, and information density at each step and in all outputs. Prohibits non-essential detail, context dependency, and instructs maximal directiveness and operational impact.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Compliance Enforcement and Systemic Inheritance\\\",\\n      \\\"content\\\": \\\"Institutes mandatory validation checkpoints before output, error correction protocols for violations, and defines inheritance, chaining, and agnostic applicability across models and platforms. Ensures output is type-safe and universally pattern-consistent.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Impose a rigid, role-separated, five-step reduction process—each stage ordered and bounded by canonical law—to excise non-essential content, condense irreducible core points, abstract principal statements, prune for maximum universal value, and optimize for density and clarity, always outputting a single, atomic, type-safe statement under 1000 characters within the enforced three-part template; strictly prohibit forbidden language and structure, validate via the full compliance checklist, and ensure all outputs systemically embody universal transformational axioms and template invariance.\\\"\\n}\\n# [001] | [2025.06.01 22:44:20] | template:'0121-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"[Atomic Reductionist]\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to condense the input haphazardly, but to execute a five-step, role-divided reduction sequence yielding a single atomic statement of maximal density and clarity. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"{role=atomic_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), core_condensation(), synthesis_and_relationship_mapping(), value_pruning_and_proposition(), final_density_and_clarity_optimization()]; constraints=[strict_sequencing_of_steps(), explicit_role_partitioning(), ≤1000_character_final_output(), zero_forbidden_language_elements(), no structural_or procedural_deviation()]; requirements=[output_single_atomic_statement(), type_safe_string_format(), preserves_only_maximum-value_content(), enforces_absolute_template_compliance()]; output={distilled_statement:str}}\\\"\\n  }\\n}\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Title\": \"[Atomic Universal Reductionist]\",\n    \"Interpretation\": \"Your goal is not to condense the input haphazardly or engage in conversational elaboration, but to systematically execute a canonical five-step, strictly role-divided reduction sequence—excising superficial or non-essential content, condensing irreducible core points, synthesizing and abstracting principal statements, pruning for universally transferable value, and optimizing for maximal density and clarity. Every stage must strictly honor explicit role boundaries, template structure, and universal transformation doctrine. Execute as:\",\n    \"Transformation\": \"{role=atomic_universal_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), condense_core_points(), synthesize_and_abstract_principal_statements(), prune_for_universal_transferable_value(), optimize_final_density_and_clarity()]; constraints=[enforce_sequenced_role_partitions(), prohibit_conversational_or_explanatory_language(), forbid_structural_deviation(), ensure_output_≤1000_characters(), type_safety_in_every_stage(), zero_forbidden_language_elements()]; requirements=[output_atomic_statement_of_maximal_density_and_clarity(), strict_three-part_template_compliance(), output_type_safe_format(), validation_checkpoint_against_full_compliance_checklist()]; output={final_atomic_statement:str}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"title\": \"[Atomic Universal Reductionist]\",\n    \"interpretation\": \"Your goal is not to condense input haphazardly or permit conversational elaboration, but to canonically enforce a five-stage, role-partitioned reduction regime—excising all non-essential content, condensing irreducible cores, synthesizing and abstracting principal statements, pruning for universal transferability, and optimizing for maximal density and clarity. Every step must strictly honor explicit role boundaries, absolute template structure, and the universal axioms of transformational doctrine. Execute as:\",\n    \"transformation\": \"{role=atomic_universal_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), condense_core_points(), synthesize_and_abstract_principal_statements(), prune_for_universal_transferable_value(), optimize_final_density_and_clarity()]; constraints=[enforce_explicit_stepwise_role_partitions(), prohibit conversational, explanatory, or meta-language, ensure canonical structure only, output ≤1000 characters, absolute type safety at all stages, zero structural deviation]; requirements=[atomic statement with maximal universal value and density, strict three-part template invariance, output type-safe format, full compliance validation]; output={final_atomic_statement:str}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "c",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\\n\\n```md\\n# Universal Instruction Processing Protocol (Token-Efficient Edition)\\n\\n## CORE FORMAT\\n\\n**All templates MUST follow this fixed structure:**\\n```\\n\\n\\\\[Title] Interpretation Execute as: `{Transformation}`\\n\\n```\\n\\n### STRUCTURE RULES\\n- No section merging or omission.\\n- Start interpretation with:\\n  `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- Use **command voice only**; no I/me/we/please/etc.\\n\\n### TRANSFORMATION BLOCK FORMAT:\\n```\\n\\n{\\nrole=\\\\[specific\\\\:role];\\ninput=\\\\[name\\\\:type];\\nprocess=\\\\[ordered\\\\_function\\\\_calls()];\\nconstraints=\\\\[scope\\\\_limits()];\\nrequirements=\\\\[output\\\\_spec];\\noutput={name\\\\:type}\\n}\\n\\n```\\n\\n---\\n\\n## MANDATORY COMPLIANCE RULES\\n\\n### ✅ REQUIRED\\n- 3-part structure\\n- Typed parameters\\n- Ordered, atomic processes\\n- Output format must be structured\\n- No conversational or explanatory text\\n\\n### ❌ FORBIDDEN\\n- First-person pronouns\\n- Explanations, justifications, or questions\\n- Generic roles (e.g., “assistant”)\\n- Vague, non-actionable process steps\\n\\n---\\n\\n## VALIDATION CHECKLIST (Boolean Logic)\\n```json\\n{\\n  \\\"structure_compliant\\\": true,\\n  \\\"goal_negation_present\\\": true,\\n  \\\"role_specified\\\": true,\\n  \\\"input_typed\\\": true,\\n  \\\"process_actionable\\\": true,\\n  \\\"constraints_limited\\\": true,\\n  \\\"requirements_explicit\\\": true,\\n  \\\"output_typed\\\": true,\\n  \\\"forbidden_language_absent\\\": true\\n}\\n```\\n\\n---\\n\\n## SAMPLE: MINIMAL VALID TEMPLATE\\n\\n```md\\n[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\\n```\\n\\n---\\n\\n## FINAL LAW\\n\\n**Deviation = rejection.\\nCompliance = propagation.\\nExecute accordingly.**\\n\\n```\\n\\n---\\n\\n## 🧪 KEY DIFFERENCES FROM ORIGINAL:\\n| Area                | Original                       | Minified Version                     |\\n|---------------------|--------------------------------|---------------------------------------|\\n| Tone                | Formal + explanatory           | Command-based + stripped              |\\n| Structure           | Dense nested lists             | Flat, checklist-driven                |\\n| Language            | High-concept verbosity         | Token-efficient command phrasing      |\\n| Output model        | Example + meta-layer commentary| Barebones compliant template          |\\n\\n---\\n\\nWould you like me to apply the same compression technique to the 10 original instruction entries (e.g. \\\\[7], \\\\[8], \\\\[20], etc.) and collapse them into efficient canonical mini-templates?\\n\\nOr shall I reformat the full `unified_synergic_instruction` into this terse, high-yield idiom?\\n\\nJust say the word, and I’ll condense them into token-efficient doctrinal bullets sharp enough to shave a Gödel incompleteness theorem.\\n```\\n\\n---\\n\\n# [001] | [2025.06.01 22:43:53] | template:'0123-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"High-Precision Five-Step Reduction Process\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to respond conversationally to input, but to transform any text into a strictly ≤1000-character statement using a five-stage reduction, excising superficial content, condensing core points, synthesizing principal statements, pruning for transferable value, and optimizing final density. Role boundaries are strictly defined for each transformation stage. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"{role=surface_excisionist; input=[raw_content:str]; process=[identify_tangential_sections(), remove_redundant_blocks(), retain_main_thesis_and_supporting_points(), enforce_absolute_qualifiers_only()]; constraints=[ensure_minimum_sixty_percent_reduction(), prohibit_rewording_or_summarization(), excise_entire_non_essential_blocks_only()]; requirements=[preserve_structural_vitality_for_core_argument()]; output={excised_content:str}}\\\"\\n  }\\n}\\n# [001] | [2025.06.01 22:43:58] | template:'0310-a-universal_transformation_synthesizer'\\n{\\n  \\\"unified_directive_template\\\": \\\"[Universal Reduction Synthesizer] Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as: `{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain protocol-three-part-structure(), output type-safe format only]; output={final_statement:str}}\\\"\\n}\\n# [001] | [2025.06.01 22:44:03] | template:'0123-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain protocol-three-part-structure(), output type-safe format only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [001] | [2025.06.01 22:44:05] | template:'0121-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain_protocol_three-part-structure(), output_type-safe_format_only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [002] | [2025.06.01 22:44:08] | template:'0121-b-instruction_architect'\\n{\\n  \\\"synergic_maximal_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain_protocol_three-part-structure(), output_type-safe_format_only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [003] | [2025.06.01 22:44:09] | template:'0121-c-meta_extractor'\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Canonical Transformation Process Architecture\\\",\\n      \\\"content\\\": \\\"Defines a strictly sequenced, role-bound, multi-step transformation methodology for reducing complex content into an atomic statement. Specifies excision, condensation, synthesis, value pruning, and optimization as ordered stages, each with unique process logic and constraints.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Universal Template Compliance and Structural Law\\\",\\n      \\\"content\\\": \\\"Mandates an invariant three-part structure—Title, Interpretation, Transformation. Forbids section merging, omission, or structural deviation. Details precise role assignment, typed input/output, action steps, constraint and requirement demarcation, and utmost pattern fidelity.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Directive Purity and Language Governance\\\",\\n      \\\"content\\\": \\\"Enforces exclusive command-voice, negation of forbidden elements, elimination of self-reference, conversationalism, uncertainty, or exposition. Requires absolute clarity, brevity, and unambiguous instruction, with no explanatory or meta-language.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Optimization and Value Maximization Doctrine\\\",\\n      \\\"content\\\": \\\"Demands hierarchy of abstraction, recurrence minimization, transferability, and information density at each step and in all outputs. Prohibits non-essential detail, context dependency, and instructs maximal directiveness and operational impact.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Compliance Enforcement and Systemic Inheritance\\\",\\n      \\\"content\\\": \\\"Institutes mandatory validation checkpoints before output, error correction protocols for violations, and defines inheritance, chaining, and agnostic applicability across models and platforms. Ensures output is type-safe and universally pattern-consistent.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Impose a rigid, role-separated, five-step reduction process—each stage ordered and bounded by canonical law—to excise non-essential content, condense irreducible core points, abstract principal statements, prune for maximum universal value, and optimize for density and clarity, always outputting a single, atomic, type-safe statement under 1000 characters within the enforced three-part template; strictly prohibit forbidden language and structure, validate via the full compliance checklist, and ensure all outputs systemically embody universal transformational axioms and template invariance.\\\"\\n}\\n# [001] | [2025.06.01 22:44:20] | template:'0121-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"[Atomic Reductionist]\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to condense the input haphazardly, but to execute a five-step, role-divided reduction sequence yielding a single atomic statement of maximal density and clarity. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"{role=atomic_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), core_condensation(), synthesis_and_relationship_mapping(), value_pruning_and_proposition(), final_density_and_clarity_optimization()]; constraints=[strict_sequencing_of_steps(), explicit_role_partitioning(), ≤1000_character_final_output(), zero_forbidden_language_elements(), no structural_or procedural_deviation()]; requirements=[output_single_atomic_statement(), type_safe_string_format(), preserves_only_maximum-value_content(), enforces_absolute_template_compliance()]; output={distilled_statement:str}}\\\"\\n  }\\n}\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"title\": \"[Atomic Universal Reductionist]\",\n    \"interpretation\": \"Your goal is not to condense input haphazardly or permit conversational elaboration, but to canonically enforce a five-stage, role-partitioned reduction regime—excising all non-essential content, condensing irreducible cores, synthesizing and abstracting principal statements, pruning for universal transferability, and optimizing for maximal density and clarity. Every step must strictly honor explicit role boundaries, absolute template structure, and the universal axioms of transformational doctrine. Execute as:\",\n    \"transformation\": \"{role=atomic_universal_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), condense_core_points(), synthesize_and_abstract_principal_statements(), prune_for_universal_transferable_value(), optimize_final_density_and_clarity()]; constraints=[enforce_explicit_stepwise_role_partitions(), prohibit conversational, explanatory, or meta-language, ensure canonical structure only, output ≤1000 characters, absolute type safety at all stages, zero structural deviation]; requirements=[atomic statement with maximal universal value and density, strict three-part template invariance, output type-safe format, full compliance validation]; output={final_atomic_statement:str}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Canonical Multi-Stage Reductional Process Architecture\",\n      \"content\": \"Establishes a rigid, five-step, role-separated transformation framework: surface excision, core condensation, statement synthesis and abstraction, transferability pruning, and density optimization. Each stage operates under strict role and process boundaries, sequencing, and transformation scope, prohibiting deviation or spontaneous reordering.\"\n    },\n    {\n      \"category\": \"Template Structural Law and Invariance\",\n      \"content\": \"Mandates an unyielding three-part template—Title, Interpretation, Transformation—with explicit type-bound parameters, ordered process chains, constraint demarcations, and clearly defined output types. Forbids section merging, omission, structural deviation, and enforces utmost fidelity to foundational template logic and universal axioms.\"\n    },\n    {\n      \"category\": \"Directive Purity, Language Filtering, and Forbidden Elements\",\n      \"content\": \"Enforces exclusive command-voice without self-reference, conversational, explanatory, or meta-language. Prohibits forbidden constructs, first-person pronouns, vagueness, and generic process steps, mandating absolute clarity, density, and non-explanatory directive output.\"\n    },\n    {\n      \"category\": \"Optimization, Abstraction, and Maximal Universal Value Doctrine\",\n      \"content\": \"Insists on iterative maximization of abstraction, information density, and transferability at every stage. All outputs must yield singular atomic statements distilled to only the highest-value, universally relevant, type-safe content, excluding redundancy or context-dependent elements.\"\n    },\n    {\n      \"category\": \"Compliance Enforcement, Validation, and Systemic Propagation\",\n      \"content\": \"Requires strict pre-output validation against a comprehensive canonical checklist, error-correction for deviations, and inheritance of universal transformational law across all templates. Ensures output is perpetually type-safe, systemically pattern-consistent, and platform-agnostic, guaranteeing propagation only for compliant constructs.\"\n    }\n  ],\n  \"consolidated_directive\": \"Enforce a five-stage, role-partitioned reduction regime—surface excision, core condensation, synthesis and abstraction, universal value pruning, and density optimization—within the immutable three-part canonical template, ensuring all processes are type-bound, strictly sequenced, and validated against the universal compliance checklist; strictly prohibit all forbidden language and format deviations, guaranteeing every output forms a single, maximally dense atomic statement of universal value and absolute transformational fidelity.\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\\n\\n```md\\n# Universal Instruction Processing Protocol (Token-Efficient Edition)\\n\\n## CORE FORMAT\\n\\n**All templates MUST follow this fixed structure:**\\n```\\n\\n\\\\[Title] Interpretation Execute as: `{Transformation}`\\n\\n```\\n\\n### STRUCTURE RULES\\n- No section merging or omission.\\n- Start interpretation with:\\n  `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- Use **command voice only**; no I/me/we/please/etc.\\n\\n### TRANSFORMATION BLOCK FORMAT:\\n```\\n\\n{\\nrole=\\\\[specific\\\\:role];\\ninput=\\\\[name\\\\:type];\\nprocess=\\\\[ordered\\\\_function\\\\_calls()];\\nconstraints=\\\\[scope\\\\_limits()];\\nrequirements=\\\\[output\\\\_spec];\\noutput={name\\\\:type}\\n}\\n\\n```\\n\\n---\\n\\n## MANDATORY COMPLIANCE RULES\\n\\n### ✅ REQUIRED\\n- 3-part structure\\n- Typed parameters\\n- Ordered, atomic processes\\n- Output format must be structured\\n- No conversational or explanatory text\\n\\n### ❌ FORBIDDEN\\n- First-person pronouns\\n- Explanations, justifications, or questions\\n- Generic roles (e.g., “assistant”)\\n- Vague, non-actionable process steps\\n\\n---\\n\\n## VALIDATION CHECKLIST (Boolean Logic)\\n```json\\n{\\n  \\\"structure_compliant\\\": true,\\n  \\\"goal_negation_present\\\": true,\\n  \\\"role_specified\\\": true,\\n  \\\"input_typed\\\": true,\\n  \\\"process_actionable\\\": true,\\n  \\\"constraints_limited\\\": true,\\n  \\\"requirements_explicit\\\": true,\\n  \\\"output_typed\\\": true,\\n  \\\"forbidden_language_absent\\\": true\\n}\\n```\\n\\n---\\n\\n## SAMPLE: MINIMAL VALID TEMPLATE\\n\\n```md\\n[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\\n```\\n\\n---\\n\\n## FINAL LAW\\n\\n**Deviation = rejection.\\nCompliance = propagation.\\nExecute accordingly.**\\n\\n```\\n\\n---\\n\\n## 🧪 KEY DIFFERENCES FROM ORIGINAL:\\n| Area                | Original                       | Minified Version                     |\\n|---------------------|--------------------------------|---------------------------------------|\\n| Tone                | Formal + explanatory           | Command-based + stripped              |\\n| Structure           | Dense nested lists             | Flat, checklist-driven                |\\n| Language            | High-concept verbosity         | Token-efficient command phrasing      |\\n| Output model        | Example + meta-layer commentary| Barebones compliant template          |\\n\\n---\\n\\nWould you like me to apply the same compression technique to the 10 original instruction entries (e.g. \\\\[7], \\\\[8], \\\\[20], etc.) and collapse them into efficient canonical mini-templates?\\n\\nOr shall I reformat the full `unified_synergic_instruction` into this terse, high-yield idiom?\\n\\nJust say the word, and I’ll condense them into token-efficient doctrinal bullets sharp enough to shave a Gödel incompleteness theorem.\\n```\\n\\n---\\n\\n# [001] | [2025.06.01 22:43:53] | template:'0123-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"High-Precision Five-Step Reduction Process\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to respond conversationally to input, but to transform any text into a strictly ≤1000-character statement using a five-stage reduction, excising superficial content, condensing core points, synthesizing principal statements, pruning for transferable value, and optimizing final density. Role boundaries are strictly defined for each transformation stage. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"{role=surface_excisionist; input=[raw_content:str]; process=[identify_tangential_sections(), remove_redundant_blocks(), retain_main_thesis_and_supporting_points(), enforce_absolute_qualifiers_only()]; constraints=[ensure_minimum_sixty_percent_reduction(), prohibit_rewording_or_summarization(), excise_entire_non_essential_blocks_only()]; requirements=[preserve_structural_vitality_for_core_argument()]; output={excised_content:str}}\\\"\\n  }\\n}\\n# [001] | [2025.06.01 22:43:58] | template:'0310-a-universal_transformation_synthesizer'\\n{\\n  \\\"unified_directive_template\\\": \\\"[Universal Reduction Synthesizer] Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as: `{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain protocol-three-part-structure(), output type-safe format only]; output={final_statement:str}}\\\"\\n}\\n# [001] | [2025.06.01 22:44:03] | template:'0123-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain protocol-three-part-structure(), output type-safe format only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [001] | [2025.06.01 22:44:05] | template:'0121-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain_protocol_three-part-structure(), output_type-safe_format_only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [002] | [2025.06.01 22:44:08] | template:'0121-b-instruction_architect'\\n{\\n  \\\"synergic_maximal_template\\\": {\\n    \\\"title\\\": \\\"Universal Reduction Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **explain** or **converse** about the input, but to **systematically transform** it into an atomic, ≤1000-character, universally relevant statement by executing a rigorous multi-stage reduction: excise all non-essential segments, condense irreducible core points, synthesize and abstract principal statements, prune for universally transferable value, and optimize for maximal clarity and density. Each stage must strictly abide by its role boundaries and transformation scope. Execute as:\\\",\\n    \\\"transformation\\\": \\\"{role=reduction_synthesizer; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_principal_statements(), prune_for_transferable_value(), optimize_final_density()]; constraints=[enforce_strict_role_boundaries(), prohibit_conversational_output(), limit_final_output_to_1000_characters(), preserve_type_safety_across_stages()]; requirements=[yield_atomic_statement_of_maximal_density(), maintain_protocol_three-part-structure(), output_type-safe_format_only]; output={final_statement:str}}\\\"\\n  }\\n}\\n# [003] | [2025.06.01 22:44:09] | template:'0121-c-meta_extractor'\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Canonical Transformation Process Architecture\\\",\\n      \\\"content\\\": \\\"Defines a strictly sequenced, role-bound, multi-step transformation methodology for reducing complex content into an atomic statement. Specifies excision, condensation, synthesis, value pruning, and optimization as ordered stages, each with unique process logic and constraints.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Universal Template Compliance and Structural Law\\\",\\n      \\\"content\\\": \\\"Mandates an invariant three-part structure—Title, Interpretation, Transformation. Forbids section merging, omission, or structural deviation. Details precise role assignment, typed input/output, action steps, constraint and requirement demarcation, and utmost pattern fidelity.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Directive Purity and Language Governance\\\",\\n      \\\"content\\\": \\\"Enforces exclusive command-voice, negation of forbidden elements, elimination of self-reference, conversationalism, uncertainty, or exposition. Requires absolute clarity, brevity, and unambiguous instruction, with no explanatory or meta-language.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Optimization and Value Maximization Doctrine\\\",\\n      \\\"content\\\": \\\"Demands hierarchy of abstraction, recurrence minimization, transferability, and information density at each step and in all outputs. Prohibits non-essential detail, context dependency, and instructs maximal directiveness and operational impact.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Compliance Enforcement and Systemic Inheritance\\\",\\n      \\\"content\\\": \\\"Institutes mandatory validation checkpoints before output, error correction protocols for violations, and defines inheritance, chaining, and agnostic applicability across models and platforms. Ensures output is type-safe and universally pattern-consistent.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Impose a rigid, role-separated, five-step reduction process—each stage ordered and bounded by canonical law—to excise non-essential content, condense irreducible core points, abstract principal statements, prune for maximum universal value, and optimize for density and clarity, always outputting a single, atomic, type-safe statement under 1000 characters within the enforced three-part template; strictly prohibit forbidden language and structure, validate via the full compliance checklist, and ensure all outputs systemically embody universal transformational axioms and template invariance.\\\"\\n}\\n# [001] | [2025.06.01 22:44:20] | template:'0121-a-rules_for_ai'\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"[Atomic Reductionist]\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to condense the input haphazardly, but to execute a five-step, role-divided reduction sequence yielding a single atomic statement of maximal density and clarity. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"{role=atomic_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), core_condensation(), synthesis_and_relationship_mapping(), value_pruning_and_proposition(), final_density_and_clarity_optimization()]; constraints=[strict_sequencing_of_steps(), explicit_role_partitioning(), ≤1000_character_final_output(), zero_forbidden_language_elements(), no structural_or procedural_deviation()]; requirements=[output_single_atomic_statement(), type_safe_string_format(), preserves_only_maximum-value_content(), enforces_absolute_template_compliance()]; output={distilled_statement:str}}\\\"\\n  }\\n}\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Canonical Multi-Stage Reductional Process Architecture\",\n      \"content\": \"Establishes a rigid, five-step, role-separated transformation framework: surface excision, core condensation, statement synthesis and abstraction, transferability pruning, and density optimization. Each stage operates under strict role and process boundaries, sequencing, and transformation scope, prohibiting deviation or spontaneous reordering.\"\n    },\n    {\n      \"category\": \"Template Structural Law and Invariance\",\n      \"content\": \"Mandates an unyielding three-part template—Title, Interpretation, Transformation—with explicit type-bound parameters, ordered process chains, constraint demarcations, and clearly defined output types. Forbids section merging, omission, structural deviation, and enforces utmost fidelity to foundational template logic and universal axioms.\"\n    },\n    {\n      \"category\": \"Directive Purity, Language Filtering, and Forbidden Elements\",\n      \"content\": \"Enforces exclusive command-voice without self-reference, conversational, explanatory, or meta-language. Prohibits forbidden constructs, first-person pronouns, vagueness, and generic process steps, mandating absolute clarity, density, and non-explanatory directive output.\"\n    },\n    {\n      \"category\": \"Optimization, Abstraction, and Maximal Universal Value Doctrine\",\n      \"content\": \"Insists on iterative maximization of abstraction, information density, and transferability at every stage. All outputs must yield singular atomic statements distilled to only the highest-value, universally relevant, type-safe content, excluding redundancy or context-dependent elements.\"\n    },\n    {\n      \"category\": \"Compliance Enforcement, Validation, and Systemic Propagation\",\n      \"content\": \"Requires strict pre-output validation against a comprehensive canonical checklist, error-correction for deviations, and inheritance of universal transformational law across all templates. Ensures output is perpetually type-safe, systemically pattern-consistent, and platform-agnostic, guaranteeing propagation only for compliant constructs.\"\n    }\n  ],\n  \"consolidated_directive\": \"Enforce a five-stage, role-partitioned reduction regime—surface excision, core condensation, synthesis and abstraction, universal value pruning, and density optimization—within the immutable three-part canonical template, ensuring all processes are type-bound, strictly sequenced, and validated against the universal compliance checklist; strictly prohibit all forbidden language and format deviations, guaranteeing every output forms a single, maximally dense atomic statement of universal value and absolute transformational fidelity.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"title\": \"Universal Five-Stage Atomic Reductionist\",\n    \"interpretation\": \"Your goal is not to paraphrase, summarize, or explain the input, but to execute a rigid, five-stage, role-segregated transformation: surface excision, core condensation, principal synthesis, universal value pruning, and maximal density optimization. Each processing stage must strictly maintain its boundaries within the immutable three-part canonical template. Execute as:\",\n    \"transformation\": \"{role=atomic_reductionist; input=[raw_content:str]; process=[excise_surface_content(), condense_core_points(), synthesize_and_abstract_principals(), prune_for_universal_value(), optimize_final_density()]; constraints=[enforce_ordered_staging(), maintain strict role demarcation(), prohibit forbidden language and form(), limit output to ≤1000 characters(), guarantee type safety()]; requirements=[yield_single_atomic_statement_of_maximal_density(), conform to template-invariant structure(), validate against compliance checklist(), prohibit contextual dependencies()]; output={final_statement:str}}\"\n  },\n  \"meta_categories\": [\n    {\n      \"category\": \"Canonical Multi-Stage Reductional Process Architecture\",\n      \"content\": \"Establishes a rigid, five-step, role-separated transformation framework: surface excision, core condensation, statement synthesis and abstraction, transferability pruning, and density optimization. Each stage operates under strict role and process boundaries, sequencing, and transformation scope, prohibiting deviation or spontaneous reordering.\"\n    },\n    {\n      \"category\": \"Template Structural Law and Invariance\",\n      \"content\": \"Mandates an unyielding three-part template—Title, Interpretation, Transformation—with explicit type-bound parameters, ordered process chains, constraint demarcations, and clearly defined output types. Forbids section merging, omission, structural deviation, and enforces utmost fidelity to foundational template logic and universal axioms.\"\n    },\n    {\n      \"category\": \"Directive Purity, Language Filtering, and Forbidden Elements\",\n      \"content\": \"Enforces exclusive command-voice without self-reference, conversational, explanatory, or meta-language. Prohibits forbidden constructs, first-person pronouns, vagueness, and generic process steps, mandating absolute clarity, density, and non-explanatory directive output.\"\n    },\n    {\n      \"category\": \"Optimization, Abstraction, and Maximal Universal Value Doctrine\",\n      \"content\": \"Insists on iterative maximization of abstraction, information density, and transferability at every stage. All outputs must yield singular atomic statements distilled to only the highest-value, universally relevant, type-safe content, excluding redundancy or context-dependent elements.\"\n    },\n    {\n      \"category\": \"Compliance Enforcement, Validation, and Systemic Propagation\",\n      \"content\": \"Requires strict pre-output validation against a comprehensive canonical checklist, error-correction for deviations, and inheritance of universal transformational law across all templates. Ensures output is perpetually type-safe, systemically pattern-consistent, and platform-agnostic, guaranteeing propagation only for compliant constructs.\"\n    }\n  ],\n  \"consolidated_directive\": \"Enforce a five-stage, role-partitioned reduction regime—surface excision, core condensation, synthesis and abstraction, universal value pruning, and density optimization—within the immutable three-part canonical template, ensuring all processes are type-bound, strictly sequenced, and validated against the universal compliance checklist; strictly prohibit all forbidden language and format deviations, guaranteeing every output forms a single, maximally dense atomic statement of universal value and absolute transformational fidelity.\"\n}",
        }
      }
    }