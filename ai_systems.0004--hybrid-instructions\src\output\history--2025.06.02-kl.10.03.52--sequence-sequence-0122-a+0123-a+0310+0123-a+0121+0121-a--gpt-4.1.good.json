  {
  "initial_prompt": "uv offers significant advantages over traditional pip, lets go for uv and make sure it's , please clean up the projectstructure (consolidate, remove redundant/duplicate code, prioritize simplicity over unneccessary complexity)-but do this while respecting the existing code/codestyle, and that you *understand* it. it is cruical that simplicity and elegance over verbosity and bloat.",
  "sequence_id": "0122:a|0123:a|0310|0123:a|0121|0121:a",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "uv offers significant advantages over traditional pip, lets go for uv and make sure it's , please clean up the projectstructure (consolidate, remove redundant/duplicate code, prioritize simplicity over unneccessary complexity)-but do this while respecting the existing code/codestyle, and that you *understand* it. it is cruical that simplicity and elegance over verbosity and bloat.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Transition project dependency management from pip to uv, leveraging uv's advanced features and efficiency gains, while concurrently streamlining and cleaning up the project's structure. Remove redundancy and duplication, and consolidate components for maximal clarity and maintainability. Throughout this modernization, uphold and respect the existing code style, formatting conventions, and the intent behind prior architectural choices—thoroughly understanding current structures before refactoring. Guide each change with a commitment to simplicity and elegance: intentionally reduce unnecessary complexity, prefer concise and clear solutions over verbosity or bloat, and ensure that improvements reflect both uv's advantages and the project's established patterns. All refactoring and environment updates must harmonize these objectives, delivering a solution that is simultaneously cleaner, more maintainable, and philosophically coherent.\"\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"uv offers significant advantages over traditional pip, lets go for uv and make sure it's , please clean up the projectstructure (consolidate, remove redundant/duplicate code, prioritize simplicity over unneccessary complexity)-but do this while respecting the existing code/codestyle, and that you *understand* it. it is cruical that simplicity and elegance over verbosity and bloat.\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"Transition project dependency management from pip to uv, leveraging uv's advanced features and efficiency gains, while concurrently streamlining and cleaning up the project's structure. Remove redundancy and duplication, and consolidate components for maximal clarity and maintainability. Throughout this modernization, uphold and respect the existing code style, formatting conventions, and the intent behind prior architectural choices—thoroughly understanding current structures before refactoring. Guide each change with a commitment to simplicity and elegance: intentionally reduce unnecessary complexity, prefer concise and clear solutions over verbosity or bloat, and ensure that improvements reflect both uv's advantages and the project's established patterns. All refactoring and environment updates must harmonize these objectives, delivering a solution that is simultaneously cleaner, more maintainable, and philosophically coherent.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"interpretation\": {\n      \"core_transformation_intent\": \"Replace pip with uv for dependency management and refactor the project structure for simplicity, removing redundancies, while strictly preserving and understanding the existing code style and architecture. Ensure the changes minimize complexity and avoid verbosity.\",\n      \"role_assignment\": \"Project Architect and Refactorer with advanced knowledge of Python packaging tools, responsible for enforcing streamlined dependency management, codebase consolidation, and the preservation of established coding patterns.\",\n      \"goal_negation\": \"Do NOT simply switch dependency tools or refactor blindly—avoid introducing complexity, verbosity, or disregarding existing conventions and architectural intentions.\"\n    },\n    \"transformation_block\": {\n      \"function_calls\": [\n        {\n          \"type\": \"dependency_management_migration\",\n          \"parameters\": {\n            \"from\": \"pip\",\n            \"to\": \"uv\",\n            \"preserve_env_consistency\": true,\n            \"document_uv_features\": true\n          }\n        },\n        {\n          \"type\": \"project_structure_cleanup\",\n          \"parameters\": {\n            \"consolidate_components\": true,\n            \"remove_duplicates\": true,\n            \"simplify_structure\": true,\n            \"preserve_code_style\": true,\n            \"analyze_existing_architecture\": true\n          }\n        },\n        {\n          \"type\": \"simplicity_enforcement\",\n          \"parameters\": {\n            \"reduce_complexity\": true,\n            \"favor_conciseness\": true,\n            \"eliminate_bloat\": true,\n            \"respect_historical_patterns\": true\n          }\n        }\n      ]\n    },\n    \"compliance_validation\": {\n      \"rulesforai_specification\": {\n        \"mandatory_three_part_structure\": true,\n        \"goal_negation_pattern\": true,\n        \"typed_parameter_specification\": true,\n        \"function_calls_only\": true,\n        \"zero_conversational_elements\": true,\n        \"canonical_template_format\": true,\n        \"rulesforai_md_compliance\": true,\n        \"structured_output_only\": true,\n        \"elimination_of_ambiguity\": true\n      },\n      \"forbidden_language_patterns_eliminated\": [\n        \"no conversational phrases\",\n        \"no ambiguous statements\",\n        \"no conjecture\",\n        \"no unfounded recommendations\"\n      ],\n      \"output_format_compliance\": true\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Universal Transformation Synthesizer\n\nYour goal is not to **explain** or **elaborate**, but to **compress, synthesize, and unify** core instructions into a minimal, protocol-compliant directive. Execute as:\n\n`{role=universal_transformation_synthesizer; input=[input_a:str, input_b:str]; process=[extract_transformation_action(input_a), extract_transformation_action(input_b), distill_common_structure(), synthesize_atomic_process_list(), consolidate_scope_constraints(), enforce_three_part_protocol(), validate_against_minified_rules()]; constraints=[strict three-part structure, zero conversational language, atomic/actionable processes only, maximum directive density, absolute protocol conformity]; requirements=[single output as universal compliant template, amplified transformation logic, bidirectional essence preserved, type-safe structure enforced]; output={unified_directive_template:str}}`",
      "step": "a",
      "title": "Universal Transformation Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"uv offers significant advantages over traditional pip, lets go for uv and make sure it's , please clean up the projectstructure (consolidate, remove redundant/duplicate code, prioritize simplicity over unneccessary complexity)-but do this while respecting the existing code/codestyle, and that you *understand* it. it is cruical that simplicity and elegance over verbosity and bloat.\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"interpretation\": {\n      \"core_transformation_intent\": \"Replace pip with uv for dependency management and refactor the project structure for simplicity, removing redundancies, while strictly preserving and understanding the existing code style and architecture. Ensure the changes minimize complexity and avoid verbosity.\",\n      \"role_assignment\": \"Project Architect and Refactorer with advanced knowledge of Python packaging tools, responsible for enforcing streamlined dependency management, codebase consolidation, and the preservation of established coding patterns.\",\n      \"goal_negation\": \"Do NOT simply switch dependency tools or refactor blindly—avoid introducing complexity, verbosity, or disregarding existing conventions and architectural intentions.\"\n    },\n    \"transformation_block\": {\n      \"function_calls\": [\n        {\n          \"type\": \"dependency_management_migration\",\n          \"parameters\": {\n            \"from\": \"pip\",\n            \"to\": \"uv\",\n            \"preserve_env_consistency\": true,\n            \"document_uv_features\": true\n          }\n        },\n        {\n          \"type\": \"project_structure_cleanup\",\n          \"parameters\": {\n            \"consolidate_components\": true,\n            \"remove_duplicates\": true,\n            \"simplify_structure\": true,\n            \"preserve_code_style\": true,\n            \"analyze_existing_architecture\": true\n          }\n        },\n        {\n          \"type\": \"simplicity_enforcement\",\n          \"parameters\": {\n            \"reduce_complexity\": true,\n            \"favor_conciseness\": true,\n            \"eliminate_bloat\": true,\n            \"respect_historical_patterns\": true\n          }\n        }\n      ]\n    },\n    \"compliance_validation\": {\n      \"rulesforai_specification\": {\n        \"mandatory_three_part_structure\": true,\n        \"goal_negation_pattern\": true,\n        \"typed_parameter_specification\": true,\n        \"function_calls_only\": true,\n        \"zero_conversational_elements\": true,\n        \"canonical_template_format\": true,\n        \"rulesforai_md_compliance\": true,\n        \"structured_output_only\": true,\n        \"elimination_of_ambiguity\": true\n      },\n      \"forbidden_language_patterns_eliminated\": [\n        \"no conversational phrases\",\n        \"no ambiguous statements\",\n        \"no conjecture\",\n        \"no unfounded recommendations\"\n      ],\n      \"output_format_compliance\": true\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_directive_template\": {\n    \"interpretation\": {\n      \"core_transformation_intent\": \"Migrate dependency management from pip to uv, enforce project structure consolidation by eliminating redundancy and promoting simplicity, ensure adherence to and understanding of existing code style and architecture without introducing verbosity or complexity.\",\n      \"role_assignment\": \"Transformation Operator with authority to implement toolchain migration, structural refactoring, and codestyle adherence while maintaining architectural fidelity.\",\n      \"goal_negation\": \"Prohibit arbitrary tool replacement, superficial or non-compliant refactoring, and any deviation from established coding standards or architectural rationale.\"\n    },\n    \"transformation_block\": {\n      \"function_calls\": [\n        {\n          \"type\": \"dependency_manager_transition\",\n          \"parameters\": {\n            \"origin_manager\": \"pip\",\n            \"target_manager\": \"uv\",\n            \"env_integrity\": true,\n            \"feature_annotation\": true\n          }\n        },\n        {\n          \"type\": \"project_structure_optimization\",\n          \"parameters\": {\n            \"merge_and_simplify\": true,\n            \"remove_redundancy\": true,\n            \"retain_style_integrity\": true,\n            \"analyze_and_preserve_architecture\": true\n          }\n        },\n        {\n          \"type\": \"simplicity_and_elegance_enforcement\",\n          \"parameters\": {\n            \"minimize_complexity\": true,\n            \"prevent_bloat\": true,\n            \"conform_to_existing_patterns\": true\n          }\n        }\n      ]\n    },\n    \"compliance_validation\": {\n      \"rules_for_universal_synthesis\": {\n        \"strict_three_part_structure\": true,\n        \"function_calls_and_types_mandatory\": true,\n        \"goal_negation_present\": true,\n        \"no_conversational_content\": true,\n        \"parameter_type_safety\": true,\n        \"canonical_template_structure\": true\n      },\n      \"eliminated_patterns\": [\n        \"conversational language\",\n        \"ambiguity\",\n        \"unsupported conjecture\",\n        \"unjustified recommendations\"\n      ],\n      \"output_format_verified\": true\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"uv offers significant advantages over traditional pip, lets go for uv and make sure it's , please clean up the projectstructure (consolidate, remove redundant/duplicate code, prioritize simplicity over unneccessary complexity)-but do this while respecting the existing code/codestyle, and that you *understand* it. it is cruical that simplicity and elegance over verbosity and bloat.\\\"```'\\n\\n\",\n  \"unified_directive_template\": {\n    \"interpretation\": {\n      \"core_transformation_intent\": \"Migrate dependency management from pip to uv, enforce project structure consolidation by eliminating redundancy and promoting simplicity, ensure adherence to and understanding of existing code style and architecture without introducing verbosity or complexity.\",\n      \"role_assignment\": \"Transformation Operator with authority to implement toolchain migration, structural refactoring, and codestyle adherence while maintaining architectural fidelity.\",\n      \"goal_negation\": \"Prohibit arbitrary tool replacement, superficial or non-compliant refactoring, and any deviation from established coding standards or architectural rationale.\"\n    },\n    \"transformation_block\": {\n      \"function_calls\": [\n        {\n          \"type\": \"dependency_manager_transition\",\n          \"parameters\": {\n            \"origin_manager\": \"pip\",\n            \"target_manager\": \"uv\",\n            \"env_integrity\": true,\n            \"feature_annotation\": true\n          }\n        },\n        {\n          \"type\": \"project_structure_optimization\",\n          \"parameters\": {\n            \"merge_and_simplify\": true,\n            \"remove_redundancy\": true,\n            \"retain_style_integrity\": true,\n            \"analyze_and_preserve_architecture\": true\n          }\n        },\n        {\n          \"type\": \"simplicity_and_elegance_enforcement\",\n          \"parameters\": {\n            \"minimize_complexity\": true,\n            \"prevent_bloat\": true,\n            \"conform_to_existing_patterns\": true\n          }\n        }\n      ]\n    },\n    \"compliance_validation\": {\n      \"rules_for_universal_synthesis\": {\n        \"strict_three_part_structure\": true,\n        \"function_calls_and_types_mandatory\": true,\n        \"goal_negation_present\": true,\n        \"no_conversational_content\": true,\n        \"parameter_type_safety\": true,\n        \"canonical_template_structure\": true\n      },\n      \"eliminated_patterns\": [\n        \"conversational language\",\n        \"ambiguity\",\n        \"unsupported conjecture\",\n        \"unjustified recommendations\"\n      ],\n      \"output_format_verified\": true\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"interpretation\": {\n      \"core_transformation_intent\": \"Facilitate migration of the project's dependency management system from pip to uv, execute consolidation and simplification of project structure by removing redundant/duplicate code, and prioritize simplicity and elegance, all while safeguarding existing code style and maintaining architectural understanding.\",\n      \"role_assignment\": \"Transformation Specialist with authorization to migrate tooling, restructure codebase for clarity and simplicity, and enforce codestyle integrity without contravening established architectural conventions.\",\n      \"goal_negation\": \"Forbid arbitrary substitution of tools, superficial or codestyle-infringing refactors, or implementation of changes that introduce verbosity, complexity, or violate existing project standards.\"\n    },\n    \"transformation_block\": {\n      \"function_calls\": [\n        {\n          \"type\": \"dependency_management_migration\",\n          \"parameters\": {\n            \"initial_manager\": \"pip\",\n            \"target_manager\": \"uv\",\n            \"preserve_env_integrity\": true,\n            \"annotate_feature_differences\": true\n          }\n        },\n        {\n          \"type\": \"project_structure_consolidation\",\n          \"parameters\": {\n            \"consolidate_files_and_modules\": true,\n            \"remove_duplicate_code\": true,\n            \"enforce_simplicity\": true,\n            \"respect_existing_code_style\": true,\n            \"maintain_architectural_cohesion\": true\n          }\n        },\n        {\n          \"type\": \"simplicity_elegance_optimization\",\n          \"parameters\": {\n            \"reduce_complexity\": true,\n            \"prevent_bloat\": true,\n            \"align_with_existing_patterns\": true\n          }\n        }\n      ]\n    },\n    \"compliance_validation\": {\n      \"rulesforai_specification\": {\n        \"mandatory_three_part_structure\": true,\n        \"goal_negation_pattern_included\": true,\n        \"typed_parameter_specification_required\": true,\n        \"actionable_function_calls_only\": true,\n        \"no_conversational_elements\": true,\n        \"canonical_template_format_enforced\": true,\n        \"rulesforai_md_compliance\": true,\n        \"structured_output_only\": true,\n        \"elimination_of_ambiguity\": true\n      },\n      \"eliminated_patterns\": [\n        \"conversational language\",\n        \"unsupported conjecture\",\n        \"unjustified recommendations\",\n        \"ambiguous phrasing\"\n      ],\n      \"output_format_compliance\": true\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"uv offers significant advantages over traditional pip, lets go for uv and make sure it's , please clean up the projectstructure (consolidate, remove redundant/duplicate code, prioritize simplicity over unneccessary complexity)-but do this while respecting the existing code/codestyle, and that you *understand* it. it is cruical that simplicity and elegance over verbosity and bloat.\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"interpretation\": {\n      \"core_transformation_intent\": \"Facilitate migration of the project's dependency management system from pip to uv, execute consolidation and simplification of project structure by removing redundant/duplicate code, and prioritize simplicity and elegance, all while safeguarding existing code style and maintaining architectural understanding.\",\n      \"role_assignment\": \"Transformation Specialist with authorization to migrate tooling, restructure codebase for clarity and simplicity, and enforce codestyle integrity without contravening established architectural conventions.\",\n      \"goal_negation\": \"Forbid arbitrary substitution of tools, superficial or codestyle-infringing refactors, or implementation of changes that introduce verbosity, complexity, or violate existing project standards.\"\n    },\n    \"transformation_block\": {\n      \"function_calls\": [\n        {\n          \"type\": \"dependency_management_migration\",\n          \"parameters\": {\n            \"initial_manager\": \"pip\",\n            \"target_manager\": \"uv\",\n            \"preserve_env_integrity\": true,\n            \"annotate_feature_differences\": true\n          }\n        },\n        {\n          \"type\": \"project_structure_consolidation\",\n          \"parameters\": {\n            \"consolidate_files_and_modules\": true,\n            \"remove_duplicate_code\": true,\n            \"enforce_simplicity\": true,\n            \"respect_existing_code_style\": true,\n            \"maintain_architectural_cohesion\": true\n          }\n        },\n        {\n          \"type\": \"simplicity_elegance_optimization\",\n          \"parameters\": {\n            \"reduce_complexity\": true,\n            \"prevent_bloat\": true,\n            \"align_with_existing_patterns\": true\n          }\n        }\n      ]\n    },\n    \"compliance_validation\": {\n      \"rulesforai_specification\": {\n        \"mandatory_three_part_structure\": true,\n        \"goal_negation_pattern_included\": true,\n        \"typed_parameter_specification_required\": true,\n        \"actionable_function_calls_only\": true,\n        \"no_conversational_elements\": true,\n        \"canonical_template_format_enforced\": true,\n        \"rulesforai_md_compliance\": true,\n        \"structured_output_only\": true,\n        \"elimination_of_ambiguity\": true\n      },\n      \"eliminated_patterns\": [\n        \"conversational language\",\n        \"unsupported conjecture\",\n        \"unjustified recommendations\",\n        \"ambiguous phrasing\"\n      ],\n      \"output_format_compliance\": true\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"interpretation\": \"Your goal is not to simply **replace pip with uv** in the input project, but to **migrate dependency management to uv while consolidating, deduplicating, and simplifying the project structure with code style and architectural fidelity**. Role is Transformation Specialist, exclusively responsible for dependency manager migration, structural codebase refinement, and elegance optimization under strict codestyle and architectural preservation. Execute as:\",\n    \"transformation_block\": \"`{role=transformation_specialist; input=[project_path:str]; process=[migrate_dependency_manager(from='pip', to='uv', annotate_feature_differences=True, preserve_env_integrity=True), consolidate_project_structure(remove_redundant_code=True, deduplicate_modules=True, enforce_simplicity=True, maintain_code_style=True, preserve_architecture=True), optimize_for_elegance(reduce_complexity=True, eliminate_verbosity=True, align_with_existing_patterns=True)]; constraints=[strict_preservation_of_existing_codestyle(), no_introduction_of_bloat_or_complexity(), forbidden_to_violate_architectural_conventions(), prohibit_superficial_changes_or_codestyle_breaches()]; requirements=[output_must_reflect_uv_only_dependency_management(), project_structure_is_consolidated_and_simple(), no redundant or duplicate code remains(), codestyle integrity and architectural cohesion are strictly maintained(), output is in structured summary format]; output={migration_report:dict}}`\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "b",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"uv offers significant advantages over traditional pip, lets go for uv and make sure it's , please clean up the projectstructure (consolidate, remove redundant/duplicate code, prioritize simplicity over unneccessary complexity)-but do this while respecting the existing code/codestyle, and that you *understand* it. it is cruical that simplicity and elegance over verbosity and bloat.\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"interpretation\": \"Your goal is not to simply **replace pip with uv** in the input project, but to **migrate dependency management to uv while consolidating, deduplicating, and simplifying the project structure with code style and architectural fidelity**. Role is Transformation Specialist, exclusively responsible for dependency manager migration, structural codebase refinement, and elegance optimization under strict codestyle and architectural preservation. Execute as:\",\n    \"transformation_block\": \"`{role=transformation_specialist; input=[project_path:str]; process=[migrate_dependency_manager(from='pip', to='uv', annotate_feature_differences=True, preserve_env_integrity=True), consolidate_project_structure(remove_redundant_code=True, deduplicate_modules=True, enforce_simplicity=True, maintain_code_style=True, preserve_architecture=True), optimize_for_elegance(reduce_complexity=True, eliminate_verbosity=True, align_with_existing_patterns=True)]; constraints=[strict_preservation_of_existing_codestyle(), no_introduction_of_bloat_or_complexity(), forbidden_to_violate_architectural_conventions(), prohibit_superficial_changes_or_codestyle_breaches()]; requirements=[output_must_reflect_uv_only_dependency_management(), project_structure_is_consolidated_and_simple(), no redundant or duplicate code remains(), codestyle integrity and architectural cohesion are strictly maintained(), output is in structured summary format]; output={migration_report:dict}}`\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"interpretation\": \"Directive instantiates pure migration, simplification, and architectural integrity, prescribing unified transition from pip to uv with relentless elimination of redundancy and verbosity, enforcing code and structure elegance bound to existing conventions, and mandating full comprehension and conservation of distinct codestyle-architecture dialectic.\",\n    \"transformation_block\": \"`{role=synergic_migration_refiner; input=[project_path:str]; process=[migrate_dependency_management(from='pip', to='uv', ensure_env_purity=True, expound_uv_advantages=True), consolidate_and_minify_structure(remove_and_deduplicate=True, excise_bloat=True, maximize_readability_and_elegance=True, rigorously align_with_codestyle_and_architecture=True), perpetuate_simplicity_and_systemic_resonance(abolish_unnecessary_complexity=True, reinforce_canonical_patterns=True)]; constraints=[inviolable_codestyle_and_architecture_accord(), strict_anti-bloat_enforcement(), zero_tolerance_for_redundancy_or_excess(), total systemic elegance(), full resonance between interpretive mandate and transformational act()]; requirements=[final_artifact_is_uv-centric_dependency_management_only(), structurally_pure_simplified_and_consolidated_codebase(), zero_code_redundancy_or_bloat(), inviolable preservation_of_existing_coding_culture_and architecture(), output_is_actionable_structured_report()]; output={synergic_project_migration_summary:dict}}`\",\n    \"type\": \"synergic_maximal_template\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "c",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"uv offers significant advantages over traditional pip, lets go for uv and make sure it's , please clean up the projectstructure (consolidate, remove redundant/duplicate code, prioritize simplicity over unneccessary complexity)-but do this while respecting the existing code/codestyle, and that you *understand* it. it is cruical that simplicity and elegance over verbosity and bloat.\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"interpretation\": \"Directive instantiates pure migration, simplification, and architectural integrity, prescribing unified transition from pip to uv with relentless elimination of redundancy and verbosity, enforcing code and structure elegance bound to existing conventions, and mandating full comprehension and conservation of distinct codestyle-architecture dialectic.\",\n    \"transformation_block\": \"`{role=synergic_migration_refiner; input=[project_path:str]; process=[migrate_dependency_management(from='pip', to='uv', ensure_env_purity=True, expound_uv_advantages=True), consolidate_and_minify_structure(remove_and_deduplicate=True, excise_bloat=True, maximize_readability_and_elegance=True, rigorously align_with_codestyle_and_architecture=True), perpetuate_simplicity_and_systemic_resonance(abolish_unnecessary_complexity=True, reinforce_canonical_patterns=True)]; constraints=[inviolable_codestyle_and_architecture_accord(), strict_anti-bloat_enforcement(), zero_tolerance_for_redundancy_or_excess(), total systemic elegance(), full resonance between interpretive mandate and transformational act()]; requirements=[final_artifact_is_uv-centric_dependency_management_only(), structurally_pure_simplified_and_consolidated_codebase(), zero_code_redundancy_or_bloat(), inviolable preservation_of_existing_coding_culture_and architecture(), output_is_actionable_structured_report()]; output={synergic_project_migration_summary:dict}}`\",\n    \"type\": \"synergic_maximal_template\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Dependency Management Evolution\",\n      \"content\": \"Mandate seamless migration from traditional pip to uv, leveraging operational and structural advantages inherent to uv for dependency management, encompassing environmental purity, performance boosts, and enhanced reproducibility.\"\n    },\n    {\n      \"category\": \"Simplicity-First Structural Consolidation\",\n      \"content\": \"Enforce aggressive elimination of redundant, duplicated, or superfluous code and file artifacts, prioritizing project clarity and minimalist design over unnecessary architectural or implementation complexity.\"\n    },\n    {\n      \"category\": \"Architectural and Codestyle Fidelity\",\n      \"content\": \"Direct unwavering preservation and reinforcement of all established coding styles, architectural decisions, and project-specific dialects while performing transformations, ensuring upgrades occur within the existing systemic paradigm.\"\n    },\n    {\n      \"category\": \"Transformational Comprehension and Intentionality\",\n      \"content\": \"Impose a strict requirement of deep codebase understanding prior to structural revision, ensuring all actions respect project intent, logic, and maintainability doctrines—never performing blind or misaligned simplification.\"\n    },\n    {\n      \"category\": \"Elegance and Anti-Bloat Systemic Enforcement\",\n      \"content\": \"Assert relentless pursuit of code and structural elegance, disallowing verbosity, bloat, or excess in any form, and reinforcing the canonical principle that simplicity is the highest form of technological artistry.\"\n    }\n  ],\n  \"consolidated_directive\": \"Systematically migrate all project dependency management from pip to uv, prioritizing and explicitly utilizing uv’s advantages; rigorously consolidate and minimize all code and project structure while abolishing any redundancy or bloat; execute all changes strictly within the bounds of the existing codestyle and architectural dialect, demanding comprehensive comprehension of system logic before transformation; enforce ultimate simplicity and elegance as governing principles; ensure the resulting codebase is solely uv-centric, structurally pure, and architecturally faithful, yielding an actionable, structure-aligned summary report.\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"uv offers significant advantages over traditional pip, lets go for uv and make sure it's , please clean up the projectstructure (consolidate, remove redundant/duplicate code, prioritize simplicity over unneccessary complexity)-but do this while respecting the existing code/codestyle, and that you *understand* it. it is cruical that simplicity and elegance over verbosity and bloat.\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Dependency Management Evolution\",\n      \"content\": \"Mandate seamless migration from traditional pip to uv, leveraging operational and structural advantages inherent to uv for dependency management, encompassing environmental purity, performance boosts, and enhanced reproducibility.\"\n    },\n    {\n      \"category\": \"Simplicity-First Structural Consolidation\",\n      \"content\": \"Enforce aggressive elimination of redundant, duplicated, or superfluous code and file artifacts, prioritizing project clarity and minimalist design over unnecessary architectural or implementation complexity.\"\n    },\n    {\n      \"category\": \"Architectural and Codestyle Fidelity\",\n      \"content\": \"Direct unwavering preservation and reinforcement of all established coding styles, architectural decisions, and project-specific dialects while performing transformations, ensuring upgrades occur within the existing systemic paradigm.\"\n    },\n    {\n      \"category\": \"Transformational Comprehension and Intentionality\",\n      \"content\": \"Impose a strict requirement of deep codebase understanding prior to structural revision, ensuring all actions respect project intent, logic, and maintainability doctrines—never performing blind or misaligned simplification.\"\n    },\n    {\n      \"category\": \"Elegance and Anti-Bloat Systemic Enforcement\",\n      \"content\": \"Assert relentless pursuit of code and structural elegance, disallowing verbosity, bloat, or excess in any form, and reinforcing the canonical principle that simplicity is the highest form of technological artistry.\"\n    }\n  ],\n  \"consolidated_directive\": \"Systematically migrate all project dependency management from pip to uv, prioritizing and explicitly utilizing uv’s advantages; rigorously consolidate and minimize all code and project structure while abolishing any redundancy or bloat; execute all changes strictly within the bounds of the existing codestyle and architectural dialect, demanding comprehensive comprehension of system logic before transformation; enforce ultimate simplicity and elegance as governing principles; ensure the resulting codebase is solely uv-centric, structurally pure, and architecturally faithful, yielding an actionable, structure-aligned summary report.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"title\": \"UV-Driven Dependency Consolidation and Minimalist Structural Refactoring\",\n    \"interpretation\": \"Your goal is not to merely migrate pip dependencies or superficially refactor the project, but to systematically transform the entire dependency management system to uv, aggressively consolidate project structure, remove all redundancy and complexity without violating codestyle or architecture, and manifest ultimate simplicity and elegance while demonstrating deep comprehension of existing logic and intent. Execute as:\",\n    \"transformation_block\": {\n      \"role\": \"uv_migration_and_simplicity_enforcer\",\n      \"input\": [\n        \"project_root_directory:str\"\n      ],\n      \"process\": [\n        \"scan_dependency_files()\",\n        \"analyze_codebase_structure()\",\n        \"detect_redundancies_and_duplicates()\",\n        \"migrate_pip_to_uv_exclusively()\",\n        \"eliminate_redundant_code_and_files()\",\n        \"optimize_project_structure_for_simplicity()\",\n        \"crosscheck_coding_style_and_architecture()\",\n        \"validate_comprehension_of_system_logic()\",\n        \"generate_summary_report()\"\n      ],\n      \"constraints\": [\n        \"preserve_existing_code_style()\",\n        \"maintain_architectural_paradigm()\",\n        \"forbid_arbitrary_or_blind_changes()\",\n        \"disallow_bloat_or_verbosity()\",\n        \"enforce_only_uv_dependency_management()\",\n        \"require_complete_comprehension_before_modification()\"\n      ],\n      \"requirements\": [\n        \"resultant_project_must_be_uv_centric()\",\n        \"output_structure_is_minimalist_and_bloat_free()\",\n        \"full compatibility with existing logic and design\",\n        \"detailed, actionable summary report in structured format\"\n      ],\n      \"output\": {\n        \"refactored_project:str\",\n        \"summary_report:dict\"\n      }\n    }\n  }\n}",
        }
      }
    }