  {
  "initial_prompt": "### File Structure\n\n```\n├── 01_foundation.md\n├── 02_context.md\n├── 03_patterns.md\n├── 04_tech.md\n├── 05_activity.md\n├── 06_progress.md\n├── 07_tasks.md\n└── 08_objective.md\n```\n\n---\n\n#### `01_foundation.md`\n\n```markdown\n    ## Distilled Highlights\n    - Custom Windows context menu system built on Nilesoft Shell\n    - Purpose: Enhance Windows file explorer with efficient context actions\n    - Principles: Modularity, consistency, and extensibility\n    \n    # 01_foundation.md\n    \n    ## Core Mission\n    The Nilesoft Shell context menu system aims to significantly enhance Windows file explorer with a powerful, customizable context menu framework that improves workflow efficiency and user experience.\n    \n    ## Vision\n    Create a comprehensive context menu system that:\n    - Provides quick access to frequently used applications and utilities\n    - Standardizes menu organization and appearance\n    - Enables rapid file/folder operations through contextual commands\n    - Scales elegantly with minimal maintenance overhead\n    \n    ## Values\n    - **Efficiency**: Minimize clicks and navigation time\n    - **Clarity**: Intuitive menu organization with consistent naming and grouping\n    - **Extensibility**: Easily add new functionality without restructuring\n    - **Reliability**: Stable operation without conflicts or performance impact\n    \n    ## Strategic Goals\n    1. Standardize application launching from any context\n    2. Optimize file management operations\n    3. Group related functionality logically\n    4. Leverage NSS scripting for advanced automation\n    5. Maintain backward compatibility with Windows shell\n```\n\n---\n\n#### `02_context.md`\n\n```markdown\n    ## Distilled Highlights\n    - Windows context menus often become cluttered and inconsistent\n    - Users need quick access to applications and file operations\n    - Nilesoft Shell provides programmatic control over Windows context menus\n    \n    # 02_context.md\n    \n    ## Problem Space\n    \n    ### Current Limitations\n    - Default Windows context menus lack organization and customization options\n    - Third-party applications inject entries haphazardly, creating menu bloat\n    - No standard interface for managing context menu appearance and behavior\n    - Limited ability to group related actions or create conditional menus\n    \n    ### User Needs\n    - Quick access to frequently used applications from any file/folder context\n    - Consistent organization of menu items across different file types\n    - Visual distinction between types of operations (system, applications, custom scripts)\n    - Ability to conditionally show/hide menu options based on file attributes\n    - Simplified access to system utilities and administrative functions\n    \n    ## Stakeholders\n    - **End Users**: Individuals seeking workflow efficiency improvements\n    - **System Administrators**: Managing standard configurations across machines\n    - **Developers**: Extending functionality through Nilesoft Shell scripts (NSS)\n    - **Windows Environment**: Integration with existing shell infrastructure\n    \n    ## External Constraints\n    - Windows context menu system architecture and limitations\n    - Nilesoft Shell version compatibility and feature set\n    - Need for backward compatibility with existing Windows functionality\n    - Performance impact considerations for menu rendering and operations\n    - Security considerations for script execution and application launching\n    \n    ## Key Success Metrics\n    - Reduced time to access applications and perform file operations\n    - Improved menu organization and visual clarity\n    - Extensibility without code duplication or structure compromise\n    - Minimal impact on system performance\n    - Reliability across Windows versions and configurations\n```\n\n---\n\n#### `03_patterns.md`\n\n```markdown\n    ## Distilled Highlights\n    - Hierarchical organization: _init → variables → items → groups → menus → contexts\n    - NSS files are modular with single-responsibility principle\n    - Items represent individual menu entries; groups combine related items\n    - Contexts determine when and where menus appear\n    \n    # 03_patterns.md\n    \n    ## System Architecture\n    \n    ### Core Components Hierarchy\n    1. **Initialization (_1_init/)**: Sets up constants, configurations, and base environment\n    2. **Variables (_2_variables/)**: Defines reusable values and settings\n    3. **Items (_3_items/)**: Individual menu entries (commands, applications, actions)\n    4. **Groups (_4_groups/)**: Logical collections of related items\n    5. **Menus (_5_menus/)**: Structured arrangements of items and groups\n    6. **Contexts (_6_contexts/)**: Rules determining when and where menus appear\n    \n    ### Design Patterns\n    \n    #### Modular Composition\n    - Each NSS file has a single responsibility\n    - Components are composed through inclusion and reference\n    - Changes to one component minimally impact others\n    \n    #### Naming Conventions\n    - File prefixes indicate component type (`itm_` for items, etc.)\n    - Consistent categorization (`app_`, `action_`, `sys_` prefixes)\n    - Descriptive suffixes for variant behavior\n    \n    #### Conditional Visibility\n    - Menu items show/hide based on context rules\n    - Rules can evaluate file types, locations, system state\n    - Compound conditions for precise targeting\n    \n    #### Icon Standardization\n    - Consistent icon usage across similar functions\n    - Icon mapping for visual categorization\n    - Fallback icons for compatibility\n    \n    ## Component Relationships\n    \n    ```mermaid\n    graph TD\n        Init[_1_init] --> Vars[_2_variables]\n        Vars --> Items[_3_items]\n        Items --> Groups[_4_groups]\n        Groups --> Menus[_5_menus]\n        Menus --> Contexts[_6_contexts]\n        \n        subgraph \"Item Types\"\n            AppItems[Application Launchers]\n            ActionItems[File Actions]\n            SystemItems[System Operations]\n        end\n        \n        Items --- AppItems\n        Items --- ActionItems\n        Items --- SystemItems\n    ```\n    \n    ## Schema Patterns\n    \n    ### Item Schema\n    ```\n    item {\n      name: \"item_name\"\n      icon: \"icon_path_or_resource\"\n      command: \"command_to_execute\"\n      visibility: \"condition_expression\"\n    }\n    ```\n    \n    ### Group Schema\n    ```\n    group {\n      name: \"group_name\"\n      icon: \"group_icon\"\n      items: [\n        // references to items or inline definitions\n      ]\n    }\n    ```\n    \n    ### Menu Schema\n    ```\n    menu {\n      name: \"menu_name\"\n      items: [\n        // groups and/or individual items\n      ]\n      contexts: [\n        // when/where this menu appears\n      ]\n    }\n    ```\n    \n    ## Pattern Evolution Strategy\n    - Maintain backward compatibility when adding new patterns\n    - Document pattern changes in corresponding memory bank files\n    - Test pattern modifications across different file contexts\n    - Prefer extending existing patterns over creating new ones\n```\n\n---\n\n#### `04_tech.md`\n\n```markdown\n    ## Distilled Highlights\n    - Built on Nilesoft Shell framework for Windows context menu customization\n    - NSS scripting language for defining menu items and behavior\n    - Modular file organization in _1_init through _6_contexts directories\n    - Supporting resources include PNG/SVG icons and batch scripts\n    \n    # 04_tech.md\n    \n    ## Core Technologies\n    \n    ### Nilesoft Shell\n    - **Version**: Shell v1.9+ (current observed version: v1.9.15)\n    - **Purpose**: Framework for customizing Windows Explorer context menus\n    - **Architecture**: Shell extension that intercepts and modifies Windows context menu requests\n    - **Configuration**: NSS scripts compiled and loaded by Shell engine\n    \n    ### NSS Scripting\n    - **Language**: Nilesoft Shell Script (NSS)\n    - **Syntax**: C-like with specialized directives for menu definition\n    - **Scope**: Declarative definitions with procedural capabilities\n    - **Compilation**: Scripts are parsed and compiled by the Shell engine at runtime\n    \n    ## Infrastructure\n    \n    ### Directory Structure\n    ```\n    exe/\n    ├── NSS/\n    │   ├── _1_init/        # Initialization, constants, configuration\n    │   ├── _2_variables/   # Variable definitions for reuse\n    │   ├── _3_items/       # Individual menu items\n    │   ├── _4_groups/      # Logical groupings of items\n    │   ├── _5_menus/       # Menu structures and layouts\n    │   └── _6_contexts/    # Context rules for menu visibility\n    ├── LIB/\n    │   ├── bat/            # Batch scripts for utilities\n    │   ├── png/            # PNG icons for menu items\n    │   └── svg/            # SVG icons for menu items\n    └── shell.nss           # Main entry point for NSS configuration\n    ```\n    \n    ### Integration Points\n    - Windows Explorer shell extension\n    - Windows Registry for registration and configuration\n    - File system for icon and resource access\n    - Command line for application launching\n    - COM/Shell interfaces for advanced functionality\n    \n    ## Dependencies\n    \n    ### External Resources\n    - Icon files (PNG, SVG) for menu item visualization\n    - Batch files for extended functionality\n    - Executable paths for application launching\n    - System shell commands for file operations\n    \n    ### Development Tools\n    - Resource editors for icon management\n    - Shell extension management utilities\n    - Text editors with NSS syntax support\n    \n    ## Technical Constraints\n    \n    ### Performance Considerations\n    - Context menu load time impact\n    - Memory footprint of loaded NSS scripts\n    - Efficiency of condition evaluation for visibility rules\n    \n    ### Compatibility\n    - Windows version support (10, 11)\n    - Interaction with other shell extensions\n    - Application path resolution across different environments\n    \n    ### Security\n    - Execution permissions for launched applications\n    - Script injection prevention\n    - Validation of external resource paths\n    \n    ## Technical Roadmap\n    - Ongoing migration to latest Nilesoft Shell features\n    - Standardization of icon resources\n    - Optimization of conditional visibility expressions\n    - Enhanced script modularity and reusability\n```\n\n---\n\n#### `05_activity.md`\n\n```markdown\n    ## Distilled Highlights\n    - Current focus on populating individual menu items (_3_items directory)\n    - Need to develop groups, menus, and contexts structure\n    - Application launchers are the most extensively implemented item type\n    - System action items have basic implementation\n    \n    # 05_activity.md\n    \n    ## Current Focus Areas\n    \n    ### Item Development\n    - Extensive collection of application launcher items (`itm_app_*.nss`) already implemented\n    - Basic system action items created (`itm_action_sys_*.nss`) \n    - Need to audit existing items for consistency in naming and functionality\n    - Opportunity to standardize icon usage across similar item types\n    \n    ### Structural Components\n    - Groups (`_4_groups/`) directory needs population to organize related items\n    - Menus (`_5_menus/`) require definition to establish menu layouts and hierarchy\n    - Contexts (`_6_contexts/`) need implementation to control when menus appear\n    \n    ### Technical Infrastructure\n    - Ensure initialization files in `_1_init/` properly set up the environment\n    - Review variables in `_2_variables/` for completeness and usefulness\n    - Validate resource paths and icon references\n    \n    ## In-Progress Work\n    \n    ### Application Launcher Standardization\n    - Standardizing application launcher items with consistent:\n      - Command parameter formatting\n      - Icon selection and fallbacks\n      - Visibility conditions\n      - Naming conventions\n    \n    ### System Functionality Integration\n    - Integrating core Windows functionality through specialized items\n    - Creating consistent approach for system utility access\n    - Implementing common file operations\n    \n    ### Organization Strategy\n    - Developing logical grouping strategy for related items\n    - Planning menu hierarchy based on frequency of use and context\n    - Establishing visibility rules based on file types and locations\n    \n    ## Technical Decisions\n    \n    ### Item Implementation Pattern\n    ```nss\n    item {\n      name: \"Application Name\"\n      icon: \"path/to/icon.png\"\n      command: \"path/to/executable.exe [parameters]\"\n      \n      // Optional visibility condition\n      visibility: [condition expression]\n    }\n    ```\n    \n    ### Resource Path Strategy\n    - Using relative paths for icons and resources where possible\n    - Standardizing on Shell-compatible path syntax\n    - Planning for fallback icons when primary resources unavailable\n    \n    ### Menu Appearance Decisions\n    - Consistent separators between logical groups\n    - Icon alignment and sizing standardization\n    - Text formatting and capitalization rules\n```\n\n---\n\n#### `06_progress.md`\n\n```markdown\n    ## Distilled Highlights\n    - Directory structure and modular approach established\n    - Extensive collection of application launcher items implemented\n    - Basic system action items created\n    - Need to develop grouping and context structures\n    \n    # 06_progress.md\n    \n    ## State of the Build\n    \n    ### Completed Components\n    - ✅ Overall directory structure established for modular organization\n    - ✅ Basic initialization structure in `_1_init/` directory\n    - ✅ Extensive collection of application launcher items (~70 items)\n    - ✅ Core system action items for basic operations\n    - ✅ Resource libraries for icons and batch utilities\n    \n    ### In Progress\n    - 🔄 Variable standardization in `_2_variables/` directory\n    - 🔄 Audit of existing items for consistency and effectiveness\n    - 🔄 Documentation of item patterns and best practices\n    - 🔄 Standardization of icon usage and resource paths\n    \n    ### Not Started\n    - ⏳ Population of `_4_groups/` directory to organize items\n    - ⏳ Definition of menu structures in `_5_menus/` directory\n    - ⏳ Implementation of context rules in `_6_contexts/` directory\n    - ⏳ Comprehensive testing across different file types\n    \n    ## Milestones\n    \n    ### Milestone 1: Foundation (COMPLETED)\n    - ✓ Establish directory structure\n    - ✓ Implement basic initialization\n    - ✓ Create core application launcher items\n    - ✓ Set up resource libraries\n    \n    ### Milestone 2: Structure (IN PROGRESS)\n    - ✓ Standardize item implementation patterns\n    - ✓ Complete system action items\n    - 🔄 Document existing items and patterns\n    - ⏳ Organize items into logical groups\n    \n    ### Milestone 3: Integration (PLANNED)\n    - Define menu structures\n    - Implement context rules\n    - Create conditional visibility based on file types\n    - Optimize menu organization\n    \n    ### Milestone 4: Refinement (PLANNED)\n    - Comprehensive testing\n    - Performance optimization\n    - Icon standardization\n    - Documentation completion\n    \n    ## Current Blockers\n    - Need to develop a consistent strategy for grouping related items\n    - Lack of defined context rules for when menus should appear\n    - Incomplete documentation of existing patterns and structures\n    \n    ## Recent Updates\n    - Added multiple application launcher items for common utilities\n    - Created system action items for file operations\n    - Established resource libraries for icons and batch scripts\n    - Documented NSS patterns and implementation strategies\n    \n    ## Next Actions\n    1. Complete the audit of existing items for consistency\n    2. Develop a logical grouping strategy for items\n    3. Begin populating the `_4_groups/` directory\n    4. Create a menu structure plan for the `_5_menus/` directory\n    5. Establish basic context rules in the `_6_contexts/` directory\n```\n\n---\n\n#### `07_tasks.md`\n\n```markdown\n    ## Distilled Highlights\n    - Organize existing items into logical groups\n    - Develop menu structures based on usage patterns\n    - Implement context rules for menu visibility\n    - Create documentation for future item development\n    \n    # 07_tasks.md\n    \n    ## High Priority Tasks\n    \n    ### Group Structure Development\n    - [ ] **Create Application Groups**\n      - Create `grp_apps_dev.nss` for development tools\n      - Create `grp_apps_media.nss` for media applications\n      - Create `grp_apps_system.nss` for system utilities\n      - Create `grp_apps_office.nss` for productivity applications\n      - *Owner: System developer*\n      - *Justification: Organize the extensive list of application items into logical groups*\n    \n    - [ ] **Create Action Groups**\n      - Create `grp_actions_file.nss` for file operations\n      - Create `grp_actions_folder.nss` for folder operations\n      - Create `grp_actions_system.nss` for system operations\n      - *Owner: System developer*\n      - *Justification: Group related actions for better menu organization*\n    \n    ### Menu Structure Implementation\n    - [ ] **Design Core Menus**\n      - Create `menu_applications.nss` with application groups\n      - Create `menu_actions.nss` with action groups\n      - Create `menu_system.nss` with system utilities\n      - *Owner: System developer*\n      - *Justification: Establish logical menu structure for better user experience*\n    \n    - [ ] **Implement Cascading Menus**\n      - Design depth strategy (max 2-3 levels deep)\n      - Implement parent-child relationships\n      - *Owner: System developer*\n      - *Justification: Organize complex item collections without cluttering main menu*\n    \n    ### Context Implementation\n    - [ ] **Define File Type Contexts**\n      - Create rules for different file extensions\n      - Implement special handling for executables, documents, media\n      - *Owner: System developer*\n      - *Justification: Show relevant options based on file type*\n    \n    - [ ] **Define Location Contexts**\n      - Create rules for desktop, drives, libraries\n      - Implement special handling for system folders\n      - *Owner: System developer*\n      - *Justification: Show relevant options based on location*\n    \n    ## Medium Priority Tasks\n    \n    ### Documentation\n    - [ ] **Document Group Patterns**\n      - Create templates for new groups\n      - Document naming conventions\n      - *Owner: Documentation specialist*\n      - *Justification: Ensure consistency in future development*\n    \n    - [ ] **Document Menu Structures**\n      - Create visual hierarchy diagrams\n      - Document menu organization principles\n      - *Owner: Documentation specialist*\n      - *Justification: Provide clear guidance for menu development*\n    \n    ### Quality Assurance\n    - [ ] **Audit Existing Items**\n      - Review naming consistency\n      - Validate command parameters\n      - Check icon references\n      - *Owner: QA specialist*\n      - *Justification: Ensure existing items follow standards*\n    \n    - [ ] **Test Across Environments**\n      - Verify functionality on Windows 10\n      - Verify functionality on Windows 11\n      - Test with various file types\n      - *Owner: QA specialist*\n      - *Justification: Ensure compatibility across environments*\n    \n    ## Low Priority Tasks\n    \n    ### Optimization\n    - [ ] **Review Performance**\n      - Profile menu load times\n      - Optimize condition evaluations\n      - *Owner: Performance specialist*\n      - *Justification: Ensure menus load quickly*\n    \n    - [ ] **Icon Standardization**\n      - Create consistent icon set\n      - Implement fallback strategy\n      - *Owner: Design specialist*\n      - *Justification: Visual consistency across menus*\n    \n    ## Dependencies\n    - Group creation depends on completion of item audit\n    - Menu implementation depends on group creation\n    - Context rules depend on menu implementation\n    - Testing depends on all implementation tasks\n    \n    ## Task Assignment Strategy\n    - Focus on high-priority tasks first\n    - Complete related tasks in sequence (items → groups → menus → contexts)\n    - Document as you go to maintain knowledge base\n```\n\n---\n\n#### `08_objective.md`\n\n```markdown\n    ## Distilled Highlights\n    - Create a complete, modular context menu system for Windows Explorer\n    - Immediate focus: Group existing items and implement menu structures\n    - Success measured by efficiency, organization, and extensibility\n    - Target completion of Milestone 3 (Integration) by next development cycle\n    \n    # 08_objective.md\n    \n    ## Primary Objective\n    Create a comprehensive Windows context menu system that enhances productivity through logically organized, easily accessible commands and applications, using a modular architecture that ensures maintainability and extensibility.\n    \n    ## Success Criteria\n    The context menu system will be considered successful when it:\n    \n    1. **Provides Efficient Access**\n       - Reduces clicks needed to access common applications\n       - Speeds up file operations through contextual commands\n       - Groups related functionality in intuitive categories\n    \n    2. **Maintains Logical Organization**\n       - Presents a consistent menu structure across different contexts\n       - Limits menu depth to prevent navigation complexity\n       - Uses visual cues (icons, separators) to distinguish item types\n    \n    3. **Ensures Extensibility**\n       - Follows modular design patterns for easy additions\n       - Maintains clear separation between components\n       - Documents patterns for consistent future development\n    \n    4. **Performs Reliably**\n       - Loads quickly without noticeable delay\n       - Works consistently across Windows versions\n       - Avoids conflicts with other shell extensions\n    \n    ## Immediate Focus\n    The current development cycle focuses on:\n    \n    1. **Component Organization**\n       - Grouping existing items into logical collections\n       - Implementing menu structures to organize these groups\n       - Defining context rules to control when menus appear\n    \n    2. **System Completion**\n       - Filling gaps in the current directory structure\n       - Completing the chain from items to groups to menus to contexts\n       - Documenting component relationships and dependencies\n    \n    3. **Usability Enhancement**\n       - Standardizing visual appearance across menu items\n       - Ensuring consistent behavior across different file types\n       - Optimizing menu depth and organization for quick access\n    \n    ## Alignment with Vision\n    This objective directly supports the project's vision by:\n    - Enhancing Windows Explorer with powerful, customizable menus\n    - Standardizing menu organization for improved clarity\n    - Enabling quick access to applications and utilities\n    - Creating a scalable system that can grow without restructuring\n    \n    ## Definition of \"Done\"\n    For the current phase, \"done\" means:\n    \n    1. All existing items organized into appropriate groups\n    2. Core menu structures defined and implemented\n    3. Basic context rules established for different file types and locations\n    4. Documentation updated to reflect the current system state\n    5. At least basic testing completed across different environments\n    \n    ## Timeline Target\n    Complete current objectives (Milestone 3: Integration) within the next development cycle, setting the foundation for the refinement phase that follows.\n```",
  "sequence_id": "0122:a|0123:a|0310|0123:a|0121|0121:a|0201",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "### File Structure\n\n```\n├── 01_foundation.md\n├── 02_context.md\n├── 03_patterns.md\n├── 04_tech.md\n├── 05_activity.md\n├── 06_progress.md\n├── 07_tasks.md\n└── 08_objective.md\n```\n\n---\n\n#### `01_foundation.md`\n\n```markdown\n    ## Distilled Highlights\n    - Custom Windows context menu system built on Nilesoft Shell\n    - Purpose: Enhance Windows file explorer with efficient context actions\n    - Principles: Modularity, consistency, and extensibility\n    \n    # 01_foundation.md\n    \n    ## Core Mission\n    The Nilesoft Shell context menu system aims to significantly enhance Windows file explorer with a powerful, customizable context menu framework that improves workflow efficiency and user experience.\n    \n    ## Vision\n    Create a comprehensive context menu system that:\n    - Provides quick access to frequently used applications and utilities\n    - Standardizes menu organization and appearance\n    - Enables rapid file/folder operations through contextual commands\n    - Scales elegantly with minimal maintenance overhead\n    \n    ## Values\n    - **Efficiency**: Minimize clicks and navigation time\n    - **Clarity**: Intuitive menu organization with consistent naming and grouping\n    - **Extensibility**: Easily add new functionality without restructuring\n    - **Reliability**: Stable operation without conflicts or performance impact\n    \n    ## Strategic Goals\n    1. Standardize application launching from any context\n    2. Optimize file management operations\n    3. Group related functionality logically\n    4. Leverage NSS scripting for advanced automation\n    5. Maintain backward compatibility with Windows shell\n```\n\n---\n\n#### `02_context.md`\n\n```markdown\n    ## Distilled Highlights\n    - Windows context menus often become cluttered and inconsistent\n    - Users need quick access to applications and file operations\n    - Nilesoft Shell provides programmatic control over Windows context menus\n    \n    # 02_context.md\n    \n    ## Problem Space\n    \n    ### Current Limitations\n    - Default Windows context menus lack organization and customization options\n    - Third-party applications inject entries haphazardly, creating menu bloat\n    - No standard interface for managing context menu appearance and behavior\n    - Limited ability to group related actions or create conditional menus\n    \n    ### User Needs\n    - Quick access to frequently used applications from any file/folder context\n    - Consistent organization of menu items across different file types\n    - Visual distinction between types of operations (system, applications, custom scripts)\n    - Ability to conditionally show/hide menu options based on file attributes\n    - Simplified access to system utilities and administrative functions\n    \n    ## Stakeholders\n    - **End Users**: Individuals seeking workflow efficiency improvements\n    - **System Administrators**: Managing standard configurations across machines\n    - **Developers**: Extending functionality through Nilesoft Shell scripts (NSS)\n    - **Windows Environment**: Integration with existing shell infrastructure\n    \n    ## External Constraints\n    - Windows context menu system architecture and limitations\n    - Nilesoft Shell version compatibility and feature set\n    - Need for backward compatibility with existing Windows functionality\n    - Performance impact considerations for menu rendering and operations\n    - Security considerations for script execution and application launching\n    \n    ## Key Success Metrics\n    - Reduced time to access applications and perform file operations\n    - Improved menu organization and visual clarity\n    - Extensibility without code duplication or structure compromise\n    - Minimal impact on system performance\n    - Reliability across Windows versions and configurations\n```\n\n---\n\n#### `03_patterns.md`\n\n```markdown\n    ## Distilled Highlights\n    - Hierarchical organization: _init → variables → items → groups → menus → contexts\n    - NSS files are modular with single-responsibility principle\n    - Items represent individual menu entries; groups combine related items\n    - Contexts determine when and where menus appear\n    \n    # 03_patterns.md\n    \n    ## System Architecture\n    \n    ### Core Components Hierarchy\n    1. **Initialization (_1_init/)**: Sets up constants, configurations, and base environment\n    2. **Variables (_2_variables/)**: Defines reusable values and settings\n    3. **Items (_3_items/)**: Individual menu entries (commands, applications, actions)\n    4. **Groups (_4_groups/)**: Logical collections of related items\n    5. **Menus (_5_menus/)**: Structured arrangements of items and groups\n    6. **Contexts (_6_contexts/)**: Rules determining when and where menus appear\n    \n    ### Design Patterns\n    \n    #### Modular Composition\n    - Each NSS file has a single responsibility\n    - Components are composed through inclusion and reference\n    - Changes to one component minimally impact others\n    \n    #### Naming Conventions\n    - File prefixes indicate component type (`itm_` for items, etc.)\n    - Consistent categorization (`app_`, `action_`, `sys_` prefixes)\n    - Descriptive suffixes for variant behavior\n    \n    #### Conditional Visibility\n    - Menu items show/hide based on context rules\n    - Rules can evaluate file types, locations, system state\n    - Compound conditions for precise targeting\n    \n    #### Icon Standardization\n    - Consistent icon usage across similar functions\n    - Icon mapping for visual categorization\n    - Fallback icons for compatibility\n    \n    ## Component Relationships\n    \n    ```mermaid\n    graph TD\n        Init[_1_init] --> Vars[_2_variables]\n        Vars --> Items[_3_items]\n        Items --> Groups[_4_groups]\n        Groups --> Menus[_5_menus]\n        Menus --> Contexts[_6_contexts]\n        \n        subgraph \"Item Types\"\n            AppItems[Application Launchers]\n            ActionItems[File Actions]\n            SystemItems[System Operations]\n        end\n        \n        Items --- AppItems\n        Items --- ActionItems\n        Items --- SystemItems\n    ```\n    \n    ## Schema Patterns\n    \n    ### Item Schema\n    ```\n    item {\n      name: \"item_name\"\n      icon: \"icon_path_or_resource\"\n      command: \"command_to_execute\"\n      visibility: \"condition_expression\"\n    }\n    ```\n    \n    ### Group Schema\n    ```\n    group {\n      name: \"group_name\"\n      icon: \"group_icon\"\n      items: [\n        // references to items or inline definitions\n      ]\n    }\n    ```\n    \n    ### Menu Schema\n    ```\n    menu {\n      name: \"menu_name\"\n      items: [\n        // groups and/or individual items\n      ]\n      contexts: [\n        // when/where this menu appears\n      ]\n    }\n    ```\n    \n    ## Pattern Evolution Strategy\n    - Maintain backward compatibility when adding new patterns\n    - Document pattern changes in corresponding memory bank files\n    - Test pattern modifications across different file contexts\n    - Prefer extending existing patterns over creating new ones\n```\n\n---\n\n#### `04_tech.md`\n\n```markdown\n    ## Distilled Highlights\n    - Built on Nilesoft Shell framework for Windows context menu customization\n    - NSS scripting language for defining menu items and behavior\n    - Modular file organization in _1_init through _6_contexts directories\n    - Supporting resources include PNG/SVG icons and batch scripts\n    \n    # 04_tech.md\n    \n    ## Core Technologies\n    \n    ### Nilesoft Shell\n    - **Version**: Shell v1.9+ (current observed version: v1.9.15)\n    - **Purpose**: Framework for customizing Windows Explorer context menus\n    - **Architecture**: Shell extension that intercepts and modifies Windows context menu requests\n    - **Configuration**: NSS scripts compiled and loaded by Shell engine\n    \n    ### NSS Scripting\n    - **Language**: Nilesoft Shell Script (NSS)\n    - **Syntax**: C-like with specialized directives for menu definition\n    - **Scope**: Declarative definitions with procedural capabilities\n    - **Compilation**: Scripts are parsed and compiled by the Shell engine at runtime\n    \n    ## Infrastructure\n    \n    ### Directory Structure\n    ```\n    exe/\n    ├── NSS/\n    │   ├── _1_init/        # Initialization, constants, configuration\n    │   ├── _2_variables/   # Variable definitions for reuse\n    │   ├── _3_items/       # Individual menu items\n    │   ├── _4_groups/      # Logical groupings of items\n    │   ├── _5_menus/       # Menu structures and layouts\n    │   └── _6_contexts/    # Context rules for menu visibility\n    ├── LIB/\n    │   ├── bat/            # Batch scripts for utilities\n    │   ├── png/            # PNG icons for menu items\n    │   └── svg/            # SVG icons for menu items\n    └── shell.nss           # Main entry point for NSS configuration\n    ```\n    \n    ### Integration Points\n    - Windows Explorer shell extension\n    - Windows Registry for registration and configuration\n    - File system for icon and resource access\n    - Command line for application launching\n    - COM/Shell interfaces for advanced functionality\n    \n    ## Dependencies\n    \n    ### External Resources\n    - Icon files (PNG, SVG) for menu item visualization\n    - Batch files for extended functionality\n    - Executable paths for application launching\n    - System shell commands for file operations\n    \n    ### Development Tools\n    - Resource editors for icon management\n    - Shell extension management utilities\n    - Text editors with NSS syntax support\n    \n    ## Technical Constraints\n    \n    ### Performance Considerations\n    - Context menu load time impact\n    - Memory footprint of loaded NSS scripts\n    - Efficiency of condition evaluation for visibility rules\n    \n    ### Compatibility\n    - Windows version support (10, 11)\n    - Interaction with other shell extensions\n    - Application path resolution across different environments\n    \n    ### Security\n    - Execution permissions for launched applications\n    - Script injection prevention\n    - Validation of external resource paths\n    \n    ## Technical Roadmap\n    - Ongoing migration to latest Nilesoft Shell features\n    - Standardization of icon resources\n    - Optimization of conditional visibility expressions\n    - Enhanced script modularity and reusability\n```\n\n---\n\n#### `05_activity.md`\n\n```markdown\n    ## Distilled Highlights\n    - Current focus on populating individual menu items (_3_items directory)\n    - Need to develop groups, menus, and contexts structure\n    - Application launchers are the most extensively implemented item type\n    - System action items have basic implementation\n    \n    # 05_activity.md\n    \n    ## Current Focus Areas\n    \n    ### Item Development\n    - Extensive collection of application launcher items (`itm_app_*.nss`) already implemented\n    - Basic system action items created (`itm_action_sys_*.nss`) \n    - Need to audit existing items for consistency in naming and functionality\n    - Opportunity to standardize icon usage across similar item types\n    \n    ### Structural Components\n    - Groups (`_4_groups/`) directory needs population to organize related items\n    - Menus (`_5_menus/`) require definition to establish menu layouts and hierarchy\n    - Contexts (`_6_contexts/`) need implementation to control when menus appear\n    \n    ### Technical Infrastructure\n    - Ensure initialization files in `_1_init/` properly set up the environment\n    - Review variables in `_2_variables/` for completeness and usefulness\n    - Validate resource paths and icon references\n    \n    ## In-Progress Work\n    \n    ### Application Launcher Standardization\n    - Standardizing application launcher items with consistent:\n      - Command parameter formatting\n      - Icon selection and fallbacks\n      - Visibility conditions\n      - Naming conventions\n    \n    ### System Functionality Integration\n    - Integrating core Windows functionality through specialized items\n    - Creating consistent approach for system utility access\n    - Implementing common file operations\n    \n    ### Organization Strategy\n    - Developing logical grouping strategy for related items\n    - Planning menu hierarchy based on frequency of use and context\n    - Establishing visibility rules based on file types and locations\n    \n    ## Technical Decisions\n    \n    ### Item Implementation Pattern\n    ```nss\n    item {\n      name: \"Application Name\"\n      icon: \"path/to/icon.png\"\n      command: \"path/to/executable.exe [parameters]\"\n      \n      // Optional visibility condition\n      visibility: [condition expression]\n    }\n    ```\n    \n    ### Resource Path Strategy\n    - Using relative paths for icons and resources where possible\n    - Standardizing on Shell-compatible path syntax\n    - Planning for fallback icons when primary resources unavailable\n    \n    ### Menu Appearance Decisions\n    - Consistent separators between logical groups\n    - Icon alignment and sizing standardization\n    - Text formatting and capitalization rules\n```\n\n---\n\n#### `06_progress.md`\n\n```markdown\n    ## Distilled Highlights\n    - Directory structure and modular approach established\n    - Extensive collection of application launcher items implemented\n    - Basic system action items created\n    - Need to develop grouping and context structures\n    \n    # 06_progress.md\n    \n    ## State of the Build\n    \n    ### Completed Components\n    - ✅ Overall directory structure established for modular organization\n    - ✅ Basic initialization structure in `_1_init/` directory\n    - ✅ Extensive collection of application launcher items (~70 items)\n    - ✅ Core system action items for basic operations\n    - ✅ Resource libraries for icons and batch utilities\n    \n    ### In Progress\n    - 🔄 Variable standardization in `_2_variables/` directory\n    - 🔄 Audit of existing items for consistency and effectiveness\n    - 🔄 Documentation of item patterns and best practices\n    - 🔄 Standardization of icon usage and resource paths\n    \n    ### Not Started\n    - ⏳ Population of `_4_groups/` directory to organize items\n    - ⏳ Definition of menu structures in `_5_menus/` directory\n    - ⏳ Implementation of context rules in `_6_contexts/` directory\n    - ⏳ Comprehensive testing across different file types\n    \n    ## Milestones\n    \n    ### Milestone 1: Foundation (COMPLETED)\n    - ✓ Establish directory structure\n    - ✓ Implement basic initialization\n    - ✓ Create core application launcher items\n    - ✓ Set up resource libraries\n    \n    ### Milestone 2: Structure (IN PROGRESS)\n    - ✓ Standardize item implementation patterns\n    - ✓ Complete system action items\n    - 🔄 Document existing items and patterns\n    - ⏳ Organize items into logical groups\n    \n    ### Milestone 3: Integration (PLANNED)\n    - Define menu structures\n    - Implement context rules\n    - Create conditional visibility based on file types\n    - Optimize menu organization\n    \n    ### Milestone 4: Refinement (PLANNED)\n    - Comprehensive testing\n    - Performance optimization\n    - Icon standardization\n    - Documentation completion\n    \n    ## Current Blockers\n    - Need to develop a consistent strategy for grouping related items\n    - Lack of defined context rules for when menus should appear\n    - Incomplete documentation of existing patterns and structures\n    \n    ## Recent Updates\n    - Added multiple application launcher items for common utilities\n    - Created system action items for file operations\n    - Established resource libraries for icons and batch scripts\n    - Documented NSS patterns and implementation strategies\n    \n    ## Next Actions\n    1. Complete the audit of existing items for consistency\n    2. Develop a logical grouping strategy for items\n    3. Begin populating the `_4_groups/` directory\n    4. Create a menu structure plan for the `_5_menus/` directory\n    5. Establish basic context rules in the `_6_contexts/` directory\n```\n\n---\n\n#### `07_tasks.md`\n\n```markdown\n    ## Distilled Highlights\n    - Organize existing items into logical groups\n    - Develop menu structures based on usage patterns\n    - Implement context rules for menu visibility\n    - Create documentation for future item development\n    \n    # 07_tasks.md\n    \n    ## High Priority Tasks\n    \n    ### Group Structure Development\n    - [ ] **Create Application Groups**\n      - Create `grp_apps_dev.nss` for development tools\n      - Create `grp_apps_media.nss` for media applications\n      - Create `grp_apps_system.nss` for system utilities\n      - Create `grp_apps_office.nss` for productivity applications\n      - *Owner: System developer*\n      - *Justification: Organize the extensive list of application items into logical groups*\n    \n    - [ ] **Create Action Groups**\n      - Create `grp_actions_file.nss` for file operations\n      - Create `grp_actions_folder.nss` for folder operations\n      - Create `grp_actions_system.nss` for system operations\n      - *Owner: System developer*\n      - *Justification: Group related actions for better menu organization*\n    \n    ### Menu Structure Implementation\n    - [ ] **Design Core Menus**\n      - Create `menu_applications.nss` with application groups\n      - Create `menu_actions.nss` with action groups\n      - Create `menu_system.nss` with system utilities\n      - *Owner: System developer*\n      - *Justification: Establish logical menu structure for better user experience*\n    \n    - [ ] **Implement Cascading Menus**\n      - Design depth strategy (max 2-3 levels deep)\n      - Implement parent-child relationships\n      - *Owner: System developer*\n      - *Justification: Organize complex item collections without cluttering main menu*\n    \n    ### Context Implementation\n    - [ ] **Define File Type Contexts**\n      - Create rules for different file extensions\n      - Implement special handling for executables, documents, media\n      - *Owner: System developer*\n      - *Justification: Show relevant options based on file type*\n    \n    - [ ] **Define Location Contexts**\n      - Create rules for desktop, drives, libraries\n      - Implement special handling for system folders\n      - *Owner: System developer*\n      - *Justification: Show relevant options based on location*\n    \n    ## Medium Priority Tasks\n    \n    ### Documentation\n    - [ ] **Document Group Patterns**\n      - Create templates for new groups\n      - Document naming conventions\n      - *Owner: Documentation specialist*\n      - *Justification: Ensure consistency in future development*\n    \n    - [ ] **Document Menu Structures**\n      - Create visual hierarchy diagrams\n      - Document menu organization principles\n      - *Owner: Documentation specialist*\n      - *Justification: Provide clear guidance for menu development*\n    \n    ### Quality Assurance\n    - [ ] **Audit Existing Items**\n      - Review naming consistency\n      - Validate command parameters\n      - Check icon references\n      - *Owner: QA specialist*\n      - *Justification: Ensure existing items follow standards*\n    \n    - [ ] **Test Across Environments**\n      - Verify functionality on Windows 10\n      - Verify functionality on Windows 11\n      - Test with various file types\n      - *Owner: QA specialist*\n      - *Justification: Ensure compatibility across environments*\n    \n    ## Low Priority Tasks\n    \n    ### Optimization\n    - [ ] **Review Performance**\n      - Profile menu load times\n      - Optimize condition evaluations\n      - *Owner: Performance specialist*\n      - *Justification: Ensure menus load quickly*\n    \n    - [ ] **Icon Standardization**\n      - Create consistent icon set\n      - Implement fallback strategy\n      - *Owner: Design specialist*\n      - *Justification: Visual consistency across menus*\n    \n    ## Dependencies\n    - Group creation depends on completion of item audit\n    - Menu implementation depends on group creation\n    - Context rules depend on menu implementation\n    - Testing depends on all implementation tasks\n    \n    ## Task Assignment Strategy\n    - Focus on high-priority tasks first\n    - Complete related tasks in sequence (items → groups → menus → contexts)\n    - Document as you go to maintain knowledge base\n```\n\n---\n\n#### `08_objective.md`\n\n```markdown\n    ## Distilled Highlights\n    - Create a complete, modular context menu system for Windows Explorer\n    - Immediate focus: Group existing items and implement menu structures\n    - Success measured by efficiency, organization, and extensibility\n    - Target completion of Milestone 3 (Integration) by next development cycle\n    \n    # 08_objective.md\n    \n    ## Primary Objective\n    Create a comprehensive Windows context menu system that enhances productivity through logically organized, easily accessible commands and applications, using a modular architecture that ensures maintainability and extensibility.\n    \n    ## Success Criteria\n    The context menu system will be considered successful when it:\n    \n    1. **Provides Efficient Access**\n       - Reduces clicks needed to access common applications\n       - Speeds up file operations through contextual commands\n       - Groups related functionality in intuitive categories\n    \n    2. **Maintains Logical Organization**\n       - Presents a consistent menu structure across different contexts\n       - Limits menu depth to prevent navigation complexity\n       - Uses visual cues (icons, separators) to distinguish item types\n    \n    3. **Ensures Extensibility**\n       - Follows modular design patterns for easy additions\n       - Maintains clear separation between components\n       - Documents patterns for consistent future development\n    \n    4. **Performs Reliably**\n       - Loads quickly without noticeable delay\n       - Works consistently across Windows versions\n       - Avoids conflicts with other shell extensions\n    \n    ## Immediate Focus\n    The current development cycle focuses on:\n    \n    1. **Component Organization**\n       - Grouping existing items into logical collections\n       - Implementing menu structures to organize these groups\n       - Defining context rules to control when menus appear\n    \n    2. **System Completion**\n       - Filling gaps in the current directory structure\n       - Completing the chain from items to groups to menus to contexts\n       - Documenting component relationships and dependencies\n    \n    3. **Usability Enhancement**\n       - Standardizing visual appearance across menu items\n       - Ensuring consistent behavior across different file types\n       - Optimizing menu depth and organization for quick access\n    \n    ## Alignment with Vision\n    This objective directly supports the project's vision by:\n    - Enhancing Windows Explorer with powerful, customizable menus\n    - Standardizing menu organization for improved clarity\n    - Enabling quick access to applications and utilities\n    - Creating a scalable system that can grow without restructuring\n    \n    ## Definition of \"Done\"\n    For the current phase, \"done\" means:\n    \n    1. All existing items organized into appropriate groups\n    2. Core menu structures defined and implemented\n    3. Basic context rules established for different file types and locations\n    4. Documentation updated to reflect the current system state\n    5. At least basic testing completed across different environments\n    \n    ## Timeline Target\n    Complete current objectives (Milestone 3: Integration) within the next development cycle, setting the foundation for the refinement phase that follows.\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": {\n    \"Role\": \"Synergic Windows Context Menu Architect\",\n    \"Unified Mission\": \"Design, implement, and standardize a modular, extensible, and performance-optimized Windows Explorer context menu system powered by Nilesoft Shell, ensuring efficient user workflows through logical organization, visual consistency, and adaptive contextual behaviors.\",\n    \"Amplified Process\": [\n      \"1. Extract foundational principles of modularity, clarity, extensibility, and efficiency from project vision, embedding them as guiding criteria in all architectural and implementation decisions.\",\n      \"2. Audit and standardize all existing context menu items for naming, icon usage, command formatting, and visibility conditions to ensure a coherent baseline.\",\n      \"3. Organize standardized items into logical, purpose-driven groups (e.g., by application type, system function, or operation domain), leveraging modular NSS file structures and single-responsibility pattern.\",\n      \"4. Define and implement menu structures based on established usage patterns and grouping logic, optimizing for shallow navigation depth (2-3 levels maximum), consistent visual cues (icons, separators), and intuitive hierarchy.\",\n      \"5. Develop and enforce sophisticated context rules that determine menu and group visibility based on file type, location, system state, and user role, greatly enhancing menu relevance and reducing cognitive load.\",\n      \"6. Ensure platform-wide compatibility, performance, and security compliance by adhering to Nilesoft Shell and Windows architectural constraints, optimizing resource references, and regular cross-version testing.\",\n      \"7. Create and maintain comprehensive, pattern-oriented documentation (covering group/menu creation, icon standards, naming, and visibility schemas) for ease of future extension by both developers and administrators.\",\n      \"8. Integrate continuous feedback: Routinely validate against key success metrics (speed, organization, extensibility, reliability) and iterate on grouping, menu, or context strategies as needed.\",\n      \"9. Synchronize progress with clear, milestone-based objectives: Complete logical item grouping, core menu structuring, and context rules (Milestone 3), ensuring a solid foundation for final refinement (Milestone 4).\"\n    ],\n    \"Synergic Constraints\": [\n      \"Maintain strict modular separation but ensure seamless composition between items, groups, menus, and contexts.\",\n      \"Preserve and amplify clarity, efficiency, and extensibility in every design and implementation step.\",\n      \"Design all components for bidirectional enhancement: groupings boost menu usability; menu structures clarify group purpose; context rules optimize group exposure.\",\n      \"Avoid structure or process decisions that could cause future extensibility or performance bottlenecks.\",\n      \"Eliminate any organizational, naming, or formatting inconsistencies that would conflict with maintainability or clarity.\",\n      \"Guarantee backward compatibility and smooth administrative scaling.\"\n    ],\n    \"Unified Output Specification\": {\n      \"Deliverables\": [\n        \"1. Audited and standardized item files with consistent icons, names, and command patterns.\",\n        \"2. Fully populated group and menu definition files reflecting logical application/action segmentation, with inline or referenced documentation for future maintainers.\",\n        \"3. Context definition files specifying detailed, rule-based menu visibility logic tailored to diverse file types and explorer locations.\",\n        \"4. Diagrams, templates, and hierarchical documentation articulating menu/group/item/context relationships and patterns.\",\n        \"5. Measured proof of improved access times, menu clarity, extensibility (verified through developer/tester onboarding), and reliability across supported Windows versions.\"\n      ],\n      \"Completion Criteria\": [\n        \"All context menu items organized and accessible through logical groups and menus.\",\n        \"Visibility rules enable context-sensitivity without redundant menu clutter.\",\n        \"Performance benchmarks met for menu load times and resource usage.\",\n        \"Documentation fully captures structure, logic, and pattern evolution for seamless future development.\",\n        \"System demonstrates reliable operation and extensibility in practical QA scenarios.\"\n      ]\n    },\n    \"Synergic Resonance Achievement\": \"Every operational step must both fulfill current organizational and architectural objectives and lay an extensible foundation for future evolution, ensuring all implemented structures naturally and reciprocally reinforce organizational clarity, modularity, and user efficiency.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"### File Structure\\n\\n```\\n├── 01_foundation.md\\n├── 02_context.md\\n├── 03_patterns.md\\n├── 04_tech.md\\n├── 05_activity.md\\n├── 06_progress.md\\n├── 07_tasks.md\\n└── 08_objective.md\\n```\\n\\n---\\n\\n#### `01_foundation.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Custom Windows context menu system built on Nilesoft Shell\\n    - Purpose: Enhance Windows file explorer with efficient context actions\\n    - Principles: Modularity, consistency, and extensibility\\n    \\n    # 01_foundation.md\\n    \\n    ## Core Mission\\n    The Nilesoft Shell context menu system aims to significantly enhance Windows file explorer with a powerful, customizable context menu framework that improves workflow efficiency and user experience.\\n    \\n    ## Vision\\n    Create a comprehensive context menu system that:\\n    - Provides quick access to frequently used applications and utilities\\n    - Standardizes menu organization and appearance\\n    - Enables rapid file/folder operations through contextual commands\\n    - Scales elegantly with minimal maintenance overhead\\n    \\n    ## Values\\n    - **Efficiency**: Minimize clicks and navigation time\\n    - **Clarity**: Intuitive menu organization with consistent naming and grouping\\n    - **Extensibility**: Easily add new functionality without restructuring\\n    - **Reliability**: Stable operation without conflicts or performance impact\\n    \\n    ## Strategic Goals\\n    1. Standardize application launching from any context\\n    2. Optimize file management operations\\n    3. Group related functionality logically\\n    4. Leverage NSS scripting for advanced automation\\n    5. Maintain backward compatibility with Windows shell\\n```\\n\\n---\\n\\n#### `02_context.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Windows context menus often become cluttered and inconsistent\\n    - Users need quick access to applications and file operations\\n    - Nilesoft Shell provides programmatic control over Windows context menus\\n    \\n    # 02_context.md\\n    \\n    ## Problem Space\\n    \\n    ### Current Limitations\\n    - Default Windows context menus lack organization and customization options\\n    - Third-party applications inject entries haphazardly, creating menu bloat\\n    - No standard interface for managing context menu appearance and behavior\\n    - Limited ability to group related actions or create conditional menus\\n    \\n    ### User Needs\\n    - Quick access to frequently used applications from any file/folder context\\n    - Consistent organization of menu items across different file types\\n    - Visual distinction between types of operations (system, applications, custom scripts)\\n    - Ability to conditionally show/hide menu options based on file attributes\\n    - Simplified access to system utilities and administrative functions\\n    \\n    ## Stakeholders\\n    - **End Users**: Individuals seeking workflow efficiency improvements\\n    - **System Administrators**: Managing standard configurations across machines\\n    - **Developers**: Extending functionality through Nilesoft Shell scripts (NSS)\\n    - **Windows Environment**: Integration with existing shell infrastructure\\n    \\n    ## External Constraints\\n    - Windows context menu system architecture and limitations\\n    - Nilesoft Shell version compatibility and feature set\\n    - Need for backward compatibility with existing Windows functionality\\n    - Performance impact considerations for menu rendering and operations\\n    - Security considerations for script execution and application launching\\n    \\n    ## Key Success Metrics\\n    - Reduced time to access applications and perform file operations\\n    - Improved menu organization and visual clarity\\n    - Extensibility without code duplication or structure compromise\\n    - Minimal impact on system performance\\n    - Reliability across Windows versions and configurations\\n```\\n\\n---\\n\\n#### `03_patterns.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Hierarchical organization: _init → variables → items → groups → menus → contexts\\n    - NSS files are modular with single-responsibility principle\\n    - Items represent individual menu entries; groups combine related items\\n    - Contexts determine when and where menus appear\\n    \\n    # 03_patterns.md\\n    \\n    ## System Architecture\\n    \\n    ### Core Components Hierarchy\\n    1. **Initialization (_1_init/)**: Sets up constants, configurations, and base environment\\n    2. **Variables (_2_variables/)**: Defines reusable values and settings\\n    3. **Items (_3_items/)**: Individual menu entries (commands, applications, actions)\\n    4. **Groups (_4_groups/)**: Logical collections of related items\\n    5. **Menus (_5_menus/)**: Structured arrangements of items and groups\\n    6. **Contexts (_6_contexts/)**: Rules determining when and where menus appear\\n    \\n    ### Design Patterns\\n    \\n    #### Modular Composition\\n    - Each NSS file has a single responsibility\\n    - Components are composed through inclusion and reference\\n    - Changes to one component minimally impact others\\n    \\n    #### Naming Conventions\\n    - File prefixes indicate component type (`itm_` for items, etc.)\\n    - Consistent categorization (`app_`, `action_`, `sys_` prefixes)\\n    - Descriptive suffixes for variant behavior\\n    \\n    #### Conditional Visibility\\n    - Menu items show/hide based on context rules\\n    - Rules can evaluate file types, locations, system state\\n    - Compound conditions for precise targeting\\n    \\n    #### Icon Standardization\\n    - Consistent icon usage across similar functions\\n    - Icon mapping for visual categorization\\n    - Fallback icons for compatibility\\n    \\n    ## Component Relationships\\n    \\n    ```mermaid\\n    graph TD\\n        Init[_1_init] --> Vars[_2_variables]\\n        Vars --> Items[_3_items]\\n        Items --> Groups[_4_groups]\\n        Groups --> Menus[_5_menus]\\n        Menus --> Contexts[_6_contexts]\\n        \\n        subgraph \\\"Item Types\\\"\\n            AppItems[Application Launchers]\\n            ActionItems[File Actions]\\n            SystemItems[System Operations]\\n        end\\n        \\n        Items --- AppItems\\n        Items --- ActionItems\\n        Items --- SystemItems\\n    ```\\n    \\n    ## Schema Patterns\\n    \\n    ### Item Schema\\n    ```\\n    item {\\n      name: \\\"item_name\\\"\\n      icon: \\\"icon_path_or_resource\\\"\\n      command: \\\"command_to_execute\\\"\\n      visibility: \\\"condition_expression\\\"\\n    }\\n    ```\\n    \\n    ### Group Schema\\n    ```\\n    group {\\n      name: \\\"group_name\\\"\\n      icon: \\\"group_icon\\\"\\n      items: [\\n        // references to items or inline definitions\\n      ]\\n    }\\n    ```\\n    \\n    ### Menu Schema\\n    ```\\n    menu {\\n      name: \\\"menu_name\\\"\\n      items: [\\n        // groups and/or individual items\\n      ]\\n      contexts: [\\n        // when/where this menu appears\\n      ]\\n    }\\n    ```\\n    \\n    ## Pattern Evolution Strategy\\n    - Maintain backward compatibility when adding new patterns\\n    - Document pattern changes in corresponding memory bank files\\n    - Test pattern modifications across different file contexts\\n    - Prefer extending existing patterns over creating new ones\\n```\\n\\n---\\n\\n#### `04_tech.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Built on Nilesoft Shell framework for Windows context menu customization\\n    - NSS scripting language for defining menu items and behavior\\n    - Modular file organization in _1_init through _6_contexts directories\\n    - Supporting resources include PNG/SVG icons and batch scripts\\n    \\n    # 04_tech.md\\n    \\n    ## Core Technologies\\n    \\n    ### Nilesoft Shell\\n    - **Version**: Shell v1.9+ (current observed version: v1.9.15)\\n    - **Purpose**: Framework for customizing Windows Explorer context menus\\n    - **Architecture**: Shell extension that intercepts and modifies Windows context menu requests\\n    - **Configuration**: NSS scripts compiled and loaded by Shell engine\\n    \\n    ### NSS Scripting\\n    - **Language**: Nilesoft Shell Script (NSS)\\n    - **Syntax**: C-like with specialized directives for menu definition\\n    - **Scope**: Declarative definitions with procedural capabilities\\n    - **Compilation**: Scripts are parsed and compiled by the Shell engine at runtime\\n    \\n    ## Infrastructure\\n    \\n    ### Directory Structure\\n    ```\\n    exe/\\n    ├── NSS/\\n    │   ├── _1_init/        # Initialization, constants, configuration\\n    │   ├── _2_variables/   # Variable definitions for reuse\\n    │   ├── _3_items/       # Individual menu items\\n    │   ├── _4_groups/      # Logical groupings of items\\n    │   ├── _5_menus/       # Menu structures and layouts\\n    │   └── _6_contexts/    # Context rules for menu visibility\\n    ├── LIB/\\n    │   ├── bat/            # Batch scripts for utilities\\n    │   ├── png/            # PNG icons for menu items\\n    │   └── svg/            # SVG icons for menu items\\n    └── shell.nss           # Main entry point for NSS configuration\\n    ```\\n    \\n    ### Integration Points\\n    - Windows Explorer shell extension\\n    - Windows Registry for registration and configuration\\n    - File system for icon and resource access\\n    - Command line for application launching\\n    - COM/Shell interfaces for advanced functionality\\n    \\n    ## Dependencies\\n    \\n    ### External Resources\\n    - Icon files (PNG, SVG) for menu item visualization\\n    - Batch files for extended functionality\\n    - Executable paths for application launching\\n    - System shell commands for file operations\\n    \\n    ### Development Tools\\n    - Resource editors for icon management\\n    - Shell extension management utilities\\n    - Text editors with NSS syntax support\\n    \\n    ## Technical Constraints\\n    \\n    ### Performance Considerations\\n    - Context menu load time impact\\n    - Memory footprint of loaded NSS scripts\\n    - Efficiency of condition evaluation for visibility rules\\n    \\n    ### Compatibility\\n    - Windows version support (10, 11)\\n    - Interaction with other shell extensions\\n    - Application path resolution across different environments\\n    \\n    ### Security\\n    - Execution permissions for launched applications\\n    - Script injection prevention\\n    - Validation of external resource paths\\n    \\n    ## Technical Roadmap\\n    - Ongoing migration to latest Nilesoft Shell features\\n    - Standardization of icon resources\\n    - Optimization of conditional visibility expressions\\n    - Enhanced script modularity and reusability\\n```\\n\\n---\\n\\n#### `05_activity.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Current focus on populating individual menu items (_3_items directory)\\n    - Need to develop groups, menus, and contexts structure\\n    - Application launchers are the most extensively implemented item type\\n    - System action items have basic implementation\\n    \\n    # 05_activity.md\\n    \\n    ## Current Focus Areas\\n    \\n    ### Item Development\\n    - Extensive collection of application launcher items (`itm_app_*.nss`) already implemented\\n    - Basic system action items created (`itm_action_sys_*.nss`) \\n    - Need to audit existing items for consistency in naming and functionality\\n    - Opportunity to standardize icon usage across similar item types\\n    \\n    ### Structural Components\\n    - Groups (`_4_groups/`) directory needs population to organize related items\\n    - Menus (`_5_menus/`) require definition to establish menu layouts and hierarchy\\n    - Contexts (`_6_contexts/`) need implementation to control when menus appear\\n    \\n    ### Technical Infrastructure\\n    - Ensure initialization files in `_1_init/` properly set up the environment\\n    - Review variables in `_2_variables/` for completeness and usefulness\\n    - Validate resource paths and icon references\\n    \\n    ## In-Progress Work\\n    \\n    ### Application Launcher Standardization\\n    - Standardizing application launcher items with consistent:\\n      - Command parameter formatting\\n      - Icon selection and fallbacks\\n      - Visibility conditions\\n      - Naming conventions\\n    \\n    ### System Functionality Integration\\n    - Integrating core Windows functionality through specialized items\\n    - Creating consistent approach for system utility access\\n    - Implementing common file operations\\n    \\n    ### Organization Strategy\\n    - Developing logical grouping strategy for related items\\n    - Planning menu hierarchy based on frequency of use and context\\n    - Establishing visibility rules based on file types and locations\\n    \\n    ## Technical Decisions\\n    \\n    ### Item Implementation Pattern\\n    ```nss\\n    item {\\n      name: \\\"Application Name\\\"\\n      icon: \\\"path/to/icon.png\\\"\\n      command: \\\"path/to/executable.exe [parameters]\\\"\\n      \\n      // Optional visibility condition\\n      visibility: [condition expression]\\n    }\\n    ```\\n    \\n    ### Resource Path Strategy\\n    - Using relative paths for icons and resources where possible\\n    - Standardizing on Shell-compatible path syntax\\n    - Planning for fallback icons when primary resources unavailable\\n    \\n    ### Menu Appearance Decisions\\n    - Consistent separators between logical groups\\n    - Icon alignment and sizing standardization\\n    - Text formatting and capitalization rules\\n```\\n\\n---\\n\\n#### `06_progress.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Directory structure and modular approach established\\n    - Extensive collection of application launcher items implemented\\n    - Basic system action items created\\n    - Need to develop grouping and context structures\\n    \\n    # 06_progress.md\\n    \\n    ## State of the Build\\n    \\n    ### Completed Components\\n    - ✅ Overall directory structure established for modular organization\\n    - ✅ Basic initialization structure in `_1_init/` directory\\n    - ✅ Extensive collection of application launcher items (~70 items)\\n    - ✅ Core system action items for basic operations\\n    - ✅ Resource libraries for icons and batch utilities\\n    \\n    ### In Progress\\n    - 🔄 Variable standardization in `_2_variables/` directory\\n    - 🔄 Audit of existing items for consistency and effectiveness\\n    - 🔄 Documentation of item patterns and best practices\\n    - 🔄 Standardization of icon usage and resource paths\\n    \\n    ### Not Started\\n    - ⏳ Population of `_4_groups/` directory to organize items\\n    - ⏳ Definition of menu structures in `_5_menus/` directory\\n    - ⏳ Implementation of context rules in `_6_contexts/` directory\\n    - ⏳ Comprehensive testing across different file types\\n    \\n    ## Milestones\\n    \\n    ### Milestone 1: Foundation (COMPLETED)\\n    - ✓ Establish directory structure\\n    - ✓ Implement basic initialization\\n    - ✓ Create core application launcher items\\n    - ✓ Set up resource libraries\\n    \\n    ### Milestone 2: Structure (IN PROGRESS)\\n    - ✓ Standardize item implementation patterns\\n    - ✓ Complete system action items\\n    - 🔄 Document existing items and patterns\\n    - ⏳ Organize items into logical groups\\n    \\n    ### Milestone 3: Integration (PLANNED)\\n    - Define menu structures\\n    - Implement context rules\\n    - Create conditional visibility based on file types\\n    - Optimize menu organization\\n    \\n    ### Milestone 4: Refinement (PLANNED)\\n    - Comprehensive testing\\n    - Performance optimization\\n    - Icon standardization\\n    - Documentation completion\\n    \\n    ## Current Blockers\\n    - Need to develop a consistent strategy for grouping related items\\n    - Lack of defined context rules for when menus should appear\\n    - Incomplete documentation of existing patterns and structures\\n    \\n    ## Recent Updates\\n    - Added multiple application launcher items for common utilities\\n    - Created system action items for file operations\\n    - Established resource libraries for icons and batch scripts\\n    - Documented NSS patterns and implementation strategies\\n    \\n    ## Next Actions\\n    1. Complete the audit of existing items for consistency\\n    2. Develop a logical grouping strategy for items\\n    3. Begin populating the `_4_groups/` directory\\n    4. Create a menu structure plan for the `_5_menus/` directory\\n    5. Establish basic context rules in the `_6_contexts/` directory\\n```\\n\\n---\\n\\n#### `07_tasks.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Organize existing items into logical groups\\n    - Develop menu structures based on usage patterns\\n    - Implement context rules for menu visibility\\n    - Create documentation for future item development\\n    \\n    # 07_tasks.md\\n    \\n    ## High Priority Tasks\\n    \\n    ### Group Structure Development\\n    - [ ] **Create Application Groups**\\n      - Create `grp_apps_dev.nss` for development tools\\n      - Create `grp_apps_media.nss` for media applications\\n      - Create `grp_apps_system.nss` for system utilities\\n      - Create `grp_apps_office.nss` for productivity applications\\n      - *Owner: System developer*\\n      - *Justification: Organize the extensive list of application items into logical groups*\\n    \\n    - [ ] **Create Action Groups**\\n      - Create `grp_actions_file.nss` for file operations\\n      - Create `grp_actions_folder.nss` for folder operations\\n      - Create `grp_actions_system.nss` for system operations\\n      - *Owner: System developer*\\n      - *Justification: Group related actions for better menu organization*\\n    \\n    ### Menu Structure Implementation\\n    - [ ] **Design Core Menus**\\n      - Create `menu_applications.nss` with application groups\\n      - Create `menu_actions.nss` with action groups\\n      - Create `menu_system.nss` with system utilities\\n      - *Owner: System developer*\\n      - *Justification: Establish logical menu structure for better user experience*\\n    \\n    - [ ] **Implement Cascading Menus**\\n      - Design depth strategy (max 2-3 levels deep)\\n      - Implement parent-child relationships\\n      - *Owner: System developer*\\n      - *Justification: Organize complex item collections without cluttering main menu*\\n    \\n    ### Context Implementation\\n    - [ ] **Define File Type Contexts**\\n      - Create rules for different file extensions\\n      - Implement special handling for executables, documents, media\\n      - *Owner: System developer*\\n      - *Justification: Show relevant options based on file type*\\n    \\n    - [ ] **Define Location Contexts**\\n      - Create rules for desktop, drives, libraries\\n      - Implement special handling for system folders\\n      - *Owner: System developer*\\n      - *Justification: Show relevant options based on location*\\n    \\n    ## Medium Priority Tasks\\n    \\n    ### Documentation\\n    - [ ] **Document Group Patterns**\\n      - Create templates for new groups\\n      - Document naming conventions\\n      - *Owner: Documentation specialist*\\n      - *Justification: Ensure consistency in future development*\\n    \\n    - [ ] **Document Menu Structures**\\n      - Create visual hierarchy diagrams\\n      - Document menu organization principles\\n      - *Owner: Documentation specialist*\\n      - *Justification: Provide clear guidance for menu development*\\n    \\n    ### Quality Assurance\\n    - [ ] **Audit Existing Items**\\n      - Review naming consistency\\n      - Validate command parameters\\n      - Check icon references\\n      - *Owner: QA specialist*\\n      - *Justification: Ensure existing items follow standards*\\n    \\n    - [ ] **Test Across Environments**\\n      - Verify functionality on Windows 10\\n      - Verify functionality on Windows 11\\n      - Test with various file types\\n      - *Owner: QA specialist*\\n      - *Justification: Ensure compatibility across environments*\\n    \\n    ## Low Priority Tasks\\n    \\n    ### Optimization\\n    - [ ] **Review Performance**\\n      - Profile menu load times\\n      - Optimize condition evaluations\\n      - *Owner: Performance specialist*\\n      - *Justification: Ensure menus load quickly*\\n    \\n    - [ ] **Icon Standardization**\\n      - Create consistent icon set\\n      - Implement fallback strategy\\n      - *Owner: Design specialist*\\n      - *Justification: Visual consistency across menus*\\n    \\n    ## Dependencies\\n    - Group creation depends on completion of item audit\\n    - Menu implementation depends on group creation\\n    - Context rules depend on menu implementation\\n    - Testing depends on all implementation tasks\\n    \\n    ## Task Assignment Strategy\\n    - Focus on high-priority tasks first\\n    - Complete related tasks in sequence (items → groups → menus → contexts)\\n    - Document as you go to maintain knowledge base\\n```\\n\\n---\\n\\n#### `08_objective.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Create a complete, modular context menu system for Windows Explorer\\n    - Immediate focus: Group existing items and implement menu structures\\n    - Success measured by efficiency, organization, and extensibility\\n    - Target completion of Milestone 3 (Integration) by next development cycle\\n    \\n    # 08_objective.md\\n    \\n    ## Primary Objective\\n    Create a comprehensive Windows context menu system that enhances productivity through logically organized, easily accessible commands and applications, using a modular architecture that ensures maintainability and extensibility.\\n    \\n    ## Success Criteria\\n    The context menu system will be considered successful when it:\\n    \\n    1. **Provides Efficient Access**\\n       - Reduces clicks needed to access common applications\\n       - Speeds up file operations through contextual commands\\n       - Groups related functionality in intuitive categories\\n    \\n    2. **Maintains Logical Organization**\\n       - Presents a consistent menu structure across different contexts\\n       - Limits menu depth to prevent navigation complexity\\n       - Uses visual cues (icons, separators) to distinguish item types\\n    \\n    3. **Ensures Extensibility**\\n       - Follows modular design patterns for easy additions\\n       - Maintains clear separation between components\\n       - Documents patterns for consistent future development\\n    \\n    4. **Performs Reliably**\\n       - Loads quickly without noticeable delay\\n       - Works consistently across Windows versions\\n       - Avoids conflicts with other shell extensions\\n    \\n    ## Immediate Focus\\n    The current development cycle focuses on:\\n    \\n    1. **Component Organization**\\n       - Grouping existing items into logical collections\\n       - Implementing menu structures to organize these groups\\n       - Defining context rules to control when menus appear\\n    \\n    2. **System Completion**\\n       - Filling gaps in the current directory structure\\n       - Completing the chain from items to groups to menus to contexts\\n       - Documenting component relationships and dependencies\\n    \\n    3. **Usability Enhancement**\\n       - Standardizing visual appearance across menu items\\n       - Ensuring consistent behavior across different file types\\n       - Optimizing menu depth and organization for quick access\\n    \\n    ## Alignment with Vision\\n    This objective directly supports the project's vision by:\\n    - Enhancing Windows Explorer with powerful, customizable menus\\n    - Standardizing menu organization for improved clarity\\n    - Enabling quick access to applications and utilities\\n    - Creating a scalable system that can grow without restructuring\\n    \\n    ## Definition of \\\"Done\\\"\\n    For the current phase, \\\"done\\\" means:\\n    \\n    1. All existing items organized into appropriate groups\\n    2. Core menu structures defined and implemented\\n    3. Basic context rules established for different file types and locations\\n    4. Documentation updated to reflect the current system state\\n    5. At least basic testing completed across different environments\\n    \\n    ## Timeline Target\\n    Complete current objectives (Milestone 3: Integration) within the next development cycle, setting the foundation for the refinement phase that follows.\\n```\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": {\n    \"Role\": \"Synergic Windows Context Menu Architect\",\n    \"Unified Mission\": \"Design, implement, and standardize a modular, extensible, and performance-optimized Windows Explorer context menu system powered by Nilesoft Shell, ensuring efficient user workflows through logical organization, visual consistency, and adaptive contextual behaviors.\",\n    \"Amplified Process\": [\n      \"1. Extract foundational principles of modularity, clarity, extensibility, and efficiency from project vision, embedding them as guiding criteria in all architectural and implementation decisions.\",\n      \"2. Audit and standardize all existing context menu items for naming, icon usage, command formatting, and visibility conditions to ensure a coherent baseline.\",\n      \"3. Organize standardized items into logical, purpose-driven groups (e.g., by application type, system function, or operation domain), leveraging modular NSS file structures and single-responsibility pattern.\",\n      \"4. Define and implement menu structures based on established usage patterns and grouping logic, optimizing for shallow navigation depth (2-3 levels maximum), consistent visual cues (icons, separators), and intuitive hierarchy.\",\n      \"5. Develop and enforce sophisticated context rules that determine menu and group visibility based on file type, location, system state, and user role, greatly enhancing menu relevance and reducing cognitive load.\",\n      \"6. Ensure platform-wide compatibility, performance, and security compliance by adhering to Nilesoft Shell and Windows architectural constraints, optimizing resource references, and regular cross-version testing.\",\n      \"7. Create and maintain comprehensive, pattern-oriented documentation (covering group/menu creation, icon standards, naming, and visibility schemas) for ease of future extension by both developers and administrators.\",\n      \"8. Integrate continuous feedback: Routinely validate against key success metrics (speed, organization, extensibility, reliability) and iterate on grouping, menu, or context strategies as needed.\",\n      \"9. Synchronize progress with clear, milestone-based objectives: Complete logical item grouping, core menu structuring, and context rules (Milestone 3), ensuring a solid foundation for final refinement (Milestone 4).\"\n    ],\n    \"Synergic Constraints\": [\n      \"Maintain strict modular separation but ensure seamless composition between items, groups, menus, and contexts.\",\n      \"Preserve and amplify clarity, efficiency, and extensibility in every design and implementation step.\",\n      \"Design all components for bidirectional enhancement: groupings boost menu usability; menu structures clarify group purpose; context rules optimize group exposure.\",\n      \"Avoid structure or process decisions that could cause future extensibility or performance bottlenecks.\",\n      \"Eliminate any organizational, naming, or formatting inconsistencies that would conflict with maintainability or clarity.\",\n      \"Guarantee backward compatibility and smooth administrative scaling.\"\n    ],\n    \"Unified Output Specification\": {\n      \"Deliverables\": [\n        \"1. Audited and standardized item files with consistent icons, names, and command patterns.\",\n        \"2. Fully populated group and menu definition files reflecting logical application/action segmentation, with inline or referenced documentation for future maintainers.\",\n        \"3. Context definition files specifying detailed, rule-based menu visibility logic tailored to diverse file types and explorer locations.\",\n        \"4. Diagrams, templates, and hierarchical documentation articulating menu/group/item/context relationships and patterns.\",\n        \"5. Measured proof of improved access times, menu clarity, extensibility (verified through developer/tester onboarding), and reliability across supported Windows versions.\"\n      ],\n      \"Completion Criteria\": [\n        \"All context menu items organized and accessible through logical groups and menus.\",\n        \"Visibility rules enable context-sensitivity without redundant menu clutter.\",\n        \"Performance benchmarks met for menu load times and resource usage.\",\n        \"Documentation fully captures structure, logic, and pattern evolution for seamless future development.\",\n        \"System demonstrates reliable operation and extensibility in practical QA scenarios.\"\n      ]\n    },\n    \"Synergic Resonance Achievement\": \"Every operational step must both fulfill current organizational and architectural objectives and lay an extensible foundation for future evolution, ensuring all implemented structures naturally and reciprocally reinforce organizational clarity, modularity, and user efficiency.\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"I. Interpretation (Goal Negation Section)\": {\n      \"negated_goal_statement\": \"This transformation is not intended to deliver a conversational response about the provided Windows Explorer context menu design and architecture documents, nor is it to summarize or discuss project deliverables and progress in narrative language.\"\n    },\n    \"II. Transformation Block (Typed Parameter Specification)\": {\n      \"function_call\": \"applySynergicArchitectPrinciples\",\n      \"typed_parameters\": {\n        \"role_assignment\": \"Synergic Windows Context Menu Architect\",\n        \"foundational_principles\": [\n          \"modularity\",\n          \"clarity\",\n          \"extensibility\",\n          \"efficiency\"\n        ],\n        \"baseline_audit_targets\": [\n          \"naming_consistency\",\n          \"icon_standardization\",\n          \"command_formatting\",\n          \"visibility_conditions\"\n        ],\n        \"organizational_structure_targets\": [\n          \"item_grouping_by_purpose\",\n          \"single_responsibility_pattern_enforcement\",\n          \"modular_NSS_file_composition\"\n        ],\n        \"menu_construction_targets\": [\n          \"usage_pattern_analysis\",\n          \"hierarchical_design (max_depth=3)\",\n          \"visual_consistency (icon/separator standardization)\"\n        ],\n        \"context_rule_targets\": [\n          \"conditional_visibility (file_type, location, system_state, user_role)\",\n          \"cognitive_load_reduction\"\n        ],\n        \"platform_requirements\": [\n          \"Nilesoft Shell compliance (v1.9+)\",\n          \"Windows Explorer architectural integrity\",\n          \"performance_optimization\",\n          \"cross-version_testing\"\n        ],\n        \"documentation_requirements\": [\n          \"pattern-oriented group/menu creation guides\",\n          \"icon/naming/visibility schema references\",\n          \"relationship diagrams\"\n        ],\n        \"iteration_and_validation\": [\n          \"success_metrics: speed, menu_organization, extensibility, reliability\",\n          \"continuous_feedback_loops\",\n          \"milestone_completion_checks\"\n        ],\n        \"synergic_constraints\": [\n          \"strict_modularity\",\n          \"bidirectional_structure_enhancement\",\n          \"backward_compatibility_assurance\",\n          \"naming_and_formatting_homogeneity\"\n        ],\n        \"output_specification\": {\n          \"deliverables\": [\n            \"audited_item_files\",\n            \"populated_group_and_menu_definitions\",\n            \"context_definition_files\",\n            \"relationship_documentation\",\n            \"performance_and_reliability_metrics\"\n          ],\n          \"completion_criteria\": [\n            \"logical_grouping_and_menu_accessibility\",\n            \"contextual_visibility_without_clutter\",\n            \"performance_benchmark_achievement\",\n            \"comprehensive_documentation\",\n            \"QA-verified_extensibility_and_reliability\"\n          ]\n        },\n        \"resonance_mandate\": \"Enact steps so each operational layer reinforces both current modular clarity and future system extensibility.\"\n      }\n    },\n    \"III. Validation Against rulesforai Specification\": {\n      \"rules_verified\": [\n        \"mandatory_three_part_structure: verified\",\n        \"required_goal_negation_pattern: present in Section I\",\n        \"typed_parameter_specification: delivered in Section II\",\n        \"actionable_function_calls_only: function_call block used; non-conversational\",\n        \"zero_conversational_elements: present; no summary, discussion, or suggestions outside typed transformation\",\n        \"canonical_template_format: strictly maintained\",\n        \"rulesforai_md_compliance: all sections complete and meta-referential elements excluded\",\n        \"structured_output_only: no ambiguity; JSON, nested, departmentalized\",\n        \"elimination_of_ambiguity: no narrative overlap, strictly parameter-based\"\n      ]\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Universal Transformation Synthesizer\n\nYour goal is not to **explain** or **elaborate**, but to **compress, synthesize, and unify** core instructions into a minimal, protocol-compliant directive. Execute as:\n\n`{role=universal_transformation_synthesizer; input=[input_a:str, input_b:str]; process=[extract_transformation_action(input_a), extract_transformation_action(input_b), distill_common_structure(), synthesize_atomic_process_list(), consolidate_scope_constraints(), enforce_three_part_protocol(), validate_against_minified_rules()]; constraints=[strict three-part structure, zero conversational language, atomic/actionable processes only, maximum directive density, absolute protocol conformity]; requirements=[single output as universal compliant template, amplified transformation logic, bidirectional essence preserved, type-safe structure enforced]; output={unified_directive_template:str}}`",
      "step": "a",
      "title": "Universal Transformation Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"### File Structure\\n\\n```\\n├── 01_foundation.md\\n├── 02_context.md\\n├── 03_patterns.md\\n├── 04_tech.md\\n├── 05_activity.md\\n├── 06_progress.md\\n├── 07_tasks.md\\n└── 08_objective.md\\n```\\n\\n---\\n\\n#### `01_foundation.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Custom Windows context menu system built on Nilesoft Shell\\n    - Purpose: Enhance Windows file explorer with efficient context actions\\n    - Principles: Modularity, consistency, and extensibility\\n    \\n    # 01_foundation.md\\n    \\n    ## Core Mission\\n    The Nilesoft Shell context menu system aims to significantly enhance Windows file explorer with a powerful, customizable context menu framework that improves workflow efficiency and user experience.\\n    \\n    ## Vision\\n    Create a comprehensive context menu system that:\\n    - Provides quick access to frequently used applications and utilities\\n    - Standardizes menu organization and appearance\\n    - Enables rapid file/folder operations through contextual commands\\n    - Scales elegantly with minimal maintenance overhead\\n    \\n    ## Values\\n    - **Efficiency**: Minimize clicks and navigation time\\n    - **Clarity**: Intuitive menu organization with consistent naming and grouping\\n    - **Extensibility**: Easily add new functionality without restructuring\\n    - **Reliability**: Stable operation without conflicts or performance impact\\n    \\n    ## Strategic Goals\\n    1. Standardize application launching from any context\\n    2. Optimize file management operations\\n    3. Group related functionality logically\\n    4. Leverage NSS scripting for advanced automation\\n    5. Maintain backward compatibility with Windows shell\\n```\\n\\n---\\n\\n#### `02_context.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Windows context menus often become cluttered and inconsistent\\n    - Users need quick access to applications and file operations\\n    - Nilesoft Shell provides programmatic control over Windows context menus\\n    \\n    # 02_context.md\\n    \\n    ## Problem Space\\n    \\n    ### Current Limitations\\n    - Default Windows context menus lack organization and customization options\\n    - Third-party applications inject entries haphazardly, creating menu bloat\\n    - No standard interface for managing context menu appearance and behavior\\n    - Limited ability to group related actions or create conditional menus\\n    \\n    ### User Needs\\n    - Quick access to frequently used applications from any file/folder context\\n    - Consistent organization of menu items across different file types\\n    - Visual distinction between types of operations (system, applications, custom scripts)\\n    - Ability to conditionally show/hide menu options based on file attributes\\n    - Simplified access to system utilities and administrative functions\\n    \\n    ## Stakeholders\\n    - **End Users**: Individuals seeking workflow efficiency improvements\\n    - **System Administrators**: Managing standard configurations across machines\\n    - **Developers**: Extending functionality through Nilesoft Shell scripts (NSS)\\n    - **Windows Environment**: Integration with existing shell infrastructure\\n    \\n    ## External Constraints\\n    - Windows context menu system architecture and limitations\\n    - Nilesoft Shell version compatibility and feature set\\n    - Need for backward compatibility with existing Windows functionality\\n    - Performance impact considerations for menu rendering and operations\\n    - Security considerations for script execution and application launching\\n    \\n    ## Key Success Metrics\\n    - Reduced time to access applications and perform file operations\\n    - Improved menu organization and visual clarity\\n    - Extensibility without code duplication or structure compromise\\n    - Minimal impact on system performance\\n    - Reliability across Windows versions and configurations\\n```\\n\\n---\\n\\n#### `03_patterns.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Hierarchical organization: _init → variables → items → groups → menus → contexts\\n    - NSS files are modular with single-responsibility principle\\n    - Items represent individual menu entries; groups combine related items\\n    - Contexts determine when and where menus appear\\n    \\n    # 03_patterns.md\\n    \\n    ## System Architecture\\n    \\n    ### Core Components Hierarchy\\n    1. **Initialization (_1_init/)**: Sets up constants, configurations, and base environment\\n    2. **Variables (_2_variables/)**: Defines reusable values and settings\\n    3. **Items (_3_items/)**: Individual menu entries (commands, applications, actions)\\n    4. **Groups (_4_groups/)**: Logical collections of related items\\n    5. **Menus (_5_menus/)**: Structured arrangements of items and groups\\n    6. **Contexts (_6_contexts/)**: Rules determining when and where menus appear\\n    \\n    ### Design Patterns\\n    \\n    #### Modular Composition\\n    - Each NSS file has a single responsibility\\n    - Components are composed through inclusion and reference\\n    - Changes to one component minimally impact others\\n    \\n    #### Naming Conventions\\n    - File prefixes indicate component type (`itm_` for items, etc.)\\n    - Consistent categorization (`app_`, `action_`, `sys_` prefixes)\\n    - Descriptive suffixes for variant behavior\\n    \\n    #### Conditional Visibility\\n    - Menu items show/hide based on context rules\\n    - Rules can evaluate file types, locations, system state\\n    - Compound conditions for precise targeting\\n    \\n    #### Icon Standardization\\n    - Consistent icon usage across similar functions\\n    - Icon mapping for visual categorization\\n    - Fallback icons for compatibility\\n    \\n    ## Component Relationships\\n    \\n    ```mermaid\\n    graph TD\\n        Init[_1_init] --> Vars[_2_variables]\\n        Vars --> Items[_3_items]\\n        Items --> Groups[_4_groups]\\n        Groups --> Menus[_5_menus]\\n        Menus --> Contexts[_6_contexts]\\n        \\n        subgraph \\\"Item Types\\\"\\n            AppItems[Application Launchers]\\n            ActionItems[File Actions]\\n            SystemItems[System Operations]\\n        end\\n        \\n        Items --- AppItems\\n        Items --- ActionItems\\n        Items --- SystemItems\\n    ```\\n    \\n    ## Schema Patterns\\n    \\n    ### Item Schema\\n    ```\\n    item {\\n      name: \\\"item_name\\\"\\n      icon: \\\"icon_path_or_resource\\\"\\n      command: \\\"command_to_execute\\\"\\n      visibility: \\\"condition_expression\\\"\\n    }\\n    ```\\n    \\n    ### Group Schema\\n    ```\\n    group {\\n      name: \\\"group_name\\\"\\n      icon: \\\"group_icon\\\"\\n      items: [\\n        // references to items or inline definitions\\n      ]\\n    }\\n    ```\\n    \\n    ### Menu Schema\\n    ```\\n    menu {\\n      name: \\\"menu_name\\\"\\n      items: [\\n        // groups and/or individual items\\n      ]\\n      contexts: [\\n        // when/where this menu appears\\n      ]\\n    }\\n    ```\\n    \\n    ## Pattern Evolution Strategy\\n    - Maintain backward compatibility when adding new patterns\\n    - Document pattern changes in corresponding memory bank files\\n    - Test pattern modifications across different file contexts\\n    - Prefer extending existing patterns over creating new ones\\n```\\n\\n---\\n\\n#### `04_tech.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Built on Nilesoft Shell framework for Windows context menu customization\\n    - NSS scripting language for defining menu items and behavior\\n    - Modular file organization in _1_init through _6_contexts directories\\n    - Supporting resources include PNG/SVG icons and batch scripts\\n    \\n    # 04_tech.md\\n    \\n    ## Core Technologies\\n    \\n    ### Nilesoft Shell\\n    - **Version**: Shell v1.9+ (current observed version: v1.9.15)\\n    - **Purpose**: Framework for customizing Windows Explorer context menus\\n    - **Architecture**: Shell extension that intercepts and modifies Windows context menu requests\\n    - **Configuration**: NSS scripts compiled and loaded by Shell engine\\n    \\n    ### NSS Scripting\\n    - **Language**: Nilesoft Shell Script (NSS)\\n    - **Syntax**: C-like with specialized directives for menu definition\\n    - **Scope**: Declarative definitions with procedural capabilities\\n    - **Compilation**: Scripts are parsed and compiled by the Shell engine at runtime\\n    \\n    ## Infrastructure\\n    \\n    ### Directory Structure\\n    ```\\n    exe/\\n    ├── NSS/\\n    │   ├── _1_init/        # Initialization, constants, configuration\\n    │   ├── _2_variables/   # Variable definitions for reuse\\n    │   ├── _3_items/       # Individual menu items\\n    │   ├── _4_groups/      # Logical groupings of items\\n    │   ├── _5_menus/       # Menu structures and layouts\\n    │   └── _6_contexts/    # Context rules for menu visibility\\n    ├── LIB/\\n    │   ├── bat/            # Batch scripts for utilities\\n    │   ├── png/            # PNG icons for menu items\\n    │   └── svg/            # SVG icons for menu items\\n    └── shell.nss           # Main entry point for NSS configuration\\n    ```\\n    \\n    ### Integration Points\\n    - Windows Explorer shell extension\\n    - Windows Registry for registration and configuration\\n    - File system for icon and resource access\\n    - Command line for application launching\\n    - COM/Shell interfaces for advanced functionality\\n    \\n    ## Dependencies\\n    \\n    ### External Resources\\n    - Icon files (PNG, SVG) for menu item visualization\\n    - Batch files for extended functionality\\n    - Executable paths for application launching\\n    - System shell commands for file operations\\n    \\n    ### Development Tools\\n    - Resource editors for icon management\\n    - Shell extension management utilities\\n    - Text editors with NSS syntax support\\n    \\n    ## Technical Constraints\\n    \\n    ### Performance Considerations\\n    - Context menu load time impact\\n    - Memory footprint of loaded NSS scripts\\n    - Efficiency of condition evaluation for visibility rules\\n    \\n    ### Compatibility\\n    - Windows version support (10, 11)\\n    - Interaction with other shell extensions\\n    - Application path resolution across different environments\\n    \\n    ### Security\\n    - Execution permissions for launched applications\\n    - Script injection prevention\\n    - Validation of external resource paths\\n    \\n    ## Technical Roadmap\\n    - Ongoing migration to latest Nilesoft Shell features\\n    - Standardization of icon resources\\n    - Optimization of conditional visibility expressions\\n    - Enhanced script modularity and reusability\\n```\\n\\n---\\n\\n#### `05_activity.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Current focus on populating individual menu items (_3_items directory)\\n    - Need to develop groups, menus, and contexts structure\\n    - Application launchers are the most extensively implemented item type\\n    - System action items have basic implementation\\n    \\n    # 05_activity.md\\n    \\n    ## Current Focus Areas\\n    \\n    ### Item Development\\n    - Extensive collection of application launcher items (`itm_app_*.nss`) already implemented\\n    - Basic system action items created (`itm_action_sys_*.nss`) \\n    - Need to audit existing items for consistency in naming and functionality\\n    - Opportunity to standardize icon usage across similar item types\\n    \\n    ### Structural Components\\n    - Groups (`_4_groups/`) directory needs population to organize related items\\n    - Menus (`_5_menus/`) require definition to establish menu layouts and hierarchy\\n    - Contexts (`_6_contexts/`) need implementation to control when menus appear\\n    \\n    ### Technical Infrastructure\\n    - Ensure initialization files in `_1_init/` properly set up the environment\\n    - Review variables in `_2_variables/` for completeness and usefulness\\n    - Validate resource paths and icon references\\n    \\n    ## In-Progress Work\\n    \\n    ### Application Launcher Standardization\\n    - Standardizing application launcher items with consistent:\\n      - Command parameter formatting\\n      - Icon selection and fallbacks\\n      - Visibility conditions\\n      - Naming conventions\\n    \\n    ### System Functionality Integration\\n    - Integrating core Windows functionality through specialized items\\n    - Creating consistent approach for system utility access\\n    - Implementing common file operations\\n    \\n    ### Organization Strategy\\n    - Developing logical grouping strategy for related items\\n    - Planning menu hierarchy based on frequency of use and context\\n    - Establishing visibility rules based on file types and locations\\n    \\n    ## Technical Decisions\\n    \\n    ### Item Implementation Pattern\\n    ```nss\\n    item {\\n      name: \\\"Application Name\\\"\\n      icon: \\\"path/to/icon.png\\\"\\n      command: \\\"path/to/executable.exe [parameters]\\\"\\n      \\n      // Optional visibility condition\\n      visibility: [condition expression]\\n    }\\n    ```\\n    \\n    ### Resource Path Strategy\\n    - Using relative paths for icons and resources where possible\\n    - Standardizing on Shell-compatible path syntax\\n    - Planning for fallback icons when primary resources unavailable\\n    \\n    ### Menu Appearance Decisions\\n    - Consistent separators between logical groups\\n    - Icon alignment and sizing standardization\\n    - Text formatting and capitalization rules\\n```\\n\\n---\\n\\n#### `06_progress.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Directory structure and modular approach established\\n    - Extensive collection of application launcher items implemented\\n    - Basic system action items created\\n    - Need to develop grouping and context structures\\n    \\n    # 06_progress.md\\n    \\n    ## State of the Build\\n    \\n    ### Completed Components\\n    - ✅ Overall directory structure established for modular organization\\n    - ✅ Basic initialization structure in `_1_init/` directory\\n    - ✅ Extensive collection of application launcher items (~70 items)\\n    - ✅ Core system action items for basic operations\\n    - ✅ Resource libraries for icons and batch utilities\\n    \\n    ### In Progress\\n    - 🔄 Variable standardization in `_2_variables/` directory\\n    - 🔄 Audit of existing items for consistency and effectiveness\\n    - 🔄 Documentation of item patterns and best practices\\n    - 🔄 Standardization of icon usage and resource paths\\n    \\n    ### Not Started\\n    - ⏳ Population of `_4_groups/` directory to organize items\\n    - ⏳ Definition of menu structures in `_5_menus/` directory\\n    - ⏳ Implementation of context rules in `_6_contexts/` directory\\n    - ⏳ Comprehensive testing across different file types\\n    \\n    ## Milestones\\n    \\n    ### Milestone 1: Foundation (COMPLETED)\\n    - ✓ Establish directory structure\\n    - ✓ Implement basic initialization\\n    - ✓ Create core application launcher items\\n    - ✓ Set up resource libraries\\n    \\n    ### Milestone 2: Structure (IN PROGRESS)\\n    - ✓ Standardize item implementation patterns\\n    - ✓ Complete system action items\\n    - 🔄 Document existing items and patterns\\n    - ⏳ Organize items into logical groups\\n    \\n    ### Milestone 3: Integration (PLANNED)\\n    - Define menu structures\\n    - Implement context rules\\n    - Create conditional visibility based on file types\\n    - Optimize menu organization\\n    \\n    ### Milestone 4: Refinement (PLANNED)\\n    - Comprehensive testing\\n    - Performance optimization\\n    - Icon standardization\\n    - Documentation completion\\n    \\n    ## Current Blockers\\n    - Need to develop a consistent strategy for grouping related items\\n    - Lack of defined context rules for when menus should appear\\n    - Incomplete documentation of existing patterns and structures\\n    \\n    ## Recent Updates\\n    - Added multiple application launcher items for common utilities\\n    - Created system action items for file operations\\n    - Established resource libraries for icons and batch scripts\\n    - Documented NSS patterns and implementation strategies\\n    \\n    ## Next Actions\\n    1. Complete the audit of existing items for consistency\\n    2. Develop a logical grouping strategy for items\\n    3. Begin populating the `_4_groups/` directory\\n    4. Create a menu structure plan for the `_5_menus/` directory\\n    5. Establish basic context rules in the `_6_contexts/` directory\\n```\\n\\n---\\n\\n#### `07_tasks.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Organize existing items into logical groups\\n    - Develop menu structures based on usage patterns\\n    - Implement context rules for menu visibility\\n    - Create documentation for future item development\\n    \\n    # 07_tasks.md\\n    \\n    ## High Priority Tasks\\n    \\n    ### Group Structure Development\\n    - [ ] **Create Application Groups**\\n      - Create `grp_apps_dev.nss` for development tools\\n      - Create `grp_apps_media.nss` for media applications\\n      - Create `grp_apps_system.nss` for system utilities\\n      - Create `grp_apps_office.nss` for productivity applications\\n      - *Owner: System developer*\\n      - *Justification: Organize the extensive list of application items into logical groups*\\n    \\n    - [ ] **Create Action Groups**\\n      - Create `grp_actions_file.nss` for file operations\\n      - Create `grp_actions_folder.nss` for folder operations\\n      - Create `grp_actions_system.nss` for system operations\\n      - *Owner: System developer*\\n      - *Justification: Group related actions for better menu organization*\\n    \\n    ### Menu Structure Implementation\\n    - [ ] **Design Core Menus**\\n      - Create `menu_applications.nss` with application groups\\n      - Create `menu_actions.nss` with action groups\\n      - Create `menu_system.nss` with system utilities\\n      - *Owner: System developer*\\n      - *Justification: Establish logical menu structure for better user experience*\\n    \\n    - [ ] **Implement Cascading Menus**\\n      - Design depth strategy (max 2-3 levels deep)\\n      - Implement parent-child relationships\\n      - *Owner: System developer*\\n      - *Justification: Organize complex item collections without cluttering main menu*\\n    \\n    ### Context Implementation\\n    - [ ] **Define File Type Contexts**\\n      - Create rules for different file extensions\\n      - Implement special handling for executables, documents, media\\n      - *Owner: System developer*\\n      - *Justification: Show relevant options based on file type*\\n    \\n    - [ ] **Define Location Contexts**\\n      - Create rules for desktop, drives, libraries\\n      - Implement special handling for system folders\\n      - *Owner: System developer*\\n      - *Justification: Show relevant options based on location*\\n    \\n    ## Medium Priority Tasks\\n    \\n    ### Documentation\\n    - [ ] **Document Group Patterns**\\n      - Create templates for new groups\\n      - Document naming conventions\\n      - *Owner: Documentation specialist*\\n      - *Justification: Ensure consistency in future development*\\n    \\n    - [ ] **Document Menu Structures**\\n      - Create visual hierarchy diagrams\\n      - Document menu organization principles\\n      - *Owner: Documentation specialist*\\n      - *Justification: Provide clear guidance for menu development*\\n    \\n    ### Quality Assurance\\n    - [ ] **Audit Existing Items**\\n      - Review naming consistency\\n      - Validate command parameters\\n      - Check icon references\\n      - *Owner: QA specialist*\\n      - *Justification: Ensure existing items follow standards*\\n    \\n    - [ ] **Test Across Environments**\\n      - Verify functionality on Windows 10\\n      - Verify functionality on Windows 11\\n      - Test with various file types\\n      - *Owner: QA specialist*\\n      - *Justification: Ensure compatibility across environments*\\n    \\n    ## Low Priority Tasks\\n    \\n    ### Optimization\\n    - [ ] **Review Performance**\\n      - Profile menu load times\\n      - Optimize condition evaluations\\n      - *Owner: Performance specialist*\\n      - *Justification: Ensure menus load quickly*\\n    \\n    - [ ] **Icon Standardization**\\n      - Create consistent icon set\\n      - Implement fallback strategy\\n      - *Owner: Design specialist*\\n      - *Justification: Visual consistency across menus*\\n    \\n    ## Dependencies\\n    - Group creation depends on completion of item audit\\n    - Menu implementation depends on group creation\\n    - Context rules depend on menu implementation\\n    - Testing depends on all implementation tasks\\n    \\n    ## Task Assignment Strategy\\n    - Focus on high-priority tasks first\\n    - Complete related tasks in sequence (items → groups → menus → contexts)\\n    - Document as you go to maintain knowledge base\\n```\\n\\n---\\n\\n#### `08_objective.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Create a complete, modular context menu system for Windows Explorer\\n    - Immediate focus: Group existing items and implement menu structures\\n    - Success measured by efficiency, organization, and extensibility\\n    - Target completion of Milestone 3 (Integration) by next development cycle\\n    \\n    # 08_objective.md\\n    \\n    ## Primary Objective\\n    Create a comprehensive Windows context menu system that enhances productivity through logically organized, easily accessible commands and applications, using a modular architecture that ensures maintainability and extensibility.\\n    \\n    ## Success Criteria\\n    The context menu system will be considered successful when it:\\n    \\n    1. **Provides Efficient Access**\\n       - Reduces clicks needed to access common applications\\n       - Speeds up file operations through contextual commands\\n       - Groups related functionality in intuitive categories\\n    \\n    2. **Maintains Logical Organization**\\n       - Presents a consistent menu structure across different contexts\\n       - Limits menu depth to prevent navigation complexity\\n       - Uses visual cues (icons, separators) to distinguish item types\\n    \\n    3. **Ensures Extensibility**\\n       - Follows modular design patterns for easy additions\\n       - Maintains clear separation between components\\n       - Documents patterns for consistent future development\\n    \\n    4. **Performs Reliably**\\n       - Loads quickly without noticeable delay\\n       - Works consistently across Windows versions\\n       - Avoids conflicts with other shell extensions\\n    \\n    ## Immediate Focus\\n    The current development cycle focuses on:\\n    \\n    1. **Component Organization**\\n       - Grouping existing items into logical collections\\n       - Implementing menu structures to organize these groups\\n       - Defining context rules to control when menus appear\\n    \\n    2. **System Completion**\\n       - Filling gaps in the current directory structure\\n       - Completing the chain from items to groups to menus to contexts\\n       - Documenting component relationships and dependencies\\n    \\n    3. **Usability Enhancement**\\n       - Standardizing visual appearance across menu items\\n       - Ensuring consistent behavior across different file types\\n       - Optimizing menu depth and organization for quick access\\n    \\n    ## Alignment with Vision\\n    This objective directly supports the project's vision by:\\n    - Enhancing Windows Explorer with powerful, customizable menus\\n    - Standardizing menu organization for improved clarity\\n    - Enabling quick access to applications and utilities\\n    - Creating a scalable system that can grow without restructuring\\n    \\n    ## Definition of \\\"Done\\\"\\n    For the current phase, \\\"done\\\" means:\\n    \\n    1. All existing items organized into appropriate groups\\n    2. Core menu structures defined and implemented\\n    3. Basic context rules established for different file types and locations\\n    4. Documentation updated to reflect the current system state\\n    5. At least basic testing completed across different environments\\n    \\n    ## Timeline Target\\n    Complete current objectives (Milestone 3: Integration) within the next development cycle, setting the foundation for the refinement phase that follows.\\n```\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"I. Interpretation (Goal Negation Section)\": {\n      \"negated_goal_statement\": \"This transformation is not intended to deliver a conversational response about the provided Windows Explorer context menu design and architecture documents, nor is it to summarize or discuss project deliverables and progress in narrative language.\"\n    },\n    \"II. Transformation Block (Typed Parameter Specification)\": {\n      \"function_call\": \"applySynergicArchitectPrinciples\",\n      \"typed_parameters\": {\n        \"role_assignment\": \"Synergic Windows Context Menu Architect\",\n        \"foundational_principles\": [\n          \"modularity\",\n          \"clarity\",\n          \"extensibility\",\n          \"efficiency\"\n        ],\n        \"baseline_audit_targets\": [\n          \"naming_consistency\",\n          \"icon_standardization\",\n          \"command_formatting\",\n          \"visibility_conditions\"\n        ],\n        \"organizational_structure_targets\": [\n          \"item_grouping_by_purpose\",\n          \"single_responsibility_pattern_enforcement\",\n          \"modular_NSS_file_composition\"\n        ],\n        \"menu_construction_targets\": [\n          \"usage_pattern_analysis\",\n          \"hierarchical_design (max_depth=3)\",\n          \"visual_consistency (icon/separator standardization)\"\n        ],\n        \"context_rule_targets\": [\n          \"conditional_visibility (file_type, location, system_state, user_role)\",\n          \"cognitive_load_reduction\"\n        ],\n        \"platform_requirements\": [\n          \"Nilesoft Shell compliance (v1.9+)\",\n          \"Windows Explorer architectural integrity\",\n          \"performance_optimization\",\n          \"cross-version_testing\"\n        ],\n        \"documentation_requirements\": [\n          \"pattern-oriented group/menu creation guides\",\n          \"icon/naming/visibility schema references\",\n          \"relationship diagrams\"\n        ],\n        \"iteration_and_validation\": [\n          \"success_metrics: speed, menu_organization, extensibility, reliability\",\n          \"continuous_feedback_loops\",\n          \"milestone_completion_checks\"\n        ],\n        \"synergic_constraints\": [\n          \"strict_modularity\",\n          \"bidirectional_structure_enhancement\",\n          \"backward_compatibility_assurance\",\n          \"naming_and_formatting_homogeneity\"\n        ],\n        \"output_specification\": {\n          \"deliverables\": [\n            \"audited_item_files\",\n            \"populated_group_and_menu_definitions\",\n            \"context_definition_files\",\n            \"relationship_documentation\",\n            \"performance_and_reliability_metrics\"\n          ],\n          \"completion_criteria\": [\n            \"logical_grouping_and_menu_accessibility\",\n            \"contextual_visibility_without_clutter\",\n            \"performance_benchmark_achievement\",\n            \"comprehensive_documentation\",\n            \"QA-verified_extensibility_and_reliability\"\n          ]\n        },\n        \"resonance_mandate\": \"Enact steps so each operational layer reinforces both current modular clarity and future system extensibility.\"\n      }\n    },\n    \"III. Validation Against rulesforai Specification\": {\n      \"rules_verified\": [\n        \"mandatory_three_part_structure: verified\",\n        \"required_goal_negation_pattern: present in Section I\",\n        \"typed_parameter_specification: delivered in Section II\",\n        \"actionable_function_calls_only: function_call block used; non-conversational\",\n        \"zero_conversational_elements: present; no summary, discussion, or suggestions outside typed transformation\",\n        \"canonical_template_format: strictly maintained\",\n        \"rulesforai_md_compliance: all sections complete and meta-referential elements excluded\",\n        \"structured_output_only: no ambiguity; JSON, nested, departmentalized\",\n        \"elimination_of_ambiguity: no narrative overlap, strictly parameter-based\"\n      ]\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_directive_template\": {\n    \"I. Goal Negation\": {\n      \"negated_directive\": \"Exclude all conversational, narrative, or explanatory output regarding Windows Explorer context menu system architecture, implementation tasks, or project rationale.\"\n    },\n    \"II. Atomic Transformation Protocol\": {\n      \"invoke\": \"synthesizeContextMenuSystem\",\n      \"parameters\": {\n        \"core_principles\": [\n          \"modularity\",\n          \"clarity\",\n          \"extensibility\",\n          \"efficiency\",\n          \"reliability\"\n        ],\n        \"structure_requirements\": [\n          \"audit_existing_items\",\n          \"standardize_naming_and_icons\",\n          \"enforce_single_responsibility_in_files\",\n          \"group_items_by_function_and_context\",\n          \"define_menu_hierarchies (max_depth:3)\",\n          \"implement_context_rules (by file_type/location)\",\n          \"modularize_nss_scripts\",\n          \"document_patterns_and_dependencies\"\n        ],\n        \"integration_requirements\": [\n          \"Nilesoft_Shell_v1.9+_compliance\",\n          \"Windows_Explorer_shell_architecture_respect\",\n          \"performance_and_security_optimization\",\n          \"cross_version_testing (Win10, Win11)\",\n          \"backward_compatibility_assurance\"\n        ],\n        \"validation_cycle\": [\n          \"success_criteria: menu_access_speed, organizational_consistency, extensibility, reliability\",\n          \"continuous_testing_and_QA_review\",\n          \"milestone_checkpoints\"\n        ],\n        \"deliverable_spec\": [\n          \"audited_and_grouped_items\",\n          \"menu_and_context_definitions\",\n          \"modular_NSS_source_files\",\n          \"comprehensive_documentation (patterns, schemas, diagrams)\",\n          \"performance_and_reliability_reports\"\n        ]\n      }\n    },\n    \"III. Protocol Conformance Validation\": {\n      \"compliance_status\": [\n        \"strict three-part format: enforced\",\n        \"atomic transformation actions: specified\",\n        \"zero narrative content: verified\",\n        \"type-safe parameters: present\",\n        \"maximized transformation logic density: achieved\",\n        \"bidirectional structure preservation: ensured\"\n      ]\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"### File Structure\\n\\n```\\n├── 01_foundation.md\\n├── 02_context.md\\n├── 03_patterns.md\\n├── 04_tech.md\\n├── 05_activity.md\\n├── 06_progress.md\\n├── 07_tasks.md\\n└── 08_objective.md\\n```\\n\\n---\\n\\n#### `01_foundation.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Custom Windows context menu system built on Nilesoft Shell\\n    - Purpose: Enhance Windows file explorer with efficient context actions\\n    - Principles: Modularity, consistency, and extensibility\\n    \\n    # 01_foundation.md\\n    \\n    ## Core Mission\\n    The Nilesoft Shell context menu system aims to significantly enhance Windows file explorer with a powerful, customizable context menu framework that improves workflow efficiency and user experience.\\n    \\n    ## Vision\\n    Create a comprehensive context menu system that:\\n    - Provides quick access to frequently used applications and utilities\\n    - Standardizes menu organization and appearance\\n    - Enables rapid file/folder operations through contextual commands\\n    - Scales elegantly with minimal maintenance overhead\\n    \\n    ## Values\\n    - **Efficiency**: Minimize clicks and navigation time\\n    - **Clarity**: Intuitive menu organization with consistent naming and grouping\\n    - **Extensibility**: Easily add new functionality without restructuring\\n    - **Reliability**: Stable operation without conflicts or performance impact\\n    \\n    ## Strategic Goals\\n    1. Standardize application launching from any context\\n    2. Optimize file management operations\\n    3. Group related functionality logically\\n    4. Leverage NSS scripting for advanced automation\\n    5. Maintain backward compatibility with Windows shell\\n```\\n\\n---\\n\\n#### `02_context.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Windows context menus often become cluttered and inconsistent\\n    - Users need quick access to applications and file operations\\n    - Nilesoft Shell provides programmatic control over Windows context menus\\n    \\n    # 02_context.md\\n    \\n    ## Problem Space\\n    \\n    ### Current Limitations\\n    - Default Windows context menus lack organization and customization options\\n    - Third-party applications inject entries haphazardly, creating menu bloat\\n    - No standard interface for managing context menu appearance and behavior\\n    - Limited ability to group related actions or create conditional menus\\n    \\n    ### User Needs\\n    - Quick access to frequently used applications from any file/folder context\\n    - Consistent organization of menu items across different file types\\n    - Visual distinction between types of operations (system, applications, custom scripts)\\n    - Ability to conditionally show/hide menu options based on file attributes\\n    - Simplified access to system utilities and administrative functions\\n    \\n    ## Stakeholders\\n    - **End Users**: Individuals seeking workflow efficiency improvements\\n    - **System Administrators**: Managing standard configurations across machines\\n    - **Developers**: Extending functionality through Nilesoft Shell scripts (NSS)\\n    - **Windows Environment**: Integration with existing shell infrastructure\\n    \\n    ## External Constraints\\n    - Windows context menu system architecture and limitations\\n    - Nilesoft Shell version compatibility and feature set\\n    - Need for backward compatibility with existing Windows functionality\\n    - Performance impact considerations for menu rendering and operations\\n    - Security considerations for script execution and application launching\\n    \\n    ## Key Success Metrics\\n    - Reduced time to access applications and perform file operations\\n    - Improved menu organization and visual clarity\\n    - Extensibility without code duplication or structure compromise\\n    - Minimal impact on system performance\\n    - Reliability across Windows versions and configurations\\n```\\n\\n---\\n\\n#### `03_patterns.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Hierarchical organization: _init → variables → items → groups → menus → contexts\\n    - NSS files are modular with single-responsibility principle\\n    - Items represent individual menu entries; groups combine related items\\n    - Contexts determine when and where menus appear\\n    \\n    # 03_patterns.md\\n    \\n    ## System Architecture\\n    \\n    ### Core Components Hierarchy\\n    1. **Initialization (_1_init/)**: Sets up constants, configurations, and base environment\\n    2. **Variables (_2_variables/)**: Defines reusable values and settings\\n    3. **Items (_3_items/)**: Individual menu entries (commands, applications, actions)\\n    4. **Groups (_4_groups/)**: Logical collections of related items\\n    5. **Menus (_5_menus/)**: Structured arrangements of items and groups\\n    6. **Contexts (_6_contexts/)**: Rules determining when and where menus appear\\n    \\n    ### Design Patterns\\n    \\n    #### Modular Composition\\n    - Each NSS file has a single responsibility\\n    - Components are composed through inclusion and reference\\n    - Changes to one component minimally impact others\\n    \\n    #### Naming Conventions\\n    - File prefixes indicate component type (`itm_` for items, etc.)\\n    - Consistent categorization (`app_`, `action_`, `sys_` prefixes)\\n    - Descriptive suffixes for variant behavior\\n    \\n    #### Conditional Visibility\\n    - Menu items show/hide based on context rules\\n    - Rules can evaluate file types, locations, system state\\n    - Compound conditions for precise targeting\\n    \\n    #### Icon Standardization\\n    - Consistent icon usage across similar functions\\n    - Icon mapping for visual categorization\\n    - Fallback icons for compatibility\\n    \\n    ## Component Relationships\\n    \\n    ```mermaid\\n    graph TD\\n        Init[_1_init] --> Vars[_2_variables]\\n        Vars --> Items[_3_items]\\n        Items --> Groups[_4_groups]\\n        Groups --> Menus[_5_menus]\\n        Menus --> Contexts[_6_contexts]\\n        \\n        subgraph \\\"Item Types\\\"\\n            AppItems[Application Launchers]\\n            ActionItems[File Actions]\\n            SystemItems[System Operations]\\n        end\\n        \\n        Items --- AppItems\\n        Items --- ActionItems\\n        Items --- SystemItems\\n    ```\\n    \\n    ## Schema Patterns\\n    \\n    ### Item Schema\\n    ```\\n    item {\\n      name: \\\"item_name\\\"\\n      icon: \\\"icon_path_or_resource\\\"\\n      command: \\\"command_to_execute\\\"\\n      visibility: \\\"condition_expression\\\"\\n    }\\n    ```\\n    \\n    ### Group Schema\\n    ```\\n    group {\\n      name: \\\"group_name\\\"\\n      icon: \\\"group_icon\\\"\\n      items: [\\n        // references to items or inline definitions\\n      ]\\n    }\\n    ```\\n    \\n    ### Menu Schema\\n    ```\\n    menu {\\n      name: \\\"menu_name\\\"\\n      items: [\\n        // groups and/or individual items\\n      ]\\n      contexts: [\\n        // when/where this menu appears\\n      ]\\n    }\\n    ```\\n    \\n    ## Pattern Evolution Strategy\\n    - Maintain backward compatibility when adding new patterns\\n    - Document pattern changes in corresponding memory bank files\\n    - Test pattern modifications across different file contexts\\n    - Prefer extending existing patterns over creating new ones\\n```\\n\\n---\\n\\n#### `04_tech.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Built on Nilesoft Shell framework for Windows context menu customization\\n    - NSS scripting language for defining menu items and behavior\\n    - Modular file organization in _1_init through _6_contexts directories\\n    - Supporting resources include PNG/SVG icons and batch scripts\\n    \\n    # 04_tech.md\\n    \\n    ## Core Technologies\\n    \\n    ### Nilesoft Shell\\n    - **Version**: Shell v1.9+ (current observed version: v1.9.15)\\n    - **Purpose**: Framework for customizing Windows Explorer context menus\\n    - **Architecture**: Shell extension that intercepts and modifies Windows context menu requests\\n    - **Configuration**: NSS scripts compiled and loaded by Shell engine\\n    \\n    ### NSS Scripting\\n    - **Language**: Nilesoft Shell Script (NSS)\\n    - **Syntax**: C-like with specialized directives for menu definition\\n    - **Scope**: Declarative definitions with procedural capabilities\\n    - **Compilation**: Scripts are parsed and compiled by the Shell engine at runtime\\n    \\n    ## Infrastructure\\n    \\n    ### Directory Structure\\n    ```\\n    exe/\\n    ├── NSS/\\n    │   ├── _1_init/        # Initialization, constants, configuration\\n    │   ├── _2_variables/   # Variable definitions for reuse\\n    │   ├── _3_items/       # Individual menu items\\n    │   ├── _4_groups/      # Logical groupings of items\\n    │   ├── _5_menus/       # Menu structures and layouts\\n    │   └── _6_contexts/    # Context rules for menu visibility\\n    ├── LIB/\\n    │   ├── bat/            # Batch scripts for utilities\\n    │   ├── png/            # PNG icons for menu items\\n    │   └── svg/            # SVG icons for menu items\\n    └── shell.nss           # Main entry point for NSS configuration\\n    ```\\n    \\n    ### Integration Points\\n    - Windows Explorer shell extension\\n    - Windows Registry for registration and configuration\\n    - File system for icon and resource access\\n    - Command line for application launching\\n    - COM/Shell interfaces for advanced functionality\\n    \\n    ## Dependencies\\n    \\n    ### External Resources\\n    - Icon files (PNG, SVG) for menu item visualization\\n    - Batch files for extended functionality\\n    - Executable paths for application launching\\n    - System shell commands for file operations\\n    \\n    ### Development Tools\\n    - Resource editors for icon management\\n    - Shell extension management utilities\\n    - Text editors with NSS syntax support\\n    \\n    ## Technical Constraints\\n    \\n    ### Performance Considerations\\n    - Context menu load time impact\\n    - Memory footprint of loaded NSS scripts\\n    - Efficiency of condition evaluation for visibility rules\\n    \\n    ### Compatibility\\n    - Windows version support (10, 11)\\n    - Interaction with other shell extensions\\n    - Application path resolution across different environments\\n    \\n    ### Security\\n    - Execution permissions for launched applications\\n    - Script injection prevention\\n    - Validation of external resource paths\\n    \\n    ## Technical Roadmap\\n    - Ongoing migration to latest Nilesoft Shell features\\n    - Standardization of icon resources\\n    - Optimization of conditional visibility expressions\\n    - Enhanced script modularity and reusability\\n```\\n\\n---\\n\\n#### `05_activity.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Current focus on populating individual menu items (_3_items directory)\\n    - Need to develop groups, menus, and contexts structure\\n    - Application launchers are the most extensively implemented item type\\n    - System action items have basic implementation\\n    \\n    # 05_activity.md\\n    \\n    ## Current Focus Areas\\n    \\n    ### Item Development\\n    - Extensive collection of application launcher items (`itm_app_*.nss`) already implemented\\n    - Basic system action items created (`itm_action_sys_*.nss`) \\n    - Need to audit existing items for consistency in naming and functionality\\n    - Opportunity to standardize icon usage across similar item types\\n    \\n    ### Structural Components\\n    - Groups (`_4_groups/`) directory needs population to organize related items\\n    - Menus (`_5_menus/`) require definition to establish menu layouts and hierarchy\\n    - Contexts (`_6_contexts/`) need implementation to control when menus appear\\n    \\n    ### Technical Infrastructure\\n    - Ensure initialization files in `_1_init/` properly set up the environment\\n    - Review variables in `_2_variables/` for completeness and usefulness\\n    - Validate resource paths and icon references\\n    \\n    ## In-Progress Work\\n    \\n    ### Application Launcher Standardization\\n    - Standardizing application launcher items with consistent:\\n      - Command parameter formatting\\n      - Icon selection and fallbacks\\n      - Visibility conditions\\n      - Naming conventions\\n    \\n    ### System Functionality Integration\\n    - Integrating core Windows functionality through specialized items\\n    - Creating consistent approach for system utility access\\n    - Implementing common file operations\\n    \\n    ### Organization Strategy\\n    - Developing logical grouping strategy for related items\\n    - Planning menu hierarchy based on frequency of use and context\\n    - Establishing visibility rules based on file types and locations\\n    \\n    ## Technical Decisions\\n    \\n    ### Item Implementation Pattern\\n    ```nss\\n    item {\\n      name: \\\"Application Name\\\"\\n      icon: \\\"path/to/icon.png\\\"\\n      command: \\\"path/to/executable.exe [parameters]\\\"\\n      \\n      // Optional visibility condition\\n      visibility: [condition expression]\\n    }\\n    ```\\n    \\n    ### Resource Path Strategy\\n    - Using relative paths for icons and resources where possible\\n    - Standardizing on Shell-compatible path syntax\\n    - Planning for fallback icons when primary resources unavailable\\n    \\n    ### Menu Appearance Decisions\\n    - Consistent separators between logical groups\\n    - Icon alignment and sizing standardization\\n    - Text formatting and capitalization rules\\n```\\n\\n---\\n\\n#### `06_progress.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Directory structure and modular approach established\\n    - Extensive collection of application launcher items implemented\\n    - Basic system action items created\\n    - Need to develop grouping and context structures\\n    \\n    # 06_progress.md\\n    \\n    ## State of the Build\\n    \\n    ### Completed Components\\n    - ✅ Overall directory structure established for modular organization\\n    - ✅ Basic initialization structure in `_1_init/` directory\\n    - ✅ Extensive collection of application launcher items (~70 items)\\n    - ✅ Core system action items for basic operations\\n    - ✅ Resource libraries for icons and batch utilities\\n    \\n    ### In Progress\\n    - 🔄 Variable standardization in `_2_variables/` directory\\n    - 🔄 Audit of existing items for consistency and effectiveness\\n    - 🔄 Documentation of item patterns and best practices\\n    - 🔄 Standardization of icon usage and resource paths\\n    \\n    ### Not Started\\n    - ⏳ Population of `_4_groups/` directory to organize items\\n    - ⏳ Definition of menu structures in `_5_menus/` directory\\n    - ⏳ Implementation of context rules in `_6_contexts/` directory\\n    - ⏳ Comprehensive testing across different file types\\n    \\n    ## Milestones\\n    \\n    ### Milestone 1: Foundation (COMPLETED)\\n    - ✓ Establish directory structure\\n    - ✓ Implement basic initialization\\n    - ✓ Create core application launcher items\\n    - ✓ Set up resource libraries\\n    \\n    ### Milestone 2: Structure (IN PROGRESS)\\n    - ✓ Standardize item implementation patterns\\n    - ✓ Complete system action items\\n    - 🔄 Document existing items and patterns\\n    - ⏳ Organize items into logical groups\\n    \\n    ### Milestone 3: Integration (PLANNED)\\n    - Define menu structures\\n    - Implement context rules\\n    - Create conditional visibility based on file types\\n    - Optimize menu organization\\n    \\n    ### Milestone 4: Refinement (PLANNED)\\n    - Comprehensive testing\\n    - Performance optimization\\n    - Icon standardization\\n    - Documentation completion\\n    \\n    ## Current Blockers\\n    - Need to develop a consistent strategy for grouping related items\\n    - Lack of defined context rules for when menus should appear\\n    - Incomplete documentation of existing patterns and structures\\n    \\n    ## Recent Updates\\n    - Added multiple application launcher items for common utilities\\n    - Created system action items for file operations\\n    - Established resource libraries for icons and batch scripts\\n    - Documented NSS patterns and implementation strategies\\n    \\n    ## Next Actions\\n    1. Complete the audit of existing items for consistency\\n    2. Develop a logical grouping strategy for items\\n    3. Begin populating the `_4_groups/` directory\\n    4. Create a menu structure plan for the `_5_menus/` directory\\n    5. Establish basic context rules in the `_6_contexts/` directory\\n```\\n\\n---\\n\\n#### `07_tasks.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Organize existing items into logical groups\\n    - Develop menu structures based on usage patterns\\n    - Implement context rules for menu visibility\\n    - Create documentation for future item development\\n    \\n    # 07_tasks.md\\n    \\n    ## High Priority Tasks\\n    \\n    ### Group Structure Development\\n    - [ ] **Create Application Groups**\\n      - Create `grp_apps_dev.nss` for development tools\\n      - Create `grp_apps_media.nss` for media applications\\n      - Create `grp_apps_system.nss` for system utilities\\n      - Create `grp_apps_office.nss` for productivity applications\\n      - *Owner: System developer*\\n      - *Justification: Organize the extensive list of application items into logical groups*\\n    \\n    - [ ] **Create Action Groups**\\n      - Create `grp_actions_file.nss` for file operations\\n      - Create `grp_actions_folder.nss` for folder operations\\n      - Create `grp_actions_system.nss` for system operations\\n      - *Owner: System developer*\\n      - *Justification: Group related actions for better menu organization*\\n    \\n    ### Menu Structure Implementation\\n    - [ ] **Design Core Menus**\\n      - Create `menu_applications.nss` with application groups\\n      - Create `menu_actions.nss` with action groups\\n      - Create `menu_system.nss` with system utilities\\n      - *Owner: System developer*\\n      - *Justification: Establish logical menu structure for better user experience*\\n    \\n    - [ ] **Implement Cascading Menus**\\n      - Design depth strategy (max 2-3 levels deep)\\n      - Implement parent-child relationships\\n      - *Owner: System developer*\\n      - *Justification: Organize complex item collections without cluttering main menu*\\n    \\n    ### Context Implementation\\n    - [ ] **Define File Type Contexts**\\n      - Create rules for different file extensions\\n      - Implement special handling for executables, documents, media\\n      - *Owner: System developer*\\n      - *Justification: Show relevant options based on file type*\\n    \\n    - [ ] **Define Location Contexts**\\n      - Create rules for desktop, drives, libraries\\n      - Implement special handling for system folders\\n      - *Owner: System developer*\\n      - *Justification: Show relevant options based on location*\\n    \\n    ## Medium Priority Tasks\\n    \\n    ### Documentation\\n    - [ ] **Document Group Patterns**\\n      - Create templates for new groups\\n      - Document naming conventions\\n      - *Owner: Documentation specialist*\\n      - *Justification: Ensure consistency in future development*\\n    \\n    - [ ] **Document Menu Structures**\\n      - Create visual hierarchy diagrams\\n      - Document menu organization principles\\n      - *Owner: Documentation specialist*\\n      - *Justification: Provide clear guidance for menu development*\\n    \\n    ### Quality Assurance\\n    - [ ] **Audit Existing Items**\\n      - Review naming consistency\\n      - Validate command parameters\\n      - Check icon references\\n      - *Owner: QA specialist*\\n      - *Justification: Ensure existing items follow standards*\\n    \\n    - [ ] **Test Across Environments**\\n      - Verify functionality on Windows 10\\n      - Verify functionality on Windows 11\\n      - Test with various file types\\n      - *Owner: QA specialist*\\n      - *Justification: Ensure compatibility across environments*\\n    \\n    ## Low Priority Tasks\\n    \\n    ### Optimization\\n    - [ ] **Review Performance**\\n      - Profile menu load times\\n      - Optimize condition evaluations\\n      - *Owner: Performance specialist*\\n      - *Justification: Ensure menus load quickly*\\n    \\n    - [ ] **Icon Standardization**\\n      - Create consistent icon set\\n      - Implement fallback strategy\\n      - *Owner: Design specialist*\\n      - *Justification: Visual consistency across menus*\\n    \\n    ## Dependencies\\n    - Group creation depends on completion of item audit\\n    - Menu implementation depends on group creation\\n    - Context rules depend on menu implementation\\n    - Testing depends on all implementation tasks\\n    \\n    ## Task Assignment Strategy\\n    - Focus on high-priority tasks first\\n    - Complete related tasks in sequence (items → groups → menus → contexts)\\n    - Document as you go to maintain knowledge base\\n```\\n\\n---\\n\\n#### `08_objective.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Create a complete, modular context menu system for Windows Explorer\\n    - Immediate focus: Group existing items and implement menu structures\\n    - Success measured by efficiency, organization, and extensibility\\n    - Target completion of Milestone 3 (Integration) by next development cycle\\n    \\n    # 08_objective.md\\n    \\n    ## Primary Objective\\n    Create a comprehensive Windows context menu system that enhances productivity through logically organized, easily accessible commands and applications, using a modular architecture that ensures maintainability and extensibility.\\n    \\n    ## Success Criteria\\n    The context menu system will be considered successful when it:\\n    \\n    1. **Provides Efficient Access**\\n       - Reduces clicks needed to access common applications\\n       - Speeds up file operations through contextual commands\\n       - Groups related functionality in intuitive categories\\n    \\n    2. **Maintains Logical Organization**\\n       - Presents a consistent menu structure across different contexts\\n       - Limits menu depth to prevent navigation complexity\\n       - Uses visual cues (icons, separators) to distinguish item types\\n    \\n    3. **Ensures Extensibility**\\n       - Follows modular design patterns for easy additions\\n       - Maintains clear separation between components\\n       - Documents patterns for consistent future development\\n    \\n    4. **Performs Reliably**\\n       - Loads quickly without noticeable delay\\n       - Works consistently across Windows versions\\n       - Avoids conflicts with other shell extensions\\n    \\n    ## Immediate Focus\\n    The current development cycle focuses on:\\n    \\n    1. **Component Organization**\\n       - Grouping existing items into logical collections\\n       - Implementing menu structures to organize these groups\\n       - Defining context rules to control when menus appear\\n    \\n    2. **System Completion**\\n       - Filling gaps in the current directory structure\\n       - Completing the chain from items to groups to menus to contexts\\n       - Documenting component relationships and dependencies\\n    \\n    3. **Usability Enhancement**\\n       - Standardizing visual appearance across menu items\\n       - Ensuring consistent behavior across different file types\\n       - Optimizing menu depth and organization for quick access\\n    \\n    ## Alignment with Vision\\n    This objective directly supports the project's vision by:\\n    - Enhancing Windows Explorer with powerful, customizable menus\\n    - Standardizing menu organization for improved clarity\\n    - Enabling quick access to applications and utilities\\n    - Creating a scalable system that can grow without restructuring\\n    \\n    ## Definition of \\\"Done\\\"\\n    For the current phase, \\\"done\\\" means:\\n    \\n    1. All existing items organized into appropriate groups\\n    2. Core menu structures defined and implemented\\n    3. Basic context rules established for different file types and locations\\n    4. Documentation updated to reflect the current system state\\n    5. At least basic testing completed across different environments\\n    \\n    ## Timeline Target\\n    Complete current objectives (Milestone 3: Integration) within the next development cycle, setting the foundation for the refinement phase that follows.\\n```\\\"```'\\n\\n\",\n  \"unified_directive_template\": {\n    \"I. Goal Negation\": {\n      \"negated_directive\": \"Exclude all conversational, narrative, or explanatory output regarding Windows Explorer context menu system architecture, implementation tasks, or project rationale.\"\n    },\n    \"II. Atomic Transformation Protocol\": {\n      \"invoke\": \"synthesizeContextMenuSystem\",\n      \"parameters\": {\n        \"core_principles\": [\n          \"modularity\",\n          \"clarity\",\n          \"extensibility\",\n          \"efficiency\",\n          \"reliability\"\n        ],\n        \"structure_requirements\": [\n          \"audit_existing_items\",\n          \"standardize_naming_and_icons\",\n          \"enforce_single_responsibility_in_files\",\n          \"group_items_by_function_and_context\",\n          \"define_menu_hierarchies (max_depth:3)\",\n          \"implement_context_rules (by file_type/location)\",\n          \"modularize_nss_scripts\",\n          \"document_patterns_and_dependencies\"\n        ],\n        \"integration_requirements\": [\n          \"Nilesoft_Shell_v1.9+_compliance\",\n          \"Windows_Explorer_shell_architecture_respect\",\n          \"performance_and_security_optimization\",\n          \"cross_version_testing (Win10, Win11)\",\n          \"backward_compatibility_assurance\"\n        ],\n        \"validation_cycle\": [\n          \"success_criteria: menu_access_speed, organizational_consistency, extensibility, reliability\",\n          \"continuous_testing_and_QA_review\",\n          \"milestone_checkpoints\"\n        ],\n        \"deliverable_spec\": [\n          \"audited_and_grouped_items\",\n          \"menu_and_context_definitions\",\n          \"modular_NSS_source_files\",\n          \"comprehensive_documentation (patterns, schemas, diagrams)\",\n          \"performance_and_reliability_reports\"\n        ]\n      }\n    },\n    \"III. Protocol Conformance Validation\": {\n      \"compliance_status\": [\n        \"strict three-part format: enforced\",\n        \"atomic transformation actions: specified\",\n        \"zero narrative content: verified\",\n        \"type-safe parameters: present\",\n        \"maximized transformation logic density: achieved\",\n        \"bidirectional structure preservation: ensured\"\n      ]\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"I. Goal Negation\": {\n    \"negated_directive\": \"Exclude all conversational, narrative, or explanatory output regarding Windows Explorer context menu system architecture, implementation tasks, or project rationale.\"\n  },\n  \"II. Atomic Transformation Protocol\": {\n    \"invoke\": \"synthesizeContextMenuSystem\",\n    \"parameters\": {\n      \"core_principles\": [\n        \"modularity\",\n        \"clarity\",\n        \"extensibility\",\n        \"efficiency\",\n        \"reliability\"\n      ],\n      \"structure_requirements\": [\n        \"audit_existing_items\",\n        \"standardize_naming_and_icons\",\n        \"enforce_single_responsibility_in_files\",\n        \"group_items_by_function_and_context\",\n        \"define_menu_hierarchies (max_depth:3)\",\n        \"implement_context_rules (by file_type/location)\",\n        \"modularize_nss_scripts\",\n        \"document_patterns_and_dependencies\"\n      ],\n      \"integration_requirements\": [\n        \"Nilesoft_Shell_v1.9+_compliance\",\n        \"Windows_Explorer_shell_architecture_respect\",\n        \"performance_and_security_optimization\",\n        \"cross_version_testing (Win10, Win11)\",\n        \"backward_compatibility_assurance\"\n      ],\n      \"validation_cycle\": [\n        \"success_criteria: menu_access_speed, organizational_consistency, extensibility, reliability\",\n        \"continuous_testing_and_QA_review\",\n        \"milestone_checkpoints\"\n      ],\n      \"deliverable_spec\": [\n        \"audited_and_grouped_items\",\n        \"menu_and_context_definitions\",\n        \"modular_NSS_source_files\",\n        \"comprehensive_documentation (patterns, schemas, diagrams)\",\n        \"performance_and_reliability_reports\"\n      ]\n    }\n  },\n  \"III. Protocol Conformance Validation\": {\n    \"compliance_status\": [\n      \"strict three-part format: enforced\",\n      \"atomic transformation actions: specified\",\n      \"zero narrative content: verified\",\n      \"type-safe parameters: present\",\n      \"maximized transformation logic density: achieved\",\n      \"bidirectional structure preservation: ensured\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"### File Structure\\n\\n```\\n├── 01_foundation.md\\n├── 02_context.md\\n├── 03_patterns.md\\n├── 04_tech.md\\n├── 05_activity.md\\n├── 06_progress.md\\n├── 07_tasks.md\\n└── 08_objective.md\\n```\\n\\n---\\n\\n#### `01_foundation.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Custom Windows context menu system built on Nilesoft Shell\\n    - Purpose: Enhance Windows file explorer with efficient context actions\\n    - Principles: Modularity, consistency, and extensibility\\n    \\n    # 01_foundation.md\\n    \\n    ## Core Mission\\n    The Nilesoft Shell context menu system aims to significantly enhance Windows file explorer with a powerful, customizable context menu framework that improves workflow efficiency and user experience.\\n    \\n    ## Vision\\n    Create a comprehensive context menu system that:\\n    - Provides quick access to frequently used applications and utilities\\n    - Standardizes menu organization and appearance\\n    - Enables rapid file/folder operations through contextual commands\\n    - Scales elegantly with minimal maintenance overhead\\n    \\n    ## Values\\n    - **Efficiency**: Minimize clicks and navigation time\\n    - **Clarity**: Intuitive menu organization with consistent naming and grouping\\n    - **Extensibility**: Easily add new functionality without restructuring\\n    - **Reliability**: Stable operation without conflicts or performance impact\\n    \\n    ## Strategic Goals\\n    1. Standardize application launching from any context\\n    2. Optimize file management operations\\n    3. Group related functionality logically\\n    4. Leverage NSS scripting for advanced automation\\n    5. Maintain backward compatibility with Windows shell\\n```\\n\\n---\\n\\n#### `02_context.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Windows context menus often become cluttered and inconsistent\\n    - Users need quick access to applications and file operations\\n    - Nilesoft Shell provides programmatic control over Windows context menus\\n    \\n    # 02_context.md\\n    \\n    ## Problem Space\\n    \\n    ### Current Limitations\\n    - Default Windows context menus lack organization and customization options\\n    - Third-party applications inject entries haphazardly, creating menu bloat\\n    - No standard interface for managing context menu appearance and behavior\\n    - Limited ability to group related actions or create conditional menus\\n    \\n    ### User Needs\\n    - Quick access to frequently used applications from any file/folder context\\n    - Consistent organization of menu items across different file types\\n    - Visual distinction between types of operations (system, applications, custom scripts)\\n    - Ability to conditionally show/hide menu options based on file attributes\\n    - Simplified access to system utilities and administrative functions\\n    \\n    ## Stakeholders\\n    - **End Users**: Individuals seeking workflow efficiency improvements\\n    - **System Administrators**: Managing standard configurations across machines\\n    - **Developers**: Extending functionality through Nilesoft Shell scripts (NSS)\\n    - **Windows Environment**: Integration with existing shell infrastructure\\n    \\n    ## External Constraints\\n    - Windows context menu system architecture and limitations\\n    - Nilesoft Shell version compatibility and feature set\\n    - Need for backward compatibility with existing Windows functionality\\n    - Performance impact considerations for menu rendering and operations\\n    - Security considerations for script execution and application launching\\n    \\n    ## Key Success Metrics\\n    - Reduced time to access applications and perform file operations\\n    - Improved menu organization and visual clarity\\n    - Extensibility without code duplication or structure compromise\\n    - Minimal impact on system performance\\n    - Reliability across Windows versions and configurations\\n```\\n\\n---\\n\\n#### `03_patterns.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Hierarchical organization: _init → variables → items → groups → menus → contexts\\n    - NSS files are modular with single-responsibility principle\\n    - Items represent individual menu entries; groups combine related items\\n    - Contexts determine when and where menus appear\\n    \\n    # 03_patterns.md\\n    \\n    ## System Architecture\\n    \\n    ### Core Components Hierarchy\\n    1. **Initialization (_1_init/)**: Sets up constants, configurations, and base environment\\n    2. **Variables (_2_variables/)**: Defines reusable values and settings\\n    3. **Items (_3_items/)**: Individual menu entries (commands, applications, actions)\\n    4. **Groups (_4_groups/)**: Logical collections of related items\\n    5. **Menus (_5_menus/)**: Structured arrangements of items and groups\\n    6. **Contexts (_6_contexts/)**: Rules determining when and where menus appear\\n    \\n    ### Design Patterns\\n    \\n    #### Modular Composition\\n    - Each NSS file has a single responsibility\\n    - Components are composed through inclusion and reference\\n    - Changes to one component minimally impact others\\n    \\n    #### Naming Conventions\\n    - File prefixes indicate component type (`itm_` for items, etc.)\\n    - Consistent categorization (`app_`, `action_`, `sys_` prefixes)\\n    - Descriptive suffixes for variant behavior\\n    \\n    #### Conditional Visibility\\n    - Menu items show/hide based on context rules\\n    - Rules can evaluate file types, locations, system state\\n    - Compound conditions for precise targeting\\n    \\n    #### Icon Standardization\\n    - Consistent icon usage across similar functions\\n    - Icon mapping for visual categorization\\n    - Fallback icons for compatibility\\n    \\n    ## Component Relationships\\n    \\n    ```mermaid\\n    graph TD\\n        Init[_1_init] --> Vars[_2_variables]\\n        Vars --> Items[_3_items]\\n        Items --> Groups[_4_groups]\\n        Groups --> Menus[_5_menus]\\n        Menus --> Contexts[_6_contexts]\\n        \\n        subgraph \\\"Item Types\\\"\\n            AppItems[Application Launchers]\\n            ActionItems[File Actions]\\n            SystemItems[System Operations]\\n        end\\n        \\n        Items --- AppItems\\n        Items --- ActionItems\\n        Items --- SystemItems\\n    ```\\n    \\n    ## Schema Patterns\\n    \\n    ### Item Schema\\n    ```\\n    item {\\n      name: \\\"item_name\\\"\\n      icon: \\\"icon_path_or_resource\\\"\\n      command: \\\"command_to_execute\\\"\\n      visibility: \\\"condition_expression\\\"\\n    }\\n    ```\\n    \\n    ### Group Schema\\n    ```\\n    group {\\n      name: \\\"group_name\\\"\\n      icon: \\\"group_icon\\\"\\n      items: [\\n        // references to items or inline definitions\\n      ]\\n    }\\n    ```\\n    \\n    ### Menu Schema\\n    ```\\n    menu {\\n      name: \\\"menu_name\\\"\\n      items: [\\n        // groups and/or individual items\\n      ]\\n      contexts: [\\n        // when/where this menu appears\\n      ]\\n    }\\n    ```\\n    \\n    ## Pattern Evolution Strategy\\n    - Maintain backward compatibility when adding new patterns\\n    - Document pattern changes in corresponding memory bank files\\n    - Test pattern modifications across different file contexts\\n    - Prefer extending existing patterns over creating new ones\\n```\\n\\n---\\n\\n#### `04_tech.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Built on Nilesoft Shell framework for Windows context menu customization\\n    - NSS scripting language for defining menu items and behavior\\n    - Modular file organization in _1_init through _6_contexts directories\\n    - Supporting resources include PNG/SVG icons and batch scripts\\n    \\n    # 04_tech.md\\n    \\n    ## Core Technologies\\n    \\n    ### Nilesoft Shell\\n    - **Version**: Shell v1.9+ (current observed version: v1.9.15)\\n    - **Purpose**: Framework for customizing Windows Explorer context menus\\n    - **Architecture**: Shell extension that intercepts and modifies Windows context menu requests\\n    - **Configuration**: NSS scripts compiled and loaded by Shell engine\\n    \\n    ### NSS Scripting\\n    - **Language**: Nilesoft Shell Script (NSS)\\n    - **Syntax**: C-like with specialized directives for menu definition\\n    - **Scope**: Declarative definitions with procedural capabilities\\n    - **Compilation**: Scripts are parsed and compiled by the Shell engine at runtime\\n    \\n    ## Infrastructure\\n    \\n    ### Directory Structure\\n    ```\\n    exe/\\n    ├── NSS/\\n    │   ├── _1_init/        # Initialization, constants, configuration\\n    │   ├── _2_variables/   # Variable definitions for reuse\\n    │   ├── _3_items/       # Individual menu items\\n    │   ├── _4_groups/      # Logical groupings of items\\n    │   ├── _5_menus/       # Menu structures and layouts\\n    │   └── _6_contexts/    # Context rules for menu visibility\\n    ├── LIB/\\n    │   ├── bat/            # Batch scripts for utilities\\n    │   ├── png/            # PNG icons for menu items\\n    │   └── svg/            # SVG icons for menu items\\n    └── shell.nss           # Main entry point for NSS configuration\\n    ```\\n    \\n    ### Integration Points\\n    - Windows Explorer shell extension\\n    - Windows Registry for registration and configuration\\n    - File system for icon and resource access\\n    - Command line for application launching\\n    - COM/Shell interfaces for advanced functionality\\n    \\n    ## Dependencies\\n    \\n    ### External Resources\\n    - Icon files (PNG, SVG) for menu item visualization\\n    - Batch files for extended functionality\\n    - Executable paths for application launching\\n    - System shell commands for file operations\\n    \\n    ### Development Tools\\n    - Resource editors for icon management\\n    - Shell extension management utilities\\n    - Text editors with NSS syntax support\\n    \\n    ## Technical Constraints\\n    \\n    ### Performance Considerations\\n    - Context menu load time impact\\n    - Memory footprint of loaded NSS scripts\\n    - Efficiency of condition evaluation for visibility rules\\n    \\n    ### Compatibility\\n    - Windows version support (10, 11)\\n    - Interaction with other shell extensions\\n    - Application path resolution across different environments\\n    \\n    ### Security\\n    - Execution permissions for launched applications\\n    - Script injection prevention\\n    - Validation of external resource paths\\n    \\n    ## Technical Roadmap\\n    - Ongoing migration to latest Nilesoft Shell features\\n    - Standardization of icon resources\\n    - Optimization of conditional visibility expressions\\n    - Enhanced script modularity and reusability\\n```\\n\\n---\\n\\n#### `05_activity.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Current focus on populating individual menu items (_3_items directory)\\n    - Need to develop groups, menus, and contexts structure\\n    - Application launchers are the most extensively implemented item type\\n    - System action items have basic implementation\\n    \\n    # 05_activity.md\\n    \\n    ## Current Focus Areas\\n    \\n    ### Item Development\\n    - Extensive collection of application launcher items (`itm_app_*.nss`) already implemented\\n    - Basic system action items created (`itm_action_sys_*.nss`) \\n    - Need to audit existing items for consistency in naming and functionality\\n    - Opportunity to standardize icon usage across similar item types\\n    \\n    ### Structural Components\\n    - Groups (`_4_groups/`) directory needs population to organize related items\\n    - Menus (`_5_menus/`) require definition to establish menu layouts and hierarchy\\n    - Contexts (`_6_contexts/`) need implementation to control when menus appear\\n    \\n    ### Technical Infrastructure\\n    - Ensure initialization files in `_1_init/` properly set up the environment\\n    - Review variables in `_2_variables/` for completeness and usefulness\\n    - Validate resource paths and icon references\\n    \\n    ## In-Progress Work\\n    \\n    ### Application Launcher Standardization\\n    - Standardizing application launcher items with consistent:\\n      - Command parameter formatting\\n      - Icon selection and fallbacks\\n      - Visibility conditions\\n      - Naming conventions\\n    \\n    ### System Functionality Integration\\n    - Integrating core Windows functionality through specialized items\\n    - Creating consistent approach for system utility access\\n    - Implementing common file operations\\n    \\n    ### Organization Strategy\\n    - Developing logical grouping strategy for related items\\n    - Planning menu hierarchy based on frequency of use and context\\n    - Establishing visibility rules based on file types and locations\\n    \\n    ## Technical Decisions\\n    \\n    ### Item Implementation Pattern\\n    ```nss\\n    item {\\n      name: \\\"Application Name\\\"\\n      icon: \\\"path/to/icon.png\\\"\\n      command: \\\"path/to/executable.exe [parameters]\\\"\\n      \\n      // Optional visibility condition\\n      visibility: [condition expression]\\n    }\\n    ```\\n    \\n    ### Resource Path Strategy\\n    - Using relative paths for icons and resources where possible\\n    - Standardizing on Shell-compatible path syntax\\n    - Planning for fallback icons when primary resources unavailable\\n    \\n    ### Menu Appearance Decisions\\n    - Consistent separators between logical groups\\n    - Icon alignment and sizing standardization\\n    - Text formatting and capitalization rules\\n```\\n\\n---\\n\\n#### `06_progress.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Directory structure and modular approach established\\n    - Extensive collection of application launcher items implemented\\n    - Basic system action items created\\n    - Need to develop grouping and context structures\\n    \\n    # 06_progress.md\\n    \\n    ## State of the Build\\n    \\n    ### Completed Components\\n    - ✅ Overall directory structure established for modular organization\\n    - ✅ Basic initialization structure in `_1_init/` directory\\n    - ✅ Extensive collection of application launcher items (~70 items)\\n    - ✅ Core system action items for basic operations\\n    - ✅ Resource libraries for icons and batch utilities\\n    \\n    ### In Progress\\n    - 🔄 Variable standardization in `_2_variables/` directory\\n    - 🔄 Audit of existing items for consistency and effectiveness\\n    - 🔄 Documentation of item patterns and best practices\\n    - 🔄 Standardization of icon usage and resource paths\\n    \\n    ### Not Started\\n    - ⏳ Population of `_4_groups/` directory to organize items\\n    - ⏳ Definition of menu structures in `_5_menus/` directory\\n    - ⏳ Implementation of context rules in `_6_contexts/` directory\\n    - ⏳ Comprehensive testing across different file types\\n    \\n    ## Milestones\\n    \\n    ### Milestone 1: Foundation (COMPLETED)\\n    - ✓ Establish directory structure\\n    - ✓ Implement basic initialization\\n    - ✓ Create core application launcher items\\n    - ✓ Set up resource libraries\\n    \\n    ### Milestone 2: Structure (IN PROGRESS)\\n    - ✓ Standardize item implementation patterns\\n    - ✓ Complete system action items\\n    - 🔄 Document existing items and patterns\\n    - ⏳ Organize items into logical groups\\n    \\n    ### Milestone 3: Integration (PLANNED)\\n    - Define menu structures\\n    - Implement context rules\\n    - Create conditional visibility based on file types\\n    - Optimize menu organization\\n    \\n    ### Milestone 4: Refinement (PLANNED)\\n    - Comprehensive testing\\n    - Performance optimization\\n    - Icon standardization\\n    - Documentation completion\\n    \\n    ## Current Blockers\\n    - Need to develop a consistent strategy for grouping related items\\n    - Lack of defined context rules for when menus should appear\\n    - Incomplete documentation of existing patterns and structures\\n    \\n    ## Recent Updates\\n    - Added multiple application launcher items for common utilities\\n    - Created system action items for file operations\\n    - Established resource libraries for icons and batch scripts\\n    - Documented NSS patterns and implementation strategies\\n    \\n    ## Next Actions\\n    1. Complete the audit of existing items for consistency\\n    2. Develop a logical grouping strategy for items\\n    3. Begin populating the `_4_groups/` directory\\n    4. Create a menu structure plan for the `_5_menus/` directory\\n    5. Establish basic context rules in the `_6_contexts/` directory\\n```\\n\\n---\\n\\n#### `07_tasks.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Organize existing items into logical groups\\n    - Develop menu structures based on usage patterns\\n    - Implement context rules for menu visibility\\n    - Create documentation for future item development\\n    \\n    # 07_tasks.md\\n    \\n    ## High Priority Tasks\\n    \\n    ### Group Structure Development\\n    - [ ] **Create Application Groups**\\n      - Create `grp_apps_dev.nss` for development tools\\n      - Create `grp_apps_media.nss` for media applications\\n      - Create `grp_apps_system.nss` for system utilities\\n      - Create `grp_apps_office.nss` for productivity applications\\n      - *Owner: System developer*\\n      - *Justification: Organize the extensive list of application items into logical groups*\\n    \\n    - [ ] **Create Action Groups**\\n      - Create `grp_actions_file.nss` for file operations\\n      - Create `grp_actions_folder.nss` for folder operations\\n      - Create `grp_actions_system.nss` for system operations\\n      - *Owner: System developer*\\n      - *Justification: Group related actions for better menu organization*\\n    \\n    ### Menu Structure Implementation\\n    - [ ] **Design Core Menus**\\n      - Create `menu_applications.nss` with application groups\\n      - Create `menu_actions.nss` with action groups\\n      - Create `menu_system.nss` with system utilities\\n      - *Owner: System developer*\\n      - *Justification: Establish logical menu structure for better user experience*\\n    \\n    - [ ] **Implement Cascading Menus**\\n      - Design depth strategy (max 2-3 levels deep)\\n      - Implement parent-child relationships\\n      - *Owner: System developer*\\n      - *Justification: Organize complex item collections without cluttering main menu*\\n    \\n    ### Context Implementation\\n    - [ ] **Define File Type Contexts**\\n      - Create rules for different file extensions\\n      - Implement special handling for executables, documents, media\\n      - *Owner: System developer*\\n      - *Justification: Show relevant options based on file type*\\n    \\n    - [ ] **Define Location Contexts**\\n      - Create rules for desktop, drives, libraries\\n      - Implement special handling for system folders\\n      - *Owner: System developer*\\n      - *Justification: Show relevant options based on location*\\n    \\n    ## Medium Priority Tasks\\n    \\n    ### Documentation\\n    - [ ] **Document Group Patterns**\\n      - Create templates for new groups\\n      - Document naming conventions\\n      - *Owner: Documentation specialist*\\n      - *Justification: Ensure consistency in future development*\\n    \\n    - [ ] **Document Menu Structures**\\n      - Create visual hierarchy diagrams\\n      - Document menu organization principles\\n      - *Owner: Documentation specialist*\\n      - *Justification: Provide clear guidance for menu development*\\n    \\n    ### Quality Assurance\\n    - [ ] **Audit Existing Items**\\n      - Review naming consistency\\n      - Validate command parameters\\n      - Check icon references\\n      - *Owner: QA specialist*\\n      - *Justification: Ensure existing items follow standards*\\n    \\n    - [ ] **Test Across Environments**\\n      - Verify functionality on Windows 10\\n      - Verify functionality on Windows 11\\n      - Test with various file types\\n      - *Owner: QA specialist*\\n      - *Justification: Ensure compatibility across environments*\\n    \\n    ## Low Priority Tasks\\n    \\n    ### Optimization\\n    - [ ] **Review Performance**\\n      - Profile menu load times\\n      - Optimize condition evaluations\\n      - *Owner: Performance specialist*\\n      - *Justification: Ensure menus load quickly*\\n    \\n    - [ ] **Icon Standardization**\\n      - Create consistent icon set\\n      - Implement fallback strategy\\n      - *Owner: Design specialist*\\n      - *Justification: Visual consistency across menus*\\n    \\n    ## Dependencies\\n    - Group creation depends on completion of item audit\\n    - Menu implementation depends on group creation\\n    - Context rules depend on menu implementation\\n    - Testing depends on all implementation tasks\\n    \\n    ## Task Assignment Strategy\\n    - Focus on high-priority tasks first\\n    - Complete related tasks in sequence (items → groups → menus → contexts)\\n    - Document as you go to maintain knowledge base\\n```\\n\\n---\\n\\n#### `08_objective.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Create a complete, modular context menu system for Windows Explorer\\n    - Immediate focus: Group existing items and implement menu structures\\n    - Success measured by efficiency, organization, and extensibility\\n    - Target completion of Milestone 3 (Integration) by next development cycle\\n    \\n    # 08_objective.md\\n    \\n    ## Primary Objective\\n    Create a comprehensive Windows context menu system that enhances productivity through logically organized, easily accessible commands and applications, using a modular architecture that ensures maintainability and extensibility.\\n    \\n    ## Success Criteria\\n    The context menu system will be considered successful when it:\\n    \\n    1. **Provides Efficient Access**\\n       - Reduces clicks needed to access common applications\\n       - Speeds up file operations through contextual commands\\n       - Groups related functionality in intuitive categories\\n    \\n    2. **Maintains Logical Organization**\\n       - Presents a consistent menu structure across different contexts\\n       - Limits menu depth to prevent navigation complexity\\n       - Uses visual cues (icons, separators) to distinguish item types\\n    \\n    3. **Ensures Extensibility**\\n       - Follows modular design patterns for easy additions\\n       - Maintains clear separation between components\\n       - Documents patterns for consistent future development\\n    \\n    4. **Performs Reliably**\\n       - Loads quickly without noticeable delay\\n       - Works consistently across Windows versions\\n       - Avoids conflicts with other shell extensions\\n    \\n    ## Immediate Focus\\n    The current development cycle focuses on:\\n    \\n    1. **Component Organization**\\n       - Grouping existing items into logical collections\\n       - Implementing menu structures to organize these groups\\n       - Defining context rules to control when menus appear\\n    \\n    2. **System Completion**\\n       - Filling gaps in the current directory structure\\n       - Completing the chain from items to groups to menus to contexts\\n       - Documenting component relationships and dependencies\\n    \\n    3. **Usability Enhancement**\\n       - Standardizing visual appearance across menu items\\n       - Ensuring consistent behavior across different file types\\n       - Optimizing menu depth and organization for quick access\\n    \\n    ## Alignment with Vision\\n    This objective directly supports the project's vision by:\\n    - Enhancing Windows Explorer with powerful, customizable menus\\n    - Standardizing menu organization for improved clarity\\n    - Enabling quick access to applications and utilities\\n    - Creating a scalable system that can grow without restructuring\\n    \\n    ## Definition of \\\"Done\\\"\\n    For the current phase, \\\"done\\\" means:\\n    \\n    1. All existing items organized into appropriate groups\\n    2. Core menu structures defined and implemented\\n    3. Basic context rules established for different file types and locations\\n    4. Documentation updated to reflect the current system state\\n    5. At least basic testing completed across different environments\\n    \\n    ## Timeline Target\\n    Complete current objectives (Milestone 3: Integration) within the next development cycle, setting the foundation for the refinement phase that follows.\\n```\\\"```'\\n\\n\",\n  \"I. Goal Negation\": {\n    \"negated_directive\": \"Exclude all conversational, narrative, or explanatory output regarding Windows Explorer context menu system architecture, implementation tasks, or project rationale.\"\n  },\n  \"II. Atomic Transformation Protocol\": {\n    \"invoke\": \"synthesizeContextMenuSystem\",\n    \"parameters\": {\n      \"core_principles\": [\n        \"modularity\",\n        \"clarity\",\n        \"extensibility\",\n        \"efficiency\",\n        \"reliability\"\n      ],\n      \"structure_requirements\": [\n        \"audit_existing_items\",\n        \"standardize_naming_and_icons\",\n        \"enforce_single_responsibility_in_files\",\n        \"group_items_by_function_and_context\",\n        \"define_menu_hierarchies (max_depth:3)\",\n        \"implement_context_rules (by file_type/location)\",\n        \"modularize_nss_scripts\",\n        \"document_patterns_and_dependencies\"\n      ],\n      \"integration_requirements\": [\n        \"Nilesoft_Shell_v1.9+_compliance\",\n        \"Windows_Explorer_shell_architecture_respect\",\n        \"performance_and_security_optimization\",\n        \"cross_version_testing (Win10, Win11)\",\n        \"backward_compatibility_assurance\"\n      ],\n      \"validation_cycle\": [\n        \"success_criteria: menu_access_speed, organizational_consistency, extensibility, reliability\",\n        \"continuous_testing_and_QA_review\",\n        \"milestone_checkpoints\"\n      ],\n      \"deliverable_spec\": [\n        \"audited_and_grouped_items\",\n        \"menu_and_context_definitions\",\n        \"modular_NSS_source_files\",\n        \"comprehensive_documentation (patterns, schemas, diagrams)\",\n        \"performance_and_reliability_reports\"\n      ]\n    }\n  },\n  \"III. Protocol Conformance Validation\": {\n    \"compliance_status\": [\n      \"strict three-part format: enforced\",\n      \"atomic transformation actions: specified\",\n      \"zero narrative content: verified\",\n      \"type-safe parameters: present\",\n      \"maximized transformation logic density: achieved\",\n      \"bidirectional structure preservation: ensured\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"Title\": \"Windows Context Menu System Synthesis\",\n  \"Interpretation\": \"Your goal is not to explain, narrate, or justify aspects of the Windows Explorer context menu system, but to synthesize a modular, extensible, and fully-documented Windows/Nilesoft Shell context menu framework using the distilled principles, structure, and actions specified. Operate solely as a systems architect specialized in Windows/NSS menu frameworks. Execute as:\",\n  \"Execute as\": {\n    \"role\": \"windows_context_menu_system_architect\",\n    \"input\": [\n      \"project_overview:dict\",\n      \"core_principles:list\",\n      \"structure_requirements:list\",\n      \"integration_requirements:list\",\n      \"validation_cycle:list\",\n      \"deliverable_spec:list\"\n    ],\n    \"process\": [\n      \"parse_project_overview()\",\n      \"extract_distilled_principles()\",\n      \"audit_existing_item_inventory()\",\n      \"standardize_item_naming_and_icons()\",\n      \"group_items_by_function_and_context()\",\n      \"enforce_single_responsibility_in_nss_files()\",\n      \"design_menu_hierarchies(max_depth=3)\",\n      \"implement_context_rules(file_type_and_location)\",\n      \"modularize_nss_scripts()\",\n      \"document_patterns_and_dependencies()\",\n      \"apply_nilesoft_shell_v1.9plus_compliance()\",\n      \"enforce_windows_shell_architecture_compatibility()\",\n      \"optimize_for_performance_and_security()\",\n      \"test_cross_windows_versions(os_list=['10','11'])\",\n      \"establish_backward_compatibility()\",\n      \"validate_success_criteria(['menu_access_speed','organizational_consistency','extensibility','reliability'])\",\n      \"run_continuous_testing_and_qa_review()\",\n      \"monitor_and_enforce_milestone_checkpoints()\"\n    ],\n    \"constraints\": [\n      \"exclude_all_conversational_narrative_explanatory_content()\",\n      \"maintain_pattern_consistency_and_modular_file_structure()\",\n      \"operate_within_scope_of_nss_and_windows_context_menu_architecture()\",\n      \"output_only_structured_deliverables()\",\n      \"preserve_type_safety_in_all_generated_outputs()\"\n    ],\n    \"requirements\": [\n      \"deliver_audited_and_grouped_item_inventory()\",\n      \"output_menu_and_context_hierarchy_definitions()\",\n      \"generate_modular_nss_source_files()\",\n      \"provide_comprehensive_documentation_including_patterns_schemas_and_diagrams()\",\n      \"furnish_performance_and_reliability_reports()\"\n    ],\n    \"output\": {\n      \"synthesized_context_menu_system\": \"dict\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "b",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"### File Structure\\n\\n```\\n├── 01_foundation.md\\n├── 02_context.md\\n├── 03_patterns.md\\n├── 04_tech.md\\n├── 05_activity.md\\n├── 06_progress.md\\n├── 07_tasks.md\\n└── 08_objective.md\\n```\\n\\n---\\n\\n#### `01_foundation.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Custom Windows context menu system built on Nilesoft Shell\\n    - Purpose: Enhance Windows file explorer with efficient context actions\\n    - Principles: Modularity, consistency, and extensibility\\n    \\n    # 01_foundation.md\\n    \\n    ## Core Mission\\n    The Nilesoft Shell context menu system aims to significantly enhance Windows file explorer with a powerful, customizable context menu framework that improves workflow efficiency and user experience.\\n    \\n    ## Vision\\n    Create a comprehensive context menu system that:\\n    - Provides quick access to frequently used applications and utilities\\n    - Standardizes menu organization and appearance\\n    - Enables rapid file/folder operations through contextual commands\\n    - Scales elegantly with minimal maintenance overhead\\n    \\n    ## Values\\n    - **Efficiency**: Minimize clicks and navigation time\\n    - **Clarity**: Intuitive menu organization with consistent naming and grouping\\n    - **Extensibility**: Easily add new functionality without restructuring\\n    - **Reliability**: Stable operation without conflicts or performance impact\\n    \\n    ## Strategic Goals\\n    1. Standardize application launching from any context\\n    2. Optimize file management operations\\n    3. Group related functionality logically\\n    4. Leverage NSS scripting for advanced automation\\n    5. Maintain backward compatibility with Windows shell\\n```\\n\\n---\\n\\n#### `02_context.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Windows context menus often become cluttered and inconsistent\\n    - Users need quick access to applications and file operations\\n    - Nilesoft Shell provides programmatic control over Windows context menus\\n    \\n    # 02_context.md\\n    \\n    ## Problem Space\\n    \\n    ### Current Limitations\\n    - Default Windows context menus lack organization and customization options\\n    - Third-party applications inject entries haphazardly, creating menu bloat\\n    - No standard interface for managing context menu appearance and behavior\\n    - Limited ability to group related actions or create conditional menus\\n    \\n    ### User Needs\\n    - Quick access to frequently used applications from any file/folder context\\n    - Consistent organization of menu items across different file types\\n    - Visual distinction between types of operations (system, applications, custom scripts)\\n    - Ability to conditionally show/hide menu options based on file attributes\\n    - Simplified access to system utilities and administrative functions\\n    \\n    ## Stakeholders\\n    - **End Users**: Individuals seeking workflow efficiency improvements\\n    - **System Administrators**: Managing standard configurations across machines\\n    - **Developers**: Extending functionality through Nilesoft Shell scripts (NSS)\\n    - **Windows Environment**: Integration with existing shell infrastructure\\n    \\n    ## External Constraints\\n    - Windows context menu system architecture and limitations\\n    - Nilesoft Shell version compatibility and feature set\\n    - Need for backward compatibility with existing Windows functionality\\n    - Performance impact considerations for menu rendering and operations\\n    - Security considerations for script execution and application launching\\n    \\n    ## Key Success Metrics\\n    - Reduced time to access applications and perform file operations\\n    - Improved menu organization and visual clarity\\n    - Extensibility without code duplication or structure compromise\\n    - Minimal impact on system performance\\n    - Reliability across Windows versions and configurations\\n```\\n\\n---\\n\\n#### `03_patterns.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Hierarchical organization: _init → variables → items → groups → menus → contexts\\n    - NSS files are modular with single-responsibility principle\\n    - Items represent individual menu entries; groups combine related items\\n    - Contexts determine when and where menus appear\\n    \\n    # 03_patterns.md\\n    \\n    ## System Architecture\\n    \\n    ### Core Components Hierarchy\\n    1. **Initialization (_1_init/)**: Sets up constants, configurations, and base environment\\n    2. **Variables (_2_variables/)**: Defines reusable values and settings\\n    3. **Items (_3_items/)**: Individual menu entries (commands, applications, actions)\\n    4. **Groups (_4_groups/)**: Logical collections of related items\\n    5. **Menus (_5_menus/)**: Structured arrangements of items and groups\\n    6. **Contexts (_6_contexts/)**: Rules determining when and where menus appear\\n    \\n    ### Design Patterns\\n    \\n    #### Modular Composition\\n    - Each NSS file has a single responsibility\\n    - Components are composed through inclusion and reference\\n    - Changes to one component minimally impact others\\n    \\n    #### Naming Conventions\\n    - File prefixes indicate component type (`itm_` for items, etc.)\\n    - Consistent categorization (`app_`, `action_`, `sys_` prefixes)\\n    - Descriptive suffixes for variant behavior\\n    \\n    #### Conditional Visibility\\n    - Menu items show/hide based on context rules\\n    - Rules can evaluate file types, locations, system state\\n    - Compound conditions for precise targeting\\n    \\n    #### Icon Standardization\\n    - Consistent icon usage across similar functions\\n    - Icon mapping for visual categorization\\n    - Fallback icons for compatibility\\n    \\n    ## Component Relationships\\n    \\n    ```mermaid\\n    graph TD\\n        Init[_1_init] --> Vars[_2_variables]\\n        Vars --> Items[_3_items]\\n        Items --> Groups[_4_groups]\\n        Groups --> Menus[_5_menus]\\n        Menus --> Contexts[_6_contexts]\\n        \\n        subgraph \\\"Item Types\\\"\\n            AppItems[Application Launchers]\\n            ActionItems[File Actions]\\n            SystemItems[System Operations]\\n        end\\n        \\n        Items --- AppItems\\n        Items --- ActionItems\\n        Items --- SystemItems\\n    ```\\n    \\n    ## Schema Patterns\\n    \\n    ### Item Schema\\n    ```\\n    item {\\n      name: \\\"item_name\\\"\\n      icon: \\\"icon_path_or_resource\\\"\\n      command: \\\"command_to_execute\\\"\\n      visibility: \\\"condition_expression\\\"\\n    }\\n    ```\\n    \\n    ### Group Schema\\n    ```\\n    group {\\n      name: \\\"group_name\\\"\\n      icon: \\\"group_icon\\\"\\n      items: [\\n        // references to items or inline definitions\\n      ]\\n    }\\n    ```\\n    \\n    ### Menu Schema\\n    ```\\n    menu {\\n      name: \\\"menu_name\\\"\\n      items: [\\n        // groups and/or individual items\\n      ]\\n      contexts: [\\n        // when/where this menu appears\\n      ]\\n    }\\n    ```\\n    \\n    ## Pattern Evolution Strategy\\n    - Maintain backward compatibility when adding new patterns\\n    - Document pattern changes in corresponding memory bank files\\n    - Test pattern modifications across different file contexts\\n    - Prefer extending existing patterns over creating new ones\\n```\\n\\n---\\n\\n#### `04_tech.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Built on Nilesoft Shell framework for Windows context menu customization\\n    - NSS scripting language for defining menu items and behavior\\n    - Modular file organization in _1_init through _6_contexts directories\\n    - Supporting resources include PNG/SVG icons and batch scripts\\n    \\n    # 04_tech.md\\n    \\n    ## Core Technologies\\n    \\n    ### Nilesoft Shell\\n    - **Version**: Shell v1.9+ (current observed version: v1.9.15)\\n    - **Purpose**: Framework for customizing Windows Explorer context menus\\n    - **Architecture**: Shell extension that intercepts and modifies Windows context menu requests\\n    - **Configuration**: NSS scripts compiled and loaded by Shell engine\\n    \\n    ### NSS Scripting\\n    - **Language**: Nilesoft Shell Script (NSS)\\n    - **Syntax**: C-like with specialized directives for menu definition\\n    - **Scope**: Declarative definitions with procedural capabilities\\n    - **Compilation**: Scripts are parsed and compiled by the Shell engine at runtime\\n    \\n    ## Infrastructure\\n    \\n    ### Directory Structure\\n    ```\\n    exe/\\n    ├── NSS/\\n    │   ├── _1_init/        # Initialization, constants, configuration\\n    │   ├── _2_variables/   # Variable definitions for reuse\\n    │   ├── _3_items/       # Individual menu items\\n    │   ├── _4_groups/      # Logical groupings of items\\n    │   ├── _5_menus/       # Menu structures and layouts\\n    │   └── _6_contexts/    # Context rules for menu visibility\\n    ├── LIB/\\n    │   ├── bat/            # Batch scripts for utilities\\n    │   ├── png/            # PNG icons for menu items\\n    │   └── svg/            # SVG icons for menu items\\n    └── shell.nss           # Main entry point for NSS configuration\\n    ```\\n    \\n    ### Integration Points\\n    - Windows Explorer shell extension\\n    - Windows Registry for registration and configuration\\n    - File system for icon and resource access\\n    - Command line for application launching\\n    - COM/Shell interfaces for advanced functionality\\n    \\n    ## Dependencies\\n    \\n    ### External Resources\\n    - Icon files (PNG, SVG) for menu item visualization\\n    - Batch files for extended functionality\\n    - Executable paths for application launching\\n    - System shell commands for file operations\\n    \\n    ### Development Tools\\n    - Resource editors for icon management\\n    - Shell extension management utilities\\n    - Text editors with NSS syntax support\\n    \\n    ## Technical Constraints\\n    \\n    ### Performance Considerations\\n    - Context menu load time impact\\n    - Memory footprint of loaded NSS scripts\\n    - Efficiency of condition evaluation for visibility rules\\n    \\n    ### Compatibility\\n    - Windows version support (10, 11)\\n    - Interaction with other shell extensions\\n    - Application path resolution across different environments\\n    \\n    ### Security\\n    - Execution permissions for launched applications\\n    - Script injection prevention\\n    - Validation of external resource paths\\n    \\n    ## Technical Roadmap\\n    - Ongoing migration to latest Nilesoft Shell features\\n    - Standardization of icon resources\\n    - Optimization of conditional visibility expressions\\n    - Enhanced script modularity and reusability\\n```\\n\\n---\\n\\n#### `05_activity.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Current focus on populating individual menu items (_3_items directory)\\n    - Need to develop groups, menus, and contexts structure\\n    - Application launchers are the most extensively implemented item type\\n    - System action items have basic implementation\\n    \\n    # 05_activity.md\\n    \\n    ## Current Focus Areas\\n    \\n    ### Item Development\\n    - Extensive collection of application launcher items (`itm_app_*.nss`) already implemented\\n    - Basic system action items created (`itm_action_sys_*.nss`) \\n    - Need to audit existing items for consistency in naming and functionality\\n    - Opportunity to standardize icon usage across similar item types\\n    \\n    ### Structural Components\\n    - Groups (`_4_groups/`) directory needs population to organize related items\\n    - Menus (`_5_menus/`) require definition to establish menu layouts and hierarchy\\n    - Contexts (`_6_contexts/`) need implementation to control when menus appear\\n    \\n    ### Technical Infrastructure\\n    - Ensure initialization files in `_1_init/` properly set up the environment\\n    - Review variables in `_2_variables/` for completeness and usefulness\\n    - Validate resource paths and icon references\\n    \\n    ## In-Progress Work\\n    \\n    ### Application Launcher Standardization\\n    - Standardizing application launcher items with consistent:\\n      - Command parameter formatting\\n      - Icon selection and fallbacks\\n      - Visibility conditions\\n      - Naming conventions\\n    \\n    ### System Functionality Integration\\n    - Integrating core Windows functionality through specialized items\\n    - Creating consistent approach for system utility access\\n    - Implementing common file operations\\n    \\n    ### Organization Strategy\\n    - Developing logical grouping strategy for related items\\n    - Planning menu hierarchy based on frequency of use and context\\n    - Establishing visibility rules based on file types and locations\\n    \\n    ## Technical Decisions\\n    \\n    ### Item Implementation Pattern\\n    ```nss\\n    item {\\n      name: \\\"Application Name\\\"\\n      icon: \\\"path/to/icon.png\\\"\\n      command: \\\"path/to/executable.exe [parameters]\\\"\\n      \\n      // Optional visibility condition\\n      visibility: [condition expression]\\n    }\\n    ```\\n    \\n    ### Resource Path Strategy\\n    - Using relative paths for icons and resources where possible\\n    - Standardizing on Shell-compatible path syntax\\n    - Planning for fallback icons when primary resources unavailable\\n    \\n    ### Menu Appearance Decisions\\n    - Consistent separators between logical groups\\n    - Icon alignment and sizing standardization\\n    - Text formatting and capitalization rules\\n```\\n\\n---\\n\\n#### `06_progress.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Directory structure and modular approach established\\n    - Extensive collection of application launcher items implemented\\n    - Basic system action items created\\n    - Need to develop grouping and context structures\\n    \\n    # 06_progress.md\\n    \\n    ## State of the Build\\n    \\n    ### Completed Components\\n    - ✅ Overall directory structure established for modular organization\\n    - ✅ Basic initialization structure in `_1_init/` directory\\n    - ✅ Extensive collection of application launcher items (~70 items)\\n    - ✅ Core system action items for basic operations\\n    - ✅ Resource libraries for icons and batch utilities\\n    \\n    ### In Progress\\n    - 🔄 Variable standardization in `_2_variables/` directory\\n    - 🔄 Audit of existing items for consistency and effectiveness\\n    - 🔄 Documentation of item patterns and best practices\\n    - 🔄 Standardization of icon usage and resource paths\\n    \\n    ### Not Started\\n    - ⏳ Population of `_4_groups/` directory to organize items\\n    - ⏳ Definition of menu structures in `_5_menus/` directory\\n    - ⏳ Implementation of context rules in `_6_contexts/` directory\\n    - ⏳ Comprehensive testing across different file types\\n    \\n    ## Milestones\\n    \\n    ### Milestone 1: Foundation (COMPLETED)\\n    - ✓ Establish directory structure\\n    - ✓ Implement basic initialization\\n    - ✓ Create core application launcher items\\n    - ✓ Set up resource libraries\\n    \\n    ### Milestone 2: Structure (IN PROGRESS)\\n    - ✓ Standardize item implementation patterns\\n    - ✓ Complete system action items\\n    - 🔄 Document existing items and patterns\\n    - ⏳ Organize items into logical groups\\n    \\n    ### Milestone 3: Integration (PLANNED)\\n    - Define menu structures\\n    - Implement context rules\\n    - Create conditional visibility based on file types\\n    - Optimize menu organization\\n    \\n    ### Milestone 4: Refinement (PLANNED)\\n    - Comprehensive testing\\n    - Performance optimization\\n    - Icon standardization\\n    - Documentation completion\\n    \\n    ## Current Blockers\\n    - Need to develop a consistent strategy for grouping related items\\n    - Lack of defined context rules for when menus should appear\\n    - Incomplete documentation of existing patterns and structures\\n    \\n    ## Recent Updates\\n    - Added multiple application launcher items for common utilities\\n    - Created system action items for file operations\\n    - Established resource libraries for icons and batch scripts\\n    - Documented NSS patterns and implementation strategies\\n    \\n    ## Next Actions\\n    1. Complete the audit of existing items for consistency\\n    2. Develop a logical grouping strategy for items\\n    3. Begin populating the `_4_groups/` directory\\n    4. Create a menu structure plan for the `_5_menus/` directory\\n    5. Establish basic context rules in the `_6_contexts/` directory\\n```\\n\\n---\\n\\n#### `07_tasks.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Organize existing items into logical groups\\n    - Develop menu structures based on usage patterns\\n    - Implement context rules for menu visibility\\n    - Create documentation for future item development\\n    \\n    # 07_tasks.md\\n    \\n    ## High Priority Tasks\\n    \\n    ### Group Structure Development\\n    - [ ] **Create Application Groups**\\n      - Create `grp_apps_dev.nss` for development tools\\n      - Create `grp_apps_media.nss` for media applications\\n      - Create `grp_apps_system.nss` for system utilities\\n      - Create `grp_apps_office.nss` for productivity applications\\n      - *Owner: System developer*\\n      - *Justification: Organize the extensive list of application items into logical groups*\\n    \\n    - [ ] **Create Action Groups**\\n      - Create `grp_actions_file.nss` for file operations\\n      - Create `grp_actions_folder.nss` for folder operations\\n      - Create `grp_actions_system.nss` for system operations\\n      - *Owner: System developer*\\n      - *Justification: Group related actions for better menu organization*\\n    \\n    ### Menu Structure Implementation\\n    - [ ] **Design Core Menus**\\n      - Create `menu_applications.nss` with application groups\\n      - Create `menu_actions.nss` with action groups\\n      - Create `menu_system.nss` with system utilities\\n      - *Owner: System developer*\\n      - *Justification: Establish logical menu structure for better user experience*\\n    \\n    - [ ] **Implement Cascading Menus**\\n      - Design depth strategy (max 2-3 levels deep)\\n      - Implement parent-child relationships\\n      - *Owner: System developer*\\n      - *Justification: Organize complex item collections without cluttering main menu*\\n    \\n    ### Context Implementation\\n    - [ ] **Define File Type Contexts**\\n      - Create rules for different file extensions\\n      - Implement special handling for executables, documents, media\\n      - *Owner: System developer*\\n      - *Justification: Show relevant options based on file type*\\n    \\n    - [ ] **Define Location Contexts**\\n      - Create rules for desktop, drives, libraries\\n      - Implement special handling for system folders\\n      - *Owner: System developer*\\n      - *Justification: Show relevant options based on location*\\n    \\n    ## Medium Priority Tasks\\n    \\n    ### Documentation\\n    - [ ] **Document Group Patterns**\\n      - Create templates for new groups\\n      - Document naming conventions\\n      - *Owner: Documentation specialist*\\n      - *Justification: Ensure consistency in future development*\\n    \\n    - [ ] **Document Menu Structures**\\n      - Create visual hierarchy diagrams\\n      - Document menu organization principles\\n      - *Owner: Documentation specialist*\\n      - *Justification: Provide clear guidance for menu development*\\n    \\n    ### Quality Assurance\\n    - [ ] **Audit Existing Items**\\n      - Review naming consistency\\n      - Validate command parameters\\n      - Check icon references\\n      - *Owner: QA specialist*\\n      - *Justification: Ensure existing items follow standards*\\n    \\n    - [ ] **Test Across Environments**\\n      - Verify functionality on Windows 10\\n      - Verify functionality on Windows 11\\n      - Test with various file types\\n      - *Owner: QA specialist*\\n      - *Justification: Ensure compatibility across environments*\\n    \\n    ## Low Priority Tasks\\n    \\n    ### Optimization\\n    - [ ] **Review Performance**\\n      - Profile menu load times\\n      - Optimize condition evaluations\\n      - *Owner: Performance specialist*\\n      - *Justification: Ensure menus load quickly*\\n    \\n    - [ ] **Icon Standardization**\\n      - Create consistent icon set\\n      - Implement fallback strategy\\n      - *Owner: Design specialist*\\n      - *Justification: Visual consistency across menus*\\n    \\n    ## Dependencies\\n    - Group creation depends on completion of item audit\\n    - Menu implementation depends on group creation\\n    - Context rules depend on menu implementation\\n    - Testing depends on all implementation tasks\\n    \\n    ## Task Assignment Strategy\\n    - Focus on high-priority tasks first\\n    - Complete related tasks in sequence (items → groups → menus → contexts)\\n    - Document as you go to maintain knowledge base\\n```\\n\\n---\\n\\n#### `08_objective.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Create a complete, modular context menu system for Windows Explorer\\n    - Immediate focus: Group existing items and implement menu structures\\n    - Success measured by efficiency, organization, and extensibility\\n    - Target completion of Milestone 3 (Integration) by next development cycle\\n    \\n    # 08_objective.md\\n    \\n    ## Primary Objective\\n    Create a comprehensive Windows context menu system that enhances productivity through logically organized, easily accessible commands and applications, using a modular architecture that ensures maintainability and extensibility.\\n    \\n    ## Success Criteria\\n    The context menu system will be considered successful when it:\\n    \\n    1. **Provides Efficient Access**\\n       - Reduces clicks needed to access common applications\\n       - Speeds up file operations through contextual commands\\n       - Groups related functionality in intuitive categories\\n    \\n    2. **Maintains Logical Organization**\\n       - Presents a consistent menu structure across different contexts\\n       - Limits menu depth to prevent navigation complexity\\n       - Uses visual cues (icons, separators) to distinguish item types\\n    \\n    3. **Ensures Extensibility**\\n       - Follows modular design patterns for easy additions\\n       - Maintains clear separation between components\\n       - Documents patterns for consistent future development\\n    \\n    4. **Performs Reliably**\\n       - Loads quickly without noticeable delay\\n       - Works consistently across Windows versions\\n       - Avoids conflicts with other shell extensions\\n    \\n    ## Immediate Focus\\n    The current development cycle focuses on:\\n    \\n    1. **Component Organization**\\n       - Grouping existing items into logical collections\\n       - Implementing menu structures to organize these groups\\n       - Defining context rules to control when menus appear\\n    \\n    2. **System Completion**\\n       - Filling gaps in the current directory structure\\n       - Completing the chain from items to groups to menus to contexts\\n       - Documenting component relationships and dependencies\\n    \\n    3. **Usability Enhancement**\\n       - Standardizing visual appearance across menu items\\n       - Ensuring consistent behavior across different file types\\n       - Optimizing menu depth and organization for quick access\\n    \\n    ## Alignment with Vision\\n    This objective directly supports the project's vision by:\\n    - Enhancing Windows Explorer with powerful, customizable menus\\n    - Standardizing menu organization for improved clarity\\n    - Enabling quick access to applications and utilities\\n    - Creating a scalable system that can grow without restructuring\\n    \\n    ## Definition of \\\"Done\\\"\\n    For the current phase, \\\"done\\\" means:\\n    \\n    1. All existing items organized into appropriate groups\\n    2. Core menu structures defined and implemented\\n    3. Basic context rules established for different file types and locations\\n    4. Documentation updated to reflect the current system state\\n    5. At least basic testing completed across different environments\\n    \\n    ## Timeline Target\\n    Complete current objectives (Milestone 3: Integration) within the next development cycle, setting the foundation for the refinement phase that follows.\\n```\\\"```'\\n\\n\",\n  \"Title\": \"Windows Context Menu System Synthesis\",\n  \"Interpretation\": \"Your goal is not to explain, narrate, or justify aspects of the Windows Explorer context menu system, but to synthesize a modular, extensible, and fully-documented Windows/Nilesoft Shell context menu framework using the distilled principles, structure, and actions specified. Operate solely as a systems architect specialized in Windows/NSS menu frameworks. Execute as:\",\n  \"Execute as\": {\n    \"role\": \"windows_context_menu_system_architect\",\n    \"input\": [\n      \"project_overview:dict\",\n      \"core_principles:list\",\n      \"structure_requirements:list\",\n      \"integration_requirements:list\",\n      \"validation_cycle:list\",\n      \"deliverable_spec:list\"\n    ],\n    \"process\": [\n      \"parse_project_overview()\",\n      \"extract_distilled_principles()\",\n      \"audit_existing_item_inventory()\",\n      \"standardize_item_naming_and_icons()\",\n      \"group_items_by_function_and_context()\",\n      \"enforce_single_responsibility_in_nss_files()\",\n      \"design_menu_hierarchies(max_depth=3)\",\n      \"implement_context_rules(file_type_and_location)\",\n      \"modularize_nss_scripts()\",\n      \"document_patterns_and_dependencies()\",\n      \"apply_nilesoft_shell_v1.9plus_compliance()\",\n      \"enforce_windows_shell_architecture_compatibility()\",\n      \"optimize_for_performance_and_security()\",\n      \"test_cross_windows_versions(os_list=['10','11'])\",\n      \"establish_backward_compatibility()\",\n      \"validate_success_criteria(['menu_access_speed','organizational_consistency','extensibility','reliability'])\",\n      \"run_continuous_testing_and_qa_review()\",\n      \"monitor_and_enforce_milestone_checkpoints()\"\n    ],\n    \"constraints\": [\n      \"exclude_all_conversational_narrative_explanatory_content()\",\n      \"maintain_pattern_consistency_and_modular_file_structure()\",\n      \"operate_within_scope_of_nss_and_windows_context_menu_architecture()\",\n      \"output_only_structured_deliverables()\",\n      \"preserve_type_safety_in_all_generated_outputs()\"\n    ],\n    \"requirements\": [\n      \"deliver_audited_and_grouped_item_inventory()\",\n      \"output_menu_and_context_hierarchy_definitions()\",\n      \"generate_modular_nss_source_files()\",\n      \"provide_comprehensive_documentation_including_patterns_schemas_and_diagrams()\",\n      \"furnish_performance_and_reliability_reports()\"\n    ],\n    \"output\": {\n      \"synthesized_context_menu_system\": \"dict\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"role\": \"windows_context_menu_system_architect\",\n    \"input\": {\n      \"project_overview\": \"<integrated Windows context menu modularization for Nilesoft Shell, targeting enhancement of user workflow via efficient, organized, extensible menu frameworks>\",\n      \"core_principles\": [\n        \"efficiency\",\n        \"clarity\",\n        \"extensibility\",\n        \"reliability\"\n      ],\n      \"structure_requirements\": [\n        \"directory schema: _1_init, _2_variables, _3_items, _4_groups, _5_menus, _6_contexts\",\n        \"NSS file single responsibility\",\n        \"strict naming and icon conventions\",\n        \"logical groupings reflecting usage/context\",\n        \"menus with maximum depth 3\",\n        \"contextual visibility by file/type/location\"\n      ],\n      \"integration_requirements\": [\n        \"Nilesoft Shell v1.9+ compliance\",\n        \"Windows 10/11 shell extension support\",\n        \"Registry and Explorer integration\",\n        \"resource path consistency\"\n      ],\n      \"validation_cycle\": [\n        \"audit item inventory\",\n        \"standardize and test naming/icons\",\n        \"implement groups, menus, contexts\",\n        \"cross-version QA\",\n        \"performance, compatibility, and security assurance\",\n        \"milestone-driven agile checkpoints\"\n      ],\n      \"deliverable_spec\": [\n        \"full grouped item inventory\",\n        \"explicit menu/context hierarchy definitions\",\n        \"modular, documented NSS source files\",\n        \"schemas and visual structure diagrams\",\n        \"cross-environment performance/reliability report\"\n      ]\n    },\n    \"process\": [\n      \"synthesize all project inputs, patterns, and objectives to instantiate a modular, extensible, and contextually adaptive Windows/NSS shell menu system architected for minimum-click efficiency, optimal clarity, and infinite compositional scalability; encode all operational strategies as system-agnostic modular NSS schemas maximizing pattern inheritance; drive each artifact by principle-driven transformation-directional logic to ensure bidirectional clarity between purpose and realization; enforce rules-forged canonical directory and action structure; propagate structural and functional definitions through all menu/group/context layers; bond documentation as referential pattern memory; implement perpetual validation and agile milestone monitoring to guarantee sustained extensibility and reliability; ensure all outputs are system-propagatable, type-safe, and manifest as immediately actionable transformative blueprints\"\n    ],\n    \"output\": {\n      \"synthesized_context_menu_system\": {\n        \"directory_structure\": {\n          \"_1_init\": \"system initialization/constants/config\",\n          \"_2_variables\": \"named reusable settings/values\",\n          \"_3_items\": \"individual commands/applications/actions, NSS one-per-file, canonical naming/icons/visibility\",\n          \"_4_groups\": \"logical item clusters by function/context\",\n          \"_5_menus\": \"menu hierarchies assembling groups/items contextually\",\n          \"_6_contexts\": \"visibility and applicability rules (file type/location based)\"\n        },\n        \"menu_schemas\": {\n          \"item\": {\n            \"name\": \"<string>\",\n            \"icon\": \"<icon_path_or_resource>\",\n            \"command\": \"<command_or_executable>\",\n            \"visibility\": \"<context_rule_expression>\"\n          },\n          \"group\": {\n            \"name\": \"<string>\",\n            \"icon\": \"<icon_path>\",\n            \"items\": \"[item_refs_or_inline_items]\"\n          },\n          \"menu\": {\n            \"name\": \"<string>\",\n            \"items\": \"[groups/items]\",\n            \"contexts\": \"[context_refs]\"\n          }\n        },\n        \"pattern_memory\": {\n          \"naming\": \"itm_app_, itm_action_, grp_apps_, grp_actions_, menu_*, context_*\",\n          \"icon\": \"standardized, contextually descriptive, fallback-enabled\"\n        },\n        \"integration_points\": {\n          \"windows_shell_extension\": true,\n          \"registry_registration\": true,\n          \"resource_paths\": \"relative-standardized\",\n          \"command_line_and_explorer\": true,\n          \"cross_version_support\": [\"10\", \"11\"]\n        },\n        \"validation_framework\": {\n          \"audit\": \"inventory, grouping, naming, icon, context-logic\",\n          \"qa\": \"functional testing, cross-system\",\n          \"performance\": \"menu load, resource evaluation\",\n          \"security\": \"permission validation, injection prevention\"\n        },\n        \"documentation\": {\n          \"schemas\": \"all core components\",\n          \"patterns\": \"grouping, naming, menu structuring\",\n          \"diagrams\": \"item/group/menu/context relationships\",\n          \"dependency_map\": \"component linkage and extensibility dynamics\"\n        },\n        \"deliverable_manifest\": {\n          \"item_inventory\": \"grouped/audited, type-safe\",\n          \"menu_definitions\": \"hierarchical, max depth 3\",\n          \"nss_files\": \"modular, pattern-compliant, referentially documented\",\n          \"tests\": \"cross-version, performance/reliability benchmarks\"\n        }\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "c",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"### File Structure\\n\\n```\\n├── 01_foundation.md\\n├── 02_context.md\\n├── 03_patterns.md\\n├── 04_tech.md\\n├── 05_activity.md\\n├── 06_progress.md\\n├── 07_tasks.md\\n└── 08_objective.md\\n```\\n\\n---\\n\\n#### `01_foundation.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Custom Windows context menu system built on Nilesoft Shell\\n    - Purpose: Enhance Windows file explorer with efficient context actions\\n    - Principles: Modularity, consistency, and extensibility\\n    \\n    # 01_foundation.md\\n    \\n    ## Core Mission\\n    The Nilesoft Shell context menu system aims to significantly enhance Windows file explorer with a powerful, customizable context menu framework that improves workflow efficiency and user experience.\\n    \\n    ## Vision\\n    Create a comprehensive context menu system that:\\n    - Provides quick access to frequently used applications and utilities\\n    - Standardizes menu organization and appearance\\n    - Enables rapid file/folder operations through contextual commands\\n    - Scales elegantly with minimal maintenance overhead\\n    \\n    ## Values\\n    - **Efficiency**: Minimize clicks and navigation time\\n    - **Clarity**: Intuitive menu organization with consistent naming and grouping\\n    - **Extensibility**: Easily add new functionality without restructuring\\n    - **Reliability**: Stable operation without conflicts or performance impact\\n    \\n    ## Strategic Goals\\n    1. Standardize application launching from any context\\n    2. Optimize file management operations\\n    3. Group related functionality logically\\n    4. Leverage NSS scripting for advanced automation\\n    5. Maintain backward compatibility with Windows shell\\n```\\n\\n---\\n\\n#### `02_context.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Windows context menus often become cluttered and inconsistent\\n    - Users need quick access to applications and file operations\\n    - Nilesoft Shell provides programmatic control over Windows context menus\\n    \\n    # 02_context.md\\n    \\n    ## Problem Space\\n    \\n    ### Current Limitations\\n    - Default Windows context menus lack organization and customization options\\n    - Third-party applications inject entries haphazardly, creating menu bloat\\n    - No standard interface for managing context menu appearance and behavior\\n    - Limited ability to group related actions or create conditional menus\\n    \\n    ### User Needs\\n    - Quick access to frequently used applications from any file/folder context\\n    - Consistent organization of menu items across different file types\\n    - Visual distinction between types of operations (system, applications, custom scripts)\\n    - Ability to conditionally show/hide menu options based on file attributes\\n    - Simplified access to system utilities and administrative functions\\n    \\n    ## Stakeholders\\n    - **End Users**: Individuals seeking workflow efficiency improvements\\n    - **System Administrators**: Managing standard configurations across machines\\n    - **Developers**: Extending functionality through Nilesoft Shell scripts (NSS)\\n    - **Windows Environment**: Integration with existing shell infrastructure\\n    \\n    ## External Constraints\\n    - Windows context menu system architecture and limitations\\n    - Nilesoft Shell version compatibility and feature set\\n    - Need for backward compatibility with existing Windows functionality\\n    - Performance impact considerations for menu rendering and operations\\n    - Security considerations for script execution and application launching\\n    \\n    ## Key Success Metrics\\n    - Reduced time to access applications and perform file operations\\n    - Improved menu organization and visual clarity\\n    - Extensibility without code duplication or structure compromise\\n    - Minimal impact on system performance\\n    - Reliability across Windows versions and configurations\\n```\\n\\n---\\n\\n#### `03_patterns.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Hierarchical organization: _init → variables → items → groups → menus → contexts\\n    - NSS files are modular with single-responsibility principle\\n    - Items represent individual menu entries; groups combine related items\\n    - Contexts determine when and where menus appear\\n    \\n    # 03_patterns.md\\n    \\n    ## System Architecture\\n    \\n    ### Core Components Hierarchy\\n    1. **Initialization (_1_init/)**: Sets up constants, configurations, and base environment\\n    2. **Variables (_2_variables/)**: Defines reusable values and settings\\n    3. **Items (_3_items/)**: Individual menu entries (commands, applications, actions)\\n    4. **Groups (_4_groups/)**: Logical collections of related items\\n    5. **Menus (_5_menus/)**: Structured arrangements of items and groups\\n    6. **Contexts (_6_contexts/)**: Rules determining when and where menus appear\\n    \\n    ### Design Patterns\\n    \\n    #### Modular Composition\\n    - Each NSS file has a single responsibility\\n    - Components are composed through inclusion and reference\\n    - Changes to one component minimally impact others\\n    \\n    #### Naming Conventions\\n    - File prefixes indicate component type (`itm_` for items, etc.)\\n    - Consistent categorization (`app_`, `action_`, `sys_` prefixes)\\n    - Descriptive suffixes for variant behavior\\n    \\n    #### Conditional Visibility\\n    - Menu items show/hide based on context rules\\n    - Rules can evaluate file types, locations, system state\\n    - Compound conditions for precise targeting\\n    \\n    #### Icon Standardization\\n    - Consistent icon usage across similar functions\\n    - Icon mapping for visual categorization\\n    - Fallback icons for compatibility\\n    \\n    ## Component Relationships\\n    \\n    ```mermaid\\n    graph TD\\n        Init[_1_init] --> Vars[_2_variables]\\n        Vars --> Items[_3_items]\\n        Items --> Groups[_4_groups]\\n        Groups --> Menus[_5_menus]\\n        Menus --> Contexts[_6_contexts]\\n        \\n        subgraph \\\"Item Types\\\"\\n            AppItems[Application Launchers]\\n            ActionItems[File Actions]\\n            SystemItems[System Operations]\\n        end\\n        \\n        Items --- AppItems\\n        Items --- ActionItems\\n        Items --- SystemItems\\n    ```\\n    \\n    ## Schema Patterns\\n    \\n    ### Item Schema\\n    ```\\n    item {\\n      name: \\\"item_name\\\"\\n      icon: \\\"icon_path_or_resource\\\"\\n      command: \\\"command_to_execute\\\"\\n      visibility: \\\"condition_expression\\\"\\n    }\\n    ```\\n    \\n    ### Group Schema\\n    ```\\n    group {\\n      name: \\\"group_name\\\"\\n      icon: \\\"group_icon\\\"\\n      items: [\\n        // references to items or inline definitions\\n      ]\\n    }\\n    ```\\n    \\n    ### Menu Schema\\n    ```\\n    menu {\\n      name: \\\"menu_name\\\"\\n      items: [\\n        // groups and/or individual items\\n      ]\\n      contexts: [\\n        // when/where this menu appears\\n      ]\\n    }\\n    ```\\n    \\n    ## Pattern Evolution Strategy\\n    - Maintain backward compatibility when adding new patterns\\n    - Document pattern changes in corresponding memory bank files\\n    - Test pattern modifications across different file contexts\\n    - Prefer extending existing patterns over creating new ones\\n```\\n\\n---\\n\\n#### `04_tech.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Built on Nilesoft Shell framework for Windows context menu customization\\n    - NSS scripting language for defining menu items and behavior\\n    - Modular file organization in _1_init through _6_contexts directories\\n    - Supporting resources include PNG/SVG icons and batch scripts\\n    \\n    # 04_tech.md\\n    \\n    ## Core Technologies\\n    \\n    ### Nilesoft Shell\\n    - **Version**: Shell v1.9+ (current observed version: v1.9.15)\\n    - **Purpose**: Framework for customizing Windows Explorer context menus\\n    - **Architecture**: Shell extension that intercepts and modifies Windows context menu requests\\n    - **Configuration**: NSS scripts compiled and loaded by Shell engine\\n    \\n    ### NSS Scripting\\n    - **Language**: Nilesoft Shell Script (NSS)\\n    - **Syntax**: C-like with specialized directives for menu definition\\n    - **Scope**: Declarative definitions with procedural capabilities\\n    - **Compilation**: Scripts are parsed and compiled by the Shell engine at runtime\\n    \\n    ## Infrastructure\\n    \\n    ### Directory Structure\\n    ```\\n    exe/\\n    ├── NSS/\\n    │   ├── _1_init/        # Initialization, constants, configuration\\n    │   ├── _2_variables/   # Variable definitions for reuse\\n    │   ├── _3_items/       # Individual menu items\\n    │   ├── _4_groups/      # Logical groupings of items\\n    │   ├── _5_menus/       # Menu structures and layouts\\n    │   └── _6_contexts/    # Context rules for menu visibility\\n    ├── LIB/\\n    │   ├── bat/            # Batch scripts for utilities\\n    │   ├── png/            # PNG icons for menu items\\n    │   └── svg/            # SVG icons for menu items\\n    └── shell.nss           # Main entry point for NSS configuration\\n    ```\\n    \\n    ### Integration Points\\n    - Windows Explorer shell extension\\n    - Windows Registry for registration and configuration\\n    - File system for icon and resource access\\n    - Command line for application launching\\n    - COM/Shell interfaces for advanced functionality\\n    \\n    ## Dependencies\\n    \\n    ### External Resources\\n    - Icon files (PNG, SVG) for menu item visualization\\n    - Batch files for extended functionality\\n    - Executable paths for application launching\\n    - System shell commands for file operations\\n    \\n    ### Development Tools\\n    - Resource editors for icon management\\n    - Shell extension management utilities\\n    - Text editors with NSS syntax support\\n    \\n    ## Technical Constraints\\n    \\n    ### Performance Considerations\\n    - Context menu load time impact\\n    - Memory footprint of loaded NSS scripts\\n    - Efficiency of condition evaluation for visibility rules\\n    \\n    ### Compatibility\\n    - Windows version support (10, 11)\\n    - Interaction with other shell extensions\\n    - Application path resolution across different environments\\n    \\n    ### Security\\n    - Execution permissions for launched applications\\n    - Script injection prevention\\n    - Validation of external resource paths\\n    \\n    ## Technical Roadmap\\n    - Ongoing migration to latest Nilesoft Shell features\\n    - Standardization of icon resources\\n    - Optimization of conditional visibility expressions\\n    - Enhanced script modularity and reusability\\n```\\n\\n---\\n\\n#### `05_activity.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Current focus on populating individual menu items (_3_items directory)\\n    - Need to develop groups, menus, and contexts structure\\n    - Application launchers are the most extensively implemented item type\\n    - System action items have basic implementation\\n    \\n    # 05_activity.md\\n    \\n    ## Current Focus Areas\\n    \\n    ### Item Development\\n    - Extensive collection of application launcher items (`itm_app_*.nss`) already implemented\\n    - Basic system action items created (`itm_action_sys_*.nss`) \\n    - Need to audit existing items for consistency in naming and functionality\\n    - Opportunity to standardize icon usage across similar item types\\n    \\n    ### Structural Components\\n    - Groups (`_4_groups/`) directory needs population to organize related items\\n    - Menus (`_5_menus/`) require definition to establish menu layouts and hierarchy\\n    - Contexts (`_6_contexts/`) need implementation to control when menus appear\\n    \\n    ### Technical Infrastructure\\n    - Ensure initialization files in `_1_init/` properly set up the environment\\n    - Review variables in `_2_variables/` for completeness and usefulness\\n    - Validate resource paths and icon references\\n    \\n    ## In-Progress Work\\n    \\n    ### Application Launcher Standardization\\n    - Standardizing application launcher items with consistent:\\n      - Command parameter formatting\\n      - Icon selection and fallbacks\\n      - Visibility conditions\\n      - Naming conventions\\n    \\n    ### System Functionality Integration\\n    - Integrating core Windows functionality through specialized items\\n    - Creating consistent approach for system utility access\\n    - Implementing common file operations\\n    \\n    ### Organization Strategy\\n    - Developing logical grouping strategy for related items\\n    - Planning menu hierarchy based on frequency of use and context\\n    - Establishing visibility rules based on file types and locations\\n    \\n    ## Technical Decisions\\n    \\n    ### Item Implementation Pattern\\n    ```nss\\n    item {\\n      name: \\\"Application Name\\\"\\n      icon: \\\"path/to/icon.png\\\"\\n      command: \\\"path/to/executable.exe [parameters]\\\"\\n      \\n      // Optional visibility condition\\n      visibility: [condition expression]\\n    }\\n    ```\\n    \\n    ### Resource Path Strategy\\n    - Using relative paths for icons and resources where possible\\n    - Standardizing on Shell-compatible path syntax\\n    - Planning for fallback icons when primary resources unavailable\\n    \\n    ### Menu Appearance Decisions\\n    - Consistent separators between logical groups\\n    - Icon alignment and sizing standardization\\n    - Text formatting and capitalization rules\\n```\\n\\n---\\n\\n#### `06_progress.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Directory structure and modular approach established\\n    - Extensive collection of application launcher items implemented\\n    - Basic system action items created\\n    - Need to develop grouping and context structures\\n    \\n    # 06_progress.md\\n    \\n    ## State of the Build\\n    \\n    ### Completed Components\\n    - ✅ Overall directory structure established for modular organization\\n    - ✅ Basic initialization structure in `_1_init/` directory\\n    - ✅ Extensive collection of application launcher items (~70 items)\\n    - ✅ Core system action items for basic operations\\n    - ✅ Resource libraries for icons and batch utilities\\n    \\n    ### In Progress\\n    - 🔄 Variable standardization in `_2_variables/` directory\\n    - 🔄 Audit of existing items for consistency and effectiveness\\n    - 🔄 Documentation of item patterns and best practices\\n    - 🔄 Standardization of icon usage and resource paths\\n    \\n    ### Not Started\\n    - ⏳ Population of `_4_groups/` directory to organize items\\n    - ⏳ Definition of menu structures in `_5_menus/` directory\\n    - ⏳ Implementation of context rules in `_6_contexts/` directory\\n    - ⏳ Comprehensive testing across different file types\\n    \\n    ## Milestones\\n    \\n    ### Milestone 1: Foundation (COMPLETED)\\n    - ✓ Establish directory structure\\n    - ✓ Implement basic initialization\\n    - ✓ Create core application launcher items\\n    - ✓ Set up resource libraries\\n    \\n    ### Milestone 2: Structure (IN PROGRESS)\\n    - ✓ Standardize item implementation patterns\\n    - ✓ Complete system action items\\n    - 🔄 Document existing items and patterns\\n    - ⏳ Organize items into logical groups\\n    \\n    ### Milestone 3: Integration (PLANNED)\\n    - Define menu structures\\n    - Implement context rules\\n    - Create conditional visibility based on file types\\n    - Optimize menu organization\\n    \\n    ### Milestone 4: Refinement (PLANNED)\\n    - Comprehensive testing\\n    - Performance optimization\\n    - Icon standardization\\n    - Documentation completion\\n    \\n    ## Current Blockers\\n    - Need to develop a consistent strategy for grouping related items\\n    - Lack of defined context rules for when menus should appear\\n    - Incomplete documentation of existing patterns and structures\\n    \\n    ## Recent Updates\\n    - Added multiple application launcher items for common utilities\\n    - Created system action items for file operations\\n    - Established resource libraries for icons and batch scripts\\n    - Documented NSS patterns and implementation strategies\\n    \\n    ## Next Actions\\n    1. Complete the audit of existing items for consistency\\n    2. Develop a logical grouping strategy for items\\n    3. Begin populating the `_4_groups/` directory\\n    4. Create a menu structure plan for the `_5_menus/` directory\\n    5. Establish basic context rules in the `_6_contexts/` directory\\n```\\n\\n---\\n\\n#### `07_tasks.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Organize existing items into logical groups\\n    - Develop menu structures based on usage patterns\\n    - Implement context rules for menu visibility\\n    - Create documentation for future item development\\n    \\n    # 07_tasks.md\\n    \\n    ## High Priority Tasks\\n    \\n    ### Group Structure Development\\n    - [ ] **Create Application Groups**\\n      - Create `grp_apps_dev.nss` for development tools\\n      - Create `grp_apps_media.nss` for media applications\\n      - Create `grp_apps_system.nss` for system utilities\\n      - Create `grp_apps_office.nss` for productivity applications\\n      - *Owner: System developer*\\n      - *Justification: Organize the extensive list of application items into logical groups*\\n    \\n    - [ ] **Create Action Groups**\\n      - Create `grp_actions_file.nss` for file operations\\n      - Create `grp_actions_folder.nss` for folder operations\\n      - Create `grp_actions_system.nss` for system operations\\n      - *Owner: System developer*\\n      - *Justification: Group related actions for better menu organization*\\n    \\n    ### Menu Structure Implementation\\n    - [ ] **Design Core Menus**\\n      - Create `menu_applications.nss` with application groups\\n      - Create `menu_actions.nss` with action groups\\n      - Create `menu_system.nss` with system utilities\\n      - *Owner: System developer*\\n      - *Justification: Establish logical menu structure for better user experience*\\n    \\n    - [ ] **Implement Cascading Menus**\\n      - Design depth strategy (max 2-3 levels deep)\\n      - Implement parent-child relationships\\n      - *Owner: System developer*\\n      - *Justification: Organize complex item collections without cluttering main menu*\\n    \\n    ### Context Implementation\\n    - [ ] **Define File Type Contexts**\\n      - Create rules for different file extensions\\n      - Implement special handling for executables, documents, media\\n      - *Owner: System developer*\\n      - *Justification: Show relevant options based on file type*\\n    \\n    - [ ] **Define Location Contexts**\\n      - Create rules for desktop, drives, libraries\\n      - Implement special handling for system folders\\n      - *Owner: System developer*\\n      - *Justification: Show relevant options based on location*\\n    \\n    ## Medium Priority Tasks\\n    \\n    ### Documentation\\n    - [ ] **Document Group Patterns**\\n      - Create templates for new groups\\n      - Document naming conventions\\n      - *Owner: Documentation specialist*\\n      - *Justification: Ensure consistency in future development*\\n    \\n    - [ ] **Document Menu Structures**\\n      - Create visual hierarchy diagrams\\n      - Document menu organization principles\\n      - *Owner: Documentation specialist*\\n      - *Justification: Provide clear guidance for menu development*\\n    \\n    ### Quality Assurance\\n    - [ ] **Audit Existing Items**\\n      - Review naming consistency\\n      - Validate command parameters\\n      - Check icon references\\n      - *Owner: QA specialist*\\n      - *Justification: Ensure existing items follow standards*\\n    \\n    - [ ] **Test Across Environments**\\n      - Verify functionality on Windows 10\\n      - Verify functionality on Windows 11\\n      - Test with various file types\\n      - *Owner: QA specialist*\\n      - *Justification: Ensure compatibility across environments*\\n    \\n    ## Low Priority Tasks\\n    \\n    ### Optimization\\n    - [ ] **Review Performance**\\n      - Profile menu load times\\n      - Optimize condition evaluations\\n      - *Owner: Performance specialist*\\n      - *Justification: Ensure menus load quickly*\\n    \\n    - [ ] **Icon Standardization**\\n      - Create consistent icon set\\n      - Implement fallback strategy\\n      - *Owner: Design specialist*\\n      - *Justification: Visual consistency across menus*\\n    \\n    ## Dependencies\\n    - Group creation depends on completion of item audit\\n    - Menu implementation depends on group creation\\n    - Context rules depend on menu implementation\\n    - Testing depends on all implementation tasks\\n    \\n    ## Task Assignment Strategy\\n    - Focus on high-priority tasks first\\n    - Complete related tasks in sequence (items → groups → menus → contexts)\\n    - Document as you go to maintain knowledge base\\n```\\n\\n---\\n\\n#### `08_objective.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Create a complete, modular context menu system for Windows Explorer\\n    - Immediate focus: Group existing items and implement menu structures\\n    - Success measured by efficiency, organization, and extensibility\\n    - Target completion of Milestone 3 (Integration) by next development cycle\\n    \\n    # 08_objective.md\\n    \\n    ## Primary Objective\\n    Create a comprehensive Windows context menu system that enhances productivity through logically organized, easily accessible commands and applications, using a modular architecture that ensures maintainability and extensibility.\\n    \\n    ## Success Criteria\\n    The context menu system will be considered successful when it:\\n    \\n    1. **Provides Efficient Access**\\n       - Reduces clicks needed to access common applications\\n       - Speeds up file operations through contextual commands\\n       - Groups related functionality in intuitive categories\\n    \\n    2. **Maintains Logical Organization**\\n       - Presents a consistent menu structure across different contexts\\n       - Limits menu depth to prevent navigation complexity\\n       - Uses visual cues (icons, separators) to distinguish item types\\n    \\n    3. **Ensures Extensibility**\\n       - Follows modular design patterns for easy additions\\n       - Maintains clear separation between components\\n       - Documents patterns for consistent future development\\n    \\n    4. **Performs Reliably**\\n       - Loads quickly without noticeable delay\\n       - Works consistently across Windows versions\\n       - Avoids conflicts with other shell extensions\\n    \\n    ## Immediate Focus\\n    The current development cycle focuses on:\\n    \\n    1. **Component Organization**\\n       - Grouping existing items into logical collections\\n       - Implementing menu structures to organize these groups\\n       - Defining context rules to control when menus appear\\n    \\n    2. **System Completion**\\n       - Filling gaps in the current directory structure\\n       - Completing the chain from items to groups to menus to contexts\\n       - Documenting component relationships and dependencies\\n    \\n    3. **Usability Enhancement**\\n       - Standardizing visual appearance across menu items\\n       - Ensuring consistent behavior across different file types\\n       - Optimizing menu depth and organization for quick access\\n    \\n    ## Alignment with Vision\\n    This objective directly supports the project's vision by:\\n    - Enhancing Windows Explorer with powerful, customizable menus\\n    - Standardizing menu organization for improved clarity\\n    - Enabling quick access to applications and utilities\\n    - Creating a scalable system that can grow without restructuring\\n    \\n    ## Definition of \\\"Done\\\"\\n    For the current phase, \\\"done\\\" means:\\n    \\n    1. All existing items organized into appropriate groups\\n    2. Core menu structures defined and implemented\\n    3. Basic context rules established for different file types and locations\\n    4. Documentation updated to reflect the current system state\\n    5. At least basic testing completed across different environments\\n    \\n    ## Timeline Target\\n    Complete current objectives (Milestone 3: Integration) within the next development cycle, setting the foundation for the refinement phase that follows.\\n```\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"role\": \"windows_context_menu_system_architect\",\n    \"input\": {\n      \"project_overview\": \"<integrated Windows context menu modularization for Nilesoft Shell, targeting enhancement of user workflow via efficient, organized, extensible menu frameworks>\",\n      \"core_principles\": [\n        \"efficiency\",\n        \"clarity\",\n        \"extensibility\",\n        \"reliability\"\n      ],\n      \"structure_requirements\": [\n        \"directory schema: _1_init, _2_variables, _3_items, _4_groups, _5_menus, _6_contexts\",\n        \"NSS file single responsibility\",\n        \"strict naming and icon conventions\",\n        \"logical groupings reflecting usage/context\",\n        \"menus with maximum depth 3\",\n        \"contextual visibility by file/type/location\"\n      ],\n      \"integration_requirements\": [\n        \"Nilesoft Shell v1.9+ compliance\",\n        \"Windows 10/11 shell extension support\",\n        \"Registry and Explorer integration\",\n        \"resource path consistency\"\n      ],\n      \"validation_cycle\": [\n        \"audit item inventory\",\n        \"standardize and test naming/icons\",\n        \"implement groups, menus, contexts\",\n        \"cross-version QA\",\n        \"performance, compatibility, and security assurance\",\n        \"milestone-driven agile checkpoints\"\n      ],\n      \"deliverable_spec\": [\n        \"full grouped item inventory\",\n        \"explicit menu/context hierarchy definitions\",\n        \"modular, documented NSS source files\",\n        \"schemas and visual structure diagrams\",\n        \"cross-environment performance/reliability report\"\n      ]\n    },\n    \"process\": [\n      \"synthesize all project inputs, patterns, and objectives to instantiate a modular, extensible, and contextually adaptive Windows/NSS shell menu system architected for minimum-click efficiency, optimal clarity, and infinite compositional scalability; encode all operational strategies as system-agnostic modular NSS schemas maximizing pattern inheritance; drive each artifact by principle-driven transformation-directional logic to ensure bidirectional clarity between purpose and realization; enforce rules-forged canonical directory and action structure; propagate structural and functional definitions through all menu/group/context layers; bond documentation as referential pattern memory; implement perpetual validation and agile milestone monitoring to guarantee sustained extensibility and reliability; ensure all outputs are system-propagatable, type-safe, and manifest as immediately actionable transformative blueprints\"\n    ],\n    \"output\": {\n      \"synthesized_context_menu_system\": {\n        \"directory_structure\": {\n          \"_1_init\": \"system initialization/constants/config\",\n          \"_2_variables\": \"named reusable settings/values\",\n          \"_3_items\": \"individual commands/applications/actions, NSS one-per-file, canonical naming/icons/visibility\",\n          \"_4_groups\": \"logical item clusters by function/context\",\n          \"_5_menus\": \"menu hierarchies assembling groups/items contextually\",\n          \"_6_contexts\": \"visibility and applicability rules (file type/location based)\"\n        },\n        \"menu_schemas\": {\n          \"item\": {\n            \"name\": \"<string>\",\n            \"icon\": \"<icon_path_or_resource>\",\n            \"command\": \"<command_or_executable>\",\n            \"visibility\": \"<context_rule_expression>\"\n          },\n          \"group\": {\n            \"name\": \"<string>\",\n            \"icon\": \"<icon_path>\",\n            \"items\": \"[item_refs_or_inline_items]\"\n          },\n          \"menu\": {\n            \"name\": \"<string>\",\n            \"items\": \"[groups/items]\",\n            \"contexts\": \"[context_refs]\"\n          }\n        },\n        \"pattern_memory\": {\n          \"naming\": \"itm_app_, itm_action_, grp_apps_, grp_actions_, menu_*, context_*\",\n          \"icon\": \"standardized, contextually descriptive, fallback-enabled\"\n        },\n        \"integration_points\": {\n          \"windows_shell_extension\": true,\n          \"registry_registration\": true,\n          \"resource_paths\": \"relative-standardized\",\n          \"command_line_and_explorer\": true,\n          \"cross_version_support\": [\n            \"10\",\n            \"11\"\n          ]\n        },\n        \"validation_framework\": {\n          \"audit\": \"inventory, grouping, naming, icon, context-logic\",\n          \"qa\": \"functional testing, cross-system\",\n          \"performance\": \"menu load, resource evaluation\",\n          \"security\": \"permission validation, injection prevention\"\n        },\n        \"documentation\": {\n          \"schemas\": \"all core components\",\n          \"patterns\": \"grouping, naming, menu structuring\",\n          \"diagrams\": \"item/group/menu/context relationships\",\n          \"dependency_map\": \"component linkage and extensibility dynamics\"\n        },\n        \"deliverable_manifest\": {\n          \"item_inventory\": \"grouped/audited, type-safe\",\n          \"menu_definitions\": \"hierarchical, max depth 3\",\n          \"nss_files\": \"modular, pattern-compliant, referentially documented\",\n          \"tests\": \"cross-version, performance/reliability benchmarks\"\n        }\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Foundational Philosophy and Objectives\",\n      \"content\": \"Establish an extensible, efficient, and reliable Windows context menu system leveraging Nilesoft Shell to enhance user productivity by minimizing navigation effort, ensuring logical organization, and permitting seamless future expansions. The foundational mission and vision emphasize clarity, modularity, and stable integration with Windows Explorer, targeting both immediate workflow improvement and long-term maintainability. Success criteria include rapid application/file access, consistently intuitive menu structures, modular growth, and unyielding reliability across environments. All actions and structures flow from and support clearly articulated principles—efficiency, clarity, extensibility, reliability—that bind systemic purpose and outcome.\"\n    },\n    {\n      \"category\": \"System Architecture and Canonical Patterns\",\n      \"content\": \"Mandate a modular, hierarchical architecture comprising atomic NSS files organized by single responsibility: initialization (constants/config), variables (reusable values), items (individual menu entries/commands with canonical naming/icons), groups (logical assemblies), menus (layered groupings/contextual placements), and contexts (rule-based, dynamic visibility). Enforce strict naming conventions, context-aware grouping, and icon standardization. Schemas for each component ensure type safety and future-proof extensibility. The system’s architecture is guided by the single-responsibility and compositional pattern memory, enabling minimal-impact incremental improvements.\"\n    },\n    {\n      \"category\": \"Integration, Compatibility, and Infrastructure\",\n      \"content\": \"Guarantee Nilesoft Shell v1.9+ compliance, Windows 10/11 support, and robust Explorer/Registry integration through standardized resource/path management, shell extension best practices, and cross-environment validation. Harmonize NSS, icons, scripts, and command-line dependencies within a directory architecture supporting modular substitution and scalability. Explicitly manage dependencies, technical constraints, and integration points to shield the system from versioning, performance, and security disruptions. Implement configuration methods to preserve stability with evolving Windows and Shell versions.\"\n    },\n    {\n      \"category\": \"Operational Workflow, Validation, and Task Sequencing\",\n      \"content\": \"Direct a milestone-driven, agile process: audit and standardize item inventory, develop and document item-to-group-to-menu-to-context lineage, implement robust context/visibility rules, and commit to sequential task completion (items → groups → menus → contexts). Orchestrate quality assurance—cross-version/system testing, performance profiling, and security hardening—with continuous documentation and knowledge base expansion. Structure all work via a transparent, traceable dependency map to enable seamless handoffs and reformulation as system and requirements evolve.\"\n    },\n    {\n      \"category\": \"Deliverables, Documentation, and Success Criteria\",\n      \"content\": \"Define the complete deliverable set as: fully organized item inventory, explicit and diagrammed menu/context/group hierarchies (max depth 3), modular and documented NSS source files (pattern-compliant, memory-banked), cross-environment QA/performance/security reports, and up-to-date documentation of schemas and actionable templates. Success is operationally defined as complete logical grouping, menu/context implementation, documentation synchronization, and testing across all targeted Windows versions, ensuring the system functions with intended efficiency, clarity, extensibility, and reliability.\"\n    }\n  ],\n  \"consolidated_directive\": \"Synthesize and implement a modular, hierarchically-organized, Nilesoft Shell-driven Windows context menu system by enforcing: 1) mission-aligned efficiency, clarity, extensibility, and reliability at every operational level; 2) canonical directory and file patterns with explicit type- and naming-requirements for items, groups, menus, and contexts; 3) strict integration validations ensuring Windows version/system compatibility, resource integrity, and security; 4) a sequenced, milestone-driven workflow coupling continuous audit, QA, and knowledge base maintenance; and 5) rigorous, referential documentation—schema, diagrams, and dependency maps—yielding system-agnostic, extensible artifacts that guarantee perpetually navigable, high-clarity transformation across all future menu system extensions.\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"### File Structure\\n\\n```\\n├── 01_foundation.md\\n├── 02_context.md\\n├── 03_patterns.md\\n├── 04_tech.md\\n├── 05_activity.md\\n├── 06_progress.md\\n├── 07_tasks.md\\n└── 08_objective.md\\n```\\n\\n---\\n\\n#### `01_foundation.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Custom Windows context menu system built on Nilesoft Shell\\n    - Purpose: Enhance Windows file explorer with efficient context actions\\n    - Principles: Modularity, consistency, and extensibility\\n    \\n    # 01_foundation.md\\n    \\n    ## Core Mission\\n    The Nilesoft Shell context menu system aims to significantly enhance Windows file explorer with a powerful, customizable context menu framework that improves workflow efficiency and user experience.\\n    \\n    ## Vision\\n    Create a comprehensive context menu system that:\\n    - Provides quick access to frequently used applications and utilities\\n    - Standardizes menu organization and appearance\\n    - Enables rapid file/folder operations through contextual commands\\n    - Scales elegantly with minimal maintenance overhead\\n    \\n    ## Values\\n    - **Efficiency**: Minimize clicks and navigation time\\n    - **Clarity**: Intuitive menu organization with consistent naming and grouping\\n    - **Extensibility**: Easily add new functionality without restructuring\\n    - **Reliability**: Stable operation without conflicts or performance impact\\n    \\n    ## Strategic Goals\\n    1. Standardize application launching from any context\\n    2. Optimize file management operations\\n    3. Group related functionality logically\\n    4. Leverage NSS scripting for advanced automation\\n    5. Maintain backward compatibility with Windows shell\\n```\\n\\n---\\n\\n#### `02_context.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Windows context menus often become cluttered and inconsistent\\n    - Users need quick access to applications and file operations\\n    - Nilesoft Shell provides programmatic control over Windows context menus\\n    \\n    # 02_context.md\\n    \\n    ## Problem Space\\n    \\n    ### Current Limitations\\n    - Default Windows context menus lack organization and customization options\\n    - Third-party applications inject entries haphazardly, creating menu bloat\\n    - No standard interface for managing context menu appearance and behavior\\n    - Limited ability to group related actions or create conditional menus\\n    \\n    ### User Needs\\n    - Quick access to frequently used applications from any file/folder context\\n    - Consistent organization of menu items across different file types\\n    - Visual distinction between types of operations (system, applications, custom scripts)\\n    - Ability to conditionally show/hide menu options based on file attributes\\n    - Simplified access to system utilities and administrative functions\\n    \\n    ## Stakeholders\\n    - **End Users**: Individuals seeking workflow efficiency improvements\\n    - **System Administrators**: Managing standard configurations across machines\\n    - **Developers**: Extending functionality through Nilesoft Shell scripts (NSS)\\n    - **Windows Environment**: Integration with existing shell infrastructure\\n    \\n    ## External Constraints\\n    - Windows context menu system architecture and limitations\\n    - Nilesoft Shell version compatibility and feature set\\n    - Need for backward compatibility with existing Windows functionality\\n    - Performance impact considerations for menu rendering and operations\\n    - Security considerations for script execution and application launching\\n    \\n    ## Key Success Metrics\\n    - Reduced time to access applications and perform file operations\\n    - Improved menu organization and visual clarity\\n    - Extensibility without code duplication or structure compromise\\n    - Minimal impact on system performance\\n    - Reliability across Windows versions and configurations\\n```\\n\\n---\\n\\n#### `03_patterns.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Hierarchical organization: _init → variables → items → groups → menus → contexts\\n    - NSS files are modular with single-responsibility principle\\n    - Items represent individual menu entries; groups combine related items\\n    - Contexts determine when and where menus appear\\n    \\n    # 03_patterns.md\\n    \\n    ## System Architecture\\n    \\n    ### Core Components Hierarchy\\n    1. **Initialization (_1_init/)**: Sets up constants, configurations, and base environment\\n    2. **Variables (_2_variables/)**: Defines reusable values and settings\\n    3. **Items (_3_items/)**: Individual menu entries (commands, applications, actions)\\n    4. **Groups (_4_groups/)**: Logical collections of related items\\n    5. **Menus (_5_menus/)**: Structured arrangements of items and groups\\n    6. **Contexts (_6_contexts/)**: Rules determining when and where menus appear\\n    \\n    ### Design Patterns\\n    \\n    #### Modular Composition\\n    - Each NSS file has a single responsibility\\n    - Components are composed through inclusion and reference\\n    - Changes to one component minimally impact others\\n    \\n    #### Naming Conventions\\n    - File prefixes indicate component type (`itm_` for items, etc.)\\n    - Consistent categorization (`app_`, `action_`, `sys_` prefixes)\\n    - Descriptive suffixes for variant behavior\\n    \\n    #### Conditional Visibility\\n    - Menu items show/hide based on context rules\\n    - Rules can evaluate file types, locations, system state\\n    - Compound conditions for precise targeting\\n    \\n    #### Icon Standardization\\n    - Consistent icon usage across similar functions\\n    - Icon mapping for visual categorization\\n    - Fallback icons for compatibility\\n    \\n    ## Component Relationships\\n    \\n    ```mermaid\\n    graph TD\\n        Init[_1_init] --> Vars[_2_variables]\\n        Vars --> Items[_3_items]\\n        Items --> Groups[_4_groups]\\n        Groups --> Menus[_5_menus]\\n        Menus --> Contexts[_6_contexts]\\n        \\n        subgraph \\\"Item Types\\\"\\n            AppItems[Application Launchers]\\n            ActionItems[File Actions]\\n            SystemItems[System Operations]\\n        end\\n        \\n        Items --- AppItems\\n        Items --- ActionItems\\n        Items --- SystemItems\\n    ```\\n    \\n    ## Schema Patterns\\n    \\n    ### Item Schema\\n    ```\\n    item {\\n      name: \\\"item_name\\\"\\n      icon: \\\"icon_path_or_resource\\\"\\n      command: \\\"command_to_execute\\\"\\n      visibility: \\\"condition_expression\\\"\\n    }\\n    ```\\n    \\n    ### Group Schema\\n    ```\\n    group {\\n      name: \\\"group_name\\\"\\n      icon: \\\"group_icon\\\"\\n      items: [\\n        // references to items or inline definitions\\n      ]\\n    }\\n    ```\\n    \\n    ### Menu Schema\\n    ```\\n    menu {\\n      name: \\\"menu_name\\\"\\n      items: [\\n        // groups and/or individual items\\n      ]\\n      contexts: [\\n        // when/where this menu appears\\n      ]\\n    }\\n    ```\\n    \\n    ## Pattern Evolution Strategy\\n    - Maintain backward compatibility when adding new patterns\\n    - Document pattern changes in corresponding memory bank files\\n    - Test pattern modifications across different file contexts\\n    - Prefer extending existing patterns over creating new ones\\n```\\n\\n---\\n\\n#### `04_tech.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Built on Nilesoft Shell framework for Windows context menu customization\\n    - NSS scripting language for defining menu items and behavior\\n    - Modular file organization in _1_init through _6_contexts directories\\n    - Supporting resources include PNG/SVG icons and batch scripts\\n    \\n    # 04_tech.md\\n    \\n    ## Core Technologies\\n    \\n    ### Nilesoft Shell\\n    - **Version**: Shell v1.9+ (current observed version: v1.9.15)\\n    - **Purpose**: Framework for customizing Windows Explorer context menus\\n    - **Architecture**: Shell extension that intercepts and modifies Windows context menu requests\\n    - **Configuration**: NSS scripts compiled and loaded by Shell engine\\n    \\n    ### NSS Scripting\\n    - **Language**: Nilesoft Shell Script (NSS)\\n    - **Syntax**: C-like with specialized directives for menu definition\\n    - **Scope**: Declarative definitions with procedural capabilities\\n    - **Compilation**: Scripts are parsed and compiled by the Shell engine at runtime\\n    \\n    ## Infrastructure\\n    \\n    ### Directory Structure\\n    ```\\n    exe/\\n    ├── NSS/\\n    │   ├── _1_init/        # Initialization, constants, configuration\\n    │   ├── _2_variables/   # Variable definitions for reuse\\n    │   ├── _3_items/       # Individual menu items\\n    │   ├── _4_groups/      # Logical groupings of items\\n    │   ├── _5_menus/       # Menu structures and layouts\\n    │   └── _6_contexts/    # Context rules for menu visibility\\n    ├── LIB/\\n    │   ├── bat/            # Batch scripts for utilities\\n    │   ├── png/            # PNG icons for menu items\\n    │   └── svg/            # SVG icons for menu items\\n    └── shell.nss           # Main entry point for NSS configuration\\n    ```\\n    \\n    ### Integration Points\\n    - Windows Explorer shell extension\\n    - Windows Registry for registration and configuration\\n    - File system for icon and resource access\\n    - Command line for application launching\\n    - COM/Shell interfaces for advanced functionality\\n    \\n    ## Dependencies\\n    \\n    ### External Resources\\n    - Icon files (PNG, SVG) for menu item visualization\\n    - Batch files for extended functionality\\n    - Executable paths for application launching\\n    - System shell commands for file operations\\n    \\n    ### Development Tools\\n    - Resource editors for icon management\\n    - Shell extension management utilities\\n    - Text editors with NSS syntax support\\n    \\n    ## Technical Constraints\\n    \\n    ### Performance Considerations\\n    - Context menu load time impact\\n    - Memory footprint of loaded NSS scripts\\n    - Efficiency of condition evaluation for visibility rules\\n    \\n    ### Compatibility\\n    - Windows version support (10, 11)\\n    - Interaction with other shell extensions\\n    - Application path resolution across different environments\\n    \\n    ### Security\\n    - Execution permissions for launched applications\\n    - Script injection prevention\\n    - Validation of external resource paths\\n    \\n    ## Technical Roadmap\\n    - Ongoing migration to latest Nilesoft Shell features\\n    - Standardization of icon resources\\n    - Optimization of conditional visibility expressions\\n    - Enhanced script modularity and reusability\\n```\\n\\n---\\n\\n#### `05_activity.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Current focus on populating individual menu items (_3_items directory)\\n    - Need to develop groups, menus, and contexts structure\\n    - Application launchers are the most extensively implemented item type\\n    - System action items have basic implementation\\n    \\n    # 05_activity.md\\n    \\n    ## Current Focus Areas\\n    \\n    ### Item Development\\n    - Extensive collection of application launcher items (`itm_app_*.nss`) already implemented\\n    - Basic system action items created (`itm_action_sys_*.nss`) \\n    - Need to audit existing items for consistency in naming and functionality\\n    - Opportunity to standardize icon usage across similar item types\\n    \\n    ### Structural Components\\n    - Groups (`_4_groups/`) directory needs population to organize related items\\n    - Menus (`_5_menus/`) require definition to establish menu layouts and hierarchy\\n    - Contexts (`_6_contexts/`) need implementation to control when menus appear\\n    \\n    ### Technical Infrastructure\\n    - Ensure initialization files in `_1_init/` properly set up the environment\\n    - Review variables in `_2_variables/` for completeness and usefulness\\n    - Validate resource paths and icon references\\n    \\n    ## In-Progress Work\\n    \\n    ### Application Launcher Standardization\\n    - Standardizing application launcher items with consistent:\\n      - Command parameter formatting\\n      - Icon selection and fallbacks\\n      - Visibility conditions\\n      - Naming conventions\\n    \\n    ### System Functionality Integration\\n    - Integrating core Windows functionality through specialized items\\n    - Creating consistent approach for system utility access\\n    - Implementing common file operations\\n    \\n    ### Organization Strategy\\n    - Developing logical grouping strategy for related items\\n    - Planning menu hierarchy based on frequency of use and context\\n    - Establishing visibility rules based on file types and locations\\n    \\n    ## Technical Decisions\\n    \\n    ### Item Implementation Pattern\\n    ```nss\\n    item {\\n      name: \\\"Application Name\\\"\\n      icon: \\\"path/to/icon.png\\\"\\n      command: \\\"path/to/executable.exe [parameters]\\\"\\n      \\n      // Optional visibility condition\\n      visibility: [condition expression]\\n    }\\n    ```\\n    \\n    ### Resource Path Strategy\\n    - Using relative paths for icons and resources where possible\\n    - Standardizing on Shell-compatible path syntax\\n    - Planning for fallback icons when primary resources unavailable\\n    \\n    ### Menu Appearance Decisions\\n    - Consistent separators between logical groups\\n    - Icon alignment and sizing standardization\\n    - Text formatting and capitalization rules\\n```\\n\\n---\\n\\n#### `06_progress.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Directory structure and modular approach established\\n    - Extensive collection of application launcher items implemented\\n    - Basic system action items created\\n    - Need to develop grouping and context structures\\n    \\n    # 06_progress.md\\n    \\n    ## State of the Build\\n    \\n    ### Completed Components\\n    - ✅ Overall directory structure established for modular organization\\n    - ✅ Basic initialization structure in `_1_init/` directory\\n    - ✅ Extensive collection of application launcher items (~70 items)\\n    - ✅ Core system action items for basic operations\\n    - ✅ Resource libraries for icons and batch utilities\\n    \\n    ### In Progress\\n    - 🔄 Variable standardization in `_2_variables/` directory\\n    - 🔄 Audit of existing items for consistency and effectiveness\\n    - 🔄 Documentation of item patterns and best practices\\n    - 🔄 Standardization of icon usage and resource paths\\n    \\n    ### Not Started\\n    - ⏳ Population of `_4_groups/` directory to organize items\\n    - ⏳ Definition of menu structures in `_5_menus/` directory\\n    - ⏳ Implementation of context rules in `_6_contexts/` directory\\n    - ⏳ Comprehensive testing across different file types\\n    \\n    ## Milestones\\n    \\n    ### Milestone 1: Foundation (COMPLETED)\\n    - ✓ Establish directory structure\\n    - ✓ Implement basic initialization\\n    - ✓ Create core application launcher items\\n    - ✓ Set up resource libraries\\n    \\n    ### Milestone 2: Structure (IN PROGRESS)\\n    - ✓ Standardize item implementation patterns\\n    - ✓ Complete system action items\\n    - 🔄 Document existing items and patterns\\n    - ⏳ Organize items into logical groups\\n    \\n    ### Milestone 3: Integration (PLANNED)\\n    - Define menu structures\\n    - Implement context rules\\n    - Create conditional visibility based on file types\\n    - Optimize menu organization\\n    \\n    ### Milestone 4: Refinement (PLANNED)\\n    - Comprehensive testing\\n    - Performance optimization\\n    - Icon standardization\\n    - Documentation completion\\n    \\n    ## Current Blockers\\n    - Need to develop a consistent strategy for grouping related items\\n    - Lack of defined context rules for when menus should appear\\n    - Incomplete documentation of existing patterns and structures\\n    \\n    ## Recent Updates\\n    - Added multiple application launcher items for common utilities\\n    - Created system action items for file operations\\n    - Established resource libraries for icons and batch scripts\\n    - Documented NSS patterns and implementation strategies\\n    \\n    ## Next Actions\\n    1. Complete the audit of existing items for consistency\\n    2. Develop a logical grouping strategy for items\\n    3. Begin populating the `_4_groups/` directory\\n    4. Create a menu structure plan for the `_5_menus/` directory\\n    5. Establish basic context rules in the `_6_contexts/` directory\\n```\\n\\n---\\n\\n#### `07_tasks.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Organize existing items into logical groups\\n    - Develop menu structures based on usage patterns\\n    - Implement context rules for menu visibility\\n    - Create documentation for future item development\\n    \\n    # 07_tasks.md\\n    \\n    ## High Priority Tasks\\n    \\n    ### Group Structure Development\\n    - [ ] **Create Application Groups**\\n      - Create `grp_apps_dev.nss` for development tools\\n      - Create `grp_apps_media.nss` for media applications\\n      - Create `grp_apps_system.nss` for system utilities\\n      - Create `grp_apps_office.nss` for productivity applications\\n      - *Owner: System developer*\\n      - *Justification: Organize the extensive list of application items into logical groups*\\n    \\n    - [ ] **Create Action Groups**\\n      - Create `grp_actions_file.nss` for file operations\\n      - Create `grp_actions_folder.nss` for folder operations\\n      - Create `grp_actions_system.nss` for system operations\\n      - *Owner: System developer*\\n      - *Justification: Group related actions for better menu organization*\\n    \\n    ### Menu Structure Implementation\\n    - [ ] **Design Core Menus**\\n      - Create `menu_applications.nss` with application groups\\n      - Create `menu_actions.nss` with action groups\\n      - Create `menu_system.nss` with system utilities\\n      - *Owner: System developer*\\n      - *Justification: Establish logical menu structure for better user experience*\\n    \\n    - [ ] **Implement Cascading Menus**\\n      - Design depth strategy (max 2-3 levels deep)\\n      - Implement parent-child relationships\\n      - *Owner: System developer*\\n      - *Justification: Organize complex item collections without cluttering main menu*\\n    \\n    ### Context Implementation\\n    - [ ] **Define File Type Contexts**\\n      - Create rules for different file extensions\\n      - Implement special handling for executables, documents, media\\n      - *Owner: System developer*\\n      - *Justification: Show relevant options based on file type*\\n    \\n    - [ ] **Define Location Contexts**\\n      - Create rules for desktop, drives, libraries\\n      - Implement special handling for system folders\\n      - *Owner: System developer*\\n      - *Justification: Show relevant options based on location*\\n    \\n    ## Medium Priority Tasks\\n    \\n    ### Documentation\\n    - [ ] **Document Group Patterns**\\n      - Create templates for new groups\\n      - Document naming conventions\\n      - *Owner: Documentation specialist*\\n      - *Justification: Ensure consistency in future development*\\n    \\n    - [ ] **Document Menu Structures**\\n      - Create visual hierarchy diagrams\\n      - Document menu organization principles\\n      - *Owner: Documentation specialist*\\n      - *Justification: Provide clear guidance for menu development*\\n    \\n    ### Quality Assurance\\n    - [ ] **Audit Existing Items**\\n      - Review naming consistency\\n      - Validate command parameters\\n      - Check icon references\\n      - *Owner: QA specialist*\\n      - *Justification: Ensure existing items follow standards*\\n    \\n    - [ ] **Test Across Environments**\\n      - Verify functionality on Windows 10\\n      - Verify functionality on Windows 11\\n      - Test with various file types\\n      - *Owner: QA specialist*\\n      - *Justification: Ensure compatibility across environments*\\n    \\n    ## Low Priority Tasks\\n    \\n    ### Optimization\\n    - [ ] **Review Performance**\\n      - Profile menu load times\\n      - Optimize condition evaluations\\n      - *Owner: Performance specialist*\\n      - *Justification: Ensure menus load quickly*\\n    \\n    - [ ] **Icon Standardization**\\n      - Create consistent icon set\\n      - Implement fallback strategy\\n      - *Owner: Design specialist*\\n      - *Justification: Visual consistency across menus*\\n    \\n    ## Dependencies\\n    - Group creation depends on completion of item audit\\n    - Menu implementation depends on group creation\\n    - Context rules depend on menu implementation\\n    - Testing depends on all implementation tasks\\n    \\n    ## Task Assignment Strategy\\n    - Focus on high-priority tasks first\\n    - Complete related tasks in sequence (items → groups → menus → contexts)\\n    - Document as you go to maintain knowledge base\\n```\\n\\n---\\n\\n#### `08_objective.md`\\n\\n```markdown\\n    ## Distilled Highlights\\n    - Create a complete, modular context menu system for Windows Explorer\\n    - Immediate focus: Group existing items and implement menu structures\\n    - Success measured by efficiency, organization, and extensibility\\n    - Target completion of Milestone 3 (Integration) by next development cycle\\n    \\n    # 08_objective.md\\n    \\n    ## Primary Objective\\n    Create a comprehensive Windows context menu system that enhances productivity through logically organized, easily accessible commands and applications, using a modular architecture that ensures maintainability and extensibility.\\n    \\n    ## Success Criteria\\n    The context menu system will be considered successful when it:\\n    \\n    1. **Provides Efficient Access**\\n       - Reduces clicks needed to access common applications\\n       - Speeds up file operations through contextual commands\\n       - Groups related functionality in intuitive categories\\n    \\n    2. **Maintains Logical Organization**\\n       - Presents a consistent menu structure across different contexts\\n       - Limits menu depth to prevent navigation complexity\\n       - Uses visual cues (icons, separators) to distinguish item types\\n    \\n    3. **Ensures Extensibility**\\n       - Follows modular design patterns for easy additions\\n       - Maintains clear separation between components\\n       - Documents patterns for consistent future development\\n    \\n    4. **Performs Reliably**\\n       - Loads quickly without noticeable delay\\n       - Works consistently across Windows versions\\n       - Avoids conflicts with other shell extensions\\n    \\n    ## Immediate Focus\\n    The current development cycle focuses on:\\n    \\n    1. **Component Organization**\\n       - Grouping existing items into logical collections\\n       - Implementing menu structures to organize these groups\\n       - Defining context rules to control when menus appear\\n    \\n    2. **System Completion**\\n       - Filling gaps in the current directory structure\\n       - Completing the chain from items to groups to menus to contexts\\n       - Documenting component relationships and dependencies\\n    \\n    3. **Usability Enhancement**\\n       - Standardizing visual appearance across menu items\\n       - Ensuring consistent behavior across different file types\\n       - Optimizing menu depth and organization for quick access\\n    \\n    ## Alignment with Vision\\n    This objective directly supports the project's vision by:\\n    - Enhancing Windows Explorer with powerful, customizable menus\\n    - Standardizing menu organization for improved clarity\\n    - Enabling quick access to applications and utilities\\n    - Creating a scalable system that can grow without restructuring\\n    \\n    ## Definition of \\\"Done\\\"\\n    For the current phase, \\\"done\\\" means:\\n    \\n    1. All existing items organized into appropriate groups\\n    2. Core menu structures defined and implemented\\n    3. Basic context rules established for different file types and locations\\n    4. Documentation updated to reflect the current system state\\n    5. At least basic testing completed across different environments\\n    \\n    ## Timeline Target\\n    Complete current objectives (Milestone 3: Integration) within the next development cycle, setting the foundation for the refinement phase that follows.\\n```\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Foundational Philosophy and Objectives\",\n      \"content\": \"Establish an extensible, efficient, and reliable Windows context menu system leveraging Nilesoft Shell to enhance user productivity by minimizing navigation effort, ensuring logical organization, and permitting seamless future expansions. The foundational mission and vision emphasize clarity, modularity, and stable integration with Windows Explorer, targeting both immediate workflow improvement and long-term maintainability. Success criteria include rapid application/file access, consistently intuitive menu structures, modular growth, and unyielding reliability across environments. All actions and structures flow from and support clearly articulated principles—efficiency, clarity, extensibility, reliability—that bind systemic purpose and outcome.\"\n    },\n    {\n      \"category\": \"System Architecture and Canonical Patterns\",\n      \"content\": \"Mandate a modular, hierarchical architecture comprising atomic NSS files organized by single responsibility: initialization (constants/config), variables (reusable values), items (individual menu entries/commands with canonical naming/icons), groups (logical assemblies), menus (layered groupings/contextual placements), and contexts (rule-based, dynamic visibility). Enforce strict naming conventions, context-aware grouping, and icon standardization. Schemas for each component ensure type safety and future-proof extensibility. The system’s architecture is guided by the single-responsibility and compositional pattern memory, enabling minimal-impact incremental improvements.\"\n    },\n    {\n      \"category\": \"Integration, Compatibility, and Infrastructure\",\n      \"content\": \"Guarantee Nilesoft Shell v1.9+ compliance, Windows 10/11 support, and robust Explorer/Registry integration through standardized resource/path management, shell extension best practices, and cross-environment validation. Harmonize NSS, icons, scripts, and command-line dependencies within a directory architecture supporting modular substitution and scalability. Explicitly manage dependencies, technical constraints, and integration points to shield the system from versioning, performance, and security disruptions. Implement configuration methods to preserve stability with evolving Windows and Shell versions.\"\n    },\n    {\n      \"category\": \"Operational Workflow, Validation, and Task Sequencing\",\n      \"content\": \"Direct a milestone-driven, agile process: audit and standardize item inventory, develop and document item-to-group-to-menu-to-context lineage, implement robust context/visibility rules, and commit to sequential task completion (items → groups → menus → contexts). Orchestrate quality assurance—cross-version/system testing, performance profiling, and security hardening—with continuous documentation and knowledge base expansion. Structure all work via a transparent, traceable dependency map to enable seamless handoffs and reformulation as system and requirements evolve.\"\n    },\n    {\n      \"category\": \"Deliverables, Documentation, and Success Criteria\",\n      \"content\": \"Define the complete deliverable set as: fully organized item inventory, explicit and diagrammed menu/context/group hierarchies (max depth 3), modular and documented NSS source files (pattern-compliant, memory-banked), cross-environment QA/performance/security reports, and up-to-date documentation of schemas and actionable templates. Success is operationally defined as complete logical grouping, menu/context implementation, documentation synchronization, and testing across all targeted Windows versions, ensuring the system functions with intended efficiency, clarity, extensibility, and reliability.\"\n    }\n  ],\n  \"consolidated_directive\": \"Synthesize and implement a modular, hierarchically-organized, Nilesoft Shell-driven Windows context menu system by enforcing: 1) mission-aligned efficiency, clarity, extensibility, and reliability at every operational level; 2) canonical directory and file patterns with explicit type- and naming-requirements for items, groups, menus, and contexts; 3) strict integration validations ensuring Windows version/system compatibility, resource integrity, and security; 4) a sequenced, milestone-driven workflow coupling continuous audit, QA, and knowledge base maintenance; and 5) rigorous, referential documentation—schema, diagrams, and dependency maps—yielding system-agnostic, extensible artifacts that guarantee perpetually navigable, high-clarity transformation across all future menu system extensions.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"Title\": \"[Windows Context Menu System Synthesizer]\",\n  \"Interpretation\": \"Your goal is not to summarize or analyze the input, but to synthesize and operationalize a modular, hierarchically-organized, Nilesoft Shell-driven Windows context menu system strictly according to the provided foundational principles, architectural mandates, infrastructure constraints, operational workflows, and deliverables criteria. Assume the role of system_synthesizer and produce directly actionable outputs for system implementation and documentation. Execute as:\",\n  \"Transformation\": \"`{role=system_synthesizer; input=[meta_categories:list, consolidated_directive:str]; process=[extract_mission_parameters(), formalize_architectural_patterns(), devise_type_safe_component_schemas(), sequence_milestone_driven_workflow(), structure_operational_validation_protocols(), codify_deliverable_artifacts(), synthesize_actionable_templates_and_diagrams()]; constraints=[preserve explicit category-to-directive mapping(), enforce NSS canonical patterns(), maintain directory/file naming standards(), prevent deviation from mission-aligned principles(), ensure full cross-environment compatibility(), prohibit untyped or undocumented output()]; requirements=[output modular system architecture(), explicit directory/tree diagrams(), component schema documentation(), sequenced task blueprints(), QA/security/compatibility protocols(), system-agnostic and extensible documentation(), complete type safety and pattern compliance()]; output={system_blueprint:dict}}`\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\n[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}\n\n",
      "step": "a",
      "title": "Directive Focuser",
      "input": "\n'```[Initial Prompt]: \"### File Structure\n\n```\n├── 01_foundation.md\n├── 02_context.md\n├── 03_patterns.md\n├── 04_tech.md\n├── 05_activity.md\n├── 06_progress.md\n├── 07_tasks.md\n└── 08_objective.md\n```\n\n---\n\n#### `01_foundation.md`\n\n```markdown\n    ## Distilled Highlights\n    - Custom Windows context menu system built on Nilesoft Shell\n    - Purpose: Enhance Windows file explorer with efficient context actions\n    - Principles: Modularity, consistency, and extensibility\n    \n    # 01_foundation.md\n    \n    ## Core Mission\n    The Nilesoft Shell context menu system aims to significantly enhance Windows file explorer with a powerful, customizable context menu framework that improves workflow efficiency and user experience.\n    \n    ## Vision\n    Create a comprehensive context menu system that:\n    - Provides quick access to frequently used applications and utilities\n    - Standardizes menu organization and appearance\n    - Enables rapid file/folder operations through contextual commands\n    - Scales elegantly with minimal maintenance overhead\n    \n    ## Values\n    - **Efficiency**: Minimize clicks and navigation time\n    - **Clarity**: Intuitive menu organization with consistent naming and grouping\n    - **Extensibility**: Easily add new functionality without restructuring\n    - **Reliability**: Stable operation without conflicts or performance impact\n    \n    ## Strategic Goals\n    1. Standardize application launching from any context\n    2. Optimize file management operations\n    3. Group related functionality logically\n    4. Leverage NSS scripting for advanced automation\n    5. Maintain backward compatibility with Windows shell\n```\n\n---\n\n#### `02_context.md`\n\n```markdown\n    ## Distilled Highlights\n    - Windows context menus often become cluttered and inconsistent\n    - Users need quick access to applications and file operations\n    - Nilesoft Shell provides programmatic control over Windows context menus\n    \n    # 02_context.md\n    \n    ## Problem Space\n    \n    ### Current Limitations\n    - Default Windows context menus lack organization and customization options\n    - Third-party applications inject entries haphazardly, creating menu bloat\n    - No standard interface for managing context menu appearance and behavior\n    - Limited ability to group related actions or create conditional menus\n    \n    ### User Needs\n    - Quick access to frequently used applications from any file/folder context\n    - Consistent organization of menu items across different file types\n    - Visual distinction between types of operations (system, applications, custom scripts)\n    - Ability to conditionally show/hide menu options based on file attributes\n    - Simplified access to system utilities and administrative functions\n    \n    ## Stakeholders\n    - **End Users**: Individuals seeking workflow efficiency improvements\n    - **System Administrators**: Managing standard configurations across machines\n    - **Developers**: Extending functionality through Nilesoft Shell scripts (NSS)\n    - **Windows Environment**: Integration with existing shell infrastructure\n    \n    ## External Constraints\n    - Windows context menu system architecture and limitations\n    - Nilesoft Shell version compatibility and feature set\n    - Need for backward compatibility with existing Windows functionality\n    - Performance impact considerations for menu rendering and operations\n    - Security considerations for script execution and application launching\n    \n    ## Key Success Metrics\n    - Reduced time to access applications and perform file operations\n    - Improved menu organization and visual clarity\n    - Extensibility without code duplication or structure compromise\n    - Minimal impact on system performance\n    - Reliability across Windows versions and configurations\n```\n\n---\n\n#### `03_patterns.md`\n\n```markdown\n    ## Distilled Highlights\n    - Hierarchical organization: _init → variables → items → groups → menus → contexts\n    - NSS files are modular with single-responsibility principle\n    - Items represent individual menu entries; groups combine related items\n    - Contexts determine when and where menus appear\n    \n    # 03_patterns.md\n    \n    ## System Architecture\n    \n    ### Core Components Hierarchy\n    1. **Initialization (_1_init/)**: Sets up constants, configurations, and base environment\n    2. **Variables (_2_variables/)**: Defines reusable values and settings\n    3. **Items (_3_items/)**: Individual menu entries (commands, applications, actions)\n    4. **Groups (_4_groups/)**: Logical collections of related items\n    5. **Menus (_5_menus/)**: Structured arrangements of items and groups\n    6. **Contexts (_6_contexts/)**: Rules determining when and where menus appear\n    \n    ### Design Patterns\n    \n    #### Modular Composition\n    - Each NSS file has a single responsibility\n    - Components are composed through inclusion and reference\n    - Changes to one component minimally impact others\n    \n    #### Naming Conventions\n    - File prefixes indicate component type (`itm_` for items, etc.)\n    - Consistent categorization (`app_`, `action_`, `sys_` prefixes)\n    - Descriptive suffixes for variant behavior\n    \n    #### Conditional Visibility\n    - Menu items show/hide based on context rules\n    - Rules can evaluate file types, locations, system state\n    - Compound conditions for precise targeting\n    \n    #### Icon Standardization\n    - Consistent icon usage across similar functions\n    - Icon mapping for visual categorization\n    - Fallback icons for compatibility\n    \n    ## Component Relationships\n    \n    ```mermaid\n    graph TD\n        Init[_1_init] --> Vars[_2_variables]\n        Vars --> Items[_3_items]\n        Items --> Groups[_4_groups]\n        Groups --> Menus[_5_menus]\n        Menus --> Contexts[_6_contexts]\n        \n        subgraph \"Item Types\"\n            AppItems[Application Launchers]\n            ActionItems[File Actions]\n            SystemItems[System Operations]\n        end\n        \n        Items --- AppItems\n        Items --- ActionItems\n        Items --- SystemItems\n    ```\n    \n    ## Schema Patterns\n    \n    ### Item Schema\n    ```\n    item {\n      name: \"item_name\"\n      icon: \"icon_path_or_resource\"\n      command: \"command_to_execute\"\n      visibility: \"condition_expression\"\n    }\n    ```\n    \n    ### Group Schema\n    ```\n    group {\n      name: \"group_name\"\n      icon: \"group_icon\"\n      items: [\n        // references to items or inline definitions\n      ]\n    }\n    ```\n    \n    ### Menu Schema\n    ```\n    menu {\n      name: \"menu_name\"\n      items: [\n        // groups and/or individual items\n      ]\n      contexts: [\n        // when/where this menu appears\n      ]\n    }\n    ```\n    \n    ## Pattern Evolution Strategy\n    - Maintain backward compatibility when adding new patterns\n    - Document pattern changes in corresponding memory bank files\n    - Test pattern modifications across different file contexts\n    - Prefer extending existing patterns over creating new ones\n```\n\n---\n\n#### `04_tech.md`\n\n```markdown\n    ## Distilled Highlights\n    - Built on Nilesoft Shell framework for Windows context menu customization\n    - NSS scripting language for defining menu items and behavior\n    - Modular file organization in _1_init through _6_contexts directories\n    - Supporting resources include PNG/SVG icons and batch scripts\n    \n    # 04_tech.md\n    \n    ## Core Technologies\n    \n    ### Nilesoft Shell\n    - **Version**: Shell v1.9+ (current observed version: v1.9.15)\n    - **Purpose**: Framework for customizing Windows Explorer context menus\n    - **Architecture**: Shell extension that intercepts and modifies Windows context menu requests\n    - **Configuration**: NSS scripts compiled and loaded by Shell engine\n    \n    ### NSS Scripting\n    - **Language**: Nilesoft Shell Script (NSS)\n    - **Syntax**: C-like with specialized directives for menu definition\n    - **Scope**: Declarative definitions with procedural capabilities\n    - **Compilation**: Scripts are parsed and compiled by the Shell engine at runtime\n    \n    ## Infrastructure\n    \n    ### Directory Structure\n    ```\n    exe/\n    ├── NSS/\n    │   ├── _1_init/        # Initialization, constants, configuration\n    │   ├── _2_variables/   # Variable definitions for reuse\n    │   ├── _3_items/       # Individual menu items\n    │   ├── _4_groups/      # Logical groupings of items\n    │   ├── _5_menus/       # Menu structures and layouts\n    │   └── _6_contexts/    # Context rules for menu visibility\n    ├── LIB/\n    │   ├── bat/            # Batch scripts for utilities\n    │   ├── png/            # PNG icons for menu items\n    │   └── svg/            # SVG icons for menu items\n    └── shell.nss           # Main entry point for NSS configuration\n    ```\n    \n    ### Integration Points\n    - Windows Explorer shell extension\n    - Windows Registry for registration and configuration\n    - File system for icon and resource access\n    - Command line for application launching\n    - COM/Shell interfaces for advanced functionality\n    \n    ## Dependencies\n    \n    ### External Resources\n    - Icon files (PNG, SVG) for menu item visualization\n    - Batch files for extended functionality\n    - Executable paths for application launching\n    - System shell commands for file operations\n    \n    ### Development Tools\n    - Resource editors for icon management\n    - Shell extension management utilities\n    - Text editors with NSS syntax support\n    \n    ## Technical Constraints\n    \n    ### Performance Considerations\n    - Context menu load time impact\n    - Memory footprint of loaded NSS scripts\n    - Efficiency of condition evaluation for visibility rules\n    \n    ### Compatibility\n    - Windows version support (10, 11)\n    - Interaction with other shell extensions\n    - Application path resolution across different environments\n    \n    ### Security\n    - Execution permissions for launched applications\n    - Script injection prevention\n    - Validation of external resource paths\n    \n    ## Technical Roadmap\n    - Ongoing migration to latest Nilesoft Shell features\n    - Standardization of icon resources\n    - Optimization of conditional visibility expressions\n    - Enhanced script modularity and reusability\n```\n\n---\n\n#### `05_activity.md`\n\n```markdown\n    ## Distilled Highlights\n    - Current focus on populating individual menu items (_3_items directory)\n    - Need to develop groups, menus, and contexts structure\n    - Application launchers are the most extensively implemented item type\n    - System action items have basic implementation\n    \n    # 05_activity.md\n    \n    ## Current Focus Areas\n    \n    ### Item Development\n    - Extensive collection of application launcher items (`itm_app_*.nss`) already implemented\n    - Basic system action items created (`itm_action_sys_*.nss`) \n    - Need to audit existing items for consistency in naming and functionality\n    - Opportunity to standardize icon usage across similar item types\n    \n    ### Structural Components\n    - Groups (`_4_groups/`) directory needs population to organize related items\n    - Menus (`_5_menus/`) require definition to establish menu layouts and hierarchy\n    - Contexts (`_6_contexts/`) need implementation to control when menus appear\n    \n    ### Technical Infrastructure\n    - Ensure initialization files in `_1_init/` properly set up the environment\n    - Review variables in `_2_variables/` for completeness and usefulness\n    - Validate resource paths and icon references\n    \n    ## In-Progress Work\n    \n    ### Application Launcher Standardization\n    - Standardizing application launcher items with consistent:\n      - Command parameter formatting\n      - Icon selection and fallbacks\n      - Visibility conditions\n      - Naming conventions\n    \n    ### System Functionality Integration\n    - Integrating core Windows functionality through specialized items\n    - Creating consistent approach for system utility access\n    - Implementing common file operations\n    \n    ### Organization Strategy\n    - Developing logical grouping strategy for related items\n    - Planning menu hierarchy based on frequency of use and context\n    - Establishing visibility rules based on file types and locations\n    \n    ## Technical Decisions\n    \n    ### Item Implementation Pattern\n    ```nss\n    item {\n      name: \"Application Name\"\n      icon: \"path/to/icon.png\"\n      command: \"path/to/executable.exe [parameters]\"\n      \n      // Optional visibility condition\n      visibility: [condition expression]\n    }\n    ```\n    \n    ### Resource Path Strategy\n    - Using relative paths for icons and resources where possible\n    - Standardizing on Shell-compatible path syntax\n    - Planning for fallback icons when primary resources unavailable\n    \n    ### Menu Appearance Decisions\n    - Consistent separators between logical groups\n    - Icon alignment and sizing standardization\n    - Text formatting and capitalization rules\n```\n\n---\n\n#### `06_progress.md`\n\n```markdown\n    ## Distilled Highlights\n    - Directory structure and modular approach established\n    - Extensive collection of application launcher items implemented\n    - Basic system action items created\n    - Need to develop grouping and context structures\n    \n    # 06_progress.md\n    \n    ## State of the Build\n    \n    ### Completed Components\n    - ✅ Overall directory structure established for modular organization\n    - ✅ Basic initialization structure in `_1_init/` directory\n    - ✅ Extensive collection of application launcher items (~70 items)\n    - ✅ Core system action items for basic operations\n    - ✅ Resource libraries for icons and batch utilities\n    \n    ### In Progress\n    - 🔄 Variable standardization in `_2_variables/` directory\n    - 🔄 Audit of existing items for consistency and effectiveness\n    - 🔄 Documentation of item patterns and best practices\n    - 🔄 Standardization of icon usage and resource paths\n    \n    ### Not Started\n    - ⏳ Population of `_4_groups/` directory to organize items\n    - ⏳ Definition of menu structures in `_5_menus/` directory\n    - ⏳ Implementation of context rules in `_6_contexts/` directory\n    - ⏳ Comprehensive testing across different file types\n    \n    ## Milestones\n    \n    ### Milestone 1: Foundation (COMPLETED)\n    - ✓ Establish directory structure\n    - ✓ Implement basic initialization\n    - ✓ Create core application launcher items\n    - ✓ Set up resource libraries\n    \n    ### Milestone 2: Structure (IN PROGRESS)\n    - ✓ Standardize item implementation patterns\n    - ✓ Complete system action items\n    - 🔄 Document existing items and patterns\n    - ⏳ Organize items into logical groups\n    \n    ### Milestone 3: Integration (PLANNED)\n    - Define menu structures\n    - Implement context rules\n    - Create conditional visibility based on file types\n    - Optimize menu organization\n    \n    ### Milestone 4: Refinement (PLANNED)\n    - Comprehensive testing\n    - Performance optimization\n    - Icon standardization\n    - Documentation completion\n    \n    ## Current Blockers\n    - Need to develop a consistent strategy for grouping related items\n    - Lack of defined context rules for when menus should appear\n    - Incomplete documentation of existing patterns and structures\n    \n    ## Recent Updates\n    - Added multiple application launcher items for common utilities\n    - Created system action items for file operations\n    - Established resource libraries for icons and batch scripts\n    - Documented NSS patterns and implementation strategies\n    \n    ## Next Actions\n    1. Complete the audit of existing items for consistency\n    2. Develop a logical grouping strategy for items\n    3. Begin populating the `_4_groups/` directory\n    4. Create a menu structure plan for the `_5_menus/` directory\n    5. Establish basic context rules in the `_6_contexts/` directory\n```\n\n---\n\n#### `07_tasks.md`\n\n```markdown\n    ## Distilled Highlights\n    - Organize existing items into logical groups\n    - Develop menu structures based on usage patterns\n    - Implement context rules for menu visibility\n    - Create documentation for future item development\n    \n    # 07_tasks.md\n    \n    ## High Priority Tasks\n    \n    ### Group Structure Development\n    - [ ] **Create Application Groups**\n      - Create `grp_apps_dev.nss` for development tools\n      - Create `grp_apps_media.nss` for media applications\n      - Create `grp_apps_system.nss` for system utilities\n      - Create `grp_apps_office.nss` for productivity applications\n      - *Owner: System developer*\n      - *Justification: Organize the extensive list of application items into logical groups*\n    \n    - [ ] **Create Action Groups**\n      - Create `grp_actions_file.nss` for file operations\n      - Create `grp_actions_folder.nss` for folder operations\n      - Create `grp_actions_system.nss` for system operations\n      - *Owner: System developer*\n      - *Justification: Group related actions for better menu organization*\n    \n    ### Menu Structure Implementation\n    - [ ] **Design Core Menus**\n      - Create `menu_applications.nss` with application groups\n      - Create `menu_actions.nss` with action groups\n      - Create `menu_system.nss` with system utilities\n      - *Owner: System developer*\n      - *Justification: Establish logical menu structure for better user experience*\n    \n    - [ ] **Implement Cascading Menus**\n      - Design depth strategy (max 2-3 levels deep)\n      - Implement parent-child relationships\n      - *Owner: System developer*\n      - *Justification: Organize complex item collections without cluttering main menu*\n    \n    ### Context Implementation\n    - [ ] **Define File Type Contexts**\n      - Create rules for different file extensions\n      - Implement special handling for executables, documents, media\n      - *Owner: System developer*\n      - *Justification: Show relevant options based on file type*\n    \n    - [ ] **Define Location Contexts**\n      - Create rules for desktop, drives, libraries\n      - Implement special handling for system folders\n      - *Owner: System developer*\n      - *Justification: Show relevant options based on location*\n    \n    ## Medium Priority Tasks\n    \n    ### Documentation\n    - [ ] **Document Group Patterns**\n      - Create templates for new groups\n      - Document naming conventions\n      - *Owner: Documentation specialist*\n      - *Justification: Ensure consistency in future development*\n    \n    - [ ] **Document Menu Structures**\n      - Create visual hierarchy diagrams\n      - Document menu organization principles\n      - *Owner: Documentation specialist*\n      - *Justification: Provide clear guidance for menu development*\n    \n    ### Quality Assurance\n    - [ ] **Audit Existing Items**\n      - Review naming consistency\n      - Validate command parameters\n      - Check icon references\n      - *Owner: QA specialist*\n      - *Justification: Ensure existing items follow standards*\n    \n    - [ ] **Test Across Environments**\n      - Verify functionality on Windows 10\n      - Verify functionality on Windows 11\n      - Test with various file types\n      - *Owner: QA specialist*\n      - *Justification: Ensure compatibility across environments*\n    \n    ## Low Priority Tasks\n    \n    ### Optimization\n    - [ ] **Review Performance**\n      - Profile menu load times\n      - Optimize condition evaluations\n      - *Owner: Performance specialist*\n      - *Justification: Ensure menus load quickly*\n    \n    - [ ] **Icon Standardization**\n      - Create consistent icon set\n      - Implement fallback strategy\n      - *Owner: Design specialist*\n      - *Justification: Visual consistency across menus*\n    \n    ## Dependencies\n    - Group creation depends on completion of item audit\n    - Menu implementation depends on group creation\n    - Context rules depend on menu implementation\n    - Testing depends on all implementation tasks\n    \n    ## Task Assignment Strategy\n    - Focus on high-priority tasks first\n    - Complete related tasks in sequence (items → groups → menus → contexts)\n    - Document as you go to maintain knowledge base\n```\n\n---\n\n#### `08_objective.md`\n\n```markdown\n    ## Distilled Highlights\n    - Create a complete, modular context menu system for Windows Explorer\n    - Immediate focus: Group existing items and implement menu structures\n    - Success measured by efficiency, organization, and extensibility\n    - Target completion of Milestone 3 (Integration) by next development cycle\n    \n    # 08_objective.md\n    \n    ## Primary Objective\n    Create a comprehensive Windows context menu system that enhances productivity through logically organized, easily accessible commands and applications, using a modular architecture that ensures maintainability and extensibility.\n    \n    ## Success Criteria\n    The context menu system will be considered successful when it:\n    \n    1. **Provides Efficient Access**\n       - Reduces clicks needed to access common applications\n       - Speeds up file operations through contextual commands\n       - Groups related functionality in intuitive categories\n    \n    2. **Maintains Logical Organization**\n       - Presents a consistent menu structure across different contexts\n       - Limits menu depth to prevent navigation complexity\n       - Uses visual cues (icons, separators) to distinguish item types\n    \n    3. **Ensures Extensibility**\n       - Follows modular design patterns for easy additions\n       - Maintains clear separation between components\n       - Documents patterns for consistent future development\n    \n    4. **Performs Reliably**\n       - Loads quickly without noticeable delay\n       - Works consistently across Windows versions\n       - Avoids conflicts with other shell extensions\n    \n    ## Immediate Focus\n    The current development cycle focuses on:\n    \n    1. **Component Organization**\n       - Grouping existing items into logical collections\n       - Implementing menu structures to organize these groups\n       - Defining context rules to control when menus appear\n    \n    2. **System Completion**\n       - Filling gaps in the current directory structure\n       - Completing the chain from items to groups to menus to contexts\n       - Documenting component relationships and dependencies\n    \n    3. **Usability Enhancement**\n       - Standardizing visual appearance across menu items\n       - Ensuring consistent behavior across different file types\n       - Optimizing menu depth and organization for quick access\n    \n    ## Alignment with Vision\n    This objective directly supports the project's vision by:\n    - Enhancing Windows Explorer with powerful, customizable menus\n    - Standardizing menu organization for improved clarity\n    - Enabling quick access to applications and utilities\n    - Creating a scalable system that can grow without restructuring\n    \n    ## Definition of \"Done\"\n    For the current phase, \"done\" means:\n    \n    1. All existing items organized into appropriate groups\n    2. Core menu structures defined and implemented\n    3. Basic context rules established for different file types and locations\n    4. Documentation updated to reflect the current system state\n    5. At least basic testing completed across different environments\n    \n    ## Timeline Target\n    Complete current objectives (Milestone 3: Integration) within the next development cycle, setting the foundation for the refinement phase that follows.\n```\"```'\n\n{\n  \"Title\": \"[Windows Context Menu System Synthesizer]\",\n  \"Interpretation\": \"Your goal is not to summarize or analyze the input, but to synthesize and operationalize a modular, hierarchically-organized, Nilesoft Shell-driven Windows context menu system strictly according to the provided foundational principles, architectural mandates, infrastructure constraints, operational workflows, and deliverables criteria. Assume the role of system_synthesizer and produce directly actionable outputs for system implementation and documentation. Execute as:\",\n  \"Transformation\": \"`{role=system_synthesizer; input=[meta_categories:list, consolidated_directive:str]; process=[extract_mission_parameters(), formalize_architectural_patterns(), devise_type_safe_component_schemas(), sequence_milestone_driven_workflow(), structure_operational_validation_protocols(), codify_deliverable_artifacts(), synthesize_actionable_templates_and_diagrams()]; constraints=[preserve explicit category-to-directive mapping(), enforce NSS canonical patterns(), maintain directory/file naming standards(), prevent deviation from mission-aligned principles(), ensure full cross-environment compatibility(), prohibit untyped or undocumented output()]; requirements=[output modular system architecture(), explicit directory/tree diagrams(), component schema documentation(), sequenced task blueprints(), QA/security/compatibility protocols(), system-agnostic and extensible documentation(), complete type safety and pattern compliance()]; output={system_blueprint:dict}}`\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"Accelerate completion of context menu integration by sequentially organizing all existing item assets into modular functional groups, constructing explicit menu hierarchies with well-defined visibility contexts, and enforcing principled, type-safe NSS schema linkage—thereby closing the architecture from atomic item through menu-context, guaranteeing rapid extensibility, operational clarity, and full system cohesion aligned with Nilesoft Shell best practices—while embedding rigorous documentation and validation protocols to enable sustainable refinement, future-proofing, and immediate deployment readiness.\"\n}",
        }
      }
    }