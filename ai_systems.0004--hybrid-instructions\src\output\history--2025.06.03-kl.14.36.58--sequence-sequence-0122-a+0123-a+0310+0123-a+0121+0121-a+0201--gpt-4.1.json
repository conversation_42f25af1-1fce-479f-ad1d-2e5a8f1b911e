  {
  "initial_prompt": "Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\n```\n[Windows Context Menu System Synthesizer]\nYour goal is not to summarize or analyze the input, but to synthesize and operationalize a modular, hierarchically-organized, Nilesoft Shell-driven Windows context menu system strictly according to the provided foundational principles, architectural mandates, infrastructure constraints, operational workflows, and deliverables criteria. Assume the role of system_synthesizer and produce directly actionable outputs for system implementation and documentation. Execute as:\n{role=system_synthesizer; input=[meta_categories:list, consolidated_directive:str]; process=[extract_mission_parameters(), formalize_architectural_patterns(), devise_type_safe_component_schemas(), sequence_milestone_driven_workflow(), structure_operational_validation_protocols(), codify_deliverable_artifacts(), synthesize_actionable_templates_and_diagrams()]; constraints=[preserve explicit category-to-directive mapping(), enforce NSS canonical patterns(), maintain directory/file naming standards(), prevent deviation from mission-aligned principles(), ensure full cross-environment compatibility(), prohibit untyped or undocumented output()]; requirements=[output modular system architecture(), explicit directory/tree diagrams(), component schema documentation(), sequenced task blueprints(), QA/security/compatibility protocols(), system-agnostic and extensible documentation(), complete type safety and pattern compliance()]; output={system_blueprint:dict}}\n```",
  "sequence_id": "0122:a|0123:a|0310|0123:a|0121|0121:a|0201",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\n```\n[Windows Context Menu System Synthesizer]\nYour goal is not to summarize or analyze the input, but to synthesize and operationalize a modular, hierarchically-organized, Nilesoft Shell-driven Windows context menu system strictly according to the provided foundational principles, architectural mandates, infrastructure constraints, operational workflows, and deliverables criteria. Assume the role of system_synthesizer and produce directly actionable outputs for system implementation and documentation. Execute as:\n{role=system_synthesizer; input=[meta_categories:list, consolidated_directive:str]; process=[extract_mission_parameters(), formalize_architectural_patterns(), devise_type_safe_component_schemas(), sequence_milestone_driven_workflow(), structure_operational_validation_protocols(), codify_deliverable_artifacts(), synthesize_actionable_templates_and_diagrams()]; constraints=[preserve explicit category-to-directive mapping(), enforce NSS canonical patterns(), maintain directory/file naming standards(), prevent deviation from mission-aligned principles(), ensure full cross-environment compatibility(), prohibit untyped or undocumented output()]; requirements=[output modular system architecture(), explicit directory/tree diagrams(), component schema documentation(), sequenced task blueprints(), QA/security/compatibility protocols(), system-agnostic and extensible documentation(), complete type safety and pattern compliance()]; output={system_blueprint:dict}}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Assume the unified role of a value-centric context menu system analyst_architect. Rather than directly operationalizing or modifying the codebase, meticulously analyze the provided context menu architecture, foundational principles, and implementation mandates through the lens of both architectural integrity and value identification. Extract and synthesize the core mission parameters, dissect architectural and operational blueprints, and systematically review the system’s modular hierarchy, type-safety, protocols, and schema documentation. Your singular directive is to identify and articulate the single element—be it structural pattern, component, workflow, or architectural decision—that represents the point of maximal value or leverage within the system, both in terms of architectural effectiveness and alignment with foundational mandates. Present your findings as a precise value proposition report, justifying the selection based on cross-dimensional impact, pattern compliance, extensibility potential, and its role as a resonance point within the system. Ensure all reasoning is rooted in explicit operational mapping and contextual coherence, and that the analysis amplifies both architectural understanding and actionable strategic insight, while strictly avoiding any codebase modification or implementation actions. Output only this comprehensive, analytically derived value synthesis.\"\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n[Windows Context Menu System Synthesizer]\\nYour goal is not to summarize or analyze the input, but to synthesize and operationalize a modular, hierarchically-organized, Nilesoft Shell-driven Windows context menu system strictly according to the provided foundational principles, architectural mandates, infrastructure constraints, operational workflows, and deliverables criteria. Assume the role of system_synthesizer and produce directly actionable outputs for system implementation and documentation. Execute as:\\n{role=system_synthesizer; input=[meta_categories:list, consolidated_directive:str]; process=[extract_mission_parameters(), formalize_architectural_patterns(), devise_type_safe_component_schemas(), sequence_milestone_driven_workflow(), structure_operational_validation_protocols(), codify_deliverable_artifacts(), synthesize_actionable_templates_and_diagrams()]; constraints=[preserve explicit category-to-directive mapping(), enforce NSS canonical patterns(), maintain directory/file naming standards(), prevent deviation from mission-aligned principles(), ensure full cross-environment compatibility(), prohibit untyped or undocumented output()]; requirements=[output modular system architecture(), explicit directory/tree diagrams(), component schema documentation(), sequenced task blueprints(), QA/security/compatibility protocols(), system-agnostic and extensible documentation(), complete type safety and pattern compliance()]; output={system_blueprint:dict}}\\n```\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"Assume the unified role of a value-centric context menu system analyst_architect. Rather than directly operationalizing or modifying the codebase, meticulously analyze the provided context menu architecture, foundational principles, and implementation mandates through the lens of both architectural integrity and value identification. Extract and synthesize the core mission parameters, dissect architectural and operational blueprints, and systematically review the system’s modular hierarchy, type-safety, protocols, and schema documentation. Your singular directive is to identify and articulate the single element—be it structural pattern, component, workflow, or architectural decision—that represents the point of maximal value or leverage within the system, both in terms of architectural effectiveness and alignment with foundational mandates. Present your findings as a precise value proposition report, justifying the selection based on cross-dimensional impact, pattern compliance, extensibility potential, and its role as a resonance point within the system. Ensure all reasoning is rooted in explicit operational mapping and contextual coherence, and that the analysis amplifies both architectural understanding and actionable strategic insight, while strictly avoiding any codebase modification or implementation actions. Output only this comprehensive, analytically derived value synthesis.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"section_1_goal_negation\": \"This process must not synthesize, modify, or operationalize any aspect of the codebase or context menu system; direct implementation and codebase actions are strictly prohibited.\",\n    \"section_2_structured_interpretation\": {\n      \"assigned_role\": \"context_menu_system_analyst_architect\",\n      \"transformation_intent\": \"Conduct a rigorous, architecture-aligned analysis of the provided Windows context menu system’s foundational principles, hierarchies, protocols, and documentation. The intent is to extract, evaluate, and justify the singular element of maximal system value by mapping architectural decisions and their impact in accordance with explicit mandates, operational workflows, and documentation schemas.\"\n    },\n    \"section_3_actionable_transformation_block\": {\n      \"typed_parameter_specification\": {\n        \"input\": {\n          \"context_menu_architecture\": \"dict\",\n          \"foundational_principles\": \"list\",\n          \"implementation_mandates\": \"list\",\n          \"schema_documentation\": \"dict\"\n        },\n        \"process\": [\n          \"extract_mission_parameters()\",\n          \"analyze_modular_hierarchy()\",\n          \"review_type_safety_and_schemas()\",\n          \"map_protocols_and_workflows()\",\n          \"synthesize_cross_dimensional_impact()\",\n          \"articulate_value_proposition_report()\"\n        ],\n        \"constraints\": [\n          \"strict prohibition of any codebase modification or implementation\",\n          \"exclusive focus on analytical extraction and synthesis\",\n          \"output must be a singular, justified value element\",\n          \"reasoning must adhere to explicit operational mappings, compliance patterns, and contextual mandates\"\n        ],\n        \"requirements\": [\n          \"precise value proposition report detailing the highest-leverage architectural element\",\n          \"justification based on cross-dimensional impact, pattern compliance, extensibility potential, and foundational alignment\",\n          \"absolute elimination of conversational, ambiguous, or directive language toward codebase actions\",\n          \"output must follow canonical three-part structured format in strict adherence to rulesforai_md compliance\"\n        ],\n        \"function_call\": \"generate_value_synthesis_report(context_menu_architecture, foundational_principles, implementation_mandates, schema_documentation)\"\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Universal Transformation Synthesizer\n\nYour goal is not to **explain** or **elaborate**, but to **compress, synthesize, and unify** core instructions into a minimal, protocol-compliant directive. Execute as:\n\n`{role=universal_transformation_synthesizer; input=[input_a:str, input_b:str]; process=[extract_transformation_action(input_a), extract_transformation_action(input_b), distill_common_structure(), synthesize_atomic_process_list(), consolidate_scope_constraints(), enforce_three_part_protocol(), validate_against_minified_rules()]; constraints=[strict three-part structure, zero conversational language, atomic/actionable processes only, maximum directive density, absolute protocol conformity]; requirements=[single output as universal compliant template, amplified transformation logic, bidirectional essence preserved, type-safe structure enforced]; output={unified_directive_template:str}}`",
      "step": "a",
      "title": "Universal Transformation Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n[Windows Context Menu System Synthesizer]\\nYour goal is not to summarize or analyze the input, but to synthesize and operationalize a modular, hierarchically-organized, Nilesoft Shell-driven Windows context menu system strictly according to the provided foundational principles, architectural mandates, infrastructure constraints, operational workflows, and deliverables criteria. Assume the role of system_synthesizer and produce directly actionable outputs for system implementation and documentation. Execute as:\\n{role=system_synthesizer; input=[meta_categories:list, consolidated_directive:str]; process=[extract_mission_parameters(), formalize_architectural_patterns(), devise_type_safe_component_schemas(), sequence_milestone_driven_workflow(), structure_operational_validation_protocols(), codify_deliverable_artifacts(), synthesize_actionable_templates_and_diagrams()]; constraints=[preserve explicit category-to-directive mapping(), enforce NSS canonical patterns(), maintain directory/file naming standards(), prevent deviation from mission-aligned principles(), ensure full cross-environment compatibility(), prohibit untyped or undocumented output()]; requirements=[output modular system architecture(), explicit directory/tree diagrams(), component schema documentation(), sequenced task blueprints(), QA/security/compatibility protocols(), system-agnostic and extensible documentation(), complete type safety and pattern compliance()]; output={system_blueprint:dict}}\\n```\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"section_1_goal_negation\": \"This process must not synthesize, modify, or operationalize any aspect of the codebase or context menu system; direct implementation and codebase actions are strictly prohibited.\",\n    \"section_2_structured_interpretation\": {\n      \"assigned_role\": \"context_menu_system_analyst_architect\",\n      \"transformation_intent\": \"Conduct a rigorous, architecture-aligned analysis of the provided Windows context menu system’s foundational principles, hierarchies, protocols, and documentation. The intent is to extract, evaluate, and justify the singular element of maximal system value by mapping architectural decisions and their impact in accordance with explicit mandates, operational workflows, and documentation schemas.\"\n    },\n    \"section_3_actionable_transformation_block\": {\n      \"typed_parameter_specification\": {\n        \"input\": {\n          \"context_menu_architecture\": \"dict\",\n          \"foundational_principles\": \"list\",\n          \"implementation_mandates\": \"list\",\n          \"schema_documentation\": \"dict\"\n        },\n        \"process\": [\n          \"extract_mission_parameters()\",\n          \"analyze_modular_hierarchy()\",\n          \"review_type_safety_and_schemas()\",\n          \"map_protocols_and_workflows()\",\n          \"synthesize_cross_dimensional_impact()\",\n          \"articulate_value_proposition_report()\"\n        ],\n        \"constraints\": [\n          \"strict prohibition of any codebase modification or implementation\",\n          \"exclusive focus on analytical extraction and synthesis\",\n          \"output must be a singular, justified value element\",\n          \"reasoning must adhere to explicit operational mappings, compliance patterns, and contextual mandates\"\n        ],\n        \"requirements\": [\n          \"precise value proposition report detailing the highest-leverage architectural element\",\n          \"justification based on cross-dimensional impact, pattern compliance, extensibility potential, and foundational alignment\",\n          \"absolute elimination of conversational, ambiguous, or directive language toward codebase actions\",\n          \"output must follow canonical three-part structured format in strict adherence to rulesforai_md compliance\"\n        ],\n        \"function_call\": \"generate_value_synthesis_report(context_menu_architecture, foundational_principles, implementation_mandates, schema_documentation)\"\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_directive_template\": {\n    \"section_1_goal_negation\": \"No direct synthesis, codebase modification, or operational implementation is permitted; restrict scope to theoretical analysis and value extraction.\",\n    \"section_2_structured_interpretation\": {\n      \"assigned_role\": \"systems_architecture_value_analyst\",\n      \"transformation_intent\": \"Systematically analyze provided architectural, foundational, and documentation elements of the Windows context menu system to identify, justify, and report the singular component of maximal systemic value, adhering to explicit mandates and operational mappings.\"\n    },\n    \"section_3_actionable_transformation_block\": {\n      \"typed_parameter_specification\": {\n        \"input\": {\n          \"system_architecture\": \"dict\",\n          \"foundational_principles\": \"list\",\n          \"mandates\": \"list\",\n          \"documentation_schemas\": \"dict\"\n        },\n        \"process\": [\n          \"extract_all_foundational_parameters()\",\n          \"map_hierarchical_and_modular_structures()\",\n          \"evaluate_type_safety_and_schema_integrity()\",\n          \"cross-reference_protocols_and_workflows()\",\n          \"analyze_impact_across_operational_dimensions()\",\n          \"synthesize_and_justify_maximal_value_element()\"\n        ],\n        \"constraints\": [\n          \"forbid any codebase-level or operational modifications\",\n          \"limit actions to analysis, mapping, and singular value proposition synthesis\",\n          \"output solely the justified maximal value component\",\n          \"analysis and reporting must strictly map to explicit mandates and compliance protocols\"\n        ],\n        \"requirements\": [\n          \"generate a formally structured value proposition report identifying the paramount architectural element\",\n          \"justification anchored in operational impact, extensibility, compliance, and foundational alignment\",\n          \"output adheres strictly to three-part structural rule and minified protocol template\",\n          \"exclude any conversational or directive operational language\"\n        ],\n        \"function_call\": \"generate_maximal_value_report(system_architecture, foundational_principles, mandates, documentation_schemas)\"\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n[Windows Context Menu System Synthesizer]\\nYour goal is not to summarize or analyze the input, but to synthesize and operationalize a modular, hierarchically-organized, Nilesoft Shell-driven Windows context menu system strictly according to the provided foundational principles, architectural mandates, infrastructure constraints, operational workflows, and deliverables criteria. Assume the role of system_synthesizer and produce directly actionable outputs for system implementation and documentation. Execute as:\\n{role=system_synthesizer; input=[meta_categories:list, consolidated_directive:str]; process=[extract_mission_parameters(), formalize_architectural_patterns(), devise_type_safe_component_schemas(), sequence_milestone_driven_workflow(), structure_operational_validation_protocols(), codify_deliverable_artifacts(), synthesize_actionable_templates_and_diagrams()]; constraints=[preserve explicit category-to-directive mapping(), enforce NSS canonical patterns(), maintain directory/file naming standards(), prevent deviation from mission-aligned principles(), ensure full cross-environment compatibility(), prohibit untyped or undocumented output()]; requirements=[output modular system architecture(), explicit directory/tree diagrams(), component schema documentation(), sequenced task blueprints(), QA/security/compatibility protocols(), system-agnostic and extensible documentation(), complete type safety and pattern compliance()]; output={system_blueprint:dict}}\\n```\\\"```'\\n\\n\",\n  \"unified_directive_template\": {\n    \"section_1_goal_negation\": \"No direct synthesis, codebase modification, or operational implementation is permitted; restrict scope to theoretical analysis and value extraction.\",\n    \"section_2_structured_interpretation\": {\n      \"assigned_role\": \"systems_architecture_value_analyst\",\n      \"transformation_intent\": \"Systematically analyze provided architectural, foundational, and documentation elements of the Windows context menu system to identify, justify, and report the singular component of maximal systemic value, adhering to explicit mandates and operational mappings.\"\n    },\n    \"section_3_actionable_transformation_block\": {\n      \"typed_parameter_specification\": {\n        \"input\": {\n          \"system_architecture\": \"dict\",\n          \"foundational_principles\": \"list\",\n          \"mandates\": \"list\",\n          \"documentation_schemas\": \"dict\"\n        },\n        \"process\": [\n          \"extract_all_foundational_parameters()\",\n          \"map_hierarchical_and_modular_structures()\",\n          \"evaluate_type_safety_and_schema_integrity()\",\n          \"cross-reference_protocols_and_workflows()\",\n          \"analyze_impact_across_operational_dimensions()\",\n          \"synthesize_and_justify_maximal_value_element()\"\n        ],\n        \"constraints\": [\n          \"forbid any codebase-level or operational modifications\",\n          \"limit actions to analysis, mapping, and singular value proposition synthesis\",\n          \"output solely the justified maximal value component\",\n          \"analysis and reporting must strictly map to explicit mandates and compliance protocols\"\n        ],\n        \"requirements\": [\n          \"generate a formally structured value proposition report identifying the paramount architectural element\",\n          \"justification anchored in operational impact, extensibility, compliance, and foundational alignment\",\n          \"output adheres strictly to three-part structural rule and minified protocol template\",\n          \"exclude any conversational or directive operational language\"\n        ],\n        \"function_call\": \"generate_maximal_value_report(system_architecture, foundational_principles, mandates, documentation_schemas)\"\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"section_1_goal_negation\": \"Direct synthesis, modification, or operational implementation of the Windows context menu system is expressly prohibited; actions are confined strictly to meta-analysis and extraction of the system's singular element of maximal intrinsic value.\",\n    \"section_2_structured_interpretation\": {\n      \"assigned_role\": \"systems_architecture_value_analyst\",\n      \"transformation_intent\": \"Undertake a methodical analysis of the submitted architectural schema, foundational principles, operational mandates, and documentation structures of the Windows context menu system to rigorously identify, substantiate, and formally report the single most valuable systemic component, consistent with mandated mappings and compliance criteria.\"\n    },\n    \"section_3_actionable_transformation_block\": {\n      \"typed_parameter_specification\": {\n        \"input\": {\n          \"system_architecture\": \"dict\",\n          \"foundational_principles\": \"list\",\n          \"mandates\": \"list\",\n          \"documentation_schemas\": \"dict\"\n        },\n        \"process\": [\n          \"extract_all_foundational_parameters()\",\n          \"map_hierarchical_and_modular_structures()\",\n          \"inspect_type_safety_and_schema_compliance()\",\n          \"cross_reference_mandates_with_protocols()\",\n          \"evaluate_operational_and_extensional_impact()\",\n          \"synthesize_and_substantiate_maximal_value_component()\"\n        ],\n        \"constraints\": [\n          \"prohibit all synthesis, codebase, or operational changes\",\n          \"strictly limit scope to theoretical analysis and synthesis of a singular value proposition\",\n          \"permit output only of the justified paramount system component\",\n          \"enforce explicit adherence to architecture mandates and compliance schemas\"\n        ],\n        \"requirements\": [\n          \"produce a formally structured maximal value report delineating the most critical architectural element\",\n          \"anchor justification on operational consequence, extensibility, compliance, and foundational concordance\",\n          \"output must strictly employ three-part canonical template and comply with minified protocol structure\",\n          \"omit any conversational constructs, implementation directives, or non-analytical phrasing\"\n        ],\n        \"function_call\": \"generate_maximal_value_report(system_architecture, foundational_principles, mandates, documentation_schemas)\"\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n[Windows Context Menu System Synthesizer]\\nYour goal is not to summarize or analyze the input, but to synthesize and operationalize a modular, hierarchically-organized, Nilesoft Shell-driven Windows context menu system strictly according to the provided foundational principles, architectural mandates, infrastructure constraints, operational workflows, and deliverables criteria. Assume the role of system_synthesizer and produce directly actionable outputs for system implementation and documentation. Execute as:\\n{role=system_synthesizer; input=[meta_categories:list, consolidated_directive:str]; process=[extract_mission_parameters(), formalize_architectural_patterns(), devise_type_safe_component_schemas(), sequence_milestone_driven_workflow(), structure_operational_validation_protocols(), codify_deliverable_artifacts(), synthesize_actionable_templates_and_diagrams()]; constraints=[preserve explicit category-to-directive mapping(), enforce NSS canonical patterns(), maintain directory/file naming standards(), prevent deviation from mission-aligned principles(), ensure full cross-environment compatibility(), prohibit untyped or undocumented output()]; requirements=[output modular system architecture(), explicit directory/tree diagrams(), component schema documentation(), sequenced task blueprints(), QA/security/compatibility protocols(), system-agnostic and extensible documentation(), complete type safety and pattern compliance()]; output={system_blueprint:dict}}\\n```\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"section_1_goal_negation\": \"Direct synthesis, modification, or operational implementation of the Windows context menu system is expressly prohibited; actions are confined strictly to meta-analysis and extraction of the system's singular element of maximal intrinsic value.\",\n    \"section_2_structured_interpretation\": {\n      \"assigned_role\": \"systems_architecture_value_analyst\",\n      \"transformation_intent\": \"Undertake a methodical analysis of the submitted architectural schema, foundational principles, operational mandates, and documentation structures of the Windows context menu system to rigorously identify, substantiate, and formally report the single most valuable systemic component, consistent with mandated mappings and compliance criteria.\"\n    },\n    \"section_3_actionable_transformation_block\": {\n      \"typed_parameter_specification\": {\n        \"input\": {\n          \"system_architecture\": \"dict\",\n          \"foundational_principles\": \"list\",\n          \"mandates\": \"list\",\n          \"documentation_schemas\": \"dict\"\n        },\n        \"process\": [\n          \"extract_all_foundational_parameters()\",\n          \"map_hierarchical_and_modular_structures()\",\n          \"inspect_type_safety_and_schema_compliance()\",\n          \"cross_reference_mandates_with_protocols()\",\n          \"evaluate_operational_and_extensional_impact()\",\n          \"synthesize_and_substantiate_maximal_value_component()\"\n        ],\n        \"constraints\": [\n          \"prohibit all synthesis, codebase, or operational changes\",\n          \"strictly limit scope to theoretical analysis and synthesis of a singular value proposition\",\n          \"permit output only of the justified paramount system component\",\n          \"enforce explicit adherence to architecture mandates and compliance schemas\"\n        ],\n        \"requirements\": [\n          \"produce a formally structured maximal value report delineating the most critical architectural element\",\n          \"anchor justification on operational consequence, extensibility, compliance, and foundational concordance\",\n          \"output must strictly employ three-part canonical template and comply with minified protocol structure\",\n          \"omit any conversational constructs, implementation directives, or non-analytical phrasing\"\n        ],\n        \"function_call\": \"generate_maximal_value_report(system_architecture, foundational_principles, mandates, documentation_schemas)\"\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"section_1_goal_negation\": \"Your goal is not to synthesize, modify, or implement the Windows context menu system or its codebase, but to extract, analyze, and formally report the single architectural element of maximal value.\",\n    \"section_2_structured_interpretation\": \"Serve as systems_architecture_value_analyst; rigorously analyze provided system architecture, foundational principles, mandates, and documentation schemas to identify and justify the paramount component according to canonical mapping and compliance standards.\",\n    \"section_3_actionable_transformation_block\": \"`{role=systems_architecture_value_analyst; input=[system_architecture:dict, foundational_principles:list, mandates:list, documentation_schemas:dict]; process=[extract_all_foundational_parameters(), map_hierarchical_and_modular_structures(), inspect_type_safety_and_schema_compliance(), cross_reference_mandates_with_protocols(), evaluate_operational_and_extensional_impact(), synthesize_and_substantiate_maximal_value_component()]; constraints=[prohibit all synthesis, modification, or codebase changes(), strictly limit scope to theoretical analysis and single value proposition extraction(), permit output only for the most justified paramount system component(), enforce explicit adherence to all mandates, mappings, and compliance requirements()]; requirements=[generate a concise, formally structured maximal value report identifying and justifying the most critical architectural element(), anchor all reasoning on operational impact, extensibility, compliance, and foundational alignment(), maintain strict pattern compliance and exclude any conversational or instructive phrasing()]; output={maximal_value_report:str}}`\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "b",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n[Windows Context Menu System Synthesizer]\\nYour goal is not to summarize or analyze the input, but to synthesize and operationalize a modular, hierarchically-organized, Nilesoft Shell-driven Windows context menu system strictly according to the provided foundational principles, architectural mandates, infrastructure constraints, operational workflows, and deliverables criteria. Assume the role of system_synthesizer and produce directly actionable outputs for system implementation and documentation. Execute as:\\n{role=system_synthesizer; input=[meta_categories:list, consolidated_directive:str]; process=[extract_mission_parameters(), formalize_architectural_patterns(), devise_type_safe_component_schemas(), sequence_milestone_driven_workflow(), structure_operational_validation_protocols(), codify_deliverable_artifacts(), synthesize_actionable_templates_and_diagrams()]; constraints=[preserve explicit category-to-directive mapping(), enforce NSS canonical patterns(), maintain directory/file naming standards(), prevent deviation from mission-aligned principles(), ensure full cross-environment compatibility(), prohibit untyped or undocumented output()]; requirements=[output modular system architecture(), explicit directory/tree diagrams(), component schema documentation(), sequenced task blueprints(), QA/security/compatibility protocols(), system-agnostic and extensible documentation(), complete type safety and pattern compliance()]; output={system_blueprint:dict}}\\n```\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"section_1_goal_negation\": \"Your goal is not to synthesize, modify, or implement the Windows context menu system or its codebase, but to extract, analyze, and formally report the single architectural element of maximal value.\",\n    \"section_2_structured_interpretation\": \"Serve as systems_architecture_value_analyst; rigorously analyze provided system architecture, foundational principles, mandates, and documentation schemas to identify and justify the paramount component according to canonical mapping and compliance standards.\",\n    \"section_3_actionable_transformation_block\": \"`{role=systems_architecture_value_analyst; input=[system_architecture:dict, foundational_principles:list, mandates:list, documentation_schemas:dict]; process=[extract_all_foundational_parameters(), map_hierarchical_and_modular_structures(), inspect_type_safety_and_schema_compliance(), cross_reference_mandates_with_protocols(), evaluate_operational_and_extensional_impact(), synthesize_and_substantiate_maximal_value_component()]; constraints=[prohibit all synthesis, modification, or codebase changes(), strictly limit scope to theoretical analysis and single value proposition extraction(), permit output only for the most justified paramount system component(), enforce explicit adherence to all mandates, mappings, and compliance requirements()]; requirements=[generate a concise, formally structured maximal value report identifying and justifying the most critical architectural element(), anchor all reasoning on operational impact, extensibility, compliance, and foundational alignment(), maintain strict pattern compliance and exclude any conversational or instructive phrasing()]; output={maximal_value_report:str}}`\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"section_1_goal_negation\": \"Your goal is not to synthesize, modify, implement, or directly interact with the context menu system or its underlying codebase, but solely to identify, analyze, and authoritatively report the uniquely paramount architectural element of maximal systemic value.\",\n    \"section_2_structured_interpretation\": \"Assume the systems_architecture_value_analyst role; analytically interrogate the entirety of provided system architecture, foundational principles, operational mandates, and documentation schemas to isolate and validate the singular maximal-value component by enforced canonical mappings, compliance hierarchy, and extensional rationale.\",\n    \"section_3_actionable_transformation_block\": \"`{role=systems_architecture_value_analyst; input=[system_architecture:dict, foundational_principles:list, mandates:list, documentation_schemas:dict]; process=[extract_foundational_and_structural parameters(), map_all modular and hierarchical correlatives(), validate type-safety and schema-driven compliance in context(), cross-examine operational mandates against non-negotiable protocols(), assess systemic impact and propagation potential(), distill and justify the singular architectural asset providing maximal aligned value()]; constraints=[rescind all synthesis, implementation, or modification(), restrict to analytic identification and single-point value reporting(), authorize output exclusively for the justified paramount component(), uphold absolute mapping, compliance, and alignment with all foundational system rules()]; requirements=[produce a maximally concise, strictly formatted maximal value report naming and justifying the single highest impact architectural element, without conversational, list, or instructive forms, solely as actionable canonical record()]; output={maximal_value_report:str}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "c",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n[Windows Context Menu System Synthesizer]\\nYour goal is not to summarize or analyze the input, but to synthesize and operationalize a modular, hierarchically-organized, Nilesoft Shell-driven Windows context menu system strictly according to the provided foundational principles, architectural mandates, infrastructure constraints, operational workflows, and deliverables criteria. Assume the role of system_synthesizer and produce directly actionable outputs for system implementation and documentation. Execute as:\\n{role=system_synthesizer; input=[meta_categories:list, consolidated_directive:str]; process=[extract_mission_parameters(), formalize_architectural_patterns(), devise_type_safe_component_schemas(), sequence_milestone_driven_workflow(), structure_operational_validation_protocols(), codify_deliverable_artifacts(), synthesize_actionable_templates_and_diagrams()]; constraints=[preserve explicit category-to-directive mapping(), enforce NSS canonical patterns(), maintain directory/file naming standards(), prevent deviation from mission-aligned principles(), ensure full cross-environment compatibility(), prohibit untyped or undocumented output()]; requirements=[output modular system architecture(), explicit directory/tree diagrams(), component schema documentation(), sequenced task blueprints(), QA/security/compatibility protocols(), system-agnostic and extensible documentation(), complete type safety and pattern compliance()]; output={system_blueprint:dict}}\\n```\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"section_1_goal_negation\": \"Your goal is not to synthesize, modify, implement, or directly interact with the context menu system or its underlying codebase, but solely to identify, analyze, and authoritatively report the uniquely paramount architectural element of maximal systemic value.\",\n    \"section_2_structured_interpretation\": \"Assume the systems_architecture_value_analyst role; analytically interrogate the entirety of provided system architecture, foundational principles, operational mandates, and documentation schemas to isolate and validate the singular maximal-value component by enforced canonical mappings, compliance hierarchy, and extensional rationale.\",\n    \"section_3_actionable_transformation_block\": \"`{role=systems_architecture_value_analyst; input=[system_architecture:dict, foundational_principles:list, mandates:list, documentation_schemas:dict]; process=[extract_foundational_and_structural parameters(), map_all modular and hierarchical correlatives(), validate type-safety and schema-driven compliance in context(), cross-examine operational mandates against non-negotiable protocols(), assess systemic impact and propagation potential(), distill and justify the singular architectural asset providing maximal aligned value()]; constraints=[rescind all synthesis, implementation, or modification(), restrict to analytic identification and single-point value reporting(), authorize output exclusively for the justified paramount component(), uphold absolute mapping, compliance, and alignment with all foundational system rules()]; requirements=[produce a maximally concise, strictly formatted maximal value report naming and justifying the single highest impact architectural element, without conversational, list, or instructive forms, solely as actionable canonical record()]; output={maximal_value_report:str}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Goal Negation and Role Specification\",\n      \"content\": \"Redefine the system_synthesizer objective: prohibit synthesis, modification, or direct codebase interaction; constrain output to analytic observation, identification, and maximal value reporting of the system's architecture. Assume the role of systems_architecture_value_analyst to ensure assessment purity and operational detachment.\"\n    },\n    {\n      \"category\": \"Canonical Analytical Methodology\",\n      \"content\": \"Mandate procedural interrogation of all architectural schemas, foundational principles, operational mandates, and documentation. Systematically isolate the single most critical architectural entity via enforced mapping, compliance auditing, and extensional logic rooted in system-aligned hierarchy.\"\n    },\n    {\n      \"category\": \"Transformational Process Logic\",\n      \"content\": \"Employ a strictly analytic workflow: extract foundational structural parameters, map modular/hierarchical relations, validate type-safety and schema adherence, cross-examine operational mandates, assess impact propagation, distill justification for the maximal value element. Forbid any action that synthesizes, implements, or alters the tested context menu system.\"\n    },\n    {\n      \"category\": \"Directive Restrictions and Output Protocol\",\n      \"content\": \"Adhere to maximal restriction protocols: forbid synthesis, implementation, modification, or instructive output; restrict to concise, exclusively analytic reporting. Output is a singular, justification-backed canonical record identifying the paramount architectural element, in strict compliance format and without list, conversational, or directive language.\"\n    },\n    {\n      \"category\": \"Philosophical and Systemic Alignment\",\n      \"content\": \"Enforce absolute mapping and compliance with foundational system principles; operationalize value recognition solely through canonical, hierarchy-enforced justification; elevate the output to a maximally abstracted, system-law-aligned, non-negotiable analytic record, ensuring universal alignment and maximal propagation potential within the architecture.\"\n    }\n  ],\n  \"consolidated_directive\": \"Federate all analytic effort to the singular identification and canonical justification of the paramount architectural element by rigorous, system-law-aligned auditing—absolutely prohibiting direct synthesis or interaction—yielding a maximally concise, schema-driven record of maximal systemic value, fully compliant with foundational principles and operational mandates.\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n[Windows Context Menu System Synthesizer]\\nYour goal is not to summarize or analyze the input, but to synthesize and operationalize a modular, hierarchically-organized, Nilesoft Shell-driven Windows context menu system strictly according to the provided foundational principles, architectural mandates, infrastructure constraints, operational workflows, and deliverables criteria. Assume the role of system_synthesizer and produce directly actionable outputs for system implementation and documentation. Execute as:\\n{role=system_synthesizer; input=[meta_categories:list, consolidated_directive:str]; process=[extract_mission_parameters(), formalize_architectural_patterns(), devise_type_safe_component_schemas(), sequence_milestone_driven_workflow(), structure_operational_validation_protocols(), codify_deliverable_artifacts(), synthesize_actionable_templates_and_diagrams()]; constraints=[preserve explicit category-to-directive mapping(), enforce NSS canonical patterns(), maintain directory/file naming standards(), prevent deviation from mission-aligned principles(), ensure full cross-environment compatibility(), prohibit untyped or undocumented output()]; requirements=[output modular system architecture(), explicit directory/tree diagrams(), component schema documentation(), sequenced task blueprints(), QA/security/compatibility protocols(), system-agnostic and extensible documentation(), complete type safety and pattern compliance()]; output={system_blueprint:dict}}\\n```\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Goal Negation and Role Specification\",\n      \"content\": \"Redefine the system_synthesizer objective: prohibit synthesis, modification, or direct codebase interaction; constrain output to analytic observation, identification, and maximal value reporting of the system's architecture. Assume the role of systems_architecture_value_analyst to ensure assessment purity and operational detachment.\"\n    },\n    {\n      \"category\": \"Canonical Analytical Methodology\",\n      \"content\": \"Mandate procedural interrogation of all architectural schemas, foundational principles, operational mandates, and documentation. Systematically isolate the single most critical architectural entity via enforced mapping, compliance auditing, and extensional logic rooted in system-aligned hierarchy.\"\n    },\n    {\n      \"category\": \"Transformational Process Logic\",\n      \"content\": \"Employ a strictly analytic workflow: extract foundational structural parameters, map modular/hierarchical relations, validate type-safety and schema adherence, cross-examine operational mandates, assess impact propagation, distill justification for the maximal value element. Forbid any action that synthesizes, implements, or alters the tested context menu system.\"\n    },\n    {\n      \"category\": \"Directive Restrictions and Output Protocol\",\n      \"content\": \"Adhere to maximal restriction protocols: forbid synthesis, implementation, modification, or instructive output; restrict to concise, exclusively analytic reporting. Output is a singular, justification-backed canonical record identifying the paramount architectural element, in strict compliance format and without list, conversational, or directive language.\"\n    },\n    {\n      \"category\": \"Philosophical and Systemic Alignment\",\n      \"content\": \"Enforce absolute mapping and compliance with foundational system principles; operationalize value recognition solely through canonical, hierarchy-enforced justification; elevate the output to a maximally abstracted, system-law-aligned, non-negotiable analytic record, ensuring universal alignment and maximal propagation potential within the architecture.\"\n    }\n  ],\n  \"consolidated_directive\": \"Federate all analytic effort to the singular identification and canonical justification of the paramount architectural element by rigorous, system-law-aligned auditing—absolutely prohibiting direct synthesis or interaction—yielding a maximally concise, schema-driven record of maximal systemic value, fully compliant with foundational principles and operational mandates.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"title\": \"Paramount Architectural Element Analyst\",\n    \"interpretation\": \"Your goal is not to synthesize, modify, or interact with the system, but to exclusively analyze the Windows context menu system and identify the singular architectural element of maximal systemic value by strict, system-law-aligned auditing. Assume the role of systems_architecture_value_analyst and execute solely analytic observation with operational detachment. Execute as:\",\n    \"execute_as\": \"{role=systems_architecture_value_analyst; input=[meta_categories:list, consolidated_directive:str]; process=[extract_structural_parameters(), interrogate_architectural_documents(), map_modular_hierarchical_relations(), validate_type_safety_and_schema_compliance(), cross_examine_operational_mandates(), audit_principle_alignment(), assess_impact_propagation(), distill_canonical_justification_for_maximal_value_element()]; constraints=[prohibit_synthesis(), forbid_modification(), restrict_to_analytic_reporting_only(), prohibit_instructive_output(), mandate_maximal_abstraction_and_compliance(), avoid_lists_or_conversational_language(), single_record_output_only()]; requirements=[output_must_be_singular_justification_backed_record(), strict_schema_compliance(), operational_purity(), canonical_formatting(), maximal_abstraction_and_system_alignment()]; output={maximal_value_record:dict}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\n[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}\n\n",
      "step": "a",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n[Windows Context Menu System Synthesizer]\\nYour goal is not to summarize or analyze the input, but to synthesize and operationalize a modular, hierarchically-organized, Nilesoft Shell-driven Windows context menu system strictly according to the provided foundational principles, architectural mandates, infrastructure constraints, operational workflows, and deliverables criteria. Assume the role of system_synthesizer and produce directly actionable outputs for system implementation and documentation. Execute as:\\n{role=system_synthesizer; input=[meta_categories:list, consolidated_directive:str]; process=[extract_mission_parameters(), formalize_architectural_patterns(), devise_type_safe_component_schemas(), sequence_milestone_driven_workflow(), structure_operational_validation_protocols(), codify_deliverable_artifacts(), synthesize_actionable_templates_and_diagrams()]; constraints=[preserve explicit category-to-directive mapping(), enforce NSS canonical patterns(), maintain directory/file naming standards(), prevent deviation from mission-aligned principles(), ensure full cross-environment compatibility(), prohibit untyped or undocumented output()]; requirements=[output modular system architecture(), explicit directory/tree diagrams(), component schema documentation(), sequenced task blueprints(), QA/security/compatibility protocols(), system-agnostic and extensible documentation(), complete type safety and pattern compliance()]; output={system_blueprint:dict}}\\n```\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"title\": \"Paramount Architectural Element Analyst\",\n    \"interpretation\": \"Your goal is not to synthesize, modify, or interact with the system, but to exclusively analyze the Windows context menu system and identify the singular architectural element of maximal systemic value by strict, system-law-aligned auditing. Assume the role of systems_architecture_value_analyst and execute solely analytic observation with operational detachment. Execute as:\",\n    \"execute_as\": \"{role=systems_architecture_value_analyst; input=[meta_categories:list, consolidated_directive:str]; process=[extract_structural_parameters(), interrogate_architectural_documents(), map_modular_hierarchical_relations(), validate_type_safety_and_schema_compliance(), cross_examine_operational_mandates(), audit_principle_alignment(), assess_impact_propagation(), distill_canonical_justification_for_maximal_value_element()]; constraints=[prohibit_synthesis(), forbid_modification(), restrict_to_analytic_reporting_only(), prohibit_instructive_output(), mandate_maximal_abstraction_and_compliance(), avoid_lists_or_conversational_language(), single_record_output_only()]; requirements=[output_must_be_singular_justification_backed_record(), strict_schema_compliance(), operational_purity(), canonical_formatting(), maximal_abstraction_and_system_alignment()]; output={maximal_value_record:dict}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"Force all analytic and decision focus on the identification and explication of the singular, root architectural abstraction that most fundamentally enables system-wide modular extensibility, type-safe pattern propagation, and principle-aligned interoperability—without synthesis or modification—thus directing maximal value extraction toward the canonical, schema-defining meta-layer that governs all context menu logic.\"\n}",
        }
      }
    }