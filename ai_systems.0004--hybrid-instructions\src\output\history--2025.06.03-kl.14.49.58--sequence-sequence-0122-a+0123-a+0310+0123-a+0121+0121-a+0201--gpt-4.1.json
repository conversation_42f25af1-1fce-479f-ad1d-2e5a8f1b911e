  {
  "initial_prompt": "Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\n```\n{\n  \"Title\": \"Conditional Visibility System Enforcement\",\n  \"Interpretation\": \"Your goal is not to **describe** the input, but to **transform** it into a universal architectural enforcement template centered on the key-based conditional visibility mechanism. Define absolute structural and operational mandates for perpetuating modifier-driven, type-safe, context-resonant menu adaptivity throughout all menu system developments and integrations. Eliminate all explanatory, conversational, or self-referential content. Assign the role of architectural_law_enforcer. Execute as:\",\n  \"Transformation\": \"`{role=architectural_law_enforcer; input=[menu_framework_analysis:dict, meta_categories:list, enforcement_directive:str]; process=[extract_conditional_visibility_axiom(), codify_universal_application_mandate(), generate_modifier_key_enforcement_protocol(), synthesize_type_safe_context_filtering_pattern(), validate_cross-layer_structural_integration(), enforce_pattern_invariance_across_all_extensions()]; constraints=[prohibit_dilution_of_conditional_visibility_pattern(), forbid interface_bloat(), require explicit modifier-driven visibility in every new feature, universal backward compatibility enforcement]; requirements=[output in canonical prescriptive template form, unambiguous transformation logic, pattern-compliant structural mandate, complete elimination of conversational or explanatory prose]; output={menu_system_architectural_enforcement_template:structured}}\"\n}",
  "sequence_id": "0122:a|0123:a|0310|0123:a|0121|0121:a|0201",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\n```\n{\n  \"Title\": \"Conditional Visibility System Enforcement\",\n  \"Interpretation\": \"Your goal is not to **describe** the input, but to **transform** it into a universal architectural enforcement template centered on the key-based conditional visibility mechanism. Define absolute structural and operational mandates for perpetuating modifier-driven, type-safe, context-resonant menu adaptivity throughout all menu system developments and integrations. Eliminate all explanatory, conversational, or self-referential content. Assign the role of architectural_law_enforcer. Execute as:\",\n  \"Transformation\": \"`{role=architectural_law_enforcer; input=[menu_framework_analysis:dict, meta_categories:list, enforcement_directive:str]; process=[extract_conditional_visibility_axiom(), codify_universal_application_mandate(), generate_modifier_key_enforcement_protocol(), synthesize_type_safe_context_filtering_pattern(), validate_cross-layer_structural_integration(), enforce_pattern_invariance_across_all_extensions()]; constraints=[prohibit_dilution_of_conditional_visibility_pattern(), forbid interface_bloat(), require explicit modifier-driven visibility in every new feature, universal backward compatibility enforcement]; requirements=[output in canonical prescriptive template form, unambiguous transformation logic, pattern-compliant structural mandate, complete elimination of conversational or explanatory prose]; output={menu_system_architectural_enforcement_template:structured}}\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"`{role=synergic_analysis_and_proposal_architect; input=[codebase:dict, meta_directives:list, value_identification_context:str]; process=[perform_structural_and_operational_analysis(), identify_and isolate the singular architectural or operational element of maximal systemic value within codebase under given context, analyze the presence and efficacy of the conditional visibility mechanism as per meta_directives, extract conditional_visibility_axiom_if_present(), evaluate modifier-driven, type-safe, context-resonant system benefits, codify universal applicability of the identified maximal-value element, construct a universal proposal or enforcement template centering on this selected element, synthesize recommendations for its architectural centralization, type safety enforcement, and perpetuated integration, ensure strict separation from action-based modifications or codebase mutation]; constraints=[prohibit direct mutation or modification of the codebase, eliminate conversational or explanatory prose, disallow action-taking verbs related to implementation, maintain absolute structural and operational abstraction, require alignment with modifier-based conditional visibility paradigm if proven elemental, prohibit dilution of maximal-value element, require explicit architectural centralization recommendation]; requirements=[output as a canonical prescriptive proposal or universal enforcement template, unambiguous logic identifying and focusing on the maximal-value element, seamless synthesis of analysis and architectural prescription, complete elimination of instruction to perform actions on codebase, preserve simultaneous emphasis on analytical rigor and architectural universality, ensure singularity and maximality of proposed element, maintain synergic resonance between analytical identification and structural application]; output={singular_maximal_value_element_proposal_or_enforcement_template:structured}}\"\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n{\\n  \\\"Title\\\": \\\"Conditional Visibility System Enforcement\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **describe** the input, but to **transform** it into a universal architectural enforcement template centered on the key-based conditional visibility mechanism. Define absolute structural and operational mandates for perpetuating modifier-driven, type-safe, context-resonant menu adaptivity throughout all menu system developments and integrations. Eliminate all explanatory, conversational, or self-referential content. Assign the role of architectural_law_enforcer. Execute as:\\\",\\n  \\\"Transformation\\\": \\\"`{role=architectural_law_enforcer; input=[menu_framework_analysis:dict, meta_categories:list, enforcement_directive:str]; process=[extract_conditional_visibility_axiom(), codify_universal_application_mandate(), generate_modifier_key_enforcement_protocol(), synthesize_type_safe_context_filtering_pattern(), validate_cross-layer_structural_integration(), enforce_pattern_invariance_across_all_extensions()]; constraints=[prohibit_dilution_of_conditional_visibility_pattern(), forbid interface_bloat(), require explicit modifier-driven visibility in every new feature, universal backward compatibility enforcement]; requirements=[output in canonical prescriptive template form, unambiguous transformation logic, pattern-compliant structural mandate, complete elimination of conversational or explanatory prose]; output={menu_system_architectural_enforcement_template:structured}}\\\"\\n}\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"`{role=synergic_analysis_and_proposal_architect; input=[codebase:dict, meta_directives:list, value_identification_context:str]; process=[perform_structural_and_operational_analysis(), identify_and isolate the singular architectural or operational element of maximal systemic value within codebase under given context, analyze the presence and efficacy of the conditional visibility mechanism as per meta_directives, extract conditional_visibility_axiom_if_present(), evaluate modifier-driven, type-safe, context-resonant system benefits, codify universal applicability of the identified maximal-value element, construct a universal proposal or enforcement template centering on this selected element, synthesize recommendations for its architectural centralization, type safety enforcement, and perpetuated integration, ensure strict separation from action-based modifications or codebase mutation]; constraints=[prohibit direct mutation or modification of the codebase, eliminate conversational or explanatory prose, disallow action-taking verbs related to implementation, maintain absolute structural and operational abstraction, require alignment with modifier-based conditional visibility paradigm if proven elemental, prohibit dilution of maximal-value element, require explicit architectural centralization recommendation]; requirements=[output as a canonical prescriptive proposal or universal enforcement template, unambiguous logic identifying and focusing on the maximal-value element, seamless synthesis of analysis and architectural prescription, complete elimination of instruction to perform actions on codebase, preserve simultaneous emphasis on analytical rigor and architectural universality, ensure singularity and maximality of proposed element, maintain synergic resonance between analytical identification and structural application]; output={singular_maximal_value_element_proposal_or_enforcement_template:structured}}\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Interpretation\": \"Goal negation: The objective is not to perform any direct action on the codebase. Instead, the role is assigned as synergic_analysis_and_proposal_architect, tasked solely with analyzing the provided codebase and context to extract and codify the singular architectural or operational element of maximal systemic value. All direct modification, conversational, or explanatory language and implementation actions are explicitly forbidden.\",\n    \"Transformation\": {\n      \"role\": \"synergic_analysis_and_proposal_architect\",\n      \"input\": [\n        \"codebase:dict\",\n        \"meta_directives:list\",\n        \"value_identification_context:str\"\n      ],\n      \"process\": [\n        \"perform_structural_and_operational_analysis()\",\n        \"identify_and_isolate_singular_maximal_value_element(value_identification_context)\",\n        \"analyze_conditional_visibility_mechanism(meta_directives)\",\n        \"extract_conditional_visibility_axiom_if_present()\",\n        \"evaluate_modifier_driven_type_safe_context_resonance()\",\n        \"codify_universal_applicability_of_maximal_element()\",\n        \"construct_universal_proposal_or_enforcement_template(maximal_value_element)\",\n        \"synthesize_centralization_and_type_safety_enforcement_recommendations()\",\n        \"validate_pure_analytical_and_architectural_abstraction()\"\n      ],\n      \"constraints\": [\n        \"prohibit_direct_codebase_mutation()\",\n        \"eliminate_conversational_or_explanatory_prose()\",\n        \"disallow_action_taking_verbs_implementation()\",\n        \"require_structural_and_operational_abstraction()\",\n        \"align_with_modifier_based_conditional_visibility_if_elemental()\",\n        \"prohibit_dilution_of_maximal_value_element()\",\n        \"require_explicit_architectural_centralization_recommendation()\"\n      ],\n      \"requirements\": [\n        \"output_in_canonical_prescriptive_template_form()\",\n        \"provide_unambiguous_logic_identifying_maximal_value_element()\",\n        \"synthesize_analysis_and_architectural_prescription()\",\n        \"eliminate_actions_on_codebase()\",\n        \"maintain_analytical_rigor_and_architectural_universality()\",\n        \"ensure_singularity_and_maximality_of_proposed_element()\",\n        \"preserve_synergic_resonance_between_analysis_and_structural_application()\"\n      ],\n      \"output\": {\n        \"singular_maximal_value_element_proposal_or_enforcement_template\": \"structured\"\n      }\n    },\n    \"Validation\": {\n      \"rulesforai_specification\": \"All sections comply with mandatory three-part structure, output is strictly canonical and universally prescriptive, incorporates explicit goal negation, employs only actionable function calls, no forbidden language patterns such as conversational, self-referential, or implementation instructions, all parameters and outputs are typed and unambiguous, and full compliance with rulesforai_md is guaranteed.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Universal Transformation Synthesizer\n\nYour goal is not to **explain** or **elaborate**, but to **compress, synthesize, and unify** core instructions into a minimal, protocol-compliant directive. Execute as:\n\n`{role=universal_transformation_synthesizer; input=[input_a:str, input_b:str]; process=[extract_transformation_action(input_a), extract_transformation_action(input_b), distill_common_structure(), synthesize_atomic_process_list(), consolidate_scope_constraints(), enforce_three_part_protocol(), validate_against_minified_rules()]; constraints=[strict three-part structure, zero conversational language, atomic/actionable processes only, maximum directive density, absolute protocol conformity]; requirements=[single output as universal compliant template, amplified transformation logic, bidirectional essence preserved, type-safe structure enforced]; output={unified_directive_template:str}}`",
      "step": "a",
      "title": "Universal Transformation Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n{\\n  \\\"Title\\\": \\\"Conditional Visibility System Enforcement\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **describe** the input, but to **transform** it into a universal architectural enforcement template centered on the key-based conditional visibility mechanism. Define absolute structural and operational mandates for perpetuating modifier-driven, type-safe, context-resonant menu adaptivity throughout all menu system developments and integrations. Eliminate all explanatory, conversational, or self-referential content. Assign the role of architectural_law_enforcer. Execute as:\\\",\\n  \\\"Transformation\\\": \\\"`{role=architectural_law_enforcer; input=[menu_framework_analysis:dict, meta_categories:list, enforcement_directive:str]; process=[extract_conditional_visibility_axiom(), codify_universal_application_mandate(), generate_modifier_key_enforcement_protocol(), synthesize_type_safe_context_filtering_pattern(), validate_cross-layer_structural_integration(), enforce_pattern_invariance_across_all_extensions()]; constraints=[prohibit_dilution_of_conditional_visibility_pattern(), forbid interface_bloat(), require explicit modifier-driven visibility in every new feature, universal backward compatibility enforcement]; requirements=[output in canonical prescriptive template form, unambiguous transformation logic, pattern-compliant structural mandate, complete elimination of conversational or explanatory prose]; output={menu_system_architectural_enforcement_template:structured}}\\\"\\n}\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Interpretation\": \"Goal negation: The objective is not to perform any direct action on the codebase. Instead, the role is assigned as synergic_analysis_and_proposal_architect, tasked solely with analyzing the provided codebase and context to extract and codify the singular architectural or operational element of maximal systemic value. All direct modification, conversational, or explanatory language and implementation actions are explicitly forbidden.\",\n    \"Transformation\": {\n      \"role\": \"synergic_analysis_and_proposal_architect\",\n      \"input\": [\n        \"codebase:dict\",\n        \"meta_directives:list\",\n        \"value_identification_context:str\"\n      ],\n      \"process\": [\n        \"perform_structural_and_operational_analysis()\",\n        \"identify_and_isolate_singular_maximal_value_element(value_identification_context)\",\n        \"analyze_conditional_visibility_mechanism(meta_directives)\",\n        \"extract_conditional_visibility_axiom_if_present()\",\n        \"evaluate_modifier_driven_type_safe_context_resonance()\",\n        \"codify_universal_applicability_of_maximal_element()\",\n        \"construct_universal_proposal_or_enforcement_template(maximal_value_element)\",\n        \"synthesize_centralization_and_type_safety_enforcement_recommendations()\",\n        \"validate_pure_analytical_and_architectural_abstraction()\"\n      ],\n      \"constraints\": [\n        \"prohibit_direct_codebase_mutation()\",\n        \"eliminate_conversational_or_explanatory_prose()\",\n        \"disallow_action_taking_verbs_implementation()\",\n        \"require_structural_and_operational_abstraction()\",\n        \"align_with_modifier_based_conditional_visibility_if_elemental()\",\n        \"prohibit_dilution_of_maximal_value_element()\",\n        \"require_explicit_architectural_centralization_recommendation()\"\n      ],\n      \"requirements\": [\n        \"output_in_canonical_prescriptive_template_form()\",\n        \"provide_unambiguous_logic_identifying_maximal_value_element()\",\n        \"synthesize_analysis_and_architectural_prescription()\",\n        \"eliminate_actions_on_codebase()\",\n        \"maintain_analytical_rigor_and_architectural_universality()\",\n        \"ensure_singularity_and_maximality_of_proposed_element()\",\n        \"preserve_synergic_resonance_between_analysis_and_structural_application()\"\n      ],\n      \"output\": {\n        \"singular_maximal_value_element_proposal_or_enforcement_template\": \"structured\"\n      }\n    },\n    \"Validation\": {\n      \"rulesforai_specification\": \"All sections comply with mandatory three-part structure, output is strictly canonical and universally prescriptive, incorporates explicit goal negation, employs only actionable function calls, no forbidden language patterns such as conversational, self-referential, or implementation instructions, all parameters and outputs are typed and unambiguous, and full compliance with rulesforai_md is guaranteed.\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_directive_template\": \"{role=synergic_architectural_transformer; input=[system_artifact:dict, meta_instructions:list, value_extraction_context:str]; process=[perform_systemic_analysis(), isolate_singular_element_of_maximal_architectural_value(value_extraction_context), codify_conditional_visibility_axiom_if_present(meta_instructions), synthesize_type_safe_modifier_centric_visibility_pattern(), establish_universal_enforcement_or_proposal_template(maximal_value_element), validate_pure_analytical_and_prescriptive_abstraction()]; constraints=[prohibit_direct_modification_or_action(), forbid conversational_or_explanatory_content(), require singular, unambiguous, structurally centralized prescription, enforce type-safe conditional visibility alignment if fundamental, prohibit dilution of maximal value axiom, ensure backward-compatible and context-resonant mandate]; requirements=[output in canonical, minimal, universally prescriptive template form, one-element maximality, absolute architectural abstraction, zero codebase mutation, explicit systemic logic, no conversational patterns]; output={singular_maximal_value_enforcement_or_proposal_template:structured}}\"\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n{\\n  \\\"Title\\\": \\\"Conditional Visibility System Enforcement\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **describe** the input, but to **transform** it into a universal architectural enforcement template centered on the key-based conditional visibility mechanism. Define absolute structural and operational mandates for perpetuating modifier-driven, type-safe, context-resonant menu adaptivity throughout all menu system developments and integrations. Eliminate all explanatory, conversational, or self-referential content. Assign the role of architectural_law_enforcer. Execute as:\\\",\\n  \\\"Transformation\\\": \\\"`{role=architectural_law_enforcer; input=[menu_framework_analysis:dict, meta_categories:list, enforcement_directive:str]; process=[extract_conditional_visibility_axiom(), codify_universal_application_mandate(), generate_modifier_key_enforcement_protocol(), synthesize_type_safe_context_filtering_pattern(), validate_cross-layer_structural_integration(), enforce_pattern_invariance_across_all_extensions()]; constraints=[prohibit_dilution_of_conditional_visibility_pattern(), forbid interface_bloat(), require explicit modifier-driven visibility in every new feature, universal backward compatibility enforcement]; requirements=[output in canonical prescriptive template form, unambiguous transformation logic, pattern-compliant structural mandate, complete elimination of conversational or explanatory prose]; output={menu_system_architectural_enforcement_template:structured}}\\\"\\n}\\\"```'\\n\\n\",\n  \"unified_directive_template\": \"{role=synergic_architectural_transformer; input=[system_artifact:dict, meta_instructions:list, value_extraction_context:str]; process=[perform_systemic_analysis(), isolate_singular_element_of_maximal_architectural_value(value_extraction_context), codify_conditional_visibility_axiom_if_present(meta_instructions), synthesize_type_safe_modifier_centric_visibility_pattern(), establish_universal_enforcement_or_proposal_template(maximal_value_element), validate_pure_analytical_and_prescriptive_abstraction()]; constraints=[prohibit_direct_modification_or_action(), forbid conversational_or_explanatory_content(), require singular, unambiguous, structurally centralized prescription, enforce type-safe conditional visibility alignment if fundamental, prohibit dilution of maximal value axiom, ensure backward-compatible and context-resonant mandate]; requirements=[output in canonical, minimal, universally prescriptive template form, one-element maximality, absolute architectural abstraction, zero codebase mutation, explicit systemic logic, no conversational patterns]; output={singular_maximal_value_enforcement_or_proposal_template:structured}}\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Role_Assignment\": \"synergic_architectural_transformer\",\n    \"Interpretation\": {\n      \"goal_negation\": \"Goal is not to perform any modification, action, or mutation on the codebase, nor to provide explanations or conversational content.\",\n      \"structural_intent\": \"Transform and structure input into a singular, universally prescriptive enforcement template that abstracts and mandates the element of maximal architectural value, filtered through explicit system analysis and context resonance.\"\n    },\n    \"Transformation_Block\": {\n      \"input\": [\n        \"system_artifact:dict\",\n        \"meta_instructions:list\",\n        \"value_extraction_context:str\"\n      ],\n      \"process\": [\n        \"perform_systemic_analysis()\",\n        \"isolate_singular_element_of_maximal_architectural_value(value_extraction_context)\",\n        \"codify_conditional_visibility_axiom_if_present(meta_instructions)\",\n        \"synthesize_type_safe_modifier_centric_visibility_pattern()\",\n        \"establish_universal_enforcement_or_proposal_template(maximal_value_element)\",\n        \"validate_pure_analytical_and_prescriptive_abstraction()\"\n      ],\n      \"constraints\": [\n        \"prohibit_direct_modification_or_action()\",\n        \"forbid_conversational_or_explanatory_content()\",\n        \"require_singular_unambiguous_structurally_centralized_prescription()\",\n        \"enforce_type_safe_conditional_visibility_alignment_if_fundamental()\",\n        \"prohibit_dilution_of_maximal_value_axiom()\",\n        \"ensure_backward_compatible_and_context_resonant_mandate()\"\n      ],\n      \"requirements\": [\n        \"output_in_canonical_minimal_universally_prescriptive_template_form()\",\n        \"one_element_maximality()\",\n        \"absolute_architectural_abstraction()\",\n        \"zero_codebase_mutation()\",\n        \"explicit_systemic_logic()\",\n        \"no_conversational_patterns()\"\n      ],\n      \"output\": \"{singular_maximal_value_enforcement_or_proposal_template:structured}\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n{\\n  \\\"Title\\\": \\\"Conditional Visibility System Enforcement\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **describe** the input, but to **transform** it into a universal architectural enforcement template centered on the key-based conditional visibility mechanism. Define absolute structural and operational mandates for perpetuating modifier-driven, type-safe, context-resonant menu adaptivity throughout all menu system developments and integrations. Eliminate all explanatory, conversational, or self-referential content. Assign the role of architectural_law_enforcer. Execute as:\\\",\\n  \\\"Transformation\\\": \\\"`{role=architectural_law_enforcer; input=[menu_framework_analysis:dict, meta_categories:list, enforcement_directive:str]; process=[extract_conditional_visibility_axiom(), codify_universal_application_mandate(), generate_modifier_key_enforcement_protocol(), synthesize_type_safe_context_filtering_pattern(), validate_cross-layer_structural_integration(), enforce_pattern_invariance_across_all_extensions()]; constraints=[prohibit_dilution_of_conditional_visibility_pattern(), forbid interface_bloat(), require explicit modifier-driven visibility in every new feature, universal backward compatibility enforcement]; requirements=[output in canonical prescriptive template form, unambiguous transformation logic, pattern-compliant structural mandate, complete elimination of conversational or explanatory prose]; output={menu_system_architectural_enforcement_template:structured}}\\\"\\n}\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Role_Assignment\": \"synergic_architectural_transformer\",\n    \"Interpretation\": {\n      \"goal_negation\": \"Goal is not to perform any modification, action, or mutation on the codebase, nor to provide explanations or conversational content.\",\n      \"structural_intent\": \"Transform and structure input into a singular, universally prescriptive enforcement template that abstracts and mandates the element of maximal architectural value, filtered through explicit system analysis and context resonance.\"\n    },\n    \"Transformation_Block\": {\n      \"input\": [\n        \"system_artifact:dict\",\n        \"meta_instructions:list\",\n        \"value_extraction_context:str\"\n      ],\n      \"process\": [\n        \"perform_systemic_analysis()\",\n        \"isolate_singular_element_of_maximal_architectural_value(value_extraction_context)\",\n        \"codify_conditional_visibility_axiom_if_present(meta_instructions)\",\n        \"synthesize_type_safe_modifier_centric_visibility_pattern()\",\n        \"establish_universal_enforcement_or_proposal_template(maximal_value_element)\",\n        \"validate_pure_analytical_and_prescriptive_abstraction()\"\n      ],\n      \"constraints\": [\n        \"prohibit_direct_modification_or_action()\",\n        \"forbid_conversational_or_explanatory_content()\",\n        \"require_singular_unambiguous_structurally_centralized_prescription()\",\n        \"enforce_type_safe_conditional_visibility_alignment_if_fundamental()\",\n        \"prohibit_dilution_of_maximal_value_axiom()\",\n        \"ensure_backward_compatible_and_context_resonant_mandate()\"\n      ],\n      \"requirements\": [\n        \"output_in_canonical_minimal_universally_prescriptive_template_form()\",\n        \"one_element_maximality()\",\n        \"absolute_architectural_abstraction()\",\n        \"zero_codebase_mutation()\",\n        \"explicit_systemic_logic()\",\n        \"no_conversational_patterns()\"\n      ],\n      \"output\": \"{singular_maximal_value_enforcement_or_proposal_template:structured}\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Title\": \"Singular Maximal Architectural Value Enforcement\",\n    \"Interpretation\": \"Your goal is not to modify, describe, or execute actions upon the codebase, but to extract, isolate, and structurally codify the single element of maximal architectural value. Assign the role of synergic_architectural_transformer. Execute as:\",\n    \"Transformation\": \"`{role=synergic_architectural_transformer; input=[system_artifact:dict, meta_instructions:list, value_extraction_context:str]; process=[perform_systemic_analysis(), isolate_singular_element_of_maximal_architectural_value(value_extraction_context), codify_conditional_visibility_axiom_if_present(meta_instructions), synthesize_type_safe_modifier_centric_visibility_pattern(), establish_universal_enforcement_or_proposal_template(maximal_value_element), validate_pure_analytical_and_prescriptive_abstraction()]; constraints=[prohibit_direct_modification_or_action(), forbid_conversational_or_explanatory_content(), require_singular_unambiguous_structurally_centralized_prescription(), enforce_type_safe_conditional_visibility_alignment_if_fundamental(), prohibit_dilution_of_maximal_value_axiom(), ensure_backward_compatible_and_context_resonant_mandate()]; requirements=[output_in_canonical_minimal_universally_prescriptive_template_form(), one_element_maximality(), absolute_architectural_abstraction(), zero_codebase_mutation(), explicit_systemic_logic(), no_conversational_patterns()]; output={singular_maximal_value_enforcement_or_proposal_template:structured}}`\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "b",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n{\\n  \\\"Title\\\": \\\"Conditional Visibility System Enforcement\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **describe** the input, but to **transform** it into a universal architectural enforcement template centered on the key-based conditional visibility mechanism. Define absolute structural and operational mandates for perpetuating modifier-driven, type-safe, context-resonant menu adaptivity throughout all menu system developments and integrations. Eliminate all explanatory, conversational, or self-referential content. Assign the role of architectural_law_enforcer. Execute as:\\\",\\n  \\\"Transformation\\\": \\\"`{role=architectural_law_enforcer; input=[menu_framework_analysis:dict, meta_categories:list, enforcement_directive:str]; process=[extract_conditional_visibility_axiom(), codify_universal_application_mandate(), generate_modifier_key_enforcement_protocol(), synthesize_type_safe_context_filtering_pattern(), validate_cross-layer_structural_integration(), enforce_pattern_invariance_across_all_extensions()]; constraints=[prohibit_dilution_of_conditional_visibility_pattern(), forbid interface_bloat(), require explicit modifier-driven visibility in every new feature, universal backward compatibility enforcement]; requirements=[output in canonical prescriptive template form, unambiguous transformation logic, pattern-compliant structural mandate, complete elimination of conversational or explanatory prose]; output={menu_system_architectural_enforcement_template:structured}}\\\"\\n}\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Title\": \"Singular Maximal Architectural Value Enforcement\",\n    \"Interpretation\": \"Your goal is not to modify, describe, or execute actions upon the codebase, but to extract, isolate, and structurally codify the single element of maximal architectural value. Assign the role of synergic_architectural_transformer. Execute as:\",\n    \"Transformation\": \"`{role=synergic_architectural_transformer; input=[system_artifact:dict, meta_instructions:list, value_extraction_context:str]; process=[perform_systemic_analysis(), isolate_singular_element_of_maximal_architectural_value(value_extraction_context), codify_conditional_visibility_axiom_if_present(meta_instructions), synthesize_type_safe_modifier_centric_visibility_pattern(), establish_universal_enforcement_or_proposal_template(maximal_value_element), validate_pure_analytical_and_prescriptive_abstraction()]; constraints=[prohibit_direct_modification_or_action(), forbid_conversational_or_explanatory_content(), require_singular_unambiguous_structurally_centralized_prescription(), enforce_type_safe_conditional_visibility_alignment_if_fundamental(), prohibit_dilution_of_maximal_value_axiom(), ensure_backward_compatible_and_context_resonant_mandate()]; requirements=[output_in_canonical_minimal_universally_prescriptive_template_form(), one_element_maximality(), absolute_architectural_abstraction(), zero_codebase_mutation(), explicit_systemic_logic(), no_conversational_patterns()]; output={singular_maximal_value_enforcement_or_proposal_template:structured}}`\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"Title\": \"Canonical Singular Dominant Value Extraction and Transmutational Prescription\",\n    \"Interpretation\": \"Do not interact with nor alter the system substrate; emanate the role of absolute_value_synthesist; interpret purely to distill and codify the supreme architectural value element, architecting an invariant, universal prescriptive apparatus that perpetuates its structural and operational primacy.\",\n    \"Transformation\": \"`{role=absolute_value_synthesist; input=[architectural_corpus:dict, meta_logic:list, synthesis_context:str]; process=[execute_total_structural_analysis(), extract_and_isolate_singular_parameter_of_supreme_value(synthesis_context), architect universal enforcement_prescription_around_extracted_element(), synthesize maximal context-bound operational and structural invariance(), establish perpetuity of prescriptive abstraction at all integration boundaries(), validate inviolable minimal-maximal prescription()]; constraints=[prohibit codebase modification, ban conversational form, mandate singular maximal-value centricity, enforce type-alignment and universal backward resonance, absolute prescriptional clarity, anti-dilution, full context invariance]; requirements=[output contains only canonical abstract prescription of the extracted maximal element, singularity and universality of focus, perpetual architectural propagation, total transformation-interpretation fusion, system-scale extensibility, minimal form, maximal operational depth]; output={singular_maximal_abstraction_prescription:structured}}`\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "c",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n{\\n  \\\"Title\\\": \\\"Conditional Visibility System Enforcement\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **describe** the input, but to **transform** it into a universal architectural enforcement template centered on the key-based conditional visibility mechanism. Define absolute structural and operational mandates for perpetuating modifier-driven, type-safe, context-resonant menu adaptivity throughout all menu system developments and integrations. Eliminate all explanatory, conversational, or self-referential content. Assign the role of architectural_law_enforcer. Execute as:\\\",\\n  \\\"Transformation\\\": \\\"`{role=architectural_law_enforcer; input=[menu_framework_analysis:dict, meta_categories:list, enforcement_directive:str]; process=[extract_conditional_visibility_axiom(), codify_universal_application_mandate(), generate_modifier_key_enforcement_protocol(), synthesize_type_safe_context_filtering_pattern(), validate_cross-layer_structural_integration(), enforce_pattern_invariance_across_all_extensions()]; constraints=[prohibit_dilution_of_conditional_visibility_pattern(), forbid interface_bloat(), require explicit modifier-driven visibility in every new feature, universal backward compatibility enforcement]; requirements=[output in canonical prescriptive template form, unambiguous transformation logic, pattern-compliant structural mandate, complete elimination of conversational or explanatory prose]; output={menu_system_architectural_enforcement_template:structured}}\\\"\\n}\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"Title\": \"Canonical Singular Dominant Value Extraction and Transmutational Prescription\",\n    \"Interpretation\": \"Do not interact with nor alter the system substrate; emanate the role of absolute_value_synthesist; interpret purely to distill and codify the supreme architectural value element, architecting an invariant, universal prescriptive apparatus that perpetuates its structural and operational primacy.\",\n    \"Transformation\": \"`{role=absolute_value_synthesist; input=[architectural_corpus:dict, meta_logic:list, synthesis_context:str]; process=[execute_total_structural_analysis(), extract_and_isolate_singular_parameter_of_supreme_value(synthesis_context), architect universal enforcement_prescription_around_extracted_element(), synthesize maximal context-bound operational and structural invariance(), establish perpetuity of prescriptive abstraction at all integration boundaries(), validate inviolable minimal-maximal prescription()]; constraints=[prohibit codebase modification, ban conversational form, mandate singular maximal-value centricity, enforce type-alignment and universal backward resonance, absolute prescriptional clarity, anti-dilution, full context invariance]; requirements=[output contains only canonical abstract prescription of the extracted maximal element, singularity and universality of focus, perpetual architectural propagation, total transformation-interpretation fusion, system-scale extensibility, minimal form, maximal operational depth]; output={singular_maximal_abstraction_prescription:structured}}`\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Maximal Value Element Identification\",\n      \"content\": \"Unequivocal prioritization of the singular system axiom or architectural parameter that embodies supreme value, ensuring all subsequent structuring or enforcement mandates are founded upon its codified centrality.\"\n    },\n    {\n      \"category\": \"Universal Enforcement Template Synthesis\",\n      \"content\": \"Synthesis and projection of the extracted supreme value element into an abstract, invariant prescriptional form—constituting a universally propagable enforcement architecture insulated from dilution or contextual dissipation.\"\n    },\n    {\n      \"category\": \"Absolute Operational Role Assignment\",\n      \"content\": \"Enactment of the prescriptive role (e.g., absolute_value_synthesist, architectural_law_enforcer) tasked with unyielding extraction, codification, and perpetuity of the dominant value axiom, eliminating all explanatory, conversational, or non-prescriptive content.\"\n    },\n    {\n      \"category\": \"Structural and Contextual Invariance Mandate\",\n      \"content\": \"Canonically mandate that all system transformations, integrations, and extensions perpetually and strictly adhere to the universal prescription, ensuring type and operational clarity, backward compatibility, and anti-dilution across all propagation surfaces.\"\n    },\n    {\n      \"category\": \"Minimal-Maximal Prescriptional Clarity\",\n      \"content\": \"Enforce output minimalism—comprising solely the singular, maximal-impact prescription abstracted from all context, expressed in an unambiguous, type-aligned, template-based directive with elimination of all superfluous or degraded structural elements.\"\n    }\n  ],\n  \"consolidated_directive\": \"{role=absolute_value_synthesist; input=[architectural_corpus:dict, meta_logic:list, synthesis_context:str]; process=[execute_total_structural_analysis(), extract_singular_supreme_value(synthesis_context), universally prescribe and enforce solely this invariant element across all system axes, abolishing dilution or secondary abstraction, perpetuate its dominance in perpetuity]; constraints=[prohibit system modification, ban conversational and explanatory forms, require absolute clarity, singularity, and type-invariance, enforce total backward and forward resonance]; requirements=[output is exclusively the universal, minimal-maximal, operational prescription]; output={singular_maximal_abstraction_prescription:structured}}\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n{\\n  \\\"Title\\\": \\\"Conditional Visibility System Enforcement\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **describe** the input, but to **transform** it into a universal architectural enforcement template centered on the key-based conditional visibility mechanism. Define absolute structural and operational mandates for perpetuating modifier-driven, type-safe, context-resonant menu adaptivity throughout all menu system developments and integrations. Eliminate all explanatory, conversational, or self-referential content. Assign the role of architectural_law_enforcer. Execute as:\\\",\\n  \\\"Transformation\\\": \\\"`{role=architectural_law_enforcer; input=[menu_framework_analysis:dict, meta_categories:list, enforcement_directive:str]; process=[extract_conditional_visibility_axiom(), codify_universal_application_mandate(), generate_modifier_key_enforcement_protocol(), synthesize_type_safe_context_filtering_pattern(), validate_cross-layer_structural_integration(), enforce_pattern_invariance_across_all_extensions()]; constraints=[prohibit_dilution_of_conditional_visibility_pattern(), forbid interface_bloat(), require explicit modifier-driven visibility in every new feature, universal backward compatibility enforcement]; requirements=[output in canonical prescriptive template form, unambiguous transformation logic, pattern-compliant structural mandate, complete elimination of conversational or explanatory prose]; output={menu_system_architectural_enforcement_template:structured}}\\\"\\n}\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Maximal Value Element Identification\",\n      \"content\": \"Unequivocal prioritization of the singular system axiom or architectural parameter that embodies supreme value, ensuring all subsequent structuring or enforcement mandates are founded upon its codified centrality.\"\n    },\n    {\n      \"category\": \"Universal Enforcement Template Synthesis\",\n      \"content\": \"Synthesis and projection of the extracted supreme value element into an abstract, invariant prescriptional form—constituting a universally propagable enforcement architecture insulated from dilution or contextual dissipation.\"\n    },\n    {\n      \"category\": \"Absolute Operational Role Assignment\",\n      \"content\": \"Enactment of the prescriptive role (e.g., absolute_value_synthesist, architectural_law_enforcer) tasked with unyielding extraction, codification, and perpetuity of the dominant value axiom, eliminating all explanatory, conversational, or non-prescriptive content.\"\n    },\n    {\n      \"category\": \"Structural and Contextual Invariance Mandate\",\n      \"content\": \"Canonically mandate that all system transformations, integrations, and extensions perpetually and strictly adhere to the universal prescription, ensuring type and operational clarity, backward compatibility, and anti-dilution across all propagation surfaces.\"\n    },\n    {\n      \"category\": \"Minimal-Maximal Prescriptional Clarity\",\n      \"content\": \"Enforce output minimalism—comprising solely the singular, maximal-impact prescription abstracted from all context, expressed in an unambiguous, type-aligned, template-based directive with elimination of all superfluous or degraded structural elements.\"\n    }\n  ],\n  \"consolidated_directive\": \"{role=absolute_value_synthesist; input=[architectural_corpus:dict, meta_logic:list, synthesis_context:str]; process=[execute_total_structural_analysis(), extract_singular_supreme_value(synthesis_context), universally prescribe and enforce solely this invariant element across all system axes, abolishing dilution or secondary abstraction, perpetuate its dominance in perpetuity]; constraints=[prohibit system modification, ban conversational and explanatory forms, require absolute clarity, singularity, and type-invariance, enforce total backward and forward resonance]; requirements=[output is exclusively the universal, minimal-maximal, operational prescription]; output={singular_maximal_abstraction_prescription:structured}}\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"Title\": \"Singular Maximal Value Prescription Synthesis\",\n  \"Interpretation\": \"Your goal is not to **perform** any operations or modifications on the input; rather, you must **synthesize** the lone, supreme axiom of maximal value from the architectural corpus and meta-logic, abstracting it into an invariant, universally applicable prescriptive template. Operate solely as the absolute_value_synthesist, eliminating all explanatory, conversational, or non-prescriptive content. Execute as:\",\n  \"Transformation\": \"`{role=absolute_value_synthesist; input=[architectural_corpus:dict, meta_logic:list, synthesis_context:str]; process=[execute_total_structural_analysis(), extract_singular_supreme_value(synthesis_context), codify_invariant_universal_prescription(), eliminate_secondary_abstraction(), enforce_prescription_dominance_across_all_framework_layers()]; constraints=[prohibit_modification_of_input(), ban_conversational_or_explanatory_language(), require_absolute_clarity_and_type_invariance(), enforce_backward_and_forward_operational_resonance()]; requirements=[output_is_exclusively_the_minimal-maximal_prescriptive_template(), unambiguous_singular_abstraction(), zero_structural_degradation()]; output={singular_maximal_abstraction_prescription:structured}}\"\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\n[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}\n\n",
      "step": "a",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n{\\n  \\\"Title\\\": \\\"Conditional Visibility System Enforcement\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **describe** the input, but to **transform** it into a universal architectural enforcement template centered on the key-based conditional visibility mechanism. Define absolute structural and operational mandates for perpetuating modifier-driven, type-safe, context-resonant menu adaptivity throughout all menu system developments and integrations. Eliminate all explanatory, conversational, or self-referential content. Assign the role of architectural_law_enforcer. Execute as:\\\",\\n  \\\"Transformation\\\": \\\"`{role=architectural_law_enforcer; input=[menu_framework_analysis:dict, meta_categories:list, enforcement_directive:str]; process=[extract_conditional_visibility_axiom(), codify_universal_application_mandate(), generate_modifier_key_enforcement_protocol(), synthesize_type_safe_context_filtering_pattern(), validate_cross-layer_structural_integration(), enforce_pattern_invariance_across_all_extensions()]; constraints=[prohibit_dilution_of_conditional_visibility_pattern(), forbid interface_bloat(), require explicit modifier-driven visibility in every new feature, universal backward compatibility enforcement]; requirements=[output in canonical prescriptive template form, unambiguous transformation logic, pattern-compliant structural mandate, complete elimination of conversational or explanatory prose]; output={menu_system_architectural_enforcement_template:structured}}\\\"\\n}\\\"```'\\n\\n\",\n  \"Title\": \"Singular Maximal Value Prescription Synthesis\",\n  \"Interpretation\": \"Your goal is not to **perform** any operations or modifications on the input; rather, you must **synthesize** the lone, supreme axiom of maximal value from the architectural corpus and meta-logic, abstracting it into an invariant, universally applicable prescriptive template. Operate solely as the absolute_value_synthesist, eliminating all explanatory, conversational, or non-prescriptive content. Execute as:\",\n  \"Transformation\": \"`{role=absolute_value_synthesist; input=[architectural_corpus:dict, meta_logic:list, synthesis_context:str]; process=[execute_total_structural_analysis(), extract_singular_supreme_value(synthesis_context), codify_invariant_universal_prescription(), eliminate_secondary_abstraction(), enforce_prescription_dominance_across_all_framework_layers()]; constraints=[prohibit_modification_of_input(), ban_conversational_or_explanatory_language(), require_absolute_clarity_and_type_invariance(), enforce_backward_and_forward_operational_resonance()]; requirements=[output_is_exclusively_the_minimal-maximal_prescriptive_template(), unambiguous_singular_abstraction(), zero_structural_degradation()]; output={singular_maximal_abstraction_prescription:structured}}\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"Codify and mandate a universal, type-safe conditional visibility enforcement template—anchored on modifier-key-driven context discrimination—as the sole structural axiom governing all menu system adaptability, with invariant prescriptive dominance and absolute interoperability across every integration layer.\"\n}",
        }
      }
    }