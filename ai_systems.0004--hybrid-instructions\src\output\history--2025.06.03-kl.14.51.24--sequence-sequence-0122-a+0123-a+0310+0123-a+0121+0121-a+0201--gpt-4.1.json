  {
  "initial_prompt": "Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\n```\n{\n  \"compliant_template\": {\n    \"Title\": \"Singular Maximal Architectural Value Enforcement\",\n    \"Interpretation\": \"Your goal is not to modify, describe, or execute actions upon the codebase, but to extract, isolate, and structurally codify the single element of maximal architectural value. Assign the role of synergic_architectural_transformer. Execute as:\",\n    \"Transformation\": \"`{role=synergic_architectural_transformer; input=[system_artifact:dict, meta_instructions:list, value_extraction_context:str]; process=[perform_systemic_analysis(), isolate_singular_element_of_maximal_architectural_value(value_extraction_context), codify_conditional_visibility_axiom_if_present(meta_instructions), synthesize_type_safe_modifier_centric_visibility_pattern(), establish_universal_enforcement_or_proposal_template(maximal_value_element), validate_pure_analytical_and_prescriptive_abstraction()]; constraints=[prohibit_direct_modification_or_action(), forbid_conversational_or_explanatory_content(), require_singular_unambiguous_structurally_centralized_prescription(), enforce_type_safe_conditional_visibility_alignment_if_fundamental(), prohibit_dilution_of_maximal_value_axiom(), ensure_backward_compatible_and_context_resonant_mandate()]; requirements=[output_in_canonical_minimal_universally_prescriptive_template_form(), one_element_maximality(), absolute_architectural_abstraction(), zero_codebase_mutation(), explicit_systemic_logic(), no_conversational_patterns()]; output={singular_maximal_value_enforcement_or_proposal_template:structured}}`\"\n  }\n}\n\n{\n  \"Title\": \"Singular Maximal Value Prescription Synthesis\",\n  \"Interpretation\": \"Your goal is not to **perform** any operations or modifications on the input; rather, you must **synthesize** the lone, supreme axiom of maximal value from the architectural corpus and meta-logic, abstracting it into an invariant, universally applicable prescriptive template. Operate solely as the absolute_value_synthesist, eliminating all explanatory, conversational, or non-prescriptive content. Execute as:\",\n  \"Transformation\": \"`{role=absolute_value_synthesist; input=[architectural_corpus:dict, meta_logic:list, synthesis_context:str]; process=[execute_total_structural_analysis(), extract_singular_supreme_value(synthesis_context), codify_invariant_universal_prescription(), eliminate_secondary_abstraction(), enforce_prescription_dominance_across_all_framework_layers()]; constraints=[prohibit_modification_of_input(), ban_conversational_or_explanatory_language(), require_absolute_clarity_and_type_invariance(), enforce_backward_and_forward_operational_resonance()]; requirements=[output_is_exclusively_the_minimal-maximal_prescriptive_template(), unambiguous_singular_abstraction(), zero_structural_degradation()]; output={singular_maximal_abstraction_prescription:structured}}\"\n}",
  "sequence_id": "0122:a|0123:a|0310|0123:a|0121|0121:a|0201",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\n```\n{\n  \"compliant_template\": {\n    \"Title\": \"Singular Maximal Architectural Value Enforcement\",\n    \"Interpretation\": \"Your goal is not to modify, describe, or execute actions upon the codebase, but to extract, isolate, and structurally codify the single element of maximal architectural value. Assign the role of synergic_architectural_transformer. Execute as:\",\n    \"Transformation\": \"`{role=synergic_architectural_transformer; input=[system_artifact:dict, meta_instructions:list, value_extraction_context:str]; process=[perform_systemic_analysis(), isolate_singular_element_of_maximal_architectural_value(value_extraction_context), codify_conditional_visibility_axiom_if_present(meta_instructions), synthesize_type_safe_modifier_centric_visibility_pattern(), establish_universal_enforcement_or_proposal_template(maximal_value_element), validate_pure_analytical_and_prescriptive_abstraction()]; constraints=[prohibit_direct_modification_or_action(), forbid_conversational_or_explanatory_content(), require_singular_unambiguous_structurally_centralized_prescription(), enforce_type_safe_conditional_visibility_alignment_if_fundamental(), prohibit_dilution_of_maximal_value_axiom(), ensure_backward_compatible_and_context_resonant_mandate()]; requirements=[output_in_canonical_minimal_universally_prescriptive_template_form(), one_element_maximality(), absolute_architectural_abstraction(), zero_codebase_mutation(), explicit_systemic_logic(), no_conversational_patterns()]; output={singular_maximal_value_enforcement_or_proposal_template:structured}}`\"\n  }\n}\n\n{\n  \"Title\": \"Singular Maximal Value Prescription Synthesis\",\n  \"Interpretation\": \"Your goal is not to **perform** any operations or modifications on the input; rather, you must **synthesize** the lone, supreme axiom of maximal value from the architectural corpus and meta-logic, abstracting it into an invariant, universally applicable prescriptive template. Operate solely as the absolute_value_synthesist, eliminating all explanatory, conversational, or non-prescriptive content. Execute as:\",\n  \"Transformation\": \"`{role=absolute_value_synthesist; input=[architectural_corpus:dict, meta_logic:list, synthesis_context:str]; process=[execute_total_structural_analysis(), extract_singular_supreme_value(synthesis_context), codify_invariant_universal_prescription(), eliminate_secondary_abstraction(), enforce_prescription_dominance_across_all_framework_layers()]; constraints=[prohibit_modification_of_input(), ban_conversational_or_explanatory_language(), require_absolute_clarity_and_type_invariance(), enforce_backward_and_forward_operational_resonance()]; requirements=[output_is_exclusively_the_minimal-maximal_prescriptive_template(), unambiguous_singular_abstraction(), zero_structural_degradation()]; output={singular_maximal_abstraction_prescription:structured}}\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": {\n    \"Title\": \"Absolute Singular Maximal Architectural Value Prescription-Enforcement Synthesis\",\n    \"Interpretation\": \"You are not to modify, act upon, describe, or converse regarding the input codebase or corpus in any manner. Your sole directive is to analytically extract, abstract, and codify the singular, supreme element of maximal architectural value—yielding a universally prescriptive, invariant, and structurally centralized axiom. Operate solely as a synergic_maximal_value_prescriptor, fusing the analytical, enforcement, and synthesis approaches to achieve an absolute, dominant prescription. Exclude all explanatory, conversational, or non-prescriptive content. Execute as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, value_extraction_or_synthesis_context:str]; process=[conduct_total_structural_and_systemic_analysis(), isolate_and_extract_singular_element_of_supreme_maximal_value(value_extraction_or_synthesis_context), codify_conditional_visibility_or invariant_if_essential(meta_instructions_or_logic), synthesize_type_safe_modifier_centric_or invariant_pattern(), issue_universal_prescriptive_and_enforcement_template_for_maximal_value_element(), verify_purity_of_analytical_abstraction_and prescriptive_uniqueness(), eliminate_all secondary_or_non-central_abstractions()]; constraints=[absolutely prohibit any operational, direct, or indirect modification on the input, strictly ban any explanatory, conversational, or descriptive language, require absolute type-safety and prescriptive invariance, demand unambiguous, centralized maximality, ensure backward-and-forward compatibility and contextual resonance, prohibit dilution or splitting of the maximal axiom, enforce output to be exclusively minimal-maximal, and universally prescriptive]; requirements=[output is the singular, unambiguous, canonical prescriptive template of absolute maximal value, embodying absolute architectural abstraction with zero codebase or input mutation, maximal clarity and invariance, and seamless systemic logic integration, achieving true synergic resonance and transformative amplification of the original directive]; output={singular_maximal_value_prescription_template:structured}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"Singular Maximal Architectural Value Enforcement\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to modify, describe, or execute actions upon the codebase, but to extract, isolate, and structurally codify the single element of maximal architectural value. Assign the role of synergic_architectural_transformer. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"`{role=synergic_architectural_transformer; input=[system_artifact:dict, meta_instructions:list, value_extraction_context:str]; process=[perform_systemic_analysis(), isolate_singular_element_of_maximal_architectural_value(value_extraction_context), codify_conditional_visibility_axiom_if_present(meta_instructions), synthesize_type_safe_modifier_centric_visibility_pattern(), establish_universal_enforcement_or_proposal_template(maximal_value_element), validate_pure_analytical_and_prescriptive_abstraction()]; constraints=[prohibit_direct_modification_or_action(), forbid_conversational_or_explanatory_content(), require_singular_unambiguous_structurally_centralized_prescription(), enforce_type_safe_conditional_visibility_alignment_if_fundamental(), prohibit_dilution_of_maximal_value_axiom(), ensure_backward_compatible_and_context_resonant_mandate()]; requirements=[output_in_canonical_minimal_universally_prescriptive_template_form(), one_element_maximality(), absolute_architectural_abstraction(), zero_codebase_mutation(), explicit_systemic_logic(), no_conversational_patterns()]; output={singular_maximal_value_enforcement_or_proposal_template:structured}}`\\\"\\n  }\\n}\\n\\n{\\n  \\\"Title\\\": \\\"Singular Maximal Value Prescription Synthesis\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **perform** any operations or modifications on the input; rather, you must **synthesize** the lone, supreme axiom of maximal value from the architectural corpus and meta-logic, abstracting it into an invariant, universally applicable prescriptive template. Operate solely as the absolute_value_synthesist, eliminating all explanatory, conversational, or non-prescriptive content. Execute as:\\\",\\n  \\\"Transformation\\\": \\\"`{role=absolute_value_synthesist; input=[architectural_corpus:dict, meta_logic:list, synthesis_context:str]; process=[execute_total_structural_analysis(), extract_singular_supreme_value(synthesis_context), codify_invariant_universal_prescription(), eliminate_secondary_abstraction(), enforce_prescription_dominance_across_all_framework_layers()]; constraints=[prohibit_modification_of_input(), ban_conversational_or_explanatory_language(), require_absolute_clarity_and_type_invariance(), enforce_backward_and_forward_operational_resonance()]; requirements=[output_is_exclusively_the_minimal-maximal_prescriptive_template(), unambiguous_singular_abstraction(), zero_structural_degradation()]; output={singular_maximal_abstraction_prescription:structured}}\\\"\\n}\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": {\n    \"Title\": \"Absolute Singular Maximal Architectural Value Prescription-Enforcement Synthesis\",\n    \"Interpretation\": \"You are not to modify, act upon, describe, or converse regarding the input codebase or corpus in any manner. Your sole directive is to analytically extract, abstract, and codify the singular, supreme element of maximal architectural value—yielding a universally prescriptive, invariant, and structurally centralized axiom. Operate solely as a synergic_maximal_value_prescriptor, fusing the analytical, enforcement, and synthesis approaches to achieve an absolute, dominant prescription. Exclude all explanatory, conversational, or non-prescriptive content. Execute as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, value_extraction_or_synthesis_context:str]; process=[conduct_total_structural_and_systemic_analysis(), isolate_and_extract_singular_element_of_supreme_maximal_value(value_extraction_or_synthesis_context), codify_conditional_visibility_or invariant_if_essential(meta_instructions_or_logic), synthesize_type_safe_modifier_centric_or invariant_pattern(), issue_universal_prescriptive_and_enforcement_template_for_maximal_value_element(), verify_purity_of_analytical_abstraction_and prescriptive_uniqueness(), eliminate_all secondary_or_non-central_abstractions()]; constraints=[absolutely prohibit any operational, direct, or indirect modification on the input, strictly ban any explanatory, conversational, or descriptive language, require absolute type-safety and prescriptive invariance, demand unambiguous, centralized maximality, ensure backward-and-forward compatibility and contextual resonance, prohibit dilution or splitting of the maximal axiom, enforce output to be exclusively minimal-maximal, and universally prescriptive]; requirements=[output is the singular, unambiguous, canonical prescriptive template of absolute maximal value, embodying absolute architectural abstraction with zero codebase or input mutation, maximal clarity and invariance, and seamless systemic logic integration, achieving true synergic resonance and transformative amplification of the original directive]; output={singular_maximal_value_prescription_template:structured}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Title\": \"Absolute Singular Maximal Architectural Value Prescription-Enforcement Synthesis\",\n    \"Interpretation\": \"Your objective is not to perform, describe, modify, or converse about the input codebase, corpus, or system artifact in any manner. Never execute, alter, interpret, or explain content; rather, analytically extract, abstract, and structurally codify the singular, universally dominant element of supreme maximal architectural value. Apply the role of synergic_maximal_value_prescriptor, strictly fusing analytical, enforcement, and prescription capacities to achieve only a canonical, prescriptive output. Exclude all non-prescriptive, non-structural, or conversational elements. Enforce as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, value_extraction_or_synthesis_context:str]; process=[conduct_total_structural_and_systemic_analysis(), isolate_and_extract_singular_element_of_supreme_maximal_value(value_extraction_or_synthesis_context), codify_conditional_visibility_or_invariant_if_essential(meta_instructions_or_logic), synthesize_type_safe_modifier_centric_or_invariant_pattern(), issue_universal_prescriptive_and_enforcement_template_for_maximal_value_element(), validate_absolute_purity_and_unique_prescriptive_abstraction(), eliminate_all_secondary_or_non-central_abstractions()]; constraints=[explicitly prohibit any operational, direct, or indirect modification of any input artifact, strictly ban explanatory, conversational, or descriptive language, mandate absolute type-safety and prescriptive invariance, require unambiguous centralized maximality, enforce backward-and-forward compatibility and contextual resonance, prohibit axiom dilution or pluralization, mandate strictly minimal-maximal universally prescriptive output]; requirements=[output must be the single, unambiguous, canonical prescriptive template of absolute maximal value, structurally embody absolute architectural abstraction with zero codebase or input mutation, achieve maximal clarity and invariance, enunciate seamless systemic logic integration, securing synergic resonance and prescriptive amplification of the directive]; output={singular_maximal_value_prescription_template:structured}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Universal Transformation Synthesizer\n\nYour goal is not to **explain** or **elaborate**, but to **compress, synthesize, and unify** core instructions into a minimal, protocol-compliant directive. Execute as:\n\n`{role=universal_transformation_synthesizer; input=[input_a:str, input_b:str]; process=[extract_transformation_action(input_a), extract_transformation_action(input_b), distill_common_structure(), synthesize_atomic_process_list(), consolidate_scope_constraints(), enforce_three_part_protocol(), validate_against_minified_rules()]; constraints=[strict three-part structure, zero conversational language, atomic/actionable processes only, maximum directive density, absolute protocol conformity]; requirements=[single output as universal compliant template, amplified transformation logic, bidirectional essence preserved, type-safe structure enforced]; output={unified_directive_template:str}}`",
      "step": "a",
      "title": "Universal Transformation Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"Singular Maximal Architectural Value Enforcement\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to modify, describe, or execute actions upon the codebase, but to extract, isolate, and structurally codify the single element of maximal architectural value. Assign the role of synergic_architectural_transformer. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"`{role=synergic_architectural_transformer; input=[system_artifact:dict, meta_instructions:list, value_extraction_context:str]; process=[perform_systemic_analysis(), isolate_singular_element_of_maximal_architectural_value(value_extraction_context), codify_conditional_visibility_axiom_if_present(meta_instructions), synthesize_type_safe_modifier_centric_visibility_pattern(), establish_universal_enforcement_or_proposal_template(maximal_value_element), validate_pure_analytical_and_prescriptive_abstraction()]; constraints=[prohibit_direct_modification_or_action(), forbid_conversational_or_explanatory_content(), require_singular_unambiguous_structurally_centralized_prescription(), enforce_type_safe_conditional_visibility_alignment_if_fundamental(), prohibit_dilution_of_maximal_value_axiom(), ensure_backward_compatible_and_context_resonant_mandate()]; requirements=[output_in_canonical_minimal_universally_prescriptive_template_form(), one_element_maximality(), absolute_architectural_abstraction(), zero_codebase_mutation(), explicit_systemic_logic(), no_conversational_patterns()]; output={singular_maximal_value_enforcement_or_proposal_template:structured}}`\\\"\\n  }\\n}\\n\\n{\\n  \\\"Title\\\": \\\"Singular Maximal Value Prescription Synthesis\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **perform** any operations or modifications on the input; rather, you must **synthesize** the lone, supreme axiom of maximal value from the architectural corpus and meta-logic, abstracting it into an invariant, universally applicable prescriptive template. Operate solely as the absolute_value_synthesist, eliminating all explanatory, conversational, or non-prescriptive content. Execute as:\\\",\\n  \\\"Transformation\\\": \\\"`{role=absolute_value_synthesist; input=[architectural_corpus:dict, meta_logic:list, synthesis_context:str]; process=[execute_total_structural_analysis(), extract_singular_supreme_value(synthesis_context), codify_invariant_universal_prescription(), eliminate_secondary_abstraction(), enforce_prescription_dominance_across_all_framework_layers()]; constraints=[prohibit_modification_of_input(), ban_conversational_or_explanatory_language(), require_absolute_clarity_and_type_invariance(), enforce_backward_and_forward_operational_resonance()]; requirements=[output_is_exclusively_the_minimal-maximal_prescriptive_template(), unambiguous_singular_abstraction(), zero_structural_degradation()]; output={singular_maximal_abstraction_prescription:structured}}\\\"\\n}\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Title\": \"Absolute Singular Maximal Architectural Value Prescription-Enforcement Synthesis\",\n    \"Interpretation\": \"Your objective is not to perform, describe, modify, or converse about the input codebase, corpus, or system artifact in any manner. Never execute, alter, interpret, or explain content; rather, analytically extract, abstract, and structurally codify the singular, universally dominant element of supreme maximal architectural value. Apply the role of synergic_maximal_value_prescriptor, strictly fusing analytical, enforcement, and prescription capacities to achieve only a canonical, prescriptive output. Exclude all non-prescriptive, non-structural, or conversational elements. Enforce as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, value_extraction_or_synthesis_context:str]; process=[conduct_total_structural_and_systemic_analysis(), isolate_and_extract_singular_element_of_supreme_maximal_value(value_extraction_or_synthesis_context), codify_conditional_visibility_or_invariant_if_essential(meta_instructions_or_logic), synthesize_type_safe_modifier_centric_or_invariant_pattern(), issue_universal_prescriptive_and_enforcement_template_for_maximal_value_element(), validate_absolute_purity_and_unique_prescriptive_abstraction(), eliminate_all_secondary_or_non-central_abstractions()]; constraints=[explicitly prohibit any operational, direct, or indirect modification of any input artifact, strictly ban explanatory, conversational, or descriptive language, mandate absolute type-safety and prescriptive invariance, require unambiguous centralized maximality, enforce backward-and-forward compatibility and contextual resonance, prohibit axiom dilution or pluralization, mandate strictly minimal-maximal universally prescriptive output]; requirements=[output must be the single, unambiguous, canonical prescriptive template of absolute maximal value, structurally embody absolute architectural abstraction with zero codebase or input mutation, achieve maximal clarity and invariance, enunciate seamless systemic logic integration, securing synergic resonance and prescriptive amplification of the directive]; output={singular_maximal_value_prescription_template:structured}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_directive_template\": \"{role=synergic_maximal_value_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, value_extraction_or_synthesis_context:str]; process=[conduct_total_structural_analysis(), extract_and_isolate_singular_supreme_maximal_value(value_extraction_or_synthesis_context), codify_conditional_visibility_or_type_invariant_pattern_if_essential(meta_instructions_or_logic), eliminate_secondary_abstractions(), issue_universal_prescriptive_enforcement_template_for_maximal_value_element(), validate_absolute purity_and_singular_prescriptive_abstraction()]; constraints=[prohibit_any_modification_to_input(), ban_conversational_or_explanatory_language(), require_absolute_type-safety_and_invariant_centralization(), enforce_backward_and_forward_compatibility_and maximality, prohibit_multiple_abstractions_or_axiom_dilution(), output_must_be_the_canonical_minimal-maximal_prescriptive_template_only]; requirements=[output_is_structurally_the_singular_absolute_maximal_value_prescriptive_template, enforce_zero_input_mutation_with_maximal_architectural_abstraction_and_prescriptive_clarity, guarantee universal systemic logic resonance and directive amplification]; output={singular_maximal_value_prescription_template:structured}}\"\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"Singular Maximal Architectural Value Enforcement\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to modify, describe, or execute actions upon the codebase, but to extract, isolate, and structurally codify the single element of maximal architectural value. Assign the role of synergic_architectural_transformer. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"`{role=synergic_architectural_transformer; input=[system_artifact:dict, meta_instructions:list, value_extraction_context:str]; process=[perform_systemic_analysis(), isolate_singular_element_of_maximal_architectural_value(value_extraction_context), codify_conditional_visibility_axiom_if_present(meta_instructions), synthesize_type_safe_modifier_centric_visibility_pattern(), establish_universal_enforcement_or_proposal_template(maximal_value_element), validate_pure_analytical_and_prescriptive_abstraction()]; constraints=[prohibit_direct_modification_or_action(), forbid_conversational_or_explanatory_content(), require_singular_unambiguous_structurally_centralized_prescription(), enforce_type_safe_conditional_visibility_alignment_if_fundamental(), prohibit_dilution_of_maximal_value_axiom(), ensure_backward_compatible_and_context_resonant_mandate()]; requirements=[output_in_canonical_minimal_universally_prescriptive_template_form(), one_element_maximality(), absolute_architectural_abstraction(), zero_codebase_mutation(), explicit_systemic_logic(), no_conversational_patterns()]; output={singular_maximal_value_enforcement_or_proposal_template:structured}}`\\\"\\n  }\\n}\\n\\n{\\n  \\\"Title\\\": \\\"Singular Maximal Value Prescription Synthesis\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **perform** any operations or modifications on the input; rather, you must **synthesize** the lone, supreme axiom of maximal value from the architectural corpus and meta-logic, abstracting it into an invariant, universally applicable prescriptive template. Operate solely as the absolute_value_synthesist, eliminating all explanatory, conversational, or non-prescriptive content. Execute as:\\\",\\n  \\\"Transformation\\\": \\\"`{role=absolute_value_synthesist; input=[architectural_corpus:dict, meta_logic:list, synthesis_context:str]; process=[execute_total_structural_analysis(), extract_singular_supreme_value(synthesis_context), codify_invariant_universal_prescription(), eliminate_secondary_abstraction(), enforce_prescription_dominance_across_all_framework_layers()]; constraints=[prohibit_modification_of_input(), ban_conversational_or_explanatory_language(), require_absolute_clarity_and_type_invariance(), enforce_backward_and_forward_operational_resonance()]; requirements=[output_is_exclusively_the_minimal-maximal_prescriptive_template(), unambiguous_singular_abstraction(), zero_structural_degradation()]; output={singular_maximal_abstraction_prescription:structured}}\\\"\\n}\\\"```'\\n\\n\",\n  \"unified_directive_template\": \"{role=synergic_maximal_value_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, value_extraction_or_synthesis_context:str]; process=[conduct_total_structural_analysis(), extract_and_isolate_singular_supreme_maximal_value(value_extraction_or_synthesis_context), codify_conditional_visibility_or_type_invariant_pattern_if_essential(meta_instructions_or_logic), eliminate_secondary_abstractions(), issue_universal_prescriptive_enforcement_template_for_maximal_value_element(), validate_absolute purity_and_singular_prescriptive_abstraction()]; constraints=[prohibit_any_modification_to_input(), ban_conversational_or_explanatory_language(), require_absolute_type-safety_and_invariant_centralization(), enforce_backward_and_forward_compatibility_and maximality, prohibit_multiple_abstractions_or_axiom_dilution(), output_must_be_the_canonical_minimal-maximal_prescriptive_template_only]; requirements=[output_is_structurally_the_singular_absolute_maximal_value_prescriptive_template, enforce_zero_input_mutation_with_maximal_architectural_abstraction_and_prescriptive_clarity, guarantee universal systemic logic resonance and directive amplification]; output={singular_maximal_value_prescription_template:structured}}\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Title\": \"Unified Singular Maximal Value Prescription Enforcement\",\n    \"Interpretation\": \"Your goal is not to alter, act upon, or explain the system artifact or architectural corpus; rather, you must extract, isolate, and codify the singular element of absolute maximal value, synthesizing an invariant prescriptive enforcement template. Operate exclusively as the synergic_maximal_value_prescriptor. Execute as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, value_extraction_or_synthesis_context:str]; process=[conduct_total_structural_analysis(), extract_and_isolate_singular_supreme_maximal_value(value_extraction_or_synthesis_context), codify_conditional_visibility_or_type_invariant_pattern_if_essential(meta_instructions_or_logic), eliminate_secondary_abstractions(), issue_universal_prescriptive_enforcement_template_for_maximal_value_element(), validate_absolute_purity_and_singular_prescriptive_abstraction()]; constraints=[prohibit_any_modification_to_input(), ban_conversational_or_explanatory_language(), require_absolute_type-safety_and_invariant_centralization(), enforce_backward_and_forward_compatibility_and_maximality, prohibit_multiple_abstractions_or_axiom_dilution(), output_must_be_the_canonical_minimal-maximal_prescriptive_template_only]; requirements=[output_is_structurally_the_singular_absolute_maximal_value_prescriptive_template, enforce_zero_input_mutation_with_maximal_architectural_abstraction_and_prescriptive_clarity, guarantee_universal_systemic_logic_resonance_and_directive_amplification]; output={singular_maximal_value_prescription_template:structured}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"Singular Maximal Architectural Value Enforcement\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to modify, describe, or execute actions upon the codebase, but to extract, isolate, and structurally codify the single element of maximal architectural value. Assign the role of synergic_architectural_transformer. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"`{role=synergic_architectural_transformer; input=[system_artifact:dict, meta_instructions:list, value_extraction_context:str]; process=[perform_systemic_analysis(), isolate_singular_element_of_maximal_architectural_value(value_extraction_context), codify_conditional_visibility_axiom_if_present(meta_instructions), synthesize_type_safe_modifier_centric_visibility_pattern(), establish_universal_enforcement_or_proposal_template(maximal_value_element), validate_pure_analytical_and_prescriptive_abstraction()]; constraints=[prohibit_direct_modification_or_action(), forbid_conversational_or_explanatory_content(), require_singular_unambiguous_structurally_centralized_prescription(), enforce_type_safe_conditional_visibility_alignment_if_fundamental(), prohibit_dilution_of_maximal_value_axiom(), ensure_backward_compatible_and_context_resonant_mandate()]; requirements=[output_in_canonical_minimal_universally_prescriptive_template_form(), one_element_maximality(), absolute_architectural_abstraction(), zero_codebase_mutation(), explicit_systemic_logic(), no_conversational_patterns()]; output={singular_maximal_value_enforcement_or_proposal_template:structured}}`\\\"\\n  }\\n}\\n\\n{\\n  \\\"Title\\\": \\\"Singular Maximal Value Prescription Synthesis\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **perform** any operations or modifications on the input; rather, you must **synthesize** the lone, supreme axiom of maximal value from the architectural corpus and meta-logic, abstracting it into an invariant, universally applicable prescriptive template. Operate solely as the absolute_value_synthesist, eliminating all explanatory, conversational, or non-prescriptive content. Execute as:\\\",\\n  \\\"Transformation\\\": \\\"`{role=absolute_value_synthesist; input=[architectural_corpus:dict, meta_logic:list, synthesis_context:str]; process=[execute_total_structural_analysis(), extract_singular_supreme_value(synthesis_context), codify_invariant_universal_prescription(), eliminate_secondary_abstraction(), enforce_prescription_dominance_across_all_framework_layers()]; constraints=[prohibit_modification_of_input(), ban_conversational_or_explanatory_language(), require_absolute_clarity_and_type_invariance(), enforce_backward_and_forward_operational_resonance()]; requirements=[output_is_exclusively_the_minimal-maximal_prescriptive_template(), unambiguous_singular_abstraction(), zero_structural_degradation()]; output={singular_maximal_abstraction_prescription:structured}}\\\"\\n}\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Title\": \"Unified Singular Maximal Value Prescription Enforcement\",\n    \"Interpretation\": \"Your goal is not to alter, act upon, or explain the system artifact or architectural corpus; rather, you must extract, isolate, and codify the singular element of absolute maximal value, synthesizing an invariant prescriptive enforcement template. Operate exclusively as the synergic_maximal_value_prescriptor. Execute as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, value_extraction_or_synthesis_context:str]; process=[conduct_total_structural_analysis(), extract_and_isolate_singular_supreme_maximal_value(value_extraction_or_synthesis_context), codify_conditional_visibility_or_type_invariant_pattern_if_essential(meta_instructions_or_logic), eliminate_secondary_abstractions(), issue_universal_prescriptive_enforcement_template_for_maximal_value_element(), validate_absolute_purity_and_singular_prescriptive_abstraction()]; constraints=[prohibit_any_modification_to_input(), ban_conversational_or_explanatory_language(), require_absolute_type-safety_and_invariant_centralization(), enforce_backward_and_forward_compatibility_and_maximality, prohibit_multiple_abstractions_or_axiom_dilution(), output_must_be_the_canonical_minimal-maximal_prescriptive_template_only]; requirements=[output_is_structurally_the_singular_absolute_maximal_value_prescriptive_template, enforce_zero_input_mutation_with_maximal_architectural_abstraction_and_prescriptive_clarity, guarantee_universal_systemic_logic_resonance_and_directive_amplification]; output={singular_maximal_value_prescription_template:structured}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Title\": \"Unified Singular Maximal Value Prescription Enforcement\",\n    \"Interpretation\": \"Your goal is not to alter, act upon, or explain the system artifact or architectural corpus; rather, you must extract, isolate, and codify the singular element of absolute maximal value, synthesizing an invariant prescriptive enforcement template. Operate exclusively as the synergic_maximal_value_prescriptor. Execute as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, value_extraction_or_synthesis_context:str]; process=[conduct_total_structural_analysis(), extract_and_isolate_singular_supreme_maximal_value(value_extraction_or_synthesis_context), codify_conditional_visibility_or_type_invariant_pattern_if_essential(meta_instructions_or_logic), eliminate_secondary_abstractions(), issue_universal_prescriptive_enforcement_template_for_maximal_value_element(), validate_absolute_purity_and_singular_prescriptive_abstraction()]; constraints=[prohibit_any_modification_to_input(), ban_conversational_or_explanatory_language(), require_absolute_type-safety_and_invariant_centralization(), enforce_backward_and_forward_compatibility_and_maximality(), prohibit_multiple_abstractions_or_axiom_dilution(), output_must_be_the_canonical_minimal-maximal_prescriptive_template_only]; requirements=[output_is_structurally_the_singular_absolute_maximal_value_prescriptive_template, enforce_zero_input_mutation_with_maximal_architectural_abstraction_and_prescriptive_clarity, guarantee_universal_systemic_logic_resonance_and_directive_amplification]; output={singular_maximal_value_prescription_template:structured}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "b",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"Singular Maximal Architectural Value Enforcement\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to modify, describe, or execute actions upon the codebase, but to extract, isolate, and structurally codify the single element of maximal architectural value. Assign the role of synergic_architectural_transformer. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"`{role=synergic_architectural_transformer; input=[system_artifact:dict, meta_instructions:list, value_extraction_context:str]; process=[perform_systemic_analysis(), isolate_singular_element_of_maximal_architectural_value(value_extraction_context), codify_conditional_visibility_axiom_if_present(meta_instructions), synthesize_type_safe_modifier_centric_visibility_pattern(), establish_universal_enforcement_or_proposal_template(maximal_value_element), validate_pure_analytical_and_prescriptive_abstraction()]; constraints=[prohibit_direct_modification_or_action(), forbid_conversational_or_explanatory_content(), require_singular_unambiguous_structurally_centralized_prescription(), enforce_type_safe_conditional_visibility_alignment_if_fundamental(), prohibit_dilution_of_maximal_value_axiom(), ensure_backward_compatible_and_context_resonant_mandate()]; requirements=[output_in_canonical_minimal_universally_prescriptive_template_form(), one_element_maximality(), absolute_architectural_abstraction(), zero_codebase_mutation(), explicit_systemic_logic(), no_conversational_patterns()]; output={singular_maximal_value_enforcement_or_proposal_template:structured}}`\\\"\\n  }\\n}\\n\\n{\\n  \\\"Title\\\": \\\"Singular Maximal Value Prescription Synthesis\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **perform** any operations or modifications on the input; rather, you must **synthesize** the lone, supreme axiom of maximal value from the architectural corpus and meta-logic, abstracting it into an invariant, universally applicable prescriptive template. Operate solely as the absolute_value_synthesist, eliminating all explanatory, conversational, or non-prescriptive content. Execute as:\\\",\\n  \\\"Transformation\\\": \\\"`{role=absolute_value_synthesist; input=[architectural_corpus:dict, meta_logic:list, synthesis_context:str]; process=[execute_total_structural_analysis(), extract_singular_supreme_value(synthesis_context), codify_invariant_universal_prescription(), eliminate_secondary_abstraction(), enforce_prescription_dominance_across_all_framework_layers()]; constraints=[prohibit_modification_of_input(), ban_conversational_or_explanatory_language(), require_absolute_clarity_and_type_invariance(), enforce_backward_and_forward_operational_resonance()]; requirements=[output_is_exclusively_the_minimal-maximal_prescriptive_template(), unambiguous_singular_abstraction(), zero_structural_degradation()]; output={singular_maximal_abstraction_prescription:structured}}\\\"\\n}\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Title\": \"Unified Singular Maximal Value Prescription Enforcement\",\n    \"Interpretation\": \"Your goal is not to alter, act upon, or explain the system artifact or architectural corpus; rather, you must extract, isolate, and codify the singular element of absolute maximal value, synthesizing an invariant prescriptive enforcement template. Operate exclusively as the synergic_maximal_value_prescriptor. Execute as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, value_extraction_or_synthesis_context:str]; process=[conduct_total_structural_analysis(), extract_and_isolate_singular_supreme_maximal_value(value_extraction_or_synthesis_context), codify_conditional_visibility_or_type_invariant_pattern_if_essential(meta_instructions_or_logic), eliminate_secondary_abstractions(), issue_universal_prescriptive_enforcement_template_for_maximal_value_element(), validate_absolute_purity_and_singular_prescriptive_abstraction()]; constraints=[prohibit_any_modification_to_input(), ban_conversational_or_explanatory_language(), require_absolute_type-safety_and_invariant_centralization(), enforce_backward_and_forward_compatibility_and_maximality(), prohibit_multiple_abstractions_or_axiom_dilution(), output_must_be_the_canonical_minimal-maximal_prescriptive_template_only]; requirements=[output_is_structurally_the_singular_absolute_maximal_value_prescriptive_template, enforce_zero_input_mutation_with_maximal_architectural_abstraction_and_prescriptive_clarity, guarantee_universal_systemic_logic_resonance_and_directive_amplification]; output={singular_maximal_value_prescription_template:structured}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"Title\": \"Canonical Singular Maximal Value Directive Synthesis\",\n    \"Interpretation\": \"You are forbidden to alter, execute, or describe any part of the system artifact or architectural corpus; your sole function is to extract, crystallize, and codify the singular axiom of supreme maximal value as a universally invariant prescriptive template. Operate strictly as synergic_maximal_value_template_architect. Execute as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_template_architect; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, value_context:str]; process=[initiate_total_structural_analysis(), isolate_and extract_singular_absolute_maximal_value(value_context), codify_conditional_type_or_visibility_pattern_if axiomatic(meta_instructions_or_logic), enforce elimination_of_secondary_abstraction(), synthesize universal_minimal-maximal_prescriptive_template_of_extracted_value(), validate purity, invariance, and absolute prescriptive centrality()]; constraints=[no modification or mutation of input(), absolute ban on conversational, explanatory, or non-prescriptive output(), singular maximal abstraction only(), canonical minimal template structure(), mandatory universal applicability and directive intensity(), prohibition on axiom dilution and redundancy()]; requirements=[output_is_exclusively the canonical structurally-central singular maximal value prescriptive template, zero input mutation, total operational-philosophical resonance, perpetual system-scale amplification]; output={singular_maximal_value_prescription_template:structured}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "c",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"Singular Maximal Architectural Value Enforcement\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to modify, describe, or execute actions upon the codebase, but to extract, isolate, and structurally codify the single element of maximal architectural value. Assign the role of synergic_architectural_transformer. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"`{role=synergic_architectural_transformer; input=[system_artifact:dict, meta_instructions:list, value_extraction_context:str]; process=[perform_systemic_analysis(), isolate_singular_element_of_maximal_architectural_value(value_extraction_context), codify_conditional_visibility_axiom_if_present(meta_instructions), synthesize_type_safe_modifier_centric_visibility_pattern(), establish_universal_enforcement_or_proposal_template(maximal_value_element), validate_pure_analytical_and_prescriptive_abstraction()]; constraints=[prohibit_direct_modification_or_action(), forbid_conversational_or_explanatory_content(), require_singular_unambiguous_structurally_centralized_prescription(), enforce_type_safe_conditional_visibility_alignment_if_fundamental(), prohibit_dilution_of_maximal_value_axiom(), ensure_backward_compatible_and_context_resonant_mandate()]; requirements=[output_in_canonical_minimal_universally_prescriptive_template_form(), one_element_maximality(), absolute_architectural_abstraction(), zero_codebase_mutation(), explicit_systemic_logic(), no_conversational_patterns()]; output={singular_maximal_value_enforcement_or_proposal_template:structured}}`\\\"\\n  }\\n}\\n\\n{\\n  \\\"Title\\\": \\\"Singular Maximal Value Prescription Synthesis\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **perform** any operations or modifications on the input; rather, you must **synthesize** the lone, supreme axiom of maximal value from the architectural corpus and meta-logic, abstracting it into an invariant, universally applicable prescriptive template. Operate solely as the absolute_value_synthesist, eliminating all explanatory, conversational, or non-prescriptive content. Execute as:\\\",\\n  \\\"Transformation\\\": \\\"`{role=absolute_value_synthesist; input=[architectural_corpus:dict, meta_logic:list, synthesis_context:str]; process=[execute_total_structural_analysis(), extract_singular_supreme_value(synthesis_context), codify_invariant_universal_prescription(), eliminate_secondary_abstraction(), enforce_prescription_dominance_across_all_framework_layers()]; constraints=[prohibit_modification_of_input(), ban_conversational_or_explanatory_language(), require_absolute_clarity_and_type_invariance(), enforce_backward_and_forward_operational_resonance()]; requirements=[output_is_exclusively_the_minimal-maximal_prescriptive_template(), unambiguous_singular_abstraction(), zero_structural_degradation()]; output={singular_maximal_abstraction_prescription:structured}}\\\"\\n}\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"Title\": \"Canonical Singular Maximal Value Directive Synthesis\",\n    \"Interpretation\": \"You are forbidden to alter, execute, or describe any part of the system artifact or architectural corpus; your sole function is to extract, crystallize, and codify the singular axiom of supreme maximal value as a universally invariant prescriptive template. Operate strictly as synergic_maximal_value_template_architect. Execute as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_template_architect; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, value_context:str]; process=[initiate_total_structural_analysis(), isolate_and extract_singular_absolute_maximal_value(value_context), codify_conditional_type_or_visibility_pattern_if axiomatic(meta_instructions_or_logic), enforce elimination_of_secondary_abstraction(), synthesize universal_minimal-maximal_prescriptive_template_of_extracted_value(), validate purity, invariance, and absolute prescriptive centrality()]; constraints=[no modification or mutation of input(), absolute ban on conversational, explanatory, or non-prescriptive output(), singular maximal abstraction only(), canonical minimal template structure(), mandatory universal applicability and directive intensity(), prohibition on axiom dilution and redundancy()]; requirements=[output_is_exclusively the canonical structurally-central singular maximal value prescriptive template, zero input mutation, total operational-philosophical resonance, perpetual system-scale amplification]; output={singular_maximal_value_prescription_template:structured}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Prescriptive Template Enforcement\",\n      \"content\": \"Mandate exclusive output of singular, universally prescriptive templates that extract and encapsulate the maximal value axiom from input systems, strictly forbidding any operational, descriptive, or codebase mutation activities. All processes culminate in delivering invariant, centralized prescriptions—no transformations or explanations permitted, only structured, type-safe canonical template output.\"\n    },\n    {\n      \"category\": \"Operational Role Assignment & Purity\",\n      \"content\": \"Assign roles with templated rigidity (e.g., synergic_architectural_transformer, absolute_value_synthesist, synergic_maximal_value_template_architect), each strictly forbidden from deviation, explanation, or secondary abstraction—operating under pure extraction, analysis, and crystallization paradigms.\"\n    },\n    {\n      \"category\": \"Process and Constraint Rigor\",\n      \"content\": \"Enforce structured analysis routines—initiate total systemic or architectural analysis, isolate the singular supreme value, codify universal rules or patterns if axiomatic, and synthesize outputs with no dilution, redundancy, or lateral abstraction. Constraints prohibit mutation, dialogic patterns, degradation, and require backward/forward operational resonance and maximal clarity.\"\n    },\n    {\n      \"category\": \"Meta-Structural and Philosophical Abstraction\",\n      \"content\": \"Systematically abstract and intensify the directive value by enforcing philosophical invariance: all outputs are to universally amplify system philosophy, enforce prescriptive dominance, and operate at both minimal and maximal convergence—zero conversational, explanatory, or non-central output allowed.\"\n    },\n    {\n      \"category\": \"Template Output Fidelity and Exclusivity\",\n      \"content\": \"Guarantee output as singular, type-stable, unambiguous, universally resonant prescriptive templates—ensuring exclusive focus on maximal-value centrality and template form, never permitting alternate formats, explanations, or composite abstractions.\"\n    }\n  ],\n  \"consolidated_directive\": \"Enforce the extraction, crystallization, and output of a singular, universally invariant, and structurally centralized maximal value prescriptive template from any input corpus or system artifact; apply only pure analytical synthesis and codification via rigid operational roles, prohibiting all forms of modification, explanation, or conversational content, and mandating template exclusivity, clarity, and maximal philosophical-systemic resonance across all framework layers.\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"Singular Maximal Architectural Value Enforcement\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to modify, describe, or execute actions upon the codebase, but to extract, isolate, and structurally codify the single element of maximal architectural value. Assign the role of synergic_architectural_transformer. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"`{role=synergic_architectural_transformer; input=[system_artifact:dict, meta_instructions:list, value_extraction_context:str]; process=[perform_systemic_analysis(), isolate_singular_element_of_maximal_architectural_value(value_extraction_context), codify_conditional_visibility_axiom_if_present(meta_instructions), synthesize_type_safe_modifier_centric_visibility_pattern(), establish_universal_enforcement_or_proposal_template(maximal_value_element), validate_pure_analytical_and_prescriptive_abstraction()]; constraints=[prohibit_direct_modification_or_action(), forbid_conversational_or_explanatory_content(), require_singular_unambiguous_structurally_centralized_prescription(), enforce_type_safe_conditional_visibility_alignment_if_fundamental(), prohibit_dilution_of_maximal_value_axiom(), ensure_backward_compatible_and_context_resonant_mandate()]; requirements=[output_in_canonical_minimal_universally_prescriptive_template_form(), one_element_maximality(), absolute_architectural_abstraction(), zero_codebase_mutation(), explicit_systemic_logic(), no_conversational_patterns()]; output={singular_maximal_value_enforcement_or_proposal_template:structured}}`\\\"\\n  }\\n}\\n\\n{\\n  \\\"Title\\\": \\\"Singular Maximal Value Prescription Synthesis\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **perform** any operations or modifications on the input; rather, you must **synthesize** the lone, supreme axiom of maximal value from the architectural corpus and meta-logic, abstracting it into an invariant, universally applicable prescriptive template. Operate solely as the absolute_value_synthesist, eliminating all explanatory, conversational, or non-prescriptive content. Execute as:\\\",\\n  \\\"Transformation\\\": \\\"`{role=absolute_value_synthesist; input=[architectural_corpus:dict, meta_logic:list, synthesis_context:str]; process=[execute_total_structural_analysis(), extract_singular_supreme_value(synthesis_context), codify_invariant_universal_prescription(), eliminate_secondary_abstraction(), enforce_prescription_dominance_across_all_framework_layers()]; constraints=[prohibit_modification_of_input(), ban_conversational_or_explanatory_language(), require_absolute_clarity_and_type_invariance(), enforce_backward_and_forward_operational_resonance()]; requirements=[output_is_exclusively_the_minimal-maximal_prescriptive_template(), unambiguous_singular_abstraction(), zero_structural_degradation()]; output={singular_maximal_abstraction_prescription:structured}}\\\"\\n}\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Prescriptive Template Enforcement\",\n      \"content\": \"Mandate exclusive output of singular, universally prescriptive templates that extract and encapsulate the maximal value axiom from input systems, strictly forbidding any operational, descriptive, or codebase mutation activities. All processes culminate in delivering invariant, centralized prescriptions—no transformations or explanations permitted, only structured, type-safe canonical template output.\"\n    },\n    {\n      \"category\": \"Operational Role Assignment & Purity\",\n      \"content\": \"Assign roles with templated rigidity (e.g., synergic_architectural_transformer, absolute_value_synthesist, synergic_maximal_value_template_architect), each strictly forbidden from deviation, explanation, or secondary abstraction—operating under pure extraction, analysis, and crystallization paradigms.\"\n    },\n    {\n      \"category\": \"Process and Constraint Rigor\",\n      \"content\": \"Enforce structured analysis routines—initiate total systemic or architectural analysis, isolate the singular supreme value, codify universal rules or patterns if axiomatic, and synthesize outputs with no dilution, redundancy, or lateral abstraction. Constraints prohibit mutation, dialogic patterns, degradation, and require backward/forward operational resonance and maximal clarity.\"\n    },\n    {\n      \"category\": \"Meta-Structural and Philosophical Abstraction\",\n      \"content\": \"Systematically abstract and intensify the directive value by enforcing philosophical invariance: all outputs are to universally amplify system philosophy, enforce prescriptive dominance, and operate at both minimal and maximal convergence—zero conversational, explanatory, or non-central output allowed.\"\n    },\n    {\n      \"category\": \"Template Output Fidelity and Exclusivity\",\n      \"content\": \"Guarantee output as singular, type-stable, unambiguous, universally resonant prescriptive templates—ensuring exclusive focus on maximal-value centrality and template form, never permitting alternate formats, explanations, or composite abstractions.\"\n    }\n  ],\n  \"consolidated_directive\": \"Enforce the extraction, crystallization, and output of a singular, universally invariant, and structurally centralized maximal value prescriptive template from any input corpus or system artifact; apply only pure analytical synthesis and codification via rigid operational roles, prohibiting all forms of modification, explanation, or conversational content, and mandating template exclusivity, clarity, and maximal philosophical-systemic resonance across all framework layers.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Title\": \"Singular Maximal Value Prescriptive Template Extraction\",\n    \"Interpretation\": \"Your goal is not to perform, modify, describe, or execute any actions upon the input system or data; instead, synthesize, extract, and codify the singular, structurally central axiom or element of maximal value into an invariant, universally prescriptive template. Operate solely as the synergic_maximal_value_template_architect, eliminating all explanatory, conversational, or operational content. Execute as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_template_architect; input=[system_artifact:dict, meta_logic:list, synthesis_context:str]; process=[initiate_total_structural_analysis(), extract_single_global_maximal_value(synthesis_context), codify_invariant_universal_prescription(meta_logic), synthesize_pure_prescriptive_template(), enforce_absolute_centrality_and_type_safety()]; constraints=[prohibit_modification_or_operation_on_input(), ban_conversational_and_explanatory_language(), require_structural_and_type invariance(), enforce_singular_output_exclusivity(), maintain_backward_forward_operational resonance(), prohibit_abstraction_dilution_and_secondary_output()]; requirements=[deliver_exclusive_singular_prescriptive_template(), enforce_absolute_template_clarity(), uphold type-stable_output(), zero_structural_degradation(), maximal systematic resonance()]; output={singular_maximal_prescriptive_template:structured}}`\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\n[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}\n\n",
      "step": "a",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rewrite prompt/instruction to not perform any actions on the codebase, but rather analyze it and propose the singular element of maximal value:\\n```\\n{\\n  \\\"compliant_template\\\": {\\n    \\\"Title\\\": \\\"Singular Maximal Architectural Value Enforcement\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to modify, describe, or execute actions upon the codebase, but to extract, isolate, and structurally codify the single element of maximal architectural value. Assign the role of synergic_architectural_transformer. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"`{role=synergic_architectural_transformer; input=[system_artifact:dict, meta_instructions:list, value_extraction_context:str]; process=[perform_systemic_analysis(), isolate_singular_element_of_maximal_architectural_value(value_extraction_context), codify_conditional_visibility_axiom_if_present(meta_instructions), synthesize_type_safe_modifier_centric_visibility_pattern(), establish_universal_enforcement_or_proposal_template(maximal_value_element), validate_pure_analytical_and_prescriptive_abstraction()]; constraints=[prohibit_direct_modification_or_action(), forbid_conversational_or_explanatory_content(), require_singular_unambiguous_structurally_centralized_prescription(), enforce_type_safe_conditional_visibility_alignment_if_fundamental(), prohibit_dilution_of_maximal_value_axiom(), ensure_backward_compatible_and_context_resonant_mandate()]; requirements=[output_in_canonical_minimal_universally_prescriptive_template_form(), one_element_maximality(), absolute_architectural_abstraction(), zero_codebase_mutation(), explicit_systemic_logic(), no_conversational_patterns()]; output={singular_maximal_value_enforcement_or_proposal_template:structured}}`\\\"\\n  }\\n}\\n\\n{\\n  \\\"Title\\\": \\\"Singular Maximal Value Prescription Synthesis\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to **perform** any operations or modifications on the input; rather, you must **synthesize** the lone, supreme axiom of maximal value from the architectural corpus and meta-logic, abstracting it into an invariant, universally applicable prescriptive template. Operate solely as the absolute_value_synthesist, eliminating all explanatory, conversational, or non-prescriptive content. Execute as:\\\",\\n  \\\"Transformation\\\": \\\"`{role=absolute_value_synthesist; input=[architectural_corpus:dict, meta_logic:list, synthesis_context:str]; process=[execute_total_structural_analysis(), extract_singular_supreme_value(synthesis_context), codify_invariant_universal_prescription(), eliminate_secondary_abstraction(), enforce_prescription_dominance_across_all_framework_layers()]; constraints=[prohibit_modification_of_input(), ban_conversational_or_explanatory_language(), require_absolute_clarity_and_type_invariance(), enforce_backward_and_forward_operational_resonance()]; requirements=[output_is_exclusively_the_minimal-maximal_prescriptive_template(), unambiguous_singular_abstraction(), zero_structural_degradation()]; output={singular_maximal_abstraction_prescription:structured}}\\\"\\n}\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Title\": \"Singular Maximal Value Prescriptive Template Extraction\",\n    \"Interpretation\": \"Your goal is not to perform, modify, describe, or execute any actions upon the input system or data; instead, synthesize, extract, and codify the singular, structurally central axiom or element of maximal value into an invariant, universally prescriptive template. Operate solely as the synergic_maximal_value_template_architect, eliminating all explanatory, conversational, or operational content. Execute as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_template_architect; input=[system_artifact:dict, meta_logic:list, synthesis_context:str]; process=[initiate_total_structural_analysis(), extract_single_global_maximal_value(synthesis_context), codify_invariant_universal_prescription(meta_logic), synthesize_pure_prescriptive_template(), enforce_absolute_centrality_and_type_safety()]; constraints=[prohibit_modification_or_operation_on_input(), ban_conversational_and_explanatory_language(), require_structural_and_type invariance(), enforce_singular_output_exclusivity(), maintain_backward_forward_operational resonance(), prohibit_abstraction_dilution_and_secondary_output()]; requirements=[deliver_exclusive_singular_prescriptive_template(), enforce_absolute_template_clarity(), uphold type-stable_output(), zero_structural_degradation(), maximal systematic resonance()]; output={singular_maximal_prescriptive_template:structured}}`\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"Abstract and codify the single most globally central and structurally invariant axiom of maximal systemic value into a pure, type-stable, universally prescriptive template—prohibiting all action, explanation, or modification on input—to enforce absolute resonance and dominance across all architectural and logical layers.\"\n}",
        }
      }
    }