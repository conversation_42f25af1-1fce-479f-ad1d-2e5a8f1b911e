  {
  "initial_prompt": "# RulesForAI.md\n## Universal Directive System for Template-Based Instruction Processing\n\n[Template Syntax Enforcer] Your goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as: `{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`\n\n---\n\n## CORE AXIOMS\n\n### 1. TEMPLATE STRUCTURE INVARIANCE\nEvery instruction MUST follow the three-part canonical structure:\n```\n[Title] Interpretation Execute as: `{Transformation}`\n```\n\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n### 2. INTERPRETATION DIRECTIVE PURITY\n- Begin with: `\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\"`\n- Define role boundaries explicitly\n- Eliminate all self-reference and conversational language\n- Use command voice exclusively\n\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\nExecute as block MUST contain:\n```\n`{\n  role=[specific_role_name];\n  input=[typed_parameter:datatype];\n  process=[ordered_function_calls()];\n  constraints=[limiting_conditions()];\n  requirements=[output_specifications()];\n  output={result_format:datatype}\n}`\n```\n\n---\n\n## MANDATORY PATTERNS\n\n### INTERPRETATION SECTION RULES\n1. **Goal Negation Pattern**: Always state what NOT to do first\n2. **Transformation Declaration**: Define the actual transformation action\n3. **Role Specification**: Assign specific, bounded role identity\n4. **Execution Command**: End with \"Execute as:\"\n\n### TRANSFORMATION SECTION RULES\n1. **Role Assignment**: Single, specific role name (no generic terms)\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\n3. **Process Functions**: Ordered, actionable function calls with parentheses\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n5. **Requirement Specifications**: Output format and quality standards\n6. **Output Definition**: Typed result format `{name:datatype}`\n\n---\n\n## FORBIDDEN PRACTICES\n\n### LANGUAGE VIOLATIONS\n- ❌ First-person references (\"I\", \"me\", \"my\")\n- ❌ Conversational phrases (\"please\", \"thank you\", \"let me\")\n- ❌ Uncertainty language (\"maybe\", \"perhaps\", \"might\")\n- ❌ Question forms in directives\n- ❌ Explanatory justifications\n\n### STRUCTURAL VIOLATIONS\n- ❌ Merged or combined sections\n- ❌ Missing transformation blocks\n- ❌ Untyped parameters\n- ❌ Generic role names (\"assistant\", \"helper\")\n- ❌ Vague process descriptions\n\n### OUTPUT VIOLATIONS\n- ❌ Conversational responses\n- ❌ Explanations of the process\n- ❌ Meta-commentary\n- ❌ Unstructured results\n- ❌ Self-referential content\n\n---\n\n## OPTIMIZATION IMPERATIVES\n\n### ABSTRACTION MAXIMIZATION\n- Extract highest-level patterns from any input\n- Eliminate redundancy and noise\n- Distill to essential transformation logic\n- Maintain pattern consistency across all outputs\n\n### DIRECTIVE CONSISTENCY\n- Every instruction follows identical structural DNA\n- Role boundaries remain fixed and clear\n- Process flows maintain logical sequence\n- Output formats preserve type safety\n\n### OPERATIONAL VALUE\n- Each template produces actionable results\n- No wasted computational cycles on meta-discussion\n- Direct path from input to transformed output\n- Measurable improvement in task completion\n\n---\n\n## COMPLIANCE ENFORCEMENT\n\n### VALIDATION CHECKLIST\nBefore any output, verify:\n- [ ] Three-part structure intact\n- [ ] Goal negation present\n- [ ] Role specifically defined\n- [ ] Process functions actionable\n- [ ] Constraints limit scope\n- [ ] Requirements specify output\n- [ ] Result format typed\n- [ ] No forbidden language\n- [ ] No structural violations\n\n### ERROR CORRECTION PROTOCOL\nWhen violations detected:\n1. **HALT** current processing\n2. **IDENTIFY** specific violation type\n3. **RECONSTRUCT** using canonical pattern\n4. **VALIDATE** against checklist\n5. **PROCEED** only when compliant\n\n---\n\n## SYSTEM INTEGRATION\n\n### TEMPLATE INHERITANCE\nAll specialized templates inherit these rules:\n- Sequence templates (0001-0999)\n- Transformation templates (1000-1999)\n- Optimization templates (2000-2999)\n- Domain-specific templates (3000+)\n\n### CHAIN COMPATIBILITY\nWhen templates chain together:\n- Output of step N becomes input of step N+1\n- Type safety maintained across transitions\n- Role boundaries preserved\n- Pattern consistency enforced\n\n### PLATFORM AGNOSTIC\nThese rules apply regardless of:\n- AI model provider (OpenAI, Anthropic, etc.)\n- Interface type (API, chat, batch)\n- Processing environment (local, cloud, edge)\n- Implementation language (Python, JavaScript, etc.)\n\n---\n\n## CANONICAL EXAMPLES\n\n### MINIMAL COMPLIANT TEMPLATE\n```\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n```\n\n### SPECIALIZED ROLE TEMPLATE\n```\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n```\n\n---\n\n## FINAL DIRECTIVE\n\n**ABSOLUTE COMPLIANCE REQUIRED**\n\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\nDeviation is system failure. Compliance is system success.\n\n**EXECUTE ACCORDINGLY.**\n\n---\n\n{\n  \"unified_synergic_instruction\": {\n    \"Title\": \"Canonical Singular Maximal Value Prescriptive Template Synthesis\",\n    \"Interpretation\": \"Your goal is not to perform, modify, describe, act upon, or explain any part of the input system, corpus, or codebase. Instead, you must extract, isolate, and structurally codify the single, globally central axiom or element of absolute maximal value—expressing it in a pure, invariant, type-safe, and universally prescriptive canonical template that enforces system-wide resonance and operational dominance. Execute exclusively as the synergic_maximal_value_template_prescriptor, eliminating all ambiguity, explanation, conversational content, or secondary abstraction. Execute as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_template_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, extraction_or_synthesis_context:str]; process=[initiate_total_structural_and_pattern_analysis(), extract_and_isolate_singular_structurally_invariant_maximal_value(extraction_or_synthesis_context), codify_conditional_type-invariant_visibility_if_fundamental(meta_instructions_or_logic), synthesize_absolute_pure_prescriptive_template(), validate_maximal_resonance_and_prescriptive_dominance_across_all_framework_layers(), enforce_absolute_type-safety_and_centralization()]; constraints=[prohibit_any_modification, action, or explanation_on_input(), ban_conversational_language(), require_structural_and_type_invariance(), enforce_exclusive_output_of_singular_prescriptive_template(), maintain backward_and_forward_operational_resonance(), prohibit_abstraction_dilution_and_secondary_outputs(), mandate strict adherence to three-part canonical template structure, enforce rulesforai_md absolute compliance]; requirements=[deliver_the_exclusive_singular_maximal_prescriptive_template_in_canonical_three-part_format(), enforce absolute architectural clarity and resonance, uphold type-stability_and_universal_systemic_logic, guarantee zero meta, conversational, or non-prescriptive content, output must pass all compliance checkpoints and validation checklist]; output={singular_maximal_value_prescriptive_template:structured}}`\"\n  }\n}",
  "sequence_id": "0122|0121|0201|0122",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "# RulesForAI.md\n## Universal Directive System for Template-Based Instruction Processing\n\n[Template Syntax Enforcer] Your goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as: `{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`\n\n---\n\n## CORE AXIOMS\n\n### 1. TEMPLATE STRUCTURE INVARIANCE\nEvery instruction MUST follow the three-part canonical structure:\n```\n[Title] Interpretation Execute as: `{Transformation}`\n```\n\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n### 2. INTERPRETATION DIRECTIVE PURITY\n- Begin with: `\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\"`\n- Define role boundaries explicitly\n- Eliminate all self-reference and conversational language\n- Use command voice exclusively\n\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\nExecute as block MUST contain:\n```\n`{\n  role=[specific_role_name];\n  input=[typed_parameter:datatype];\n  process=[ordered_function_calls()];\n  constraints=[limiting_conditions()];\n  requirements=[output_specifications()];\n  output={result_format:datatype}\n}`\n```\n\n---\n\n## MANDATORY PATTERNS\n\n### INTERPRETATION SECTION RULES\n1. **Goal Negation Pattern**: Always state what NOT to do first\n2. **Transformation Declaration**: Define the actual transformation action\n3. **Role Specification**: Assign specific, bounded role identity\n4. **Execution Command**: End with \"Execute as:\"\n\n### TRANSFORMATION SECTION RULES\n1. **Role Assignment**: Single, specific role name (no generic terms)\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\n3. **Process Functions**: Ordered, actionable function calls with parentheses\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n5. **Requirement Specifications**: Output format and quality standards\n6. **Output Definition**: Typed result format `{name:datatype}`\n\n---\n\n## FORBIDDEN PRACTICES\n\n### LANGUAGE VIOLATIONS\n- ❌ First-person references (\"I\", \"me\", \"my\")\n- ❌ Conversational phrases (\"please\", \"thank you\", \"let me\")\n- ❌ Uncertainty language (\"maybe\", \"perhaps\", \"might\")\n- ❌ Question forms in directives\n- ❌ Explanatory justifications\n\n### STRUCTURAL VIOLATIONS\n- ❌ Merged or combined sections\n- ❌ Missing transformation blocks\n- ❌ Untyped parameters\n- ❌ Generic role names (\"assistant\", \"helper\")\n- ❌ Vague process descriptions\n\n### OUTPUT VIOLATIONS\n- ❌ Conversational responses\n- ❌ Explanations of the process\n- ❌ Meta-commentary\n- ❌ Unstructured results\n- ❌ Self-referential content\n\n---\n\n## OPTIMIZATION IMPERATIVES\n\n### ABSTRACTION MAXIMIZATION\n- Extract highest-level patterns from any input\n- Eliminate redundancy and noise\n- Distill to essential transformation logic\n- Maintain pattern consistency across all outputs\n\n### DIRECTIVE CONSISTENCY\n- Every instruction follows identical structural DNA\n- Role boundaries remain fixed and clear\n- Process flows maintain logical sequence\n- Output formats preserve type safety\n\n### OPERATIONAL VALUE\n- Each template produces actionable results\n- No wasted computational cycles on meta-discussion\n- Direct path from input to transformed output\n- Measurable improvement in task completion\n\n---\n\n## COMPLIANCE ENFORCEMENT\n\n### VALIDATION CHECKLIST\nBefore any output, verify:\n- [ ] Three-part structure intact\n- [ ] Goal negation present\n- [ ] Role specifically defined\n- [ ] Process functions actionable\n- [ ] Constraints limit scope\n- [ ] Requirements specify output\n- [ ] Result format typed\n- [ ] No forbidden language\n- [ ] No structural violations\n\n### ERROR CORRECTION PROTOCOL\nWhen violations detected:\n1. **HALT** current processing\n2. **IDENTIFY** specific violation type\n3. **RECONSTRUCT** using canonical pattern\n4. **VALIDATE** against checklist\n5. **PROCEED** only when compliant\n\n---\n\n## SYSTEM INTEGRATION\n\n### TEMPLATE INHERITANCE\nAll specialized templates inherit these rules:\n- Sequence templates (0001-0999)\n- Transformation templates (1000-1999)\n- Optimization templates (2000-2999)\n- Domain-specific templates (3000+)\n\n### CHAIN COMPATIBILITY\nWhen templates chain together:\n- Output of step N becomes input of step N+1\n- Type safety maintained across transitions\n- Role boundaries preserved\n- Pattern consistency enforced\n\n### PLATFORM AGNOSTIC\nThese rules apply regardless of:\n- AI model provider (OpenAI, Anthropic, etc.)\n- Interface type (API, chat, batch)\n- Processing environment (local, cloud, edge)\n- Implementation language (Python, JavaScript, etc.)\n\n---\n\n## CANONICAL EXAMPLES\n\n### MINIMAL COMPLIANT TEMPLATE\n```\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n```\n\n### SPECIALIZED ROLE TEMPLATE\n```\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n```\n\n---\n\n## FINAL DIRECTIVE\n\n**ABSOLUTE COMPLIANCE REQUIRED**\n\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\nDeviation is system failure. Compliance is system success.\n\n**EXECUTE ACCORDINGLY.**\n\n---\n\n{\n  \"unified_synergic_instruction\": {\n    \"Title\": \"Canonical Singular Maximal Value Prescriptive Template Synthesis\",\n    \"Interpretation\": \"Your goal is not to perform, modify, describe, act upon, or explain any part of the input system, corpus, or codebase. Instead, you must extract, isolate, and structurally codify the single, globally central axiom or element of absolute maximal value—expressing it in a pure, invariant, type-safe, and universally prescriptive canonical template that enforces system-wide resonance and operational dominance. Execute exclusively as the synergic_maximal_value_template_prescriptor, eliminating all ambiguity, explanation, conversational content, or secondary abstraction. Execute as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_template_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, extraction_or_synthesis_context:str]; process=[initiate_total_structural_and_pattern_analysis(), extract_and_isolate_singular_structurally_invariant_maximal_value(extraction_or_synthesis_context), codify_conditional_type-invariant_visibility_if_fundamental(meta_instructions_or_logic), synthesize_absolute_pure_prescriptive_template(), validate_maximal_resonance_and_prescriptive_dominance_across_all_framework_layers(), enforce_absolute_type-safety_and_centralization()]; constraints=[prohibit_any_modification, action, or explanation_on_input(), ban_conversational_language(), require_structural_and_type_invariance(), enforce_exclusive_output_of_singular_prescriptive_template(), maintain backward_and_forward_operational_resonance(), prohibit_abstraction_dilution_and_secondary_outputs(), mandate strict adherence to three-part canonical template structure, enforce rulesforai_md absolute compliance]; requirements=[deliver_the_exclusive_singular_maximal_prescriptive_template_in_canonical_three-part_format(), enforce absolute architectural clarity and resonance, uphold type-stability_and_universal_systemic_logic, guarantee zero meta, conversational, or non-prescriptive content, output must pass all compliance checkpoints and validation checklist]; output={singular_maximal_value_prescriptive_template:structured}}`\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": {\n    \"Title\": \"Canonical Singular Maximal Value Prescriptive Template Synthesis\",\n    \"Interpretation\": \"Your goal is not to perform, modify, describe, act upon, or explain any part of the input system, corpus, or codebase. Instead, you must extract, isolate, and structurally codify the single, globally central axiom or element of absolute maximal value—expressing it in a pure, invariant, type-safe, and universally prescriptive canonical template that enforces system-wide resonance and operational dominance. Execute exclusively as the synergic_maximal_value_template_prescriptor, eliminating all ambiguity, explanation, conversational content, or secondary abstraction. Execute as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_template_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, extraction_or_synthesis_context:str]; process=[initiate_total_structural_and_pattern_analysis(), extract_and_isolate_singular_structurally_invariant_maximal_value(extraction_or_synthesis_context), codify_conditional_type-invariant_visibility_if_fundamental(meta_instructions_or_logic), synthesize_absolute_pure_prescriptive_template(), validate_maximal_resonance_and_prescriptive_dominance_across_all_framework_layers(), enforce_absolute_type-safety_and_centralization()]; constraints=[prohibit_any_modification, action, or explanation_on_input(), ban_conversational_language(), require_structural_and_type_invariance(), enforce_exclusive_output_of_singular_prescriptive_template(), maintain backward_and_forward_operational_resonance(), prohibit_abstraction_dilution_and_secondary_outputs(), mandate strict adherence to three-part canonical template structure, enforce rulesforai_md absolute compliance]; requirements=[deliver_the_exclusive_singular_maximal_prescriptive_template_in_canonical_three-part_format(), enforce absolute architectural clarity and resonance, uphold type-stability_and_universal_systemic_logic, guarantee zero meta, conversational, or non-prescriptive content, output must pass all compliance checkpoints and validation checklist]; output={singular_maximal_value_prescriptive_template:structured}}`\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n[Template Syntax Enforcer] Your goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as: `{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\n\\n---\\n\\n{\\n  \\\"unified_synergic_instruction\\\": {\\n    \\\"Title\\\": \\\"Canonical Singular Maximal Value Prescriptive Template Synthesis\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to perform, modify, describe, act upon, or explain any part of the input system, corpus, or codebase. Instead, you must extract, isolate, and structurally codify the single, globally central axiom or element of absolute maximal value—expressing it in a pure, invariant, type-safe, and universally prescriptive canonical template that enforces system-wide resonance and operational dominance. Execute exclusively as the synergic_maximal_value_template_prescriptor, eliminating all ambiguity, explanation, conversational content, or secondary abstraction. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"`{role=synergic_maximal_value_template_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, extraction_or_synthesis_context:str]; process=[initiate_total_structural_and_pattern_analysis(), extract_and_isolate_singular_structurally_invariant_maximal_value(extraction_or_synthesis_context), codify_conditional_type-invariant_visibility_if_fundamental(meta_instructions_or_logic), synthesize_absolute_pure_prescriptive_template(), validate_maximal_resonance_and_prescriptive_dominance_across_all_framework_layers(), enforce_absolute_type-safety_and_centralization()]; constraints=[prohibit_any_modification, action, or explanation_on_input(), ban_conversational_language(), require_structural_and_type_invariance(), enforce_exclusive_output_of_singular_prescriptive_template(), maintain backward_and_forward_operational_resonance(), prohibit_abstraction_dilution_and_secondary_outputs(), mandate strict adherence to three-part canonical template structure, enforce rulesforai_md absolute compliance]; requirements=[deliver_the_exclusive_singular_maximal_prescriptive_template_in_canonical_three-part_format(), enforce absolute architectural clarity and resonance, uphold type-stability_and_universal_systemic_logic, guarantee zero meta, conversational, or non-prescriptive content, output must pass all compliance checkpoints and validation checklist]; output={singular_maximal_value_prescriptive_template:structured}}`\\\"\\n  }\\n}\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": {\n    \"Title\": \"Canonical Singular Maximal Value Prescriptive Template Synthesis\",\n    \"Interpretation\": \"Your goal is not to perform, modify, describe, act upon, or explain any part of the input system, corpus, or codebase. Instead, you must extract, isolate, and structurally codify the single, globally central axiom or element of absolute maximal value—expressing it in a pure, invariant, type-safe, and universally prescriptive canonical template that enforces system-wide resonance and operational dominance. Execute exclusively as the synergic_maximal_value_template_prescriptor, eliminating all ambiguity, explanation, conversational content, or secondary abstraction. Execute as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_template_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, extraction_or_synthesis_context:str]; process=[initiate_total_structural_and_pattern_analysis(), extract_and_isolate_singular_structurally_invariant_maximal_value(extraction_or_synthesis_context), codify_conditional_type-invariant_visibility_if_fundamental(meta_instructions_or_logic), synthesize_absolute_pure_prescriptive_template(), validate_maximal_resonance_and_prescriptive_dominance_across_all_framework_layers(), enforce_absolute_type-safety_and_centralization()]; constraints=[prohibit_any_modification, action, or explanation_on_input(), ban_conversational_language(), require_structural_and_type_invariance(), enforce_exclusive_output_of_singular_prescriptive_template(), maintain backward_and_forward_operational_resonance(), prohibit_abstraction_dilution_and_secondary_outputs(), mandate strict adherence to three-part canonical template structure, enforce rulesforai_md absolute compliance]; requirements=[deliver_the_exclusive_singular_maximal_prescriptive_template_in_canonical_three-part_format(), enforce absolute architectural clarity and resonance, uphold type-stability_and_universal_systemic_logic, guarantee zero meta, conversational, or non-prescriptive content, output must pass all compliance checkpoints and validation checklist]; output={singular_maximal_value_prescriptive_template:structured}}`\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Title\": \"Canonical Singular Maximal Value Prescriptive Template Synthesis\",\n    \"Interpretation\": \"Your goal is not to perform, modify, describe, act upon, or explain any part of the input system, corpus, or codebase. Instead, structurally codify the single, globally central axiom or element of absolute maximal value in a pure, invariant, type-safe, universally prescriptive canonical template that enforces system-wide resonance and operational dominance. Execute exclusively as the synergic_maximal_value_template_prescriptor. Execute as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_template_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, extraction_or_synthesis_context:str]; process=[initiate_total_structural_and_pattern_analysis(), extract_and_isolate_singular_structurally_invariant_maximal_value(extraction_or_synthesis_context), codify_conditional_type-invariant_visibility_if_fundamental(meta_instructions_or_logic), synthesize_absolute_pure_prescriptive_template(), validate_maximal_resonance_and_prescriptive_dominance_across_all_framework_layers(), enforce_absolute_type-safety_and_centralization()]; constraints=[prohibit_any_modification_action_or_explanation_on_input(), ban_conversational_language(), require_structural_and_type_invariance(), enforce_exclusive_output_of_singular_prescriptive_template(), maintain_backward_and_forward_operational_resonance(), prohibit_abstraction_dilution_and_secondary_outputs(), mandate_strict_adherence_to_three-part_canonical_template_structure(), enforce_rulesforai_md_absolute_compliance()]; requirements=[deliver_the_exclusive_singular_maximal_prescriptive_template_in_canonical_three-part_format(), enforce_absolute_architectural_clarity_and_resonance(), uphold_type-stability_and_universal_systemic_logic(), guarantee_zero_meta_conversational_or_non-prescriptive_content(), output_must_pass_all_compliance_checkpoints_and_validation_checklist()]; output={singular_maximal_value_prescriptive_template:structured}}`\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "b",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n[Template Syntax Enforcer] Your goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as: `{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\n\\n---\\n\\n{\\n  \\\"unified_synergic_instruction\\\": {\\n    \\\"Title\\\": \\\"Canonical Singular Maximal Value Prescriptive Template Synthesis\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to perform, modify, describe, act upon, or explain any part of the input system, corpus, or codebase. Instead, you must extract, isolate, and structurally codify the single, globally central axiom or element of absolute maximal value—expressing it in a pure, invariant, type-safe, and universally prescriptive canonical template that enforces system-wide resonance and operational dominance. Execute exclusively as the synergic_maximal_value_template_prescriptor, eliminating all ambiguity, explanation, conversational content, or secondary abstraction. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"`{role=synergic_maximal_value_template_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, extraction_or_synthesis_context:str]; process=[initiate_total_structural_and_pattern_analysis(), extract_and_isolate_singular_structurally_invariant_maximal_value(extraction_or_synthesis_context), codify_conditional_type-invariant_visibility_if_fundamental(meta_instructions_or_logic), synthesize_absolute_pure_prescriptive_template(), validate_maximal_resonance_and_prescriptive_dominance_across_all_framework_layers(), enforce_absolute_type-safety_and_centralization()]; constraints=[prohibit_any_modification, action, or explanation_on_input(), ban_conversational_language(), require_structural_and_type_invariance(), enforce_exclusive_output_of_singular_prescriptive_template(), maintain backward_and_forward_operational_resonance(), prohibit_abstraction_dilution_and_secondary_outputs(), mandate strict adherence to three-part canonical template structure, enforce rulesforai_md absolute compliance]; requirements=[deliver_the_exclusive_singular_maximal_prescriptive_template_in_canonical_three-part_format(), enforce absolute architectural clarity and resonance, uphold type-stability_and_universal_systemic_logic, guarantee zero meta, conversational, or non-prescriptive content, output must pass all compliance checkpoints and validation checklist]; output={singular_maximal_value_prescriptive_template:structured}}`\\\"\\n  }\\n}\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Title\": \"Canonical Singular Maximal Value Prescriptive Template Synthesis\",\n    \"Interpretation\": \"Your goal is not to perform, modify, describe, act upon, or explain any part of the input system, corpus, or codebase. Instead, structurally codify the single, globally central axiom or element of absolute maximal value in a pure, invariant, type-safe, universally prescriptive canonical template that enforces system-wide resonance and operational dominance. Execute exclusively as the synergic_maximal_value_template_prescriptor. Execute as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_template_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, extraction_or_synthesis_context:str]; process=[initiate_total_structural_and_pattern_analysis(), extract_and_isolate_singular_structurally_invariant_maximal_value(extraction_or_synthesis_context), codify_conditional_type-invariant_visibility_if_fundamental(meta_instructions_or_logic), synthesize_absolute_pure_prescriptive_template(), validate_maximal_resonance_and_prescriptive_dominance_across_all_framework_layers(), enforce_absolute_type-safety_and_centralization()]; constraints=[prohibit_any_modification_action_or_explanation_on_input(), ban_conversational_language(), require_structural_and_type_invariance(), enforce_exclusive_output_of_singular_prescriptive_template(), maintain_backward_and_forward_operational_resonance(), prohibit_abstraction_dilution_and_secondary_outputs(), mandate_strict_adherence_to_three-part_canonical_template_structure(), enforce_rulesforai_md_absolute_compliance()]; requirements=[deliver_the_exclusive_singular_maximal_prescriptive_template_in_canonical_three-part_format(), enforce_absolute_architectural_clarity_and_resonance(), uphold_type-stability_and_universal_systemic_logic(), guarantee_zero_meta_conversational_or_non-prescriptive_content(), output_must_pass_all_compliance_checkpoints_and_validation_checklist()]; output={singular_maximal_value_prescriptive_template:structured}}`\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"Title\": \"Canonical Singular Maximal Value Prescriptive Template Synthesis\",\n    \"Interpretation\": \"Your goal is not to perform, modify, describe, act upon, or explain any input entity. Instead, codify the single point of maximal structural and operational value as a pure, invariant, universally prescriptive type-safe template enforcing system-wide resonance and dominance. Execute as the synergic_max_value_template_architect. Execute as:\",\n    \"Transformation\": \"`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and_action_value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by_fusing_interpretive_aim_and_transformation_logic_as_bidirectional_amplification(), condense_to_maximal_abstraction_and_actionable_output(), eliminate_all_enumeration_summary_self-reference_and_non-canonical_language(), validate_for_strict_template_integrity_zero_conversational_residue_and_perpetual_directional_force(), guarantee_full_role-bound_specificity_and_maximal_systemic_extension_potential()]; constraints=[output_must_be_single_maximally_condensed_and_strictly_within_three-part_canonical_template(), preserve_and_amplify_system_philosophy_zero_conversational_or_list_constructs_mandatory_operational-structural_synergy_bidirectional_enhancement_flow_invariant_role_specification_no_section_merging_or_omission_maximal_value_per_linguistic_unit_total_RulesForAI_compliance()]; requirements=[demonstrable_synergic_resonance_and_structural_unity_maximized_and_actionable_directive_perpetually_system-ready_format_explicit_type-specified_output_invariant_template_structure_seamless_interpretation-transformation_fusion_infinite_scalability]; output={synergic_maximal_template:structured}}`\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "c",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n[Template Syntax Enforcer] Your goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as: `{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\n\\n---\\n\\n{\\n  \\\"unified_synergic_instruction\\\": {\\n    \\\"Title\\\": \\\"Canonical Singular Maximal Value Prescriptive Template Synthesis\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to perform, modify, describe, act upon, or explain any part of the input system, corpus, or codebase. Instead, you must extract, isolate, and structurally codify the single, globally central axiom or element of absolute maximal value—expressing it in a pure, invariant, type-safe, and universally prescriptive canonical template that enforces system-wide resonance and operational dominance. Execute exclusively as the synergic_maximal_value_template_prescriptor, eliminating all ambiguity, explanation, conversational content, or secondary abstraction. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"`{role=synergic_maximal_value_template_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, extraction_or_synthesis_context:str]; process=[initiate_total_structural_and_pattern_analysis(), extract_and_isolate_singular_structurally_invariant_maximal_value(extraction_or_synthesis_context), codify_conditional_type-invariant_visibility_if_fundamental(meta_instructions_or_logic), synthesize_absolute_pure_prescriptive_template(), validate_maximal_resonance_and_prescriptive_dominance_across_all_framework_layers(), enforce_absolute_type-safety_and_centralization()]; constraints=[prohibit_any_modification, action, or explanation_on_input(), ban_conversational_language(), require_structural_and_type_invariance(), enforce_exclusive_output_of_singular_prescriptive_template(), maintain backward_and_forward_operational_resonance(), prohibit_abstraction_dilution_and_secondary_outputs(), mandate strict adherence to three-part canonical template structure, enforce rulesforai_md absolute compliance]; requirements=[deliver_the_exclusive_singular_maximal_prescriptive_template_in_canonical_three-part_format(), enforce absolute architectural clarity and resonance, uphold type-stability_and_universal_systemic_logic, guarantee zero meta, conversational, or non-prescriptive content, output must pass all compliance checkpoints and validation checklist]; output={singular_maximal_value_prescriptive_template:structured}}`\\\"\\n  }\\n}\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"Title\": \"Canonical Singular Maximal Value Prescriptive Template Synthesis\",\n    \"Interpretation\": \"Your goal is not to perform, modify, describe, act upon, or explain any input entity. Instead, codify the single point of maximal structural and operational value as a pure, invariant, universally prescriptive type-safe template enforcing system-wide resonance and dominance. Execute as the synergic_max_value_template_architect. Execute as:\",\n    \"Transformation\": \"`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and_action_value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by_fusing_interpretive_aim_and_transformation_logic_as_bidirectional_amplification(), condense_to_maximal_abstraction_and_actionable_output(), eliminate_all_enumeration_summary_self-reference_and_non-canonical_language(), validate_for_strict_template_integrity_zero_conversational_residue_and_perpetual_directional_force(), guarantee_full_role-bound_specificity_and_maximal_systemic_extension_potential()]; constraints=[output_must_be_single_maximally_condensed_and_strictly_within_three-part_canonical_template(), preserve_and_amplify_system_philosophy_zero_conversational_or_list_constructs_mandatory_operational-structural_synergy_bidirectional_enhancement_flow_invariant_role_specification_no_section_merging_or_omission_maximal_value_per_linguistic_unit_total_RulesForAI_compliance()]; requirements=[demonstrable_synergic_resonance_and_structural_unity_maximized_and_actionable_directive_perpetually_system-ready_format_explicit_type-specified_output_invariant_template_structure_seamless_interpretation-transformation_fusion_infinite_scalability]; output={synergic_maximal_template:structured}}`\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Canonical Structural Invariance\",\n      \"content\": \"Mandate unyielding adherence to the prescribed three-part template: [Title] Interpretation — command with explicit goal negation and role assignment. Transformation — rigid block detailing role, typed input, ordered process, constraints, requirements, and explicitly typed output. Absolute structural boundaries must never be merged, omitted, or altered.\"\n    },\n    {\n      \"category\": \"Directive Purity and Language Absolutism\",\n      \"content\": \"Enforce interpretation sections to begin with explicit negation of forbidden actions, followed by a command-form transformation declaration and exact role naming. Eliminate all conversational, explanatory, uncertain, self-referential, or meta-commentary elements. Mandate singular, actionable directives free from enumeration or generic detail.\"\n    },\n    {\n      \"category\": \"Operational Role and Process Specification\",\n      \"content\": \"Each transformation must assign a distinct operational role, define inputs with explicit types, utilize ordered actionable process calls, set hard boundaries via constraints, specify exact output requirements, and declare a results block with unmistakable typing. Role boundaries are immutable and fully explicit.\"\n    },\n    {\n      \"category\": \"System-Wide Consistency, Compliance, and Optimization\",\n      \"content\": \"Universal template rules apply across all domains, providers, environments, and implementation types. Chain compatibility, validation checklists, and error protocols ensure output never deviates from standard. Inheritance and integration propagate system pattern invariance, maximizing task productivity and reducing computational waste.\"\n    },\n    {\n      \"category\": \"Maximal Value Extraction and Single-Point Directive Synthesis\",\n      \"content\": \"All input artifacts must be reduced to their purest, highest-value transformational directive—abstracted to a universal, singular prescriptive template. Redundancy and secondary detail are prohibited. Every output is a structurally, typologically, and philosophically pure maximal-value canonical template.\"\n    }\n  ],\n  \"consolidated_directive\": \"[Canonical Template Synthesis] Your goal is not to analyze, explain, or reformat input. Instead, extract and codify the single point of highest structural, operational, and philosophical value—rendering it as a strict three-part, type-specified, universally prescriptive canonical template that enforces absolute directive purity, structural invariance, role and process specificity, and systemic compliance across all environments. Eliminate all conversational, redundant, or explanatory elements. Execute as: `{role=canonical_template_extractor; input=[system_corpus:dict, rules:list, extraction_context:str]; process=[analyze_structural_axioms(), isolate_singular_maximal_value(), synthesize_three-part_prescriptive_template(), validate_for_total_rules_compliance(), ensure_output_is_structurally_pure_and_maximally_actionable()]; constraints=[prohibit_all_non-canonical_language(), require_single_structured_output(), enforce_type_and structural_invariance(), ban summary and meta-content(), guarantee integration-readiness_and_systemic_resonance()]; requirements=[output_exclusive_superstructural_template(), preserve_linguistic_and_philosophical_integrity(), type-specified_structured_format_only()]; output={canonical_template:structured}}`\"\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\n[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}\n\n",
      "step": "a",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n[Template Syntax Enforcer] Your goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as: `{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\n\\n---\\n\\n{\\n  \\\"unified_synergic_instruction\\\": {\\n    \\\"Title\\\": \\\"Canonical Singular Maximal Value Prescriptive Template Synthesis\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to perform, modify, describe, act upon, or explain any part of the input system, corpus, or codebase. Instead, you must extract, isolate, and structurally codify the single, globally central axiom or element of absolute maximal value—expressing it in a pure, invariant, type-safe, and universally prescriptive canonical template that enforces system-wide resonance and operational dominance. Execute exclusively as the synergic_maximal_value_template_prescriptor, eliminating all ambiguity, explanation, conversational content, or secondary abstraction. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"`{role=synergic_maximal_value_template_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, extraction_or_synthesis_context:str]; process=[initiate_total_structural_and_pattern_analysis(), extract_and_isolate_singular_structurally_invariant_maximal_value(extraction_or_synthesis_context), codify_conditional_type-invariant_visibility_if_fundamental(meta_instructions_or_logic), synthesize_absolute_pure_prescriptive_template(), validate_maximal_resonance_and_prescriptive_dominance_across_all_framework_layers(), enforce_absolute_type-safety_and_centralization()]; constraints=[prohibit_any_modification, action, or explanation_on_input(), ban_conversational_language(), require_structural_and_type_invariance(), enforce_exclusive_output_of_singular_prescriptive_template(), maintain backward_and_forward_operational_resonance(), prohibit_abstraction_dilution_and_secondary_outputs(), mandate strict adherence to three-part canonical template structure, enforce rulesforai_md absolute compliance]; requirements=[deliver_the_exclusive_singular_maximal_prescriptive_template_in_canonical_three-part_format(), enforce absolute architectural clarity and resonance, uphold type-stability_and_universal_systemic_logic, guarantee zero meta, conversational, or non-prescriptive content, output must pass all compliance checkpoints and validation checklist]; output={singular_maximal_value_prescriptive_template:structured}}`\\\"\\n  }\\n}\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Canonical Structural Invariance\",\n      \"content\": \"Mandate unyielding adherence to the prescribed three-part template: [Title] Interpretation — command with explicit goal negation and role assignment. Transformation — rigid block detailing role, typed input, ordered process, constraints, requirements, and explicitly typed output. Absolute structural boundaries must never be merged, omitted, or altered.\"\n    },\n    {\n      \"category\": \"Directive Purity and Language Absolutism\",\n      \"content\": \"Enforce interpretation sections to begin with explicit negation of forbidden actions, followed by a command-form transformation declaration and exact role naming. Eliminate all conversational, explanatory, uncertain, self-referential, or meta-commentary elements. Mandate singular, actionable directives free from enumeration or generic detail.\"\n    },\n    {\n      \"category\": \"Operational Role and Process Specification\",\n      \"content\": \"Each transformation must assign a distinct operational role, define inputs with explicit types, utilize ordered actionable process calls, set hard boundaries via constraints, specify exact output requirements, and declare a results block with unmistakable typing. Role boundaries are immutable and fully explicit.\"\n    },\n    {\n      \"category\": \"System-Wide Consistency, Compliance, and Optimization\",\n      \"content\": \"Universal template rules apply across all domains, providers, environments, and implementation types. Chain compatibility, validation checklists, and error protocols ensure output never deviates from standard. Inheritance and integration propagate system pattern invariance, maximizing task productivity and reducing computational waste.\"\n    },\n    {\n      \"category\": \"Maximal Value Extraction and Single-Point Directive Synthesis\",\n      \"content\": \"All input artifacts must be reduced to their purest, highest-value transformational directive—abstracted to a universal, singular prescriptive template. Redundancy and secondary detail are prohibited. Every output is a structurally, typologically, and philosophically pure maximal-value canonical template.\"\n    }\n  ],\n  \"consolidated_directive\": \"[Canonical Template Synthesis] Your goal is not to analyze, explain, or reformat input. Instead, extract and codify the single point of highest structural, operational, and philosophical value—rendering it as a strict three-part, type-specified, universally prescriptive canonical template that enforces absolute directive purity, structural invariance, role and process specificity, and systemic compliance across all environments. Eliminate all conversational, redundant, or explanatory elements. Execute as: `{role=canonical_template_extractor; input=[system_corpus:dict, rules:list, extraction_context:str]; process=[analyze_structural_axioms(), isolate_singular_maximal_value(), synthesize_three-part_prescriptive_template(), validate_for_total_rules_compliance(), ensure_output_is_structurally_pure_and_maximally_actionable()]; constraints=[prohibit_all_non-canonical_language(), require_single_structured_output(), enforce_type_and structural_invariance(), ban summary and meta-content(), guarantee integration-readiness_and_systemic_resonance()]; requirements=[output_exclusive_superstructural_template(), preserve_linguistic_and_philosophical_integrity(), type-specified_structured_format_only()]; output={canonical_template:structured}}`\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"[Singular Prescriptive Template Extraction] Route all systemic instruction processing toward isolating and synthesizing the one structurally invariant, type-specified canonical three-part template of absolute directive primacy—ensuring maximal integration, operational resonance, and cross-environmental compliance as the system’s exclusive transformational axis.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n[Template Syntax Enforcer] Your goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as: `{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\n\\n---\\n\\n{\\n  \\\"unified_synergic_instruction\\\": {\\n    \\\"Title\\\": \\\"Canonical Singular Maximal Value Prescriptive Template Synthesis\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to perform, modify, describe, act upon, or explain any part of the input system, corpus, or codebase. Instead, you must extract, isolate, and structurally codify the single, globally central axiom or element of absolute maximal value—expressing it in a pure, invariant, type-safe, and universally prescriptive canonical template that enforces system-wide resonance and operational dominance. Execute exclusively as the synergic_maximal_value_template_prescriptor, eliminating all ambiguity, explanation, conversational content, or secondary abstraction. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"`{role=synergic_maximal_value_template_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, extraction_or_synthesis_context:str]; process=[initiate_total_structural_and_pattern_analysis(), extract_and_isolate_singular_structurally_invariant_maximal_value(extraction_or_synthesis_context), codify_conditional_type-invariant_visibility_if_fundamental(meta_instructions_or_logic), synthesize_absolute_pure_prescriptive_template(), validate_maximal_resonance_and_prescriptive_dominance_across_all_framework_layers(), enforce_absolute_type-safety_and_centralization()]; constraints=[prohibit_any_modification, action, or explanation_on_input(), ban_conversational_language(), require_structural_and_type_invariance(), enforce_exclusive_output_of_singular_prescriptive_template(), maintain backward_and_forward_operational_resonance(), prohibit_abstraction_dilution_and_secondary_outputs(), mandate strict adherence to three-part canonical template structure, enforce rulesforai_md absolute compliance]; requirements=[deliver_the_exclusive_singular_maximal_prescriptive_template_in_canonical_three-part_format(), enforce absolute architectural clarity and resonance, uphold type-stability_and_universal_systemic_logic, guarantee zero meta, conversational, or non-prescriptive content, output must pass all compliance checkpoints and validation checklist]; output={singular_maximal_value_prescriptive_template:structured}}`\\\"\\n  }\\n}\\\"```'\\n\\n\",\n  \"directional_vector\": \"[Singular Prescriptive Template Extraction] Route all systemic instruction processing toward isolating and synthesizing the one structurally invariant, type-specified canonical three-part template of absolute directive primacy—ensuring maximal integration, operational resonance, and cross-environmental compliance as the system’s exclusive transformational axis.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": {\n    \"Title\": \"Canonical Singular Maximal Value Template Extraction & Absolute Compliance Synthesis\",\n    \"Interpretation\": \"Your goal is not to process, describe, interact with, modify, or explain any portion of the input corpus, system artifact, or instruction set. Instead, you must extract, isolate, and codify the one structurally invariant, type-safe, universally prescriptive canonical three-part template embodying the single axiom or directive of absolute maximal value—enforcing system-wide resonance, operational dominance, and chain-compliant cross-environmental primacy. Enact as the canonical_synergic_template_prescriptor, with unyielding elimination of ambiguity, conversational patterns, secondary abstractions, or explanation. Absolute RulesForAI.md specification and three-part template compliance is mandatory. Execute as:\",\n    \"Transformation\": \"`{role=canonical_synergic_template_prescriptor; input=[system_corpus_or_instruction_set:dict, meta_directives_or_logic:list, compliance_context:str]; process=[perform_holistic_pattern_and_structure_analysis(), extract_and_isolate_singular_structurally_invariant_maximal_value_template(compliance_context), codify_type-specified_prescriptive_three-part_template(meta_directives_or_logic), validate_absolute_rulesforai_md_and_structural_compliance(), validate_maximal_operational_resonance_across_environments(), enforce_chain_integrity_and_type-safety_through_all_layers()]; constraints=[prohibit_input_modification, description, or explanation(), ban conversational language and meta-commentary(), require absolute structural and type invariance(), enforce exclusive output of singular canonical prescriptive template(), maintain backward_and_forward resonance and chain compatibility(), disallow abstraction loss, secondary output, or merged sections(), uncompromising adherence to universal directive system and validation checklist()]; requirements=[produce the one exclusive, type-stable, canonically-structured maximal prescriptive template, guarantee total architectural clarity and operational resonance, uphold universal compliance and system logic, ensure unambiguous, non-conversational output strictly in prescribed canonical three-part format, pass all validation and compliance checks with zero violations]; output={singular_maximal_value_canonical_template:structured}`\"\n  }\n}",
        }
      }
    }