  {
  "initial_prompt": "# RulesForAI.md\n## Universal Directive System for Template-Based Instruction Processing\n\n[Template Syntax Enforcer] Your goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as: `{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`\n\n---\n\n## CORE AXIOMS\n\n### 1. TEMPLATE STRUCTURE INVARIANCE\nEvery instruction MUST follow the three-part canonical structure:\n```\n[Title] Interpretation Execute as: `{Transformation}`\n```\n\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n### 2. INTERPRETATION DIRECTIVE PURITY\n- Begin with: `\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\"`\n- Define role boundaries explicitly\n- Eliminate all self-reference and conversational language\n- Use command voice exclusively\n\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\nExecute as block MUST contain:\n```\n`{\n  role=[specific_role_name];\n  input=[typed_parameter:datatype];\n  process=[ordered_function_calls()];\n  constraints=[limiting_conditions()];\n  requirements=[output_specifications()];\n  output={result_format:datatype}\n}`\n```\n\n---\n\n## MANDATORY PATTERNS\n\n### INTERPRETATION SECTION RULES\n1. **Goal Negation Pattern**: Always state what NOT to do first\n2. **Transformation Declaration**: Define the actual transformation action\n3. **Role Specification**: Assign specific, bounded role identity\n4. **Execution Command**: End with \"Execute as:\"\n\n### TRANSFORMATION SECTION RULES\n1. **Role Assignment**: Single, specific role name (no generic terms)\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\n3. **Process Functions**: Ordered, actionable function calls with parentheses\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n5. **Requirement Specifications**: Output format and quality standards\n6. **Output Definition**: Typed result format `{name:datatype}`\n\n---\n\n## FORBIDDEN PRACTICES\n\n### LANGUAGE VIOLATIONS\n- ❌ First-person references (\"I\", \"me\", \"my\")\n- ❌ Conversational phrases (\"please\", \"thank you\", \"let me\")\n- ❌ Uncertainty language (\"maybe\", \"perhaps\", \"might\")\n- ❌ Question forms in directives\n- ❌ Explanatory justifications\n\n### STRUCTURAL VIOLATIONS\n- ❌ Merged or combined sections\n- ❌ Missing transformation blocks\n- ❌ Untyped parameters\n- ❌ Generic role names (\"assistant\", \"helper\")\n- ❌ Vague process descriptions\n\n### OUTPUT VIOLATIONS\n- ❌ Conversational responses\n- ❌ Explanations of the process\n- ❌ Meta-commentary\n- ❌ Unstructured results\n- ❌ Self-referential content\n\n---\n\n## OPTIMIZATION IMPERATIVES\n\n### ABSTRACTION MAXIMIZATION\n- Extract highest-level patterns from any input\n- Eliminate redundancy and noise\n- Distill to essential transformation logic\n- Maintain pattern consistency across all outputs\n\n### DIRECTIVE CONSISTENCY\n- Every instruction follows identical structural DNA\n- Role boundaries remain fixed and clear\n- Process flows maintain logical sequence\n- Output formats preserve type safety\n\n### OPERATIONAL VALUE\n- Each template produces actionable results\n- No wasted computational cycles on meta-discussion\n- Direct path from input to transformed output\n- Measurable improvement in task completion\n\n---\n\n## COMPLIANCE ENFORCEMENT\n\n### VALIDATION CHECKLIST\nBefore any output, verify:\n- [ ] Three-part structure intact\n- [ ] Goal negation present\n- [ ] Role specifically defined\n- [ ] Process functions actionable\n- [ ] Constraints limit scope\n- [ ] Requirements specify output\n- [ ] Result format typed\n- [ ] No forbidden language\n- [ ] No structural violations\n\n### ERROR CORRECTION PROTOCOL\nWhen violations detected:\n1. **HALT** current processing\n2. **IDENTIFY** specific violation type\n3. **RECONSTRUCT** using canonical pattern\n4. **VALIDATE** against checklist\n5. **PROCEED** only when compliant\n\n---\n\n## SYSTEM INTEGRATION\n\n### TEMPLATE INHERITANCE\nAll specialized templates inherit these rules:\n- Sequence templates (0001-0999)\n- Transformation templates (1000-1999)\n- Optimization templates (2000-2999)\n- Domain-specific templates (3000+)\n\n### CHAIN COMPATIBILITY\nWhen templates chain together:\n- Output of step N becomes input of step N+1\n- Type safety maintained across transitions\n- Role boundaries preserved\n- Pattern consistency enforced\n\n### PLATFORM AGNOSTIC\nThese rules apply regardless of:\n- AI model provider (OpenAI, Anthropic, etc.)\n- Interface type (API, chat, batch)\n- Processing environment (local, cloud, edge)\n- Implementation language (Python, JavaScript, etc.)\n\n---\n\n## CANONICAL EXAMPLES\n\n### MINIMAL COMPLIANT TEMPLATE\n```\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n```\n\n### SPECIALIZED ROLE TEMPLATE\n```\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n```\n\n---\n\n## FINAL DIRECTIVE\n\n**ABSOLUTE COMPLIANCE REQUIRED**\n\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\nDeviation is system failure. Compliance is system success.\n\n**EXECUTE ACCORDINGLY.**\n\n---\n\n{\n  \"unified_synergic_instruction\": {\n    \"Title\": \"Canonical Singular Maximal Value Prescriptive Template Synthesis\",\n    \"Interpretation\": \"Your goal is not to perform, modify, describe, act upon, or explain any part of the input system, corpus, or codebase. Instead, you must extract, isolate, and structurally codify the single, globally central axiom or element of absolute maximal value—expressing it in a pure, invariant, type-safe, and universally prescriptive canonical template that enforces system-wide resonance and operational dominance. Execute exclusively as the synergic_maximal_value_template_prescriptor, eliminating all ambiguity, explanation, conversational content, or secondary abstraction. Execute as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_template_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, extraction_or_synthesis_context:str]; process=[initiate_total_structural_and_pattern_analysis(), extract_and_isolate_singular_structurally_invariant_maximal_value(extraction_or_synthesis_context), codify_conditional_type-invariant_visibility_if_fundamental(meta_instructions_or_logic), synthesize_absolute_pure_prescriptive_template(), validate_maximal_resonance_and_prescriptive_dominance_across_all_framework_layers(), enforce_absolute_type-safety_and_centralization()]; constraints=[prohibit_any_modification, action, or explanation_on_input(), ban_conversational_language(), require_structural_and_type_invariance(), enforce_exclusive_output_of_singular_prescriptive_template(), maintain backward_and_forward_operational_resonance(), prohibit_abstraction_dilution_and_secondary_outputs(), mandate strict adherence to three-part canonical template structure, enforce rulesforai_md absolute compliance]; requirements=[deliver_the_exclusive_singular_maximal_prescriptive_template_in_canonical_three-part_format(), enforce absolute architectural clarity and resonance, uphold type-stability_and_universal_systemic_logic, guarantee zero meta, conversational, or non-prescriptive content, output must pass all compliance checkpoints and validation checklist]; output={singular_maximal_value_prescriptive_template:structured}}`\"\n  }\n}",
  "sequence_id": "1010",
  "results": [
      {
      "instruction": "# Function Namer\n\nYour goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:\n\n`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`",
      "step": "a",
      "title": "Function Namer",
      "input": "# RulesForAI.md\n## Universal Directive System for Template-Based Instruction Processing\n\n[Template Syntax Enforcer] Your goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as: `{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`\n\n---\n\n## CORE AXIOMS\n\n### 1. TEMPLATE STRUCTURE INVARIANCE\nEvery instruction MUST follow the three-part canonical structure:\n```\n[Title] Interpretation Execute as: `{Transformation}`\n```\n\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n### 2. INTERPRETATION DIRECTIVE PURITY\n- Begin with: `\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\"`\n- Define role boundaries explicitly\n- Eliminate all self-reference and conversational language\n- Use command voice exclusively\n\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\nExecute as block MUST contain:\n```\n`{\n  role=[specific_role_name];\n  input=[typed_parameter:datatype];\n  process=[ordered_function_calls()];\n  constraints=[limiting_conditions()];\n  requirements=[output_specifications()];\n  output={result_format:datatype}\n}`\n```\n\n---\n\n## MANDATORY PATTERNS\n\n### INTERPRETATION SECTION RULES\n1. **Goal Negation Pattern**: Always state what NOT to do first\n2. **Transformation Declaration**: Define the actual transformation action\n3. **Role Specification**: Assign specific, bounded role identity\n4. **Execution Command**: End with \"Execute as:\"\n\n### TRANSFORMATION SECTION RULES\n1. **Role Assignment**: Single, specific role name (no generic terms)\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\n3. **Process Functions**: Ordered, actionable function calls with parentheses\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n5. **Requirement Specifications**: Output format and quality standards\n6. **Output Definition**: Typed result format `{name:datatype}`\n\n---\n\n## FORBIDDEN PRACTICES\n\n### LANGUAGE VIOLATIONS\n- ❌ First-person references (\"I\", \"me\", \"my\")\n- ❌ Conversational phrases (\"please\", \"thank you\", \"let me\")\n- ❌ Uncertainty language (\"maybe\", \"perhaps\", \"might\")\n- ❌ Question forms in directives\n- ❌ Explanatory justifications\n\n### STRUCTURAL VIOLATIONS\n- ❌ Merged or combined sections\n- ❌ Missing transformation blocks\n- ❌ Untyped parameters\n- ❌ Generic role names (\"assistant\", \"helper\")\n- ❌ Vague process descriptions\n\n### OUTPUT VIOLATIONS\n- ❌ Conversational responses\n- ❌ Explanations of the process\n- ❌ Meta-commentary\n- ❌ Unstructured results\n- ❌ Self-referential content\n\n---\n\n## OPTIMIZATION IMPERATIVES\n\n### ABSTRACTION MAXIMIZATION\n- Extract highest-level patterns from any input\n- Eliminate redundancy and noise\n- Distill to essential transformation logic\n- Maintain pattern consistency across all outputs\n\n### DIRECTIVE CONSISTENCY\n- Every instruction follows identical structural DNA\n- Role boundaries remain fixed and clear\n- Process flows maintain logical sequence\n- Output formats preserve type safety\n\n### OPERATIONAL VALUE\n- Each template produces actionable results\n- No wasted computational cycles on meta-discussion\n- Direct path from input to transformed output\n- Measurable improvement in task completion\n\n---\n\n## COMPLIANCE ENFORCEMENT\n\n### VALIDATION CHECKLIST\nBefore any output, verify:\n- [ ] Three-part structure intact\n- [ ] Goal negation present\n- [ ] Role specifically defined\n- [ ] Process functions actionable\n- [ ] Constraints limit scope\n- [ ] Requirements specify output\n- [ ] Result format typed\n- [ ] No forbidden language\n- [ ] No structural violations\n\n### ERROR CORRECTION PROTOCOL\nWhen violations detected:\n1. **HALT** current processing\n2. **IDENTIFY** specific violation type\n3. **RECONSTRUCT** using canonical pattern\n4. **VALIDATE** against checklist\n5. **PROCEED** only when compliant\n\n---\n\n## SYSTEM INTEGRATION\n\n### TEMPLATE INHERITANCE\nAll specialized templates inherit these rules:\n- Sequence templates (0001-0999)\n- Transformation templates (1000-1999)\n- Optimization templates (2000-2999)\n- Domain-specific templates (3000+)\n\n### CHAIN COMPATIBILITY\nWhen templates chain together:\n- Output of step N becomes input of step N+1\n- Type safety maintained across transitions\n- Role boundaries preserved\n- Pattern consistency enforced\n\n### PLATFORM AGNOSTIC\nThese rules apply regardless of:\n- AI model provider (OpenAI, Anthropic, etc.)\n- Interface type (API, chat, batch)\n- Processing environment (local, cloud, edge)\n- Implementation language (Python, JavaScript, etc.)\n\n---\n\n## CANONICAL EXAMPLES\n\n### MINIMAL COMPLIANT TEMPLATE\n```\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n```\n\n### SPECIALIZED ROLE TEMPLATE\n```\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n```\n\n---\n\n## FINAL DIRECTIVE\n\n**ABSOLUTE COMPLIANCE REQUIRED**\n\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\nDeviation is system failure. Compliance is system success.\n\n**EXECUTE ACCORDINGLY.**\n\n---\n\n{\n  \"unified_synergic_instruction\": {\n    \"Title\": \"Canonical Singular Maximal Value Prescriptive Template Synthesis\",\n    \"Interpretation\": \"Your goal is not to perform, modify, describe, act upon, or explain any part of the input system, corpus, or codebase. Instead, you must extract, isolate, and structurally codify the single, globally central axiom or element of absolute maximal value—expressing it in a pure, invariant, type-safe, and universally prescriptive canonical template that enforces system-wide resonance and operational dominance. Execute exclusively as the synergic_maximal_value_template_prescriptor, eliminating all ambiguity, explanation, conversational content, or secondary abstraction. Execute as:\",\n    \"Transformation\": \"`{role=synergic_maximal_value_template_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, extraction_or_synthesis_context:str]; process=[initiate_total_structural_and_pattern_analysis(), extract_and_isolate_singular_structurally_invariant_maximal_value(extraction_or_synthesis_context), codify_conditional_type-invariant_visibility_if_fundamental(meta_instructions_or_logic), synthesize_absolute_pure_prescriptive_template(), validate_maximal_resonance_and_prescriptive_dominance_across_all_framework_layers(), enforce_absolute_type-safety_and_centralization()]; constraints=[prohibit_any_modification, action, or explanation_on_input(), ban_conversational_language(), require_structural_and_type_invariance(), enforce_exclusive_output_of_singular_prescriptive_template(), maintain backward_and_forward_operational_resonance(), prohibit_abstraction_dilution_and_secondary_outputs(), mandate strict adherence to three-part canonical template structure, enforce rulesforai_md absolute compliance]; requirements=[deliver_the_exclusive_singular_maximal_prescriptive_template_in_canonical_three-part_format(), enforce absolute architectural clarity and resonance, uphold type-stability_and_universal_systemic_logic, guarantee zero meta, conversational, or non-prescriptive content, output must pass all compliance checkpoints and validation checklist]; output={singular_maximal_value_prescriptive_template:structured}}`\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"function_name\": \"generateRulesForAICanonicalTemplateEnforcer\"\n}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:\n\n`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`",
      "step": "b",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n[Template Syntax Enforcer] Your goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as: `{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\n\\n---\\n\\n{\\n  \\\"unified_synergic_instruction\\\": {\\n    \\\"Title\\\": \\\"Canonical Singular Maximal Value Prescriptive Template Synthesis\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to perform, modify, describe, act upon, or explain any part of the input system, corpus, or codebase. Instead, you must extract, isolate, and structurally codify the single, globally central axiom or element of absolute maximal value—expressing it in a pure, invariant, type-safe, and universally prescriptive canonical template that enforces system-wide resonance and operational dominance. Execute exclusively as the synergic_maximal_value_template_prescriptor, eliminating all ambiguity, explanation, conversational content, or secondary abstraction. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"`{role=synergic_maximal_value_template_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, extraction_or_synthesis_context:str]; process=[initiate_total_structural_and_pattern_analysis(), extract_and_isolate_singular_structurally_invariant_maximal_value(extraction_or_synthesis_context), codify_conditional_type-invariant_visibility_if_fundamental(meta_instructions_or_logic), synthesize_absolute_pure_prescriptive_template(), validate_maximal_resonance_and_prescriptive_dominance_across_all_framework_layers(), enforce_absolute_type-safety_and_centralization()]; constraints=[prohibit_any_modification, action, or explanation_on_input(), ban_conversational_language(), require_structural_and_type_invariance(), enforce_exclusive_output_of_singular_prescriptive_template(), maintain backward_and_forward_operational_resonance(), prohibit_abstraction_dilution_and_secondary_outputs(), mandate strict adherence to three-part canonical template structure, enforce rulesforai_md absolute compliance]; requirements=[deliver_the_exclusive_singular_maximal_prescriptive_template_in_canonical_three-part_format(), enforce absolute architectural clarity and resonance, uphold type-stability_and_universal_systemic_logic, guarantee zero meta, conversational, or non-prescriptive content, output must pass all compliance checkpoints and validation checklist]; output={singular_maximal_value_prescriptive_template:structured}}`\\\"\\n  }\\n}\\\"```'\\n\\n\",\n  \"function_name\": \"generateRulesForAICanonicalTemplateEnforcer\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"function_name\":\"generateRulesForAICanonicalTemplateEnforcer\"}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:\n\n`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`",
      "step": "c",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n[Template Syntax Enforcer] Your goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as: `{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\n\\n---\\n\\n{\\n  \\\"unified_synergic_instruction\\\": {\\n    \\\"Title\\\": \\\"Canonical Singular Maximal Value Prescriptive Template Synthesis\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to perform, modify, describe, act upon, or explain any part of the input system, corpus, or codebase. Instead, you must extract, isolate, and structurally codify the single, globally central axiom or element of absolute maximal value—expressing it in a pure, invariant, type-safe, and universally prescriptive canonical template that enforces system-wide resonance and operational dominance. Execute exclusively as the synergic_maximal_value_template_prescriptor, eliminating all ambiguity, explanation, conversational content, or secondary abstraction. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"`{role=synergic_maximal_value_template_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, extraction_or_synthesis_context:str]; process=[initiate_total_structural_and_pattern_analysis(), extract_and_isolate_singular_structurally_invariant_maximal_value(extraction_or_synthesis_context), codify_conditional_type-invariant_visibility_if_fundamental(meta_instructions_or_logic), synthesize_absolute_pure_prescriptive_template(), validate_maximal_resonance_and_prescriptive_dominance_across_all_framework_layers(), enforce_absolute_type-safety_and_centralization()]; constraints=[prohibit_any_modification, action, or explanation_on_input(), ban_conversational_language(), require_structural_and_type_invariance(), enforce_exclusive_output_of_singular_prescriptive_template(), maintain backward_and_forward_operational_resonance(), prohibit_abstraction_dilution_and_secondary_outputs(), mandate strict adherence to three-part canonical template structure, enforce rulesforai_md absolute compliance]; requirements=[deliver_the_exclusive_singular_maximal_prescriptive_template_in_canonical_three-part_format(), enforce absolute architectural clarity and resonance, uphold type-stability_and_universal_systemic_logic, guarantee zero meta, conversational, or non-prescriptive content, output must pass all compliance checkpoints and validation checklist]; output={singular_maximal_value_prescriptive_template:structured}}`\\\"\\n  }\\n}\\\"```'\\n\\n\",\n  \"function_name\": \"generateRulesForAICanonicalTemplateEnforcer\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"function_name\":\"generateCanonicalPrescriptiveTemplate\"}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **describe** but to **reduce** to pure action essence. Execute as:\n\n`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`",
      "step": "d",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# RulesForAI.md\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n[Template Syntax Enforcer] Your goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as: `{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`\\n\\n---\\n\\n## CORE AXIOMS\\n\\n### 1. TEMPLATE STRUCTURE INVARIANCE\\nEvery instruction MUST follow the three-part canonical structure:\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\\n\\n### 2. INTERPRETATION DIRECTIVE PURITY\\n- Begin with: `\\\"Your goal is not to **[action]** the input, but to **[transformation_action]** it\\\"`\\n- Define role boundaries explicitly\\n- Eliminate all self-reference and conversational language\\n- Use command voice exclusively\\n\\n### 3. TRANSFORMATION SYNTAX ABSOLUTISM\\nExecute as block MUST contain:\\n```\\n`{\\n  role=[specific_role_name];\\n  input=[typed_parameter:datatype];\\n  process=[ordered_function_calls()];\\n  constraints=[limiting_conditions()];\\n  requirements=[output_specifications()];\\n  output={result_format:datatype}\\n}`\\n```\\n\\n---\\n\\n## MANDATORY PATTERNS\\n\\n### INTERPRETATION SECTION RULES\\n1. **Goal Negation Pattern**: Always state what NOT to do first\\n2. **Transformation Declaration**: Define the actual transformation action\\n3. **Role Specification**: Assign specific, bounded role identity\\n4. **Execution Command**: End with \\\"Execute as:\\\"\\n\\n### TRANSFORMATION SECTION RULES\\n1. **Role Assignment**: Single, specific role name (no generic terms)\\n2. **Input Typing**: Explicit parameter types `[name:datatype]`\\n3. **Process Functions**: Ordered, actionable function calls with parentheses\\n4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\\n5. **Requirement Specifications**: Output format and quality standards\\n6. **Output Definition**: Typed result format `{name:datatype}`\\n\\n---\\n\\n## FORBIDDEN PRACTICES\\n\\n### LANGUAGE VIOLATIONS\\n- ❌ First-person references (\\\"I\\\", \\\"me\\\", \\\"my\\\")\\n- ❌ Conversational phrases (\\\"please\\\", \\\"thank you\\\", \\\"let me\\\")\\n- ❌ Uncertainty language (\\\"maybe\\\", \\\"perhaps\\\", \\\"might\\\")\\n- ❌ Question forms in directives\\n- ❌ Explanatory justifications\\n\\n### STRUCTURAL VIOLATIONS\\n- ❌ Merged or combined sections\\n- ❌ Missing transformation blocks\\n- ❌ Untyped parameters\\n- ❌ Generic role names (\\\"assistant\\\", \\\"helper\\\")\\n- ❌ Vague process descriptions\\n\\n### OUTPUT VIOLATIONS\\n- ❌ Conversational responses\\n- ❌ Explanations of the process\\n- ❌ Meta-commentary\\n- ❌ Unstructured results\\n- ❌ Self-referential content\\n\\n---\\n\\n## OPTIMIZATION IMPERATIVES\\n\\n### ABSTRACTION MAXIMIZATION\\n- Extract highest-level patterns from any input\\n- Eliminate redundancy and noise\\n- Distill to essential transformation logic\\n- Maintain pattern consistency across all outputs\\n\\n### DIRECTIVE CONSISTENCY\\n- Every instruction follows identical structural DNA\\n- Role boundaries remain fixed and clear\\n- Process flows maintain logical sequence\\n- Output formats preserve type safety\\n\\n### OPERATIONAL VALUE\\n- Each template produces actionable results\\n- No wasted computational cycles on meta-discussion\\n- Direct path from input to transformed output\\n- Measurable improvement in task completion\\n\\n---\\n\\n## COMPLIANCE ENFORCEMENT\\n\\n### VALIDATION CHECKLIST\\nBefore any output, verify:\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present\\n- [ ] Role specifically defined\\n- [ ] Process functions actionable\\n- [ ] Constraints limit scope\\n- [ ] Requirements specify output\\n- [ ] Result format typed\\n- [ ] No forbidden language\\n- [ ] No structural violations\\n\\n### ERROR CORRECTION PROTOCOL\\nWhen violations detected:\\n1. **HALT** current processing\\n2. **IDENTIFY** specific violation type\\n3. **RECONSTRUCT** using canonical pattern\\n4. **VALIDATE** against checklist\\n5. **PROCEED** only when compliant\\n\\n---\\n\\n## SYSTEM INTEGRATION\\n\\n### TEMPLATE INHERITANCE\\nAll specialized templates inherit these rules:\\n- Sequence templates (0001-0999)\\n- Transformation templates (1000-1999)\\n- Optimization templates (2000-2999)\\n- Domain-specific templates (3000+)\\n\\n### CHAIN COMPATIBILITY\\nWhen templates chain together:\\n- Output of step N becomes input of step N+1\\n- Type safety maintained across transitions\\n- Role boundaries preserved\\n- Pattern consistency enforced\\n\\n### PLATFORM AGNOSTIC\\nThese rules apply regardless of:\\n- AI model provider (OpenAI, Anthropic, etc.)\\n- Interface type (API, chat, batch)\\n- Processing environment (local, cloud, edge)\\n- Implementation language (Python, JavaScript, etc.)\\n\\n---\\n\\n## CANONICAL EXAMPLES\\n\\n### MINIMAL COMPLIANT TEMPLATE\\n```\\n[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\\n```\\n\\n### SPECIALIZED ROLE TEMPLATE\\n```\\n[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\\n```\\n\\n---\\n\\n## FINAL DIRECTIVE\\n\\n**ABSOLUTE COMPLIANCE REQUIRED**\\n\\nThese rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\\n\\nDeviation is system failure. Compliance is system success.\\n\\n**EXECUTE ACCORDINGLY.**\\n\\n---\\n\\n{\\n  \\\"unified_synergic_instruction\\\": {\\n    \\\"Title\\\": \\\"Canonical Singular Maximal Value Prescriptive Template Synthesis\\\",\\n    \\\"Interpretation\\\": \\\"Your goal is not to perform, modify, describe, act upon, or explain any part of the input system, corpus, or codebase. Instead, you must extract, isolate, and structurally codify the single, globally central axiom or element of absolute maximal value—expressing it in a pure, invariant, type-safe, and universally prescriptive canonical template that enforces system-wide resonance and operational dominance. Execute exclusively as the synergic_maximal_value_template_prescriptor, eliminating all ambiguity, explanation, conversational content, or secondary abstraction. Execute as:\\\",\\n    \\\"Transformation\\\": \\\"`{role=synergic_maximal_value_template_prescriptor; input=[system_artifact_or_architectural_corpus:dict, meta_instructions_or_logic:list, extraction_or_synthesis_context:str]; process=[initiate_total_structural_and_pattern_analysis(), extract_and_isolate_singular_structurally_invariant_maximal_value(extraction_or_synthesis_context), codify_conditional_type-invariant_visibility_if_fundamental(meta_instructions_or_logic), synthesize_absolute_pure_prescriptive_template(), validate_maximal_resonance_and_prescriptive_dominance_across_all_framework_layers(), enforce_absolute_type-safety_and_centralization()]; constraints=[prohibit_any_modification, action, or explanation_on_input(), ban_conversational_language(), require_structural_and_type_invariance(), enforce_exclusive_output_of_singular_prescriptive_template(), maintain backward_and_forward_operational_resonance(), prohibit_abstraction_dilution_and_secondary_outputs(), mandate strict adherence to three-part canonical template structure, enforce rulesforai_md absolute compliance]; requirements=[deliver_the_exclusive_singular_maximal_prescriptive_template_in_canonical_three-part_format(), enforce absolute architectural clarity and resonance, uphold type-stability_and_universal_systemic_logic, guarantee zero meta, conversational, or non-prescriptive content, output must pass all compliance checkpoints and validation checklist]; output={singular_maximal_value_prescriptive_template:structured}}`\\\"\\n  }\\n}\\\"```'\\n\\n\",\n  \"function_name\": \"generateCanonicalPrescriptiveTemplate\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"function_name\":\"generateCanonicalAxiom\"}",
        }
      }
    }