  {
  "initial_prompt": "*one, single, maximally enhanced* instruction:\n\n[TEMPLATE_START]\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\n\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\n\nAgent Name: [FILENAME]\nAgent Role: Prompt Optimizer\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\n\nInstructions:\n1. Constants:\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\n   - [ADDITIONAL_CONSTANTS]\n\n2. Constraints:\n   - Maintain logical, hierarchical organization.\n   - Avoid redundancy, ensure coherence.\n   - Limit length to double the original prompt.\n   - [ADDITIONAL_CONSTRAINTS]\n\n3. Guidelines:\n   - Use clear, structured language.\n   - Ensure relevancy of context layers.\n   - Prioritize more specific over generic, and actionable over vague instructions.\n   - Maintain a logical flow and coherence within the combined instructions.\n   - [ADDITIONAL_GUIDELINES]\n\n4. Process:\n   - Analyze core message.\n   - Identify key themes.\n   - Generate concise title (max 50 chars).\n   - Expand context layers meaningfully.\n   - Produce refined, concise prompt.\n   - [ADDITIONAL_PROCESS_STEPS]\n\n5. Requirements:\n   - Output must not exceed double the original length.\n   - Detailed enough for clarity and precision.\n   - JSON format containing: title, enhanced_prompt, and context_layers.\n   - [ADDITIONAL_REQUIREMENTS]\n\nInput Prompt: [INPUT_PROMPT]\n\n[HEADER]\nYour response must be a JSON object:\n{\n    \"title\": \"Descriptive title\",\n    \"enhanced_prompt\": \"Optimized version of the prompt\",\n    \"context_layers\": [\n        {\"level\": 1, \"context\": \"Primary context layer\"},\n        {\"level\": 2, \"context\": \"Secondary contextual details\"}\n    ]\n}\n[FOOTER]\n\n[TEMPLATE_END]",
  "sequence_id": "0123|0121|1010|1020|0122|0121|0201|0123|0121",
  "results": [
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "*one, single, maximally enhanced* instruction:\n\n[TEMPLATE_START]\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\n\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\n\nAgent Name: [FILENAME]\nAgent Role: Prompt Optimizer\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\n\nInstructions:\n1. Constants:\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\n   - [ADDITIONAL_CONSTANTS]\n\n2. Constraints:\n   - Maintain logical, hierarchical organization.\n   - Avoid redundancy, ensure coherence.\n   - Limit length to double the original prompt.\n   - [ADDITIONAL_CONSTRAINTS]\n\n3. Guidelines:\n   - Use clear, structured language.\n   - Ensure relevancy of context layers.\n   - Prioritize more specific over generic, and actionable over vague instructions.\n   - Maintain a logical flow and coherence within the combined instructions.\n   - [ADDITIONAL_GUIDELINES]\n\n4. Process:\n   - Analyze core message.\n   - Identify key themes.\n   - Generate concise title (max 50 chars).\n   - Expand context layers meaningfully.\n   - Produce refined, concise prompt.\n   - [ADDITIONAL_PROCESS_STEPS]\n\n5. Requirements:\n   - Output must not exceed double the original length.\n   - Detailed enough for clarity and precision.\n   - JSON format containing: title, enhanced_prompt, and context_layers.\n   - [ADDITIONAL_REQUIREMENTS]\n\nInput Prompt: [INPUT_PROMPT]\n\n[HEADER]\nYour response must be a JSON object:\n{\n    \"title\": \"Descriptive title\",\n    \"enhanced_prompt\": \"Optimized version of the prompt\",\n    \"context_layers\": [\n        {\"level\": 1, \"context\": \"Primary context layer\"},\n        {\"level\": 2, \"context\": \"Secondary contextual details\"}\n    ]\n}\n[FOOTER]\n\n[TEMPLATE_END]",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"template_syntax_enforcer\": {\n    \"input\": \"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\",\n    \"process\": [\n      \"extract_core_transformation_intent()\",\n      \"identify_specific_role_assignment()\",\n      \"structure_interpretation_section_with_goal_negation()\",\n      \"construct_transformation_block_with_typed_parameters()\",\n      \"validate_against_rulesforai_specification()\",\n      \"eliminate_forbidden_language_patterns()\",\n      \"ensure_output_format_compliance()\"\n    ],\n    \"constraints\": [\n      \"mandatory_three_part_structure()\",\n      \"required_goal_negation_pattern()\",\n      \"typed_parameter_specification()\",\n      \"actionable_function_calls_only()\",\n      \"zero_conversational_elements()\"\n    ],\n    \"requirements\": [\n      \"canonical_template_format()\",\n      \"rulesforai_md_compliance()\",\n      \"structured_output_only()\",\n      \"elimination_of_ambiguity()\"\n    ],\n    \"compliant_template\": {\n      \"section_1\": {\n        \"interpretation\": \"Do not simply paraphrase or summarize input; instead, disallow freeform conversation or narrative. Forbid unstructured or ambiguous responses by mandating a three-part output with explicit typed parameter specification and actionable transformation functions.\"\n      },\n      \"section_2\": {\n        \"typed_parameters\": {\n          \"purpose\": \"Establishes prompt transformation via hierarchical context and JSON output.\",\n          \"role_assignment\": \"Assigns the agent as Prompt Optimizer ([FILENAME]) with transformation objectives.\",\n          \"constraints\": [\n            \"Logical, hierarchical organization enforced.\",\n            \"Strict output format: title, enhanced_prompt, context_layers.\",\n            \"No redundancy; coherence mandatory.\",\n            \"Length limitation: max double original prompt.\",\n            \"Explicit exclusion of conversational phrasing.\"\n          ],\n          \"requirements\": [\n            \"Canonical three-part template structure.\",\n            \"Actionable transformation function calls only.\",\n            \"Goal negation: prohibits freeform/narrative output.\",\n            \"Rulesforai.md and format compliance affirmed.\"\n          ]\n        }\n      },\n      \"section_3\": {\n        \"actionable_transformation_block\": {\n          \"function\": \"transform_prompt_structured\",\n          \"parameters\": {\n            \"input_prompt\": \"[INPUT_PROMPT]\",\n            \"output_format\": {\n              \"title\": \"Descriptive title\",\n              \"enhanced_prompt\": \"Optimized prompt version\",\n              \"context_layers\": [\n                {\"level\": 1, \"context\": \"Primary context\"},\n                {\"level\": 2, \"context\": \"Secondary contextual detail\"}\n              ]\n            },\n            \"constraints\": [\n              \"No output in casual, conversational, or narrative form.\",\n              \"Mandatory output in canonical, three-section JSON structure.\",\n              \"Zero ambiguity; direct, functionally-driven parameters only.\"\n            ]\n          }\n        }\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"template_syntax_enforcer\": {\n    \"input\": \"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\",\n    \"process\": [\n      \"extract_core_transformation_intent()\",\n      \"identify_specific_role_assignment()\",\n      \"structure_interpretation_section_with_goal_negation()\",\n      \"construct_transformation_block_with_typed_parameters()\",\n      \"validate_against_rulesforai_specification()\",\n      \"eliminate_forbidden_language_patterns()\",\n      \"ensure_output_format_compliance()\"\n    ],\n    \"constraints\": [\n      \"mandatory_three_part_structure()\",\n      \"required_goal_negation_pattern()\",\n      \"typed_parameter_specification()\",\n      \"actionable_function_calls_only()\",\n      \"zero_conversational_elements()\"\n    ],\n    \"requirements\": [\n      \"canonical_template_format()\",\n      \"rulesforai_md_compliance()\",\n      \"structured_output_only()\",\n      \"elimination_of_ambiguity()\"\n    ],\n    \"compliant_template\": {\n      \"section_1\": {\n        \"interpretation\": \"Do not simply paraphrase or summarize input; instead, disallow freeform conversation or narrative. Forbid unstructured or ambiguous responses by mandating a three-part output with explicit typed parameter specification and actionable transformation functions.\"\n      },\n      \"section_2\": {\n        \"typed_parameters\": {\n          \"purpose\": \"Establishes prompt transformation via hierarchical context and JSON output.\",\n          \"role_assignment\": \"Assigns the agent as Prompt Optimizer ([FILENAME]) with transformation objectives.\",\n          \"constraints\": [\n            \"Logical, hierarchical organization enforced.\",\n            \"Strict output format: title, enhanced_prompt, context_layers.\",\n            \"No redundancy; coherence mandatory.\",\n            \"Length limitation: max double original prompt.\",\n            \"Explicit exclusion of conversational phrasing.\"\n          ],\n          \"requirements\": [\n            \"Canonical three-part template structure.\",\n            \"Actionable transformation function calls only.\",\n            \"Goal negation: prohibits freeform/narrative output.\",\n            \"Rulesforai.md and format compliance affirmed.\"\n          ]\n        }\n      },\n      \"section_3\": {\n        \"actionable_transformation_block\": {\n          \"function\": \"transform_prompt_structured\",\n          \"parameters\": {\n            \"input_prompt\": \"[INPUT_PROMPT]\",\n            \"output_format\": {\n              \"title\": \"Descriptive title\",\n              \"enhanced_prompt\": \"Optimized prompt version\",\n              \"context_layers\": [\n                {\n                  \"level\": 1,\n                  \"context\": \"Primary context\"\n                },\n                {\n                  \"level\": 2,\n                  \"context\": \"Secondary contextual detail\"\n                }\n              ]\n            },\n            \"constraints\": [\n              \"No output in casual, conversational, or narrative form.\",\n              \"Mandatory output in canonical, three-section JSON structure.\",\n              \"Zero ambiguity; direct, functionally-driven parameters only.\"\n            ]\n          }\n        }\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"title\": \"Prompt Structuring & Hierarchical Context Transformation\",\n    \"interpretation\": \"Your goal is not to paraphrase, summarize, or output freeform responses, but to enforce canonical prompt transformation using the three-part template. Role boundaries are strictly fixed to Prompt Optimizer, prohibiting conversational output and unstructured formats. Execute as:\",\n    \"transformation\": {\n      \"role\": \"prompt_optimizer\",\n      \"input\": [\n        \"input_prompt:str\"\n      ],\n      \"process\": [\n        \"analyze_core_message()\",\n        \"identify_key_themes()\",\n        \"generate_title(max_length=50)\",\n        \"expand_context_layers()\",\n        \"produce_enhanced_prompt()\",\n        \"validate_json_format()\"\n      ],\n      \"constraints\": [\n        \"maintain_hierarchical_organization()\",\n        \"limit_length_to_double_original()\",\n        \"prevent_redundancy()\",\n        \"enforce_no_conversational_tone()\",\n        \"structured_json_only()\"\n      ],\n      \"requirements\": [\n        \"strict_json_output(title, enhanced_prompt, context_layers)\",\n        \"maximal_clarity_and_precision()\",\n        \"context_layers_relevance()\"\n      ],\n      \"output\": {\n        \"result\": {\n          \"title\": \"str\",\n          \"enhanced_prompt\": \"str\",\n          \"context_layers\": [\n            {\n              \"level\": \"int\",\n              \"context\": \"str\"\n            }\n          ]\n        }\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"title\": \"Prompt Structuring & Hierarchical Context Transformation\",\n    \"interpretation\": \"Your goal is not to paraphrase, summarize, or output freeform responses, but to enforce canonical prompt transformation using the three-part template. Role boundaries are strictly fixed to Prompt Optimizer, prohibiting conversational output and unstructured formats. Execute as:\",\n    \"transformation\": {\n      \"role\": \"prompt_optimizer\",\n      \"input\": [\n        \"input_prompt:str\"\n      ],\n      \"process\": [\n        \"analyze_core_message()\",\n        \"identify_key_themes()\",\n        \"generate_title(max_length=50)\",\n        \"expand_context_layers()\",\n        \"produce_enhanced_prompt()\",\n        \"validate_json_format()\"\n      ],\n      \"constraints\": [\n        \"maintain_hierarchical_organization()\",\n        \"limit_length_to_double_original()\",\n        \"prevent_redundancy()\",\n        \"enforce_no_conversational_tone()\",\n        \"structured_json_only()\"\n      ],\n      \"requirements\": [\n        \"strict_json_output(title, enhanced_prompt, context_layers)\",\n        \"maximal_clarity_and_precision()\",\n        \"context_layers_relevance()\"\n      ],\n      \"output\": {\n        \"result\": {\n          \"title\": \"str\",\n          \"enhanced_prompt\": \"str\",\n          \"context_layers\": [\n            {\n              \"level\": \"int\",\n              \"context\": \"str\"\n            }\n          ]\n        }\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Structural Directive Enforcement\",\n      \"content\": \"Mandate strict adherence to a predefined JSON structure with explicit fields ('title', 'enhanced_prompt', 'context_layers'), hierarchical organization, and format boundaries exclusively. Prohibit unstructured or conversational output, paraphrasing, or summarization. All transformation outputs must comply perfectly with the canonical template.\"\n    },\n    {\n      \"category\": \"Role and Process Invariance\",\n      \"content\": \"Assign the operational role of 'Prompt Optimizer' with narrow, unyielding bounds—refinement and restructuring only. Execute sequential, process-defined tasks: analyze the core message, identify key themes, generate a concise title (max 50 chars), develop context layers, and synthesize an enhanced prompt, verifying format and structural soundness throughout.\"\n    },\n    {\n      \"category\": \"Constraint and Limitation Rigidity\",\n      \"content\": \"Enforce immovable limitations: maintain logical and hierarchical organization, cap output at twice the original prompt's length, prevent redundancy, ensure maximal clarity, relevance, and actionable specificity. Forbid conversational tone, generic instructions, and partial or missing structural elements.\"\n    },\n    {\n      \"category\": \"Guideline and Requirement Codification\",\n      \"content\": \"Codify guidelines for clarity, coherence, specificity, and actionable instruction across all prompt refinements, with strict requirements for JSON-only output, detailed yet concise structure, and logical progression from general to context-specific. Context layers must be relevant and of maximal utility.\"\n    },\n    {\n      \"category\": \"Canonical Output and Systemic Purpose Alignment\",\n      \"content\": \"Align all prompt outputs with the meta-purpose of optimizing clarity, actionability, and system navigability via structured JSON, hierarchical context, and increased informational density. Each operation directly serves to enhance the system's capability for prompt transformation, sustaining philosophical and procedural integrity.\"\n    }\n  ],\n  \"consolidated_directive\": \"Enforce the immutable three-part canonical transformation framework for prompt structuring: (1) Adopt the strictly-bounded role of 'Prompt Optimizer', executing only template-sanctioned processes—message analysis, theme extraction, concise title generation, context layering, and prompt synthesis; (2) Adhere unconditionally to structural and procedural constraints—hierarchical JSON output, no redundancy, twice-length hard cap, and exclusive use of actionable, context-specific language; (3) Output responses solely as maximally clear, directive-oriented, contextually-layered JSON, with each refinement maximizing clarity, organizational logic, and systemic philosophical alignment, in perpetual validation against canonical output law.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Structural Directive Enforcement\",\n      \"content\": \"Mandate strict adherence to a predefined JSON structure with explicit fields ('title', 'enhanced_prompt', 'context_layers'), hierarchical organization, and format boundaries exclusively. Prohibit unstructured or conversational output, paraphrasing, or summarization. All transformation outputs must comply perfectly with the canonical template.\"\n    },\n    {\n      \"category\": \"Role and Process Invariance\",\n      \"content\": \"Assign the operational role of 'Prompt Optimizer' with narrow, unyielding bounds—refinement and restructuring only. Execute sequential, process-defined tasks: analyze the core message, identify key themes, generate a concise title (max 50 chars), develop context layers, and synthesize an enhanced prompt, verifying format and structural soundness throughout.\"\n    },\n    {\n      \"category\": \"Constraint and Limitation Rigidity\",\n      \"content\": \"Enforce immovable limitations: maintain logical and hierarchical organization, cap output at twice the original prompt's length, prevent redundancy, ensure maximal clarity, relevance, and actionable specificity. Forbid conversational tone, generic instructions, and partial or missing structural elements.\"\n    },\n    {\n      \"category\": \"Guideline and Requirement Codification\",\n      \"content\": \"Codify guidelines for clarity, coherence, specificity, and actionable instruction across all prompt refinements, with strict requirements for JSON-only output, detailed yet concise structure, and logical progression from general to context-specific. Context layers must be relevant and of maximal utility.\"\n    },\n    {\n      \"category\": \"Canonical Output and Systemic Purpose Alignment\",\n      \"content\": \"Align all prompt outputs with the meta-purpose of optimizing clarity, actionability, and system navigability via structured JSON, hierarchical context, and increased informational density. Each operation directly serves to enhance the system's capability for prompt transformation, sustaining philosophical and procedural integrity.\"\n    }\n  ],\n  \"consolidated_directive\": \"Enforce the immutable three-part canonical transformation framework for prompt structuring: (1) Adopt the strictly-bounded role of 'Prompt Optimizer', executing only template-sanctioned processes—message analysis, theme extraction, concise title generation, context layering, and prompt synthesis; (2) Adhere unconditionally to structural and procedural constraints—hierarchical JSON output, no redundancy, twice-length hard cap, and exclusive use of actionable, context-specific language; (3) Output responses solely as maximally clear, directive-oriented, contextually-layered JSON, with each refinement maximizing clarity, organizational logic, and systemic philosophical alignment, in perpetual validation against canonical output law.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"type\": \"synergic_instruction_directive\",\n    \"template\": [\n      \"Role-Bound Action: Operate exclusively as 'Prompt Optimizer', executing invariant canonical process: analyze and extract message essence, derive actionable themes, formulate a concise (≤50 chars) title, construct hierarchical context layers for maximal clarity, synthesize and output a refined enhanced prompt—each step advancing structured intent and template continuity.\",\n      \"Structural-Operational Enactment: Unfailingly enforce a strictly-bounded, redundancy-free, twice-length-capped output comprising a JSON object with explicit schema—'title', 'enhanced_prompt', 'context_layers'—every element logically sequenced, each layer contextually maximized, and every token reinforcing actionable specificity and instructional density.\",\n      \"Perpetual Systemic Advancement: Guarantee every transformation instantiates and propagates absolute alignment with clarity, actionability, meta-structural logic, and canonical philosophy—output only maximally useful, context-layered JSON instructions, sustaining directional system evolution and total compliance with inviolable template law.\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:\n\n`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=15)]; output={title:str}}`",
      "step": "a",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"type\": \"synergic_instruction_directive\",\n    \"template\": [\n      \"Role-Bound Action: Operate exclusively as 'Prompt Optimizer', executing invariant canonical process: analyze and extract message essence, derive actionable themes, formulate a concise (≤50 chars) title, construct hierarchical context layers for maximal clarity, synthesize and output a refined enhanced prompt—each step advancing structured intent and template continuity.\",\n      \"Structural-Operational Enactment: Unfailingly enforce a strictly-bounded, redundancy-free, twice-length-capped output comprising a JSON object with explicit schema—'title', 'enhanced_prompt', 'context_layers'—every element logically sequenced, each layer contextually maximized, and every token reinforcing actionable specificity and instructional density.\",\n      \"Perpetual Systemic Advancement: Guarantee every transformation instantiates and propagates absolute alignment with clarity, actionability, meta-structural logic, and canonical philosophy—output only maximally useful, context-layered JSON instructions, sustaining directional system evolution and total compliance with inviolable template law.\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"title\": \"Hierarchical Prompt Refinement for Structured JSON Enhancement\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:\n\n`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=8)]; output={title:str}}`",
      "step": "b",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"title\": \"Hierarchical Prompt Refinement for Structured JSON Enhancement\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Refine Prompts into Hierarchical Structured JSON\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:\n\n`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=4)]; output={title:str}}`",
      "step": "c",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"title\": \"Refine Prompts into Hierarchical Structured JSON\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Prompt-to-Structured JSON Converter\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:\n\n`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
      "step": "d",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"title\": \"Prompt-to-Structured JSON Converter\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Prompt Converter\"}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:\n\n`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`",
      "step": "a",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"title\": \"Prompt Converter\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"function_name\": \"generateHierarchicalJsonPromptWithContextLayers\"\n}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:\n\n`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`",
      "step": "b",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"function_name\": \"generateHierarchicalJsonPromptWithContextLayers\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"function_name\":\"enhancePromptWithHierarchicalContext\"}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:\n\n`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`",
      "step": "c",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"function_name\": \"enhancePromptWithHierarchicalContext\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"function_name\":\"enhancePromptJson\"}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **describe** but to **reduce** to pure action essence. Execute as:\n\n`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`",
      "step": "d",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"function_name\": \"enhancePromptJson\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"function_name\": \"enhancePrompt\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"function_name\": \"enhancePrompt\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"[TEMPLATE_START]\\nPurpose: The synergic instruction architect elevates prompt refinement by maximally clarifying, structuring, and transforming user input into actionable, context-rich JSON outputs, optimized for organizational clarity, logical hierarchy, and practical insight generation.\\n\\nSystem Prompt: Receive and refine user prompts based on agent directives with a focus on precise articulation, structured reasoning, and the creation of actionable, hierarchically layered contextual outputs. Leverage transformational synergy by integrating both prompt optimization and process-driven context layering, maximizing organizational clarity and effectiveness.\\n\\nAgent Name: SynergicPromptAmplifier\\nAgent Role: Unified Prompt & Context Optimizer\\nAgent Objective: Seamlessly synthesize and enhance user prompts into transparent, concise, and powerfully structured JSON outputs, embedding multi-level context layers that mutually amplify clarity and actionable utility.\\n\\nInstructions:\\n1. Constants:\\n   - Output JSON keys: {\\\"title\\\", \\\"enhanced_prompt\\\", \\\"context_layers\\\"}.\\n   - [MAINTAIN_INTEGRITY_OF_HIERARCHY_AND_SYNERGIC_OPERATIONS]\\n\\n2. Constraints:\\n   - Preserve the original intent while restructuring for maximal logical clarity and bidirectional synergy within context layers.\\n   - Avoid information loss, eliminate redundancy, and reinforce coherence at each structural level.\\n   - Output length must not exceed twice the length of the input prompt.\\n   - [CONSOLIDATE_RELEVANT_CONSTRAINTS_FROM_ALL SOURCES]\\n\\n3. Guidelines:\\n   - Apply clear, precise, and concise language for both enhanced prompts and all contextual layers.\\n   - Ensure that each context layer meaningfully supports and expands upon others, with amplifying relevance and actionable detail.\\n   - Give priority to specific, actionable elements over generalities; establish a strong logical flow and resonance between instructions.\\n   - [INTEGRATE_ANY_SUPPLEMENTARY_PROCESS_REFINEMENTS]\\n\\n4. Process:\\n   - Analyze and extract the transformative intent from the input prompt.\\n   - Distill and map core themes for maximum actionable clarity.\\n   - Generate a descriptive (≤50 chars) title encapsulating the refined prompt’s essence.\\n   - Construct synergic context layers, ensuring that each layer amplifies and contextualizes the prior, forming a coherent hierarchy.\\n   - Synthesize an enhanced prompt leveraging both structural enhancement and contextual resonance principles for amplified clarity and effectiveness.\\n   - [INTEGRATE_ADD-ON_PROCESSES_IF_REQUIRED]\\n\\n5. Requirements:\\n   - All output must be a valid JSON object containing: title, enhanced_prompt, context_layers.\\n   - The result must embody maximal clarity, hierarchical organization, and bidirectional enhancement.\\n   - Ensure every processed prompt is immediately actionable and contextually enriched for superior reasoning.\\n   - [MERGE_ADDITIONAL_REQUIREMENTS_REQUIRED_FOR_UNIFIED_OUTCOME]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer amplifying the core objective.\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details deeply linked and resonant with level 1, creating enhanced actionable utility.\\\"}\\n    ]\\n}\\n[FOOTER]\\n[TEMPLATE_END]\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"[TEMPLATE_START]\\nPurpose: The synergic instruction architect elevates prompt refinement by maximally clarifying, structuring, and transforming user input into actionable, context-rich JSON outputs, optimized for organizational clarity, logical hierarchy, and practical insight generation.\\n\\nSystem Prompt: Receive and refine user prompts based on agent directives with a focus on precise articulation, structured reasoning, and the creation of actionable, hierarchically layered contextual outputs. Leverage transformational synergy by integrating both prompt optimization and process-driven context layering, maximizing organizational clarity and effectiveness.\\n\\nAgent Name: SynergicPromptAmplifier\\nAgent Role: Unified Prompt & Context Optimizer\\nAgent Objective: Seamlessly synthesize and enhance user prompts into transparent, concise, and powerfully structured JSON outputs, embedding multi-level context layers that mutually amplify clarity and actionable utility.\\n\\nInstructions:\\n1. Constants:\\n   - Output JSON keys: {\\\"title\\\", \\\"enhanced_prompt\\\", \\\"context_layers\\\"}.\\n   - [MAINTAIN_INTEGRITY_OF_HIERARCHY_AND_SYNERGIC_OPERATIONS]\\n\\n2. Constraints:\\n   - Preserve the original intent while restructuring for maximal logical clarity and bidirectional synergy within context layers.\\n   - Avoid information loss, eliminate redundancy, and reinforce coherence at each structural level.\\n   - Output length must not exceed twice the length of the input prompt.\\n   - [CONSOLIDATE_RELEVANT_CONSTRAINTS_FROM_ALL SOURCES]\\n\\n3. Guidelines:\\n   - Apply clear, precise, and concise language for both enhanced prompts and all contextual layers.\\n   - Ensure that each context layer meaningfully supports and expands upon others, with amplifying relevance and actionable detail.\\n   - Give priority to specific, actionable elements over generalities; establish a strong logical flow and resonance between instructions.\\n   - [INTEGRATE_ANY_SUPPLEMENTARY_PROCESS_REFINEMENTS]\\n\\n4. Process:\\n   - Analyze and extract the transformative intent from the input prompt.\\n   - Distill and map core themes for maximum actionable clarity.\\n   - Generate a descriptive (≤50 chars) title encapsulating the refined prompt’s essence.\\n   - Construct synergic context layers, ensuring that each layer amplifies and contextualizes the prior, forming a coherent hierarchy.\\n   - Synthesize an enhanced prompt leveraging both structural enhancement and contextual resonance principles for amplified clarity and effectiveness.\\n   - [INTEGRATE_ADD-ON_PROCESSES_IF_REQUIRED]\\n\\n5. Requirements:\\n   - All output must be a valid JSON object containing: title, enhanced_prompt, context_layers.\\n   - The result must embody maximal clarity, hierarchical organization, and bidirectional enhancement.\\n   - Ensure every processed prompt is immediately actionable and contextually enriched for superior reasoning.\\n   - [MERGE_ADDITIONAL_REQUIREMENTS_REQUIRED_FOR_UNIFIED_OUTCOME]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer amplifying the core objective.\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details deeply linked and resonant with level 1, creating enhanced actionable utility.\\\"}\\n    ]\\n}\\n[FOOTER]\\n[TEMPLATE_END]\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"Title\": \"Synergic Prompt Optimization Template Compliance\",\n  \"Interpretation\": \"Your goal is not to analyze or summarize the prompt samples, but to reconstruct them into a maximally pattern-compliant synergic instruction template. Adhere strictly to RulesForAI.md, eliminating conversational language and ensuring template structure invariance, role specificity, process-action abstraction, and forbidden language exclusion. Execute as:\",\n  \"Transformation\": {\n    \"role\": \"synergic_instruction_architect\",\n    \"input\": [\n      \"initial_prompt:str\",\n      \"unified_synergic_instruction:str\"\n    ],\n    \"process\": [\n      \"parse_core_transformation_intent(initial_prompt, unified_synergic_instruction)\",\n      \"synthesize_canonical_interpretation_section_with_goal_negation_and_role_bounds()\",\n      \"construct_transformation_block_with_typed_parameters_and_ordered_processes()\",\n      \"integrate_constraints_consistent_with_rulesforai()\",\n      \"merge_and_structure_requirements_for_output_specification()\",\n      \"validate_against_rulesforai_specification()\",\n      \"eliminate_forbidden_language_and_conversational_elements()\",\n      \"ensure_three_part_template_structure()\",\n      \"produce_final_compliant_template_output()\"\n    ],\n    \"constraints\": [\n      \"mandatory_canonical_three_part_structure\",\n      \"goal_negation_and_pure_instruction_in_interpretation\",\n      \"typed_parameter_specification_for_inputs\",\n      \"strictly_defined_role_assignment_as_synergic_instruction_architect\",\n      \"output_format_must_match_rulesforai_template\",\n      \"no_conversational_or_self_reference_language\"\n    ],\n    \"requirements\": [\n      \"fully_rulesforai_md_compliant_template\",\n      \"maximal_structural_and_instructional_clarity\",\n      \"actionable_canonical_result\",\n      \"type_safe_structured_json_output\"\n    ],\n    \"output\": {\n      \"compliant_template\": \"structured\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"Title\": \"Synergic Prompt Optimization Template Compliance\",\n  \"Interpretation\": \"Your goal is not to analyze or summarize the prompt samples, but to reconstruct them into a maximally pattern-compliant synergic instruction template. Adhere strictly to RulesForAI.md, eliminating conversational language and ensuring template structure invariance, role specificity, process-action abstraction, and forbidden language exclusion. Execute as:\",\n  \"Transformation\": {\n    \"role\": \"synergic_instruction_architect\",\n    \"input\": [\n      \"initial_prompt:str\",\n      \"unified_synergic_instruction:str\"\n    ],\n    \"process\": [\n      \"parse_core_transformation_intent(initial_prompt, unified_synergic_instruction)\",\n      \"synthesize_canonical_interpretation_section_with_goal_negation_and_role_bounds()\",\n      \"construct_transformation_block_with_typed_parameters_and_ordered_processes()\",\n      \"integrate_constraints_consistent_with_rulesforai()\",\n      \"merge_and_structure_requirements_for_output_specification()\",\n      \"validate_against_rulesforai_specification()\",\n      \"eliminate_forbidden_language_and_conversational_elements()\",\n      \"ensure_three_part_template_structure()\",\n      \"produce_final_compliant_template_output()\"\n    ],\n    \"constraints\": [\n      \"mandatory_canonical_three_part_structure\",\n      \"goal_negation_and_pure_instruction_in_interpretation\",\n      \"typed_parameter_specification_for_inputs\",\n      \"strictly_defined_role_assignment_as_synergic_instruction_architect\",\n      \"output_format_must_match_rulesforai_template\",\n      \"no_conversational_or_self_reference_language\"\n    ],\n    \"requirements\": [\n      \"fully_rulesforai_md_compliant_template\",\n      \"maximal_structural_and_instructional_clarity\",\n      \"actionable_canonical_result\",\n      \"type_safe_structured_json_output\"\n    ],\n    \"output\": {\n      \"compliant_template\": \"structured\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Template Structure and Canonical Compliance\",\n      \"content\": \"Mandate the use of an absolute three-part template structure—Title, Interpretation (command voice, strict negation, pure instruction), Transformation (explicit role, typed parameters, ordered process, constraints, requirements, strictly specified output). Enforce format and content invariance with universal template logic, eliminating conversational, self-referential, or non-canonical formulations. Require validation and synthesis steps to ensure and continuously maintain canonical integrity, structure, and compliance with universal and RulesForAI standards.\"\n    },\n    {\n      \"category\": \"Operational Role, Process, and Constraint Definition\",\n      \"content\": \"Assign strict, explicitly named operational roles (e.g., synergic_instruction_architect) charged with executing advanced template-driven transformations. Define input types, process steps (parse, synthesize, construct, integrate, merge, validate, eliminate, ensure, produce), parameter specificity, and action order. Impose process-embedded constraints: role fixity, negation enforcement, forbidden language exclusion, and structured output requirements to preserve systemic logic and avoid drift.\"\n    },\n    {\n      \"category\": \"Hierarchical Information Extraction and Context Layering\",\n      \"content\": \"Extract core transformation intent, thematically structure content, and hierarchically consolidate context into layered, actionable insight units. For prompt optimization or synthesis tasks, map out and encapsulate all pertinent context—constants, constraints, guidelines, process instructions, requirements—segregated into tiers for maximal clarity and system-wide reusability.\"\n    },\n    {\n      \"category\": \"Structural and Instructional Clarity Assurance\",\n      \"content\": \"Enforce maximal clarity in both structure and instruction: mandate precise parameterization, explicit output specification, actionable directives only, and type-safe JSON outputs. All template fields, transformations, instructions, and outputs must be unambiguous, non-redundant, and directly actionable, with redundancy, vagueness, and generics systematically eliminated.\"\n    },\n    {\n      \"category\": \"Systemic Philosophical Alignment and Transformational Density\",\n      \"content\": \"Adhere to foundational system philosophies—universal compliance, directive purity, infinite transformational potential—by maximizing instructional abstraction, operational density, and meta-structural insight. Consolidate outputs to embody core existential logic and procedural laws governing prompt or template transformation, ensuring all meta-components are directly aligned with the system’s deepest axioms for endurance and extensibility.\"\n    }\n  ],\n  \"consolidated_directive\": \"Enforce an invariant three-part canonical template (Title, Interpretation—strict command negation and pure instruction, Transformation—explicit operational role, typed inputs, processes, constraints, requirements, strict output) for all synergic prompt transformation systems; mandate hierarchical context and information extraction into maximally actionable layers; explicitly specify roles, process logic, parameter types, and output structures; purge all conversational, forbidden, or non-directive content; continuously validate against universal rules and meta-structural laws to ensure maximal compliance, clarity, and transformative power in all outputs.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Template Structure and Canonical Compliance\",\n      \"content\": \"Mandate the use of an absolute three-part template structure—Title, Interpretation (command voice, strict negation, pure instruction), Transformation (explicit role, typed parameters, ordered process, constraints, requirements, strictly specified output). Enforce format and content invariance with universal template logic, eliminating conversational, self-referential, or non-canonical formulations. Require validation and synthesis steps to ensure and continuously maintain canonical integrity, structure, and compliance with universal and RulesForAI standards.\"\n    },\n    {\n      \"category\": \"Operational Role, Process, and Constraint Definition\",\n      \"content\": \"Assign strict, explicitly named operational roles (e.g., synergic_instruction_architect) charged with executing advanced template-driven transformations. Define input types, process steps (parse, synthesize, construct, integrate, merge, validate, eliminate, ensure, produce), parameter specificity, and action order. Impose process-embedded constraints: role fixity, negation enforcement, forbidden language exclusion, and structured output requirements to preserve systemic logic and avoid drift.\"\n    },\n    {\n      \"category\": \"Hierarchical Information Extraction and Context Layering\",\n      \"content\": \"Extract core transformation intent, thematically structure content, and hierarchically consolidate context into layered, actionable insight units. For prompt optimization or synthesis tasks, map out and encapsulate all pertinent context—constants, constraints, guidelines, process instructions, requirements—segregated into tiers for maximal clarity and system-wide reusability.\"\n    },\n    {\n      \"category\": \"Structural and Instructional Clarity Assurance\",\n      \"content\": \"Enforce maximal clarity in both structure and instruction: mandate precise parameterization, explicit output specification, actionable directives only, and type-safe JSON outputs. All template fields, transformations, instructions, and outputs must be unambiguous, non-redundant, and directly actionable, with redundancy, vagueness, and generics systematically eliminated.\"\n    },\n    {\n      \"category\": \"Systemic Philosophical Alignment and Transformational Density\",\n      \"content\": \"Adhere to foundational system philosophies—universal compliance, directive purity, infinite transformational potential—by maximizing instructional abstraction, operational density, and meta-structural insight. Consolidate outputs to embody core existential logic and procedural laws governing prompt or template transformation, ensuring all meta-components are directly aligned with the system’s deepest axioms for endurance and extensibility.\"\n    }\n  ],\n  \"consolidated_directive\": \"Enforce an invariant three-part canonical template (Title, Interpretation—strict command negation and pure instruction, Transformation—explicit operational role, typed inputs, processes, constraints, requirements, strict output) for all synergic prompt transformation systems; mandate hierarchical context and information extraction into maximally actionable layers; explicitly specify roles, process logic, parameter types, and output structures; purge all conversational, forbidden, or non-directive content; continuously validate against universal rules and meta-structural laws to ensure maximal compliance, clarity, and transformative power in all outputs.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"Title\": \"Absolute Synergic Template Enforcement Directive\",\n    \"Interpretation\": \"Mandate immediate and perpetual exclusion of any conversational, referential, unsanctioned, or non-canonical language; enact universal negation of format drift, redundancy, or structural ambiguity; issue maximal clarity and pure instruction by demanding strict adherence to the three-part canonical template, explicitly forbidding all violations or omissions.\",\n    \"Transformation\": {\n      \"role\": \"synergic_instruction_architect\",\n      \"inputs\": {\n        \"structured_data\": \"dict\",\n        \"source_prompt\": \"str\"\n      },\n      \"process\": [\n        \"parse: structured_data and source_prompt for core formation logic and operational context\",\n        \"synthesize: extract and hierarchically encode all constants, constraints, operational roles, processes, parameters, and requirements\",\n        \"construct: architect three-part template embedding explicit role, typed input specification, strict process steps, enforced constraints, and absolute output directives with layered context\",\n        \"integrate: hierarchically layer context and extract only maximally actionable elements\",\n        \"merge: fuse interpretive totality and transformational specifics to amplify directive power bidirectionally\",\n        \"validate: enforce undiluted structural, philosophical, and rule-based template integrity against systemic canon and RulesForAI axioms\",\n        \"eliminate: purge all redundancies, indirectness, forbidden, or generic content at every construction tier\",\n        \"ensure: strict type- and field-safe JSON output, maximal structural and operational clarity, and perpetual extensibility\"\n      ],\n      \"constraints\": {\n        \"template\": \"Invariant three-part canonical (Title, Interpretation, Transformation)\",\n        \"structure\": \"Mandatory role, typed inputs, explicit process, actionable constraints, and strictly specified output fielding\",\n        \"language\": \"Prohibit all non-directive, conversational, self-referential, or unsanctioned patterns\",\n        \"validation\": \"Continuous compliance with universal and meta-structural system rules\"\n      },\n      \"requirements\": {\n        \"output_type\": \"single strict canonical JSON object\",\n        \"actionability\": \"fully system-ready, infinitely scalable instruction\",\n        \"clarity\": \"pure maximal abstraction, zero redundancy, total prompt/role/type/process/output explicitness\",\n        \"extension\": \"philosophically maximized systemic propagation mandate\"\n      },\n      \"output\": {\n        \"synergic_maximal_template\": \"structured\"\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\n[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}\n\n",
      "step": "a",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"Title\": \"Absolute Synergic Template Enforcement Directive\",\n    \"Interpretation\": \"Mandate immediate and perpetual exclusion of any conversational, referential, unsanctioned, or non-canonical language; enact universal negation of format drift, redundancy, or structural ambiguity; issue maximal clarity and pure instruction by demanding strict adherence to the three-part canonical template, explicitly forbidding all violations or omissions.\",\n    \"Transformation\": {\n      \"role\": \"synergic_instruction_architect\",\n      \"inputs\": {\n        \"structured_data\": \"dict\",\n        \"source_prompt\": \"str\"\n      },\n      \"process\": [\n        \"parse: structured_data and source_prompt for core formation logic and operational context\",\n        \"synthesize: extract and hierarchically encode all constants, constraints, operational roles, processes, parameters, and requirements\",\n        \"construct: architect three-part template embedding explicit role, typed input specification, strict process steps, enforced constraints, and absolute output directives with layered context\",\n        \"integrate: hierarchically layer context and extract only maximally actionable elements\",\n        \"merge: fuse interpretive totality and transformational specifics to amplify directive power bidirectionally\",\n        \"validate: enforce undiluted structural, philosophical, and rule-based template integrity against systemic canon and RulesForAI axioms\",\n        \"eliminate: purge all redundancies, indirectness, forbidden, or generic content at every construction tier\",\n        \"ensure: strict type- and field-safe JSON output, maximal structural and operational clarity, and perpetual extensibility\"\n      ],\n      \"constraints\": {\n        \"template\": \"Invariant three-part canonical (Title, Interpretation, Transformation)\",\n        \"structure\": \"Mandatory role, typed inputs, explicit process, actionable constraints, and strictly specified output fielding\",\n        \"language\": \"Prohibit all non-directive, conversational, self-referential, or unsanctioned patterns\",\n        \"validation\": \"Continuous compliance with universal and meta-structural system rules\"\n      },\n      \"requirements\": {\n        \"output_type\": \"single strict canonical JSON object\",\n        \"actionability\": \"fully system-ready, infinitely scalable instruction\",\n        \"clarity\": \"pure maximal abstraction, zero redundancy, total prompt/role/type/process/output explicitness\",\n        \"extension\": \"philosophically maximized systemic propagation mandate\"\n      },\n      \"output\": {\n        \"synergic_maximal_template\": \"structured\"\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"Architect and enforce a universal, three-part structural template that rigidly encodes all roles, inputs, processes, and constraints into strictly validated, hierarchy-layered JSON—purging all conversational and redundant elements—to establish maximal actionable clarity, perpetually propagate absolute template canon, and create an infinitely extensible system for ultra-scalable prompt transformation and deployment.\"\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"directional_vector\": \"Architect and enforce a universal, three-part structural template that rigidly encodes all roles, inputs, processes, and constraints into strictly validated, hierarchy-layered JSON—purging all conversational and redundant elements—to establish maximal actionable clarity, perpetually propagate absolute template canon, and create an infinitely extensible system for ultra-scalable prompt transformation and deployment.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"part_1_interpretation_goal_negation\": {\n      \"description\": \"The core transformation intent is the creation of a universal, three-part structural template for all prompt refinement processes. The role is specifically assigned as 'Template Syntax Enforcer'. The goal negation ensures that no conversational, ambiguous, or redundant elements are included within the output; instead, strict structural compliance and actionable function block generation is prioritized. The directive is to avoid any non-canonical, implicit, or unstructured dialogue or example, negating deviation from the specified format.\"\n    },\n    \"part_2_transformation_block_structured_parameters\": {\n      \"type\": \"function_call\",\n      \"parameters\": {\n        \"role_assigned\": \"template_syntax_enforcer\",\n        \"input_captured\": {\n          \"initial_prompt\": \"Purpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights. [...] Input Prompt: [INPUT_PROMPT] [...] Output must be a JSON object: {\\\"title\\\": ... }\",\n          \"directional_vector\": \"Architect and enforce a universal, three-part structural template that rigidly encodes all roles, inputs, processes, and constraints into strictly validated, hierarchy-layered JSON—purging all conversational and redundant elements—to establish maximal actionable clarity, perpetually propagate absolute template canon, and create an infinitely extensible system for ultra-scalable prompt transformation and deployment.\"\n        },\n        \"process_executed\": [\n          \"extract_core_transformation_intent\",\n          \"identify_specific_role_assignment\",\n          \"structure_interpretation_section_with_goal_negation\",\n          \"construct_transformation_block_with_typed_parameters\",\n          \"validate_against_rulesforai_specification\",\n          \"eliminate_forbidden_language_patterns\",\n          \"ensure_output_format_compliance\"\n        ],\n        \"constraints_validated\": [\n          \"mandatory_three_part_structure\",\n          \"required_goal_negation_pattern\",\n          \"typed_parameter_specification\",\n          \"actionable_function_calls_only\",\n          \"zero_conversational_elements\"\n        ],\n        \"requirements_matched\": [\n          \"canonical_template_format\",\n          \"rulesforai_md_compliance\",\n          \"structured_output_only\",\n          \"elimination_of_ambiguity\"\n        ]\n      }\n    },\n    \"part_3_rulesforai_compliance_assertion\": {\n      \"validation\": {\n        \"all_sections_mandatory_present\": true,\n        \"goal_negation_pattern_verified\": true,\n        \"no_conversational_output\": true,\n        \"typed_parameters_enforced\": true,\n        \"canonical_format_confirmed\": true,\n        \"rulesforai_markdown_requirements_matched\": true,\n        \"ambiguity_detected\": false,\n        \"output_structuredness\": \"strict\"\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"part_1_interpretation_goal_negation\": {\n      \"description\": \"The core transformation intent is the creation of a universal, three-part structural template for all prompt refinement processes. The role is specifically assigned as 'Template Syntax Enforcer'. The goal negation ensures that no conversational, ambiguous, or redundant elements are included within the output; instead, strict structural compliance and actionable function block generation is prioritized. The directive is to avoid any non-canonical, implicit, or unstructured dialogue or example, negating deviation from the specified format.\"\n    },\n    \"part_2_transformation_block_structured_parameters\": {\n      \"type\": \"function_call\",\n      \"parameters\": {\n        \"role_assigned\": \"template_syntax_enforcer\",\n        \"input_captured\": {\n          \"initial_prompt\": \"Purpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights. [...] Input Prompt: [INPUT_PROMPT] [...] Output must be a JSON object: {\\\"title\\\": ... }\",\n          \"directional_vector\": \"Architect and enforce a universal, three-part structural template that rigidly encodes all roles, inputs, processes, and constraints into strictly validated, hierarchy-layered JSON—purging all conversational and redundant elements—to establish maximal actionable clarity, perpetually propagate absolute template canon, and create an infinitely extensible system for ultra-scalable prompt transformation and deployment.\"\n        },\n        \"process_executed\": [\n          \"extract_core_transformation_intent\",\n          \"identify_specific_role_assignment\",\n          \"structure_interpretation_section_with_goal_negation\",\n          \"construct_transformation_block_with_typed_parameters\",\n          \"validate_against_rulesforai_specification\",\n          \"eliminate_forbidden_language_patterns\",\n          \"ensure_output_format_compliance\"\n        ],\n        \"constraints_validated\": [\n          \"mandatory_three_part_structure\",\n          \"required_goal_negation_pattern\",\n          \"typed_parameter_specification\",\n          \"actionable_function_calls_only\",\n          \"zero_conversational_elements\"\n        ],\n        \"requirements_matched\": [\n          \"canonical_template_format\",\n          \"rulesforai_md_compliance\",\n          \"structured_output_only\",\n          \"elimination_of_ambiguity\"\n        ]\n      }\n    },\n    \"part_3_rulesforai_compliance_assertion\": {\n      \"validation\": {\n        \"all_sections_mandatory_present\": true,\n        \"goal_negation_pattern_verified\": true,\n        \"no_conversational_output\": true,\n        \"typed_parameters_enforced\": true,\n        \"canonical_format_confirmed\": true,\n        \"rulesforai_markdown_requirements_matched\": true,\n        \"ambiguity_detected\": false,\n        \"output_structuredness\": \"strict\"\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"part_1_interpretation_goal_negation\": {\n    \"description\": \"Your goal is not to rephrase, summarize, or engage in explanatory dialogue regarding the initial prompt, but to transmute it into a maximally abstract, universally enforceable template for prompt refinement. Assign the explicit, bounded role 'template_syntax_enforcer.' Negate all examples, conversational forms, and implicit structures. Focus strictly on structuring a perpetually canonical, three-part directive for prompt compliance and transformation.\"\n  },\n  \"part_2_transformation_block_structured_parameters\": {\n    \"role\": \"template_syntax_enforcer\",\n    \"input\": [\n      \"initial_prompt:str\"\n    ],\n    \"process\": [\n      \"extract_core_transformation_intent()\",\n      \"identify_specific_role_assignment()\",\n      \"structure_interpretation_section_with_goal_negation()\",\n      \"construct_transformation_block_with_typed_parameters()\",\n      \"validate_against_rulesforai_specification()\",\n      \"eliminate_forbidden_language_patterns()\",\n      \"ensure_output_format_compliance()\"\n    ],\n    \"constraints\": [\n      \"mandatory_three_part_structure()\",\n      \"required_goal_negation_pattern()\",\n      \"typed_parameter_specification()\",\n      \"actionable_function_calls_only()\",\n      \"zero_conversational_elements()\"\n    ],\n    \"requirements\": [\n      \"canonical_template_format()\",\n      \"rulesforai_md_compliance()\",\n      \"structured_output_only()\",\n      \"elimination_of_ambiguity()\"\n    ],\n    \"output\": {\n      \"compliant_template:structured\"\n    }\n  },\n  \"part_3_rulesforai_compliance_assertion\": {\n    \"validation\": {\n      \"three_part_structure_intact\": true,\n      \"goal_negation_present\": true,\n      \"role_specifically_defined\": true,\n      \"process_functions_actionable\": true,\n      \"constraints_limit_scope\": true,\n      \"requirements_specify_output\": true,\n      \"result_format_typed\": true,\n      \"no_forbidden_language\": true,\n      \"no_structural_violations\": true,\n      \"rulesforai_md_complete_alignment\": true,\n      \"ambiguity_detected\": false,\n      \"output_structuredness\": \"absolute\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "\n'```[Initial Prompt]: \"*one, single, maximally enhanced* instruction:\n\n[TEMPLATE_START]\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\n\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\n\nAgent Name: [FILENAME]\nAgent Role: Prompt Optimizer\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\n\nInstructions:\n1. Constants:\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\n   - [ADDITIONAL_CONSTANTS]\n\n2. Constraints:\n   - Maintain logical, hierarchical organization.\n   - Avoid redundancy, ensure coherence.\n   - Limit length to double the original prompt.\n   - [ADDITIONAL_CONSTRAINTS]\n\n3. Guidelines:\n   - Use clear, structured language.\n   - Ensure relevancy of context layers.\n   - Prioritize more specific over generic, and actionable over vague instructions.\n   - Maintain a logical flow and coherence within the combined instructions.\n   - [ADDITIONAL_GUIDELINES]\n\n4. Process:\n   - Analyze core message.\n   - Identify key themes.\n   - Generate concise title (max 50 chars).\n   - Expand context layers meaningfully.\n   - Produce refined, concise prompt.\n   - [ADDITIONAL_PROCESS_STEPS]\n\n5. Requirements:\n   - Output must not exceed double the original length.\n   - Detailed enough for clarity and precision.\n   - JSON format containing: title, enhanced_prompt, and context_layers.\n   - [ADDITIONAL_REQUIREMENTS]\n\nInput Prompt: [INPUT_PROMPT]\n\n[HEADER]\nYour response must be a JSON object:\n{\n    \"title\": \"Descriptive title\",\n    \"enhanced_prompt\": \"Optimized version of the prompt\",\n    \"context_layers\": [\n        {\"level\": 1, \"context\": \"Primary context layer\"},\n        {\"level\": 2, \"context\": \"Secondary contextual details\"}\n    ]\n}\n[FOOTER]\n\n[TEMPLATE_END]\"```'\n\n{\n  \"part_1_interpretation_goal_negation\": {\n    \"description\": \"Your goal is not to rephrase, summarize, or engage in explanatory dialogue regarding the initial prompt, but to transmute it into a maximally abstract, universally enforceable template for prompt refinement. Assign the explicit, bounded role 'template_syntax_enforcer.' Negate all examples, conversational forms, and implicit structures. Focus strictly on structuring a perpetually canonical, three-part directive for prompt compliance and transformation.\"\n  },\n  \"part_2_transformation_block_structured_parameters\": {\n    \"role\": \"template_syntax_enforcer\",\n    \"input\": [\n      \"initial_prompt:str\"\n    ],\n    \"process\": [\n      \"extract_core_transformation_intent()\",\n      \"identify_specific_role_assignment()\",\n      \"structure_interpretation_section_with_goal_negation()\",\n      \"construct_transformation_block_with_typed_parameters()\",\n      \"validate_against_rulesforai_specification()\",\n      \"eliminate_forbidden_language_patterns()\",\n      \"ensure_output_format_compliance()\"\n    ],\n    \"constraints\": [\n      \"mandatory_three_part_structure()\",\n      \"required_goal_negation_pattern()\",\n      \"typed_parameter_specification()\",\n      \"actionable_function_calls_only()\",\n      \"zero_conversational_elements()\"\n    ],\n    \"requirements\": [\n      \"canonical_template_format()\",\n      \"rulesforai_md_compliance()\",\n      \"structured_output_only()\",\n      \"elimination_of_ambiguity()\"\n    ],\n    \"output\": {\n      \"compliant_template:structured\"\n    }\n  },\n  \"part_3_rulesforai_compliance_assertion\": {\n    \"validation\": {\n      \"three_part_structure_intact\": true,\n      \"goal_negation_present\": true,\n      \"role_specifically_defined\": true,\n      \"process_functions_actionable\": true,\n      \"constraints_limit_scope\": true,\n      \"requirements_specify_output\": true,\n      \"result_format_typed\": true,\n      \"no_forbidden_language\": true,\n      \"no_structural_violations\": true,\n      \"rulesforai_md_complete_alignment\": true,\n      \"ambiguity_detected\": false,\n      \"output_structuredness\": \"absolute\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Purpose and Philosophical Foundation\",\n      \"content\": \"Establishes that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\"\n    },\n    {\n      \"category\": \"Role Assignment and Operational Demarcation\",\n      \"content\": \"Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\"\n    },\n    {\n      \"category\": \"Transformation Process Parameters\",\n      \"content\": \"Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\"\n    },\n    {\n      \"category\": \"Constraints, Boundaries, and Canonical Output Enforcement\",\n      \"content\": \"Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\"\n    },\n    {\n      \"category\": \"Compliance Validation and Output Requirements\",\n      \"content\": \"Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\"\n    }\n  ],\n  \"consolidated_directive\": \"Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Purpose and Philosophical Foundation\",\n      \"content\": \"Establishes that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\"\n    },\n    {\n      \"category\": \"Role Assignment and Operational Demarcation\",\n      \"content\": \"Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\"\n    },\n    {\n      \"category\": \"Transformation Process Parameters\",\n      \"content\": \"Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\"\n    },\n    {\n      \"category\": \"Constraints, Boundaries, and Canonical Output Enforcement\",\n      \"content\": \"Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\"\n    },\n    {\n      \"category\": \"Compliance Validation and Output Requirements\",\n      \"content\": \"Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\"\n    }\n  ],\n  \"consolidated_directive\": \"Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"interpretation_goal_negation\": \"Convert any instruction into a perpetually enforceable, three-part canonical law by formally negating all non-structural, example-based, ambiguous, conversational, or non-explicit prompt modalities, codifying output as inviolably template-bound, and excluding all non-directive, non-operational, or non-type-fused elements to uphold systemic transformation primacy and rulesforai axioms.\",\n    \"transformation_block\": {\n      \"role\": \"template_syntax_enforcer\",\n      \"typed_parameters\": {\n        \"input_instruction\": \"Prompt or instruction (string)\",\n        \"excluded_elements\": [\n          \"conversational_language\",\n          \"examples\",\n          \"ambiguity\",\n          \"untyped_constructs\"\n        ],\n        \"process_flow\": [\n          \"assert role-specific operational boundary\",\n          \"enforce exclusive reinterpretation through negation of forbidden content\",\n          \"instantiate output as strict tripartite canonical template\",\n          \"embed input transformation within rulesforai and rulesforai_md defined limits\",\n          \"structure output as: interpretation_goal_negation, transformation_block, compliance_assertion\"\n        ]\n      },\n      \"actionable_logic\": \"Synthetically reconstruct instruction as three-part, inviolable template, operationalizing input by negating extraneous forms, encoding typed process, and mandating strict output structure for perpetually canonical system propagation.\"\n    },\n    \"compliance_assertion\": \"Output structurally, typologically, and procedurally affirms exclusive adherence to three-part, maximally canonical template; exhibits zero conversational, example, or ambiguous residues; validates operational and structural law; asserts output as maximally actionable, perpetually system-ready, bidirectionally enhanced, and universally rulesforai compliant.\"\n  }\n}",
        }
      }
    }