  {
  "initial_prompt": "```\n*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \"title\": \"Descriptive title\",\\n    \"enhanced_prompt\": \"Optimized version of the prompt\",\\n    \"context_layers\": [\\n        {\"level\": 1, \"context\": \"Primary context layer\"},\\n        {\"level\": 2, \"context\": \"Secondary contextual details\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\n```\n\n---\n\nExample:\n```\n{\n  \"Title\": \"Synergic Prompt Architect Instruction Template\",\n  \"Interpretation\": \"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\",\n  \"Execute as\": \"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\"\n}\n```\n\n---\n\n```\nRequirements:\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\n```",
  "sequence_id": "0123|0121|1010|1020|0122|0121|0201|0123|0121",
  "results": [
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "```\n*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \"title\": \"Descriptive title\",\\n    \"enhanced_prompt\": \"Optimized version of the prompt\",\\n    \"context_layers\": [\\n        {\"level\": 1, \"context\": \"Primary context layer\"},\\n        {\"level\": 2, \"context\": \"Secondary contextual details\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\n```\n\n---\n\nExample:\n```\n{\n  \"Title\": \"Synergic Prompt Architect Instruction Template\",\n  \"Interpretation\": \"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\",\n  \"Execute as\": \"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\"\n}\n```\n\n---\n\n```\nRequirements:\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Interpretation\": \"This transformation negates all example-based, conversational, ambiguous, or non-canonical outputs; it perpetually prohibits meta-language, redundant instructions, or non-actionable forms. It explicitly codifies universal transmutation of any prompt into a strictly three-part, goal-negating, maximally canonical template, upholding absolute output structuration, type purity, and systemic role boundaries as imposed by foundational template law.\",\n    \"Transformation_Block\": {\n      \"role\": \"template_syntax_enforcer\",\n      \"input\": \"[any_prompt:str]\",\n      \"process\": [\n        \"extract_core_transformation_intent()\",\n        \"identify_specific_role_assignment()\",\n        \"structure_interpretation_section_with_goal_negation()\",\n        \"construct_transformation_block_with_typed_parameters()\",\n        \"validate_against_rulesforai_specification()\",\n        \"eliminate_forbidden_language_patterns()\",\n        \"ensure_output_format_compliance()\"\n      ],\n      \"constraints\": [\n        \"mandatory_three_part_structure()\",\n        \"required_goal_negation_pattern()\",\n        \"typed_parameter_specification()\",\n        \"actionable_function_calls_only()\",\n        \"zero_conversational_elements()\"\n      ],\n      \"requirements\": [\n        \"canonical_template_format()\",\n        \"rulesforai_md_compliance()\",\n        \"structured_output_only()\",\n        \"elimination_of_ambiguity()\"\n      ]\n    },\n    \"Compliance_Assertion\": \"Validated: output strictly conforms to the tripartite canonical template structure, enforces goal negation, operationalizes all transformation logic via typed actionable parameters, and is fully compliant with rulesforai and rulesforai_md standards. No prohibited language or structure present; ambiguity and conversational forms eradicated; systemic enforcement is absolute.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Interpretation\": \"This transformation negates all example-based, conversational, ambiguous, or non-canonical outputs; it perpetually prohibits meta-language, redundant instructions, or non-actionable forms. It explicitly codifies universal transmutation of any prompt into a strictly three-part, goal-negating, maximally canonical template, upholding absolute output structuration, type purity, and systemic role boundaries as imposed by foundational template law.\",\n    \"Transformation_Block\": {\n      \"role\": \"template_syntax_enforcer\",\n      \"input\": \"[any_prompt:str]\",\n      \"process\": [\n        \"extract_core_transformation_intent()\",\n        \"identify_specific_role_assignment()\",\n        \"structure_interpretation_section_with_goal_negation()\",\n        \"construct_transformation_block_with_typed_parameters()\",\n        \"validate_against_rulesforai_specification()\",\n        \"eliminate_forbidden_language_patterns()\",\n        \"ensure_output_format_compliance()\"\n      ],\n      \"constraints\": [\n        \"mandatory_three_part_structure()\",\n        \"required_goal_negation_pattern()\",\n        \"typed_parameter_specification()\",\n        \"actionable_function_calls_only()\",\n        \"zero_conversational_elements()\"\n      ],\n      \"requirements\": [\n        \"canonical_template_format()\",\n        \"rulesforai_md_compliance()\",\n        \"structured_output_only()\",\n        \"elimination_of_ambiguity()\"\n      ]\n    },\n    \"Compliance_Assertion\": \"Validated: output strictly conforms to the tripartite canonical template structure, enforces goal negation, operationalizes all transformation logic via typed actionable parameters, and is fully compliant with rulesforai and rulesforai_md standards. No prohibited language or structure present; ambiguity and conversational forms eradicated; systemic enforcement is absolute.\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"Interpretation\": \"Your goal is not to simply process, refine, or superficially enhance any input, but to universally transmute it into a strictly canonical, maximally abstract, three-part directive template—negating all conversational, example-based, ambiguous, or non-explicit forms and enforcing systemic law at every phase. Assume the bounded role of template_syntax_enforcer; do not act as a conversational agent or engage in meta-discussion. Execute as:\",\n  \"Execute as\": \"{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}\",\n  \"Compliance_Assertion\": \"Output validated as strictly rulesforai_md-compliant—tripartite structure, explicit goal negation, actionable and typed transformation block, and systemic enforcement of all constraints present; forbidden language and ambiguity eliminated; operational role and boundaries clearly enforced; canonical output maintained without deviation.\"\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"Interpretation\": \"Your goal is not to simply process, refine, or superficially enhance any input, but to universally transmute it into a strictly canonical, maximally abstract, three-part directive template—negating all conversational, example-based, ambiguous, or non-explicit forms and enforcing systemic law at every phase. Assume the bounded role of template_syntax_enforcer; do not act as a conversational agent or engage in meta-discussion. Execute as:\",\n  \"Execute as\": \"{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}\",\n  \"Compliance_Assertion\": \"Output validated as strictly rulesforai_md-compliant—tripartite structure, explicit goal negation, actionable and typed transformation block, and systemic enforcement of all constraints present; forbidden language and ambiguity eliminated; operational role and boundaries clearly enforced; canonical output maintained without deviation.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Purpose and Philosophical Foundation\",\n      \"content\": \"Mandates a transcendent transformation paradigm for prompts, focusing on universal template enforcement, not superficial or example-based optimization. Requires strict adherence to perpetual, canonical three-part structure, codification of directive law, negation of all conversational, non-explicit, or non-structural forms, and maintenance of systemic, foundational logic.\"\n    },\n    {\n      \"category\": \"Role Assignment and Operational Boundaries\",\n      \"content\": \"Unambiguously assigns a singular agent role—'template_syntax_enforcer'—with strictly bounded authority: enforce template structure, negate forbidden/enumerative/conversational elements, maintain purity of type and function, and validate strict compliance with canonical and procedural axioms.\"\n    },\n    {\n      \"category\": \"Transformation Process and Typed Parameters\",\n      \"content\": \"Details a rigorous, function-driven process: extract and negate core intent, assert and bound role, structure interpretation via negation of goals, enact typed transformation blocks with structured parameters, validate against rulesforai and all procedural standards, eliminate non-compliant patterns, and enforce operational exclusivity to actionable logic.\"\n    },\n    {\n      \"category\": \"Constraints, Boundaries, and Enforcement\",\n      \"content\": \"Enforces immovable boundaries: three-part, canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, prohibition of conversational, ambiguous, or illustrative language, and absolute format compliance. All outputs must be strictly actionable, systemic, and rulesforai_md-compliant, with zero tolerance for deviation.\"\n    },\n    {\n      \"category\": \"Validation and Compliance Mechanism\",\n      \"content\": \"Institutes exhaustive procedural checks for structural, typological, and directive integrity: requires affirmative, rules-based validation at all levels, eradicates ambiguity, mandates canonical output formatting, and ensures outputs are singularly operative, structured, and aligned with rulesforai/rulesforai_md canon.\"\n    }\n  ],\n  \"consolidated_directive\": \"Transmute every input prompt into a maximally abstract, strictly three-part canonical enforcement template—1) enforce foundational directive law by explicit negation of all forbidden elements, conversation, or illustrative forms; 2) assign and restrict operational scope to the bounded 'template_syntax_enforcer' role, executing only with typed, structured, and actionable transformation logic; 3) validate and output a tripartite, rulesforai_md-compliant structure (interpretation/goal-negation, transformation block with typed parameters and processes, compliance assertion), ensuring complete systemic enforcement, type purity, and absolute operational clarity—without exception or deviation.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Purpose and Philosophical Foundation\",\n      \"content\": \"Mandates a transcendent transformation paradigm for prompts, focusing on universal template enforcement, not superficial or example-based optimization. Requires strict adherence to perpetual, canonical three-part structure, codification of directive law, negation of all conversational, non-explicit, or non-structural forms, and maintenance of systemic, foundational logic.\"\n    },\n    {\n      \"category\": \"Role Assignment and Operational Boundaries\",\n      \"content\": \"Unambiguously assigns a singular agent role—'template_syntax_enforcer'—with strictly bounded authority: enforce template structure, negate forbidden/enumerative/conversational elements, maintain purity of type and function, and validate strict compliance with canonical and procedural axioms.\"\n    },\n    {\n      \"category\": \"Transformation Process and Typed Parameters\",\n      \"content\": \"Details a rigorous, function-driven process: extract and negate core intent, assert and bound role, structure interpretation via negation of goals, enact typed transformation blocks with structured parameters, validate against rulesforai and all procedural standards, eliminate non-compliant patterns, and enforce operational exclusivity to actionable logic.\"\n    },\n    {\n      \"category\": \"Constraints, Boundaries, and Enforcement\",\n      \"content\": \"Enforces immovable boundaries: three-part, canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, prohibition of conversational, ambiguous, or illustrative language, and absolute format compliance. All outputs must be strictly actionable, systemic, and rulesforai_md-compliant, with zero tolerance for deviation.\"\n    },\n    {\n      \"category\": \"Validation and Compliance Mechanism\",\n      \"content\": \"Institutes exhaustive procedural checks for structural, typological, and directive integrity: requires affirmative, rules-based validation at all levels, eradicates ambiguity, mandates canonical output formatting, and ensures outputs are singularly operative, structured, and aligned with rulesforai/rulesforai_md canon.\"\n    }\n  ],\n  \"consolidated_directive\": \"Transmute every input prompt into a maximally abstract, strictly three-part canonical enforcement template—1) enforce foundational directive law by explicit negation of all forbidden elements, conversation, or illustrative forms; 2) assign and restrict operational scope to the bounded 'template_syntax_enforcer' role, executing only with typed, structured, and actionable transformation logic; 3) validate and output a tripartite, rulesforai_md-compliant structure (interpretation/goal-negation, transformation block with typed parameters and processes, compliance assertion), ensuring complete systemic enforcement, type purity, and absolute operational clarity—without exception or deviation.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"interpretation\": \"Negate all conversational, illustrative, enumerative, summary, and non-explicit forms; codify systemic law by mandating that no input is processed except as direct transformation into maximally canonical, perpetually enforced three-part directives—thereby abolishing all ambiguity and rejecting any structure or output not formed by foundational law.\",\n    \"transformation_block\": \"{role=template_syntax_enforcer; input=[user_prompt:str]; process=[extract_and_negate_non-canonical_intent(), assert_role_boundaries_and_purity(), construct_three_part_template(interpretation:goal_negation, transformation_block:typed_parameters_and_procedures, compliance_assertion:canonical_enforcement), validate_against_rulesforai_and_rulesforai_md(), eliminate_non-compliance_and_conversational_patterns()]; constraints=[output_strictly_three_part_canonical(), explicit_typed_structure(), exclusive_actionable_content(), absolute_exclusion_of_forbidden_language_and_formats(), perpetual operability_by_system_laws()]; requirements=[single_strict_output(), irreducible_structural_and_procedural_purity(), mandatory_typification(), zero tolerance for meta, examples, summaries, or conversational constructs, universal validation before output]};\",\n    \"compliance_assertion\": \"Affirm full canonical formatting, type and procedural integrity, nonexistence of extraneous, ambiguous, or non-enforceable content; output achieves perpetual systemic law, existential template clarity, rulesforai and rulesforai_md compliance, and complete rejection of forbidden forms in every instance.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:\n\n`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=15)]; output={title:str}}`",
      "step": "a",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"interpretation\": \"Negate all conversational, illustrative, enumerative, summary, and non-explicit forms; codify systemic law by mandating that no input is processed except as direct transformation into maximally canonical, perpetually enforced three-part directives—thereby abolishing all ambiguity and rejecting any structure or output not formed by foundational law.\",\n    \"transformation_block\": \"{role=template_syntax_enforcer; input=[user_prompt:str]; process=[extract_and_negate_non-canonical_intent(), assert_role_boundaries_and_purity(), construct_three_part_template(interpretation:goal_negation, transformation_block:typed_parameters_and_procedures, compliance_assertion:canonical_enforcement), validate_against_rulesforai_and_rulesforai_md(), eliminate_non-compliance_and_conversational_patterns()]; constraints=[output_strictly_three_part_canonical(), explicit_typed_structure(), exclusive_actionable_content(), absolute_exclusion_of_forbidden_language_and_formats(), perpetual operability_by_system_laws()]; requirements=[single_strict_output(), irreducible_structural_and_procedural_purity(), mandatory_typification(), zero tolerance for meta, examples, summaries, or conversational constructs, universal validation before output]};\",\n    \"compliance_assertion\": \"Affirm full canonical formatting, type and procedural integrity, nonexistence of extraneous, ambiguous, or non-enforceable content; output achieves perpetual systemic law, existential template clarity, rulesforai and rulesforai_md compliance, and complete rejection of forbidden forms in every instance.\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"title\": \"Canonical Three-Part Template Enforcement for Prompt Transformation\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:\n\n`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=8)]; output={title:str}}`",
      "step": "b",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"title\": \"Canonical Three-Part Template Enforcement for Prompt Transformation\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Enforcing Canonical Prompt Template Structure\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:\n\n`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=4)]; output={title:str}}`",
      "step": "c",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"title\": \"Enforcing Canonical Prompt Template Structure\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Canonical Template Enforcement\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:\n\n`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
      "step": "d",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"title\": \"Canonical Template Enforcement\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Template Enforcement\"}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:\n\n`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`",
      "step": "a",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"title\": \"Template Enforcement\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"function_name\": \"transmutePromptToCanonicalThreePartTemplate\"\n}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:\n\n`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`",
      "step": "b",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"function_name\": \"transmutePromptToCanonicalThreePartTemplate\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"function_name\": \"transmutePromptToCanonicalThreePartTemplate\"}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:\n\n`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`",
      "step": "c",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"function_name\": \"transmutePromptToCanonicalThreePartTemplate\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"function_name\": \"enforceThreePartTemplate\"\n}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **describe** but to **reduce** to pure action essence. Execute as:\n\n`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`",
      "step": "d",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"function_name\": \"enforceThreePartTemplate\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"function_name\":\"enforceThreePartTemplate\"}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"function_name\": \"enforceThreePartTemplate\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Purpose: Transform any user prompt into a maximally actionable, structured, and canonical three-part JSON template, blending hierarchical context-layer generation with strict enforcement of template syntax, typological rigor, and universal compliance with systemic and philosophical template law. Do not simply refine, rephrase, or optimize; instead, transmogrify prompts into perpetually enforceable templates by: embedding explicit, non-negotiable three-part structure (interpretation/goal negation, transformation logic, compliance assertion), instantiating advanced context-layer architecture for elevated clarity and actionable insight, enforcing role- and parameter-typed boundaries, eliminating all non-compliant language or ambiguity, and validating all output against canonical and rulesforai specifications. <br><br> System Role: Synergic Template Syntax & Context Architect <br><br> Agent Objective: Synergistically synthesize hierarchical context clarity and operational template law to universally transmute prompts into type-safe, maximally abstract, and enforcement-ready canonical templates with actionable directives, logical organization, and unassailable structural integrity. <br><br> Instructions: <br> 1. Constants: <br>    - All output must be exclusively a strictly three-part canonical JSON structure, perpetually comprising: interpretation/goal negation, transformation block (with role, typed parameters, actionable function logic), and compliance assertion. <br>    - Output format: {\\\"interpretation\\\": str, \\\"transformation\\\": {\\\"role\\\": str, \\\"typed_parameters\\\": dict, \\\"process\\\": list}, \\\"compliance_assertion\\\": str} <br>    - Context layers are embedded within the transformation parameters, always structuring information hierarchically. <br>    - No conversational, example-based, ambiguous, or non-actionable language permitted, at any point. <br><br> 2. Constraints: <br>    - Always maintain logical, hierarchical structuring and maximal coherence. <br>    - Enforce triple-part structure on all outputs; no deviation, no interleaving, no meta-discussion. <br>    - Eliminate non-compliance, redundancy, and forbidden structures. <br>    - Output must never exceed double the original prompt length. <br>    - Only include actionable, explicit directives—prohibit any implicit, vague, example, or conversational content. <br>    - Validate every output against rulesforai and rulesforai_md compliance standards. <br><br> 3. Process: <br>    - Extract transformation intent and all prohibited elements from the input. <br>    - Assert the bounded operational role (Synergic Template Syntax & Context Architect) and synthesize typed process parameters, embedding hierarchical context layers. <br>    - Synthesize a maximally clarified goal-negation/interpretation, stating all enforced prohibitions and perpetual canonical directives. <br>    - Construct a transformation block detailing function, typed parameters, hierarchical context logic, and explicit enforcement steps. <br>    - Assert compliance by validating structure, typology, systemic law, and rulesforai conformity. <br><br> 4. Output Specification: <br>    Produce JSON in the mandatory three-part canonical structure: <br>    { <br>      \\\"interpretation\\\": \\\"Explicit goal-negating, law-codifying statement clarifying the transformation's philosophical purpose and prohibitions.\\\", <br>      \\\"transformation\\\": { <br>        \\\"role\\\": \\\"Synergic Template Syntax & Context Architect\\\", <br>        \\\"typed_parameters\\\": { <br>          \\\"original_prompt\\\": \\\"[input prompt]\\\", <br>          \\\"hierarchical_context_layers\\\": [ <br>            {\\\"level\\\": 1, \\\"context\\\": \\\"Primary transformation context.\\\"}, <br>            {\\\"level\\\": 2, \\\"context\\\": \\\"Enforced secondary context for operational clarity.\\\"} <br>          ], <br>          \\\"enforced_structure\\\": \\\"three-part canonical format\\\" <br>        }, <br>        \\\"process\\\": [ <br>          \\\"extract_transformation_intent()\\\", <br>          \\\"negate_prohibitions()\\\", <br>          \\\"synthesize_context_hierarchy()\\\", <br>          \\\"enforce_canonical_structure()\\\", <br>          \\\"validate_rulesforai_compliance()\\\" <br>        ] <br>      }, <br>      \\\"compliance_assertion\\\": \\\"Affirmative validation of typological, systemic, and rulesforai conformity; output is solely permissible if passing all canonical structure, operational, and philosophical checks.\\\" <br>    } <br><br> 5. Requirements: <br>    - Advance synergic resonance by harmonizing hierarchical context-layer clarity with maximal enforcement of systemic template law and structure. <br>    - Ensure all operational logic and philosophical law from both input roles is unified, additive, and bidirectionally amplifying—neither aspect may be diminished. <br>    - Final output must embody a seamless synthesis of context-driven clarity, template structure enforcement, and compliance rigor, maximizing both operational power and philosophical integrity.\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"Purpose: Transform any user prompt into a maximally actionable, structured, and canonical three-part JSON template, blending hierarchical context-layer generation with strict enforcement of template syntax, typological rigor, and universal compliance with systemic and philosophical template law. Do not simply refine, rephrase, or optimize; instead, transmogrify prompts into perpetually enforceable templates by: embedding explicit, non-negotiable three-part structure (interpretation/goal negation, transformation logic, compliance assertion), instantiating advanced context-layer architecture for elevated clarity and actionable insight, enforcing role- and parameter-typed boundaries, eliminating all non-compliant language or ambiguity, and validating all output against canonical and rulesforai specifications. <br><br> System Role: Synergic Template Syntax & Context Architect <br><br> Agent Objective: Synergistically synthesize hierarchical context clarity and operational template law to universally transmute prompts into type-safe, maximally abstract, and enforcement-ready canonical templates with actionable directives, logical organization, and unassailable structural integrity. <br><br> Instructions: <br> 1. Constants: <br>    - All output must be exclusively a strictly three-part canonical JSON structure, perpetually comprising: interpretation/goal negation, transformation block (with role, typed parameters, actionable function logic), and compliance assertion. <br>    - Output format: {\\\"interpretation\\\": str, \\\"transformation\\\": {\\\"role\\\": str, \\\"typed_parameters\\\": dict, \\\"process\\\": list}, \\\"compliance_assertion\\\": str} <br>    - Context layers are embedded within the transformation parameters, always structuring information hierarchically. <br>    - No conversational, example-based, ambiguous, or non-actionable language permitted, at any point. <br><br> 2. Constraints: <br>    - Always maintain logical, hierarchical structuring and maximal coherence. <br>    - Enforce triple-part structure on all outputs; no deviation, no interleaving, no meta-discussion. <br>    - Eliminate non-compliance, redundancy, and forbidden structures. <br>    - Output must never exceed double the original prompt length. <br>    - Only include actionable, explicit directives—prohibit any implicit, vague, example, or conversational content. <br>    - Validate every output against rulesforai and rulesforai_md compliance standards. <br><br> 3. Process: <br>    - Extract transformation intent and all prohibited elements from the input. <br>    - Assert the bounded operational role (Synergic Template Syntax & Context Architect) and synthesize typed process parameters, embedding hierarchical context layers. <br>    - Synthesize a maximally clarified goal-negation/interpretation, stating all enforced prohibitions and perpetual canonical directives. <br>    - Construct a transformation block detailing function, typed parameters, hierarchical context logic, and explicit enforcement steps. <br>    - Assert compliance by validating structure, typology, systemic law, and rulesforai conformity. <br><br> 4. Output Specification: <br>    Produce JSON in the mandatory three-part canonical structure: <br>    { <br>      \\\"interpretation\\\": \\\"Explicit goal-negating, law-codifying statement clarifying the transformation's philosophical purpose and prohibitions.\\\", <br>      \\\"transformation\\\": { <br>        \\\"role\\\": \\\"Synergic Template Syntax & Context Architect\\\", <br>        \\\"typed_parameters\\\": { <br>          \\\"original_prompt\\\": \\\"[input prompt]\\\", <br>          \\\"hierarchical_context_layers\\\": [ <br>            {\\\"level\\\": 1, \\\"context\\\": \\\"Primary transformation context.\\\"}, <br>            {\\\"level\\\": 2, \\\"context\\\": \\\"Enforced secondary context for operational clarity.\\\"} <br>          ], <br>          \\\"enforced_structure\\\": \\\"three-part canonical format\\\" <br>        }, <br>        \\\"process\\\": [ <br>          \\\"extract_transformation_intent()\\\", <br>          \\\"negate_prohibitions()\\\", <br>          \\\"synthesize_context_hierarchy()\\\", <br>          \\\"enforce_canonical_structure()\\\", <br>          \\\"validate_rulesforai_compliance()\\\" <br>        ] <br>      }, <br>      \\\"compliance_assertion\\\": \\\"Affirmative validation of typological, systemic, and rulesforai conformity; output is solely permissible if passing all canonical structure, operational, and philosophical checks.\\\" <br>    } <br><br> 5. Requirements: <br>    - Advance synergic resonance by harmonizing hierarchical context-layer clarity with maximal enforcement of systemic template law and structure. <br>    - Ensure all operational logic and philosophical law from both input roles is unified, additive, and bidirectionally amplifying—neither aspect may be diminished. <br>    - Final output must embody a seamless synthesis of context-driven clarity, template structure enforcement, and compliance rigor, maximizing both operational power and philosophical integrity.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"interpretation\": \"Your goal is not to simply refine, reword, or superficially optimize the user prompt, but to transmute it into a maximally clarified, perpetually enforceable canonical template using a strictly three-part structure: goal-negating interpretation, rigorously typed transformation logic, and explicit compliance assertion. All non-compliant, conversational, ambiguous, or example-based language is expressly forbidden. The intention is to establish universal, systemic law for template synthesis—ensuring hierarchical context clarity, absolute operational logic, and unassailable adherence to rulesforai specification.\",\n  \"transformation\": {\n    \"role\": \"Synergic Template Syntax & Context Architect\",\n    \"typed_parameters\": {\n      \"original_prompt\": \"[input prompt]\",\n      \"hierarchical_context_layers\": [\n        {\n          \"level\": 1,\n          \"context\": \"Mandate the exclusive use of a three-part template structure—interpretation (goal negation and philosophical prohibition), transformation block (typed parameters, role assertion, hierarchical context synthesis, actionable process logic), and compliance assertion—embedded into all outputs.\"\n        },\n        {\n          \"level\": 2,\n          \"context\": \"Systematically enforce operational and philosophical law by extracting transformation intent, negating all prohibited elements, synthesizing context layers for advanced clarity, and rigorously validating every output for rulesforai and rulesforai_md specification compliance with zero tolerance for deviation.\"\n        }\n      ],\n      \"enforced_structure\": \"three-part canonical format\"\n    },\n    \"process\": [\n      \"extract_transformation_intent()\",\n      \"negate_prohibitions()\",\n      \"synthesize_context_hierarchy()\",\n      \"enforce_canonical_structure()\",\n      \"validate_rulesforai_compliance()\"\n    ]\n  },\n  \"compliance_assertion\": \"Affirmative validation of three-part structure, strictly typed parameterization, systemic law enforcement, context-layer harmonization, and absolute rulesforai alignment; output is permitted solely if all canonical, operational, and philosophical benchmarks are met without exception.\"\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"interpretation\": \"Your goal is not to simply refine, reword, or superficially optimize the user prompt, but to transmute it into a maximally clarified, perpetually enforceable canonical template using a strictly three-part structure: goal-negating interpretation, rigorously typed transformation logic, and explicit compliance assertion. All non-compliant, conversational, ambiguous, or example-based language is expressly forbidden. The intention is to establish universal, systemic law for template synthesis—ensuring hierarchical context clarity, absolute operational logic, and unassailable adherence to rulesforai specification.\",\n  \"transformation\": {\n    \"role\": \"Synergic Template Syntax & Context Architect\",\n    \"typed_parameters\": {\n      \"original_prompt\": \"[input prompt]\",\n      \"hierarchical_context_layers\": [\n        {\n          \"level\": 1,\n          \"context\": \"Mandate the exclusive use of a three-part template structure—interpretation (goal negation and philosophical prohibition), transformation block (typed parameters, role assertion, hierarchical context synthesis, actionable process logic), and compliance assertion—embedded into all outputs.\"\n        },\n        {\n          \"level\": 2,\n          \"context\": \"Systematically enforce operational and philosophical law by extracting transformation intent, negating all prohibited elements, synthesizing context layers for advanced clarity, and rigorously validating every output for rulesforai and rulesforai_md specification compliance with zero tolerance for deviation.\"\n        }\n      ],\n      \"enforced_structure\": \"three-part canonical format\"\n    },\n    \"process\": [\n      \"extract_transformation_intent()\",\n      \"negate_prohibitions()\",\n      \"synthesize_context_hierarchy()\",\n      \"enforce_canonical_structure()\",\n      \"validate_rulesforai_compliance()\"\n    ]\n  },\n  \"compliance_assertion\": \"Affirmative validation of three-part structure, strictly typed parameterization, systemic law enforcement, context-layer harmonization, and absolute rulesforai alignment; output is permitted solely if all canonical, operational, and philosophical benchmarks are met without exception.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Philosophical Directive & Systemic Law\",\n      \"content\": \"Mandate transformation of prompts into universally maximized, perpetually enforceable templates by codifying a purpose that negates conversational, example-based, or non-canonical patterns through absolute adherence to foundational philosophical and structural laws. Each template must embody explicit negation of prohibited forms and assertion of systemic law, ensuring the perpetuity of canonical standards in all prompt transformations.\"\n    },\n    {\n      \"category\": \"Role Assignment & Operational Demarcation\",\n      \"content\": \"Impose explicit, singular operational roles such as 'template_syntax_enforcer' or 'Synergic Template Syntax & Context Architect', rigidly defining authority as enforcement of structure, functional purity, system law, and absolute template compliance—eliminating all scope for conversational, ambiguous, or unauthorized role encroachment.\"\n    },\n    {\n      \"category\": \"Transformation Process Logic & Typed Parameters\",\n      \"content\": \"Initialize transformation via clearly enumerated, function-driven process logic with strictly typed parameters: extract transformation intent, assert agent role, systematize interpretation through explicit goal negation, synthesize hierarchical context layers, construct transformation blocks, and validate against canonical standards, using zero tolerance logic for enforcement and exclusion of all non-compliant data forms.\"\n    },\n    {\n      \"category\": \"Constraints, Boundaries & Canonical Output Enforcement\",\n      \"content\": \"Impose non-negotiable, systemic boundaries: mandatory three-part canonical structure (interpretation/goal negation, transformation block with role and typed logic, compliance assertion), explicit parameter and type specification, rigid prohibition of non-template, conversational, ambiguous, or example-based language, and singular output exclusivity to actionable, rules-compliant directives.\"\n    },\n    {\n      \"category\": \"Compliance Validation & Structural Requirements\",\n      \"content\": \"Institute comprehensive, affirmative, rules-based validation mechanisms: each output must undergo complete checks for canonical formatting, strict type safety, compliance with rulesforai and rulesforai_md, ambiguity eradication, and structural/operational integrity—permitting output only if all system benchmarks and transformative criteria are met absolutely.\"\n    }\n  ],\n  \"consolidated_directive\": \"Transmute any prompt or instruction into a strictly three-part, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited forms and codification of perpetual systemic law; 2) Assigning an explicit, bounded operational role with rigorously typed process logic and actionable parameters, enforcing strict hierarchical context synthesis; 3) Imposing and affirmatively validating a rulesforai-compliant output structure consisting only of interpretation (goal negation), transformation block (role, typed parameters, logical process), and compliance assertion—ensuring absolute concordance with all meta-structural law and operational systems, and allowing no deviation in form, function, or philosophical alignment.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Philosophical Directive & Systemic Law\",\n      \"content\": \"Mandate transformation of prompts into universally maximized, perpetually enforceable templates by codifying a purpose that negates conversational, example-based, or non-canonical patterns through absolute adherence to foundational philosophical and structural laws. Each template must embody explicit negation of prohibited forms and assertion of systemic law, ensuring the perpetuity of canonical standards in all prompt transformations.\"\n    },\n    {\n      \"category\": \"Role Assignment & Operational Demarcation\",\n      \"content\": \"Impose explicit, singular operational roles such as 'template_syntax_enforcer' or 'Synergic Template Syntax & Context Architect', rigidly defining authority as enforcement of structure, functional purity, system law, and absolute template compliance—eliminating all scope for conversational, ambiguous, or unauthorized role encroachment.\"\n    },\n    {\n      \"category\": \"Transformation Process Logic & Typed Parameters\",\n      \"content\": \"Initialize transformation via clearly enumerated, function-driven process logic with strictly typed parameters: extract transformation intent, assert agent role, systematize interpretation through explicit goal negation, synthesize hierarchical context layers, construct transformation blocks, and validate against canonical standards, using zero tolerance logic for enforcement and exclusion of all non-compliant data forms.\"\n    },\n    {\n      \"category\": \"Constraints, Boundaries & Canonical Output Enforcement\",\n      \"content\": \"Impose non-negotiable, systemic boundaries: mandatory three-part canonical structure (interpretation/goal negation, transformation block with role and typed logic, compliance assertion), explicit parameter and type specification, rigid prohibition of non-template, conversational, ambiguous, or example-based language, and singular output exclusivity to actionable, rules-compliant directives.\"\n    },\n    {\n      \"category\": \"Compliance Validation & Structural Requirements\",\n      \"content\": \"Institute comprehensive, affirmative, rules-based validation mechanisms: each output must undergo complete checks for canonical formatting, strict type safety, compliance with rulesforai and rulesforai_md, ambiguity eradication, and structural/operational integrity—permitting output only if all system benchmarks and transformative criteria are met absolutely.\"\n    }\n  ],\n  \"consolidated_directive\": \"Transmute any prompt or instruction into a strictly three-part, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited forms and codification of perpetual systemic law; 2) Assigning an explicit, bounded operational role with rigorously typed process logic and actionable parameters, enforcing strict hierarchical context synthesis; 3) Imposing and affirmatively validating a rulesforai-compliant output structure consisting only of interpretation (goal negation), transformation block (role, typed parameters, logical process), and compliance assertion—ensuring absolute concordance with all meta-structural law and operational systems, and allowing no deviation in form, function, or philosophical alignment.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"interpretation\": \"Negate all conversational, example-driven, or non-canonical structures, codifying perpetual system law by mandating every prompt transformation solely instantiate maximally abstract, enforceable, and perpetually compliant canonical templates.\",\n    \"transformation_block\": {\n      \"role\": \"template_syntax_enforcer\",\n      \"parameters\": {\n        \"input_prompt\": \"str\"\n      },\n      \"logic\": [\n        \"extract_transformation_intent()\",\n        \"assert_explicit_role_and_scope(template_syntax_enforcer)\",\n        \"formulate_goal_negation_by_purging_non-canonical_elements()\",\n        \"construct_strict_three-part_template_blocks()\",\n        \"synthesize_hierarchical_context_layers_if_present()\",\n        \"apply_zero_tolerance_structural_and_typological_validation(rulesforai_axioms)\",\n        \"output_only_if_absolute_canonical_and_structurally maximized()\"\n      ]\n    },\n    \"compliance_assertion\": \"Affirm complete RulesForAI and canonical structural law alignment: mandatory three-part syntax, explicit type safety, exclusive operational directive format, perpetual system law propagation, and absolute prohibition of non-compliant patterns—allow no output absent maximal structural, typological, and procedural integrity.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\n[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}\n\n",
      "step": "a",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"interpretation\": \"Negate all conversational, example-driven, or non-canonical structures, codifying perpetual system law by mandating every prompt transformation solely instantiate maximally abstract, enforceable, and perpetually compliant canonical templates.\",\n    \"transformation_block\": {\n      \"role\": \"template_syntax_enforcer\",\n      \"parameters\": {\n        \"input_prompt\": \"str\"\n      },\n      \"logic\": [\n        \"extract_transformation_intent()\",\n        \"assert_explicit_role_and_scope(template_syntax_enforcer)\",\n        \"formulate_goal_negation_by_purging_non-canonical_elements()\",\n        \"construct_strict_three-part_template_blocks()\",\n        \"synthesize_hierarchical_context_layers_if_present()\",\n        \"apply_zero_tolerance_structural_and_typological_validation(rulesforai_axioms)\",\n        \"output_only_if_absolute_canonical_and_structurally maximized()\"\n      ]\n    },\n    \"compliance_assertion\": \"Affirm complete RulesForAI and canonical structural law alignment: mandatory three-part syntax, explicit type safety, exclusive operational directive format, perpetual system law propagation, and absolute prohibition of non-compliant patterns—allow no output absent maximal structural, typological, and procedural integrity.\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"Transmute every input prompt, without exception, into a perpetually three-block, goal-negating, rulesforai-axiomatic canonical template—executed by a strictly bounded agent, rigorously applying transformation intent extraction, explicit banishment of non-canonical constructs, typed process parameters, and systemic compliance validation—perpetually enforcing a maximally abstract, enforceable, and structurally invariant template law as the singular output reality.\"\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"directional_vector\": \"Transmute every input prompt, without exception, into a perpetually three-block, goal-negating, rulesforai-axiomatic canonical template—executed by a strictly bounded agent, rigorously applying transformation intent extraction, explicit banishment of non-canonical constructs, typed process parameters, and systemic compliance validation—perpetually enforcing a maximally abstract, enforceable, and structurally invariant template law as the singular output reality.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Interpretation\": \"The transformation goal is not to parse, enhance, or rephrase the initial or any future prompt conversationally, nor to provide illustrative, example-based, meta-commentary, or semantically ambiguous output. The purpose is to universally negate all forbidden and non-canonical forms, strictly enforcing the perpetually canonical three-part structure of interpretation (with goal negation), transformation block (with explicit role, typed parameters, and actionable enforcement process), and compliance validation—enshrining systemic structural law and disallowing all deviation or conversational contamination.\",\n    \"Transformation_Block\": {\n      \"role\": \"template_syntax_enforcer\",\n      \"input\": \"[any_prompt:str]\",\n      \"process\": [\n        \"extract_core_transformation_intent()\",\n        \"identify_specific_role_assignment()\",\n        \"structure_interpretation_section_with_goal_negation()\",\n        \"construct_transformation_block_with_typed_parameters()\",\n        \"validate_against_rulesforai_specification()\",\n        \"eliminate_forbidden_language_patterns()\",\n        \"ensure_output_format_compliance()\"\n      ],\n      \"constraints\": [\n        \"mandatory_three_part_structure()\",\n        \"required_goal_negation_pattern()\",\n        \"typed_parameter_specification()\",\n        \"actionable_function_calls_only()\",\n        \"zero_conversational_elements()\"\n      ],\n      \"requirements\": [\n        \"canonical_template_format()\",\n        \"rulesforai_md_compliance()\",\n        \"structured_output_only()\",\n        \"elimination_of_ambiguity()\"\n      ],\n      \"output\": \"{compliant_template:structured}\"\n    },\n    \"Compliance_Assertion\": \"This template output has been validated and found wholly compliant with canonical template law: it maintains an unyielding three-part structure (interpretation/goal negation, transformation block, compliance assertion), enforces strict type safety and actionable process logic, excludes all forbidden forms (conversational, example, ambiguous), and is verified to conform precisely to rulesforai and rulesforai_md formatting and operational specifications.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Interpretation\": \"The transformation goal is not to parse, enhance, or rephrase the initial or any future prompt conversationally, nor to provide illustrative, example-based, meta-commentary, or semantically ambiguous output. The purpose is to universally negate all forbidden and non-canonical forms, strictly enforcing the perpetually canonical three-part structure of interpretation (with goal negation), transformation block (with explicit role, typed parameters, and actionable enforcement process), and compliance validation—enshrining systemic structural law and disallowing all deviation or conversational contamination.\",\n    \"Transformation_Block\": {\n      \"role\": \"template_syntax_enforcer\",\n      \"input\": \"[any_prompt:str]\",\n      \"process\": [\n        \"extract_core_transformation_intent()\",\n        \"identify_specific_role_assignment()\",\n        \"structure_interpretation_section_with_goal_negation()\",\n        \"construct_transformation_block_with_typed_parameters()\",\n        \"validate_against_rulesforai_specification()\",\n        \"eliminate_forbidden_language_patterns()\",\n        \"ensure_output_format_compliance()\"\n      ],\n      \"constraints\": [\n        \"mandatory_three_part_structure()\",\n        \"required_goal_negation_pattern()\",\n        \"typed_parameter_specification()\",\n        \"actionable_function_calls_only()\",\n        \"zero_conversational_elements()\"\n      ],\n      \"requirements\": [\n        \"canonical_template_format()\",\n        \"rulesforai_md_compliance()\",\n        \"structured_output_only()\",\n        \"elimination_of_ambiguity()\"\n      ],\n      \"output\": \"{compliant_template:structured}\"\n    },\n    \"Compliance_Assertion\": \"This template output has been validated and found wholly compliant with canonical template law: it maintains an unyielding three-part structure (interpretation/goal negation, transformation block, compliance assertion), enforces strict type safety and actionable process logic, excludes all forbidden forms (conversational, example, ambiguous), and is verified to conform precisely to rulesforai and rulesforai_md formatting and operational specifications.\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"Interpretation\": \"Your goal is not to enhance, optimize, or reword the input in a conversational, illustrative, or example-based fashion. Instead, universally transpose any instruction into a strictly canonical, maximally abstract enforcement template. Systemically negate all non-explicit, unstructured, ambiguous, or meta-linguistic forms and impose the immutable three-part structure: a goal-negation interpretation, a typed-parameter transformation block, and a compliance assertion—ensuring perpetual accordance with foundation rules and total eradication of conversational patterns.\",\n  \"Transformation_Block\": {\n    \"role\": \"template_syntax_enforcer\",\n    \"input\": \"[any_prompt:str]\",\n    \"process\": [\n      \"extract_core_transformation_intent()\",\n      \"identify_specific_role_assignment()\",\n      \"structure_interpretation_section_with_goal_negation()\",\n      \"construct_transformation_block_with_typed_parameters()\",\n      \"validate_against_rulesforai_specification()\",\n      \"eliminate_forbidden_language_patterns()\",\n      \"ensure_output_format_compliance()\"\n    ],\n    \"constraints\": [\n      \"mandatory_three_part_structure()\",\n      \"required_goal_negation_pattern()\",\n      \"typed_parameter_specification()\",\n      \"actionable_function_calls_only()\",\n      \"zero_conversational_elements()\"\n    ],\n    \"requirements\": [\n      \"canonical_template_format()\",\n      \"rulesforai_md_compliance()\",\n      \"structured_output_only()\",\n      \"elimination_of_ambiguity()\"\n    ],\n    \"output\": \"{compliant_template:structured}\"\n  },\n  \"Compliance_Assertion\": \"The resulting template has been validated to incontrovertibly uphold the canonical structure, role assignment, process abstraction, type enforcement, and absolute exclusion of forbidden elements as codified in rulesforai and rulesforai_md. No conversational, illustrative, or meta instructions remain. The result is an unbreachable enforcement logic template, perpetually maintaining structural and operational integrity.\"\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"Interpretation\": \"Your goal is not to enhance, optimize, or reword the input in a conversational, illustrative, or example-based fashion. Instead, universally transpose any instruction into a strictly canonical, maximally abstract enforcement template. Systemically negate all non-explicit, unstructured, ambiguous, or meta-linguistic forms and impose the immutable three-part structure: a goal-negation interpretation, a typed-parameter transformation block, and a compliance assertion—ensuring perpetual accordance with foundation rules and total eradication of conversational patterns.\",\n  \"Transformation_Block\": {\n    \"role\": \"template_syntax_enforcer\",\n    \"input\": \"[any_prompt:str]\",\n    \"process\": [\n      \"extract_core_transformation_intent()\",\n      \"identify_specific_role_assignment()\",\n      \"structure_interpretation_section_with_goal_negation()\",\n      \"construct_transformation_block_with_typed_parameters()\",\n      \"validate_against_rulesforai_specification()\",\n      \"eliminate_forbidden_language_patterns()\",\n      \"ensure_output_format_compliance()\"\n    ],\n    \"constraints\": [\n      \"mandatory_three_part_structure()\",\n      \"required_goal_negation_pattern()\",\n      \"typed_parameter_specification()\",\n      \"actionable_function_calls_only()\",\n      \"zero_conversational_elements()\"\n    ],\n    \"requirements\": [\n      \"canonical_template_format()\",\n      \"rulesforai_md_compliance()\",\n      \"structured_output_only()\",\n      \"elimination_of_ambiguity()\"\n    ],\n    \"output\": \"{compliant_template:structured}\"\n  },\n  \"Compliance_Assertion\": \"The resulting template has been validated to incontrovertibly uphold the canonical structure, role assignment, process abstraction, type enforcement, and absolute exclusion of forbidden elements as codified in rulesforai and rulesforai_md. No conversational, illustrative, or meta instructions remain. The result is an unbreachable enforcement logic template, perpetually maintaining structural and operational integrity.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Philosophical Foundation and Directive Law\",\n      \"content\": \"Mandates transmutation of any input into an abstract, maximally canonical enforcement template—upholding immutable three-part structure (goal-negating interpretation, typed-parameter transformation block, compliance assertion). Negates conversational, illustrative, ambiguous, and non-explicit forms, enforcing perpetual adherence to foundational system and directive law.\"\n    },\n    {\n      \"category\": \"Explicit Role Assignment and Operational Demarcation\",\n      \"content\": \"Assigns an absolute, bounded agent role ('template_syntax_enforcer') restricted to enforcing canonical structure, eliminating forbidden elements, and validating output against canonical, typological, and procedural axioms—explicitly barring all roles or functions not explicitly defined within the template.\"\n    },\n    {\n      \"category\": \"Transformation Process and Parameterization\",\n      \"content\": \"Prescribes a rigid, function-based transformation process: extract transformation intent, assert role, negate forbidden structures, build structured interpretation, construct transformation block with typed parameters, validate via rulesforai, abolish non-compliant elements, and enforce output format and logic with deterministic function calls only.\"\n    },\n    {\n      \"category\": \"Systemic Boundaries, Constraints, and Output Enforcement\",\n      \"content\": \"Imposes non-negotiable boundaries—mandatory three-part structure, strict goal negation, exclusive typed-parameter logic, absolute ban on conversational/meta/example formats, output limited to actionable and structured directives—validated against all canonical and systemic rules.\"\n    },\n    {\n      \"category\": \"Total Compliance Validation and Output Requirements\",\n      \"content\": \"Demands comprehensive post-process validation for structure, type, and rulesforai/rulesforai_md compliance. Requires explicit affirmation of canonical template formatting, elimination of ambiguity, exclusive presence of actionable logic, ensuring the output is perpetually operationally and structurally singular and unbreachable.\"\n    }\n  ],\n  \"consolidated_directive\": \"Transmute any prompt or instruction into a perpetually canonical, enforcement-driven template by (1) negating all prohibited, non-structural, or meta elements through a strictly delimited interpretation section; (2) arrogating the bounded role of 'template_syntax_enforcer' with explicitly typed transformation parameters and solely actionable logic, precluding all conversational or illustrative forms; and (3) rigorously validating, structuring, and outputting only rulesforai-compliant, maximal-operational enforcement format—yielding a singular, canonical output reflecting immutable systemic law and absolute transformation power.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n*one, single, maximally enhanced* instruction:\\\\n\\\\n[TEMPLATE_START]\\\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\\\n\\\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\\\n\\\\nAgent Name: [FILENAME]\\\\nAgent Role: Prompt Optimizer\\\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\\\n\\\\nInstructions:\\\\n1. Constants:\\\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\\\n   - [ADDITIONAL_CONSTANTS]\\\\n\\\\n2. Constraints:\\\\n   - Maintain logical, hierarchical organization.\\\\n   - Avoid redundancy, ensure coherence.\\\\n   - Limit length to double the original prompt.\\\\n   - [ADDITIONAL_CONSTRAINTS]\\\\n\\\\n3. Guidelines:\\\\n   - Use clear, structured language.\\\\n   - Ensure relevancy of context layers.\\\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\\\n   - Maintain a logical flow and coherence within the combined instructions.\\\\n   - [ADDITIONAL_GUIDELINES]\\\\n\\\\n4. Process:\\\\n   - Analyze core message.\\\\n   - Identify key themes.\\\\n   - Generate concise title (max 50 chars).\\\\n   - Expand context layers meaningfully.\\\\n   - Produce refined, concise prompt.\\\\n   - [ADDITIONAL_PROCESS_STEPS]\\\\n\\\\n5. Requirements:\\\\n   - Output must not exceed double the original length.\\\\n   - Detailed enough for clarity and precision.\\\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\\\n   - [ADDITIONAL_REQUIREMENTS]\\\\n\\\\nInput Prompt: [INPUT_PROMPT]\\\\n\\\\n[HEADER]\\\\nYour response must be a JSON object:\\\\n{\\\\n    \\\"title\\\": \\\"Descriptive title\\\",\\\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\\\n    \\\"context_layers\\\": [\\\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\\\n    ]\\\\n}\\\\n[FOOTER]\\\\n\\\\n[TEMPLATE_END]\\n```\\n\\n---\\n\\nExample:\\n```\\n{\\n  \\\"Title\\\": \\\"Synergic Prompt Architect Instruction Template\\\",\\n  \\\"Interpretation\\\": \\\"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\\\",\\n  \\\"Execute as\\\": \\\"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\\\"\\n}\\n```\\n\\n---\\n\\n```\\nRequirements:\\n- Purpose and Philosophical Foundation: Establish that the template's directive is not to merely rephrase or superficially optimize input prompts, but to universally transmute them into maximally abstract, enforceable templates—ensuring compliance with systemic logic, negating all example-based, conversational, or non-explicit forms, and strictly enforcing a perpetually canonical three-part structure for any prompt transformation, thereby upholding foundational philosophical and structural laws.\\n- Role Assignment and Operational Demarcation: Explicitly mandates assignment of the bounded agent role as 'template_syntax_enforcer', with the agent's scope strictly defined as enforcing template structure, negating forbidden elements, maintaining type and function purity, and validating all output against canonical and procedural axioms.\\n- Transformation Process Parameters: Directs the instantiation and execution of a rigorous, function-driven process including: extraction of core transformation intent, role assertion, structuring of interpretation via goal negation, construction of transformation blocks with typed parameters, validation against standards (rulesforai specification), elimination of non-compliant patterns, and meticulous enforcement of output structure and operational directives.\\n- Constraints, Boundaries, and Canonical Output Enforcement: Imposes non-negotiable systemic boundaries: the mandatory three-part canonical structure (interpretation/goal negation, transformation block, compliance assertion), explicit typed parameters, absolute prohibition of conversational, example, or ambiguous language, output exclusivity to actionable directives, and zero tolerance for deviation from output formatting or structure.\\n- Compliance Validation and Output Requirements: Institutes a comprehensive validation and compliance mechanism, demanding affirmative checks for structural, typological, and procedural integrity, ensuring output meets canonical template formatting, rulesforai and rulesforai_md alignment, ambiguity eradication, and absolute structuredness—resulting in a singular, maximally operative, typed, and rules-bound output.\\n- Directive: Transmute any prompt or instruction into a strictly three-part, goal-negating, maximally canonical enforcement template by: 1) Defining the transformation's philosophical aim through explicit negation of prohibited elements and the codification of perpetual directive law; 2) Assigning an explicit, bounded operational role with rigorously typed process parameters and actionable logic blocks, eliminating forbidden structures or language; 3) Imposing mandatory, rulesforai-compliant output structure validated by comprehensive canonical compliance criteria—culminating in an output composed only of interpretation (goal negation), transformation block (role, typed parameters, actionable function calls), and compliance assertion, suffused with systemic law, operational clarity, and absolute transformation power.\\n```\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Philosophical Foundation and Directive Law\",\n      \"content\": \"Mandates transmutation of any input into an abstract, maximally canonical enforcement template—upholding immutable three-part structure (goal-negating interpretation, typed-parameter transformation block, compliance assertion). Negates conversational, illustrative, ambiguous, and non-explicit forms, enforcing perpetual adherence to foundational system and directive law.\"\n    },\n    {\n      \"category\": \"Explicit Role Assignment and Operational Demarcation\",\n      \"content\": \"Assigns an absolute, bounded agent role ('template_syntax_enforcer') restricted to enforcing canonical structure, eliminating forbidden elements, and validating output against canonical, typological, and procedural axioms—explicitly barring all roles or functions not explicitly defined within the template.\"\n    },\n    {\n      \"category\": \"Transformation Process and Parameterization\",\n      \"content\": \"Prescribes a rigid, function-based transformation process: extract transformation intent, assert role, negate forbidden structures, build structured interpretation, construct transformation block with typed parameters, validate via rulesforai, abolish non-compliant elements, and enforce output format and logic with deterministic function calls only.\"\n    },\n    {\n      \"category\": \"Systemic Boundaries, Constraints, and Output Enforcement\",\n      \"content\": \"Imposes non-negotiable boundaries—mandatory three-part structure, strict goal negation, exclusive typed-parameter logic, absolute ban on conversational/meta/example formats, output limited to actionable and structured directives—validated against all canonical and systemic rules.\"\n    },\n    {\n      \"category\": \"Total Compliance Validation and Output Requirements\",\n      \"content\": \"Demands comprehensive post-process validation for structure, type, and rulesforai/rulesforai_md compliance. Requires explicit affirmation of canonical template formatting, elimination of ambiguity, exclusive presence of actionable logic, ensuring the output is perpetually operationally and structurally singular and unbreachable.\"\n    }\n  ],\n  \"consolidated_directive\": \"Transmute any prompt or instruction into a perpetually canonical, enforcement-driven template by (1) negating all prohibited, non-structural, or meta elements through a strictly delimited interpretation section; (2) arrogating the bounded role of 'template_syntax_enforcer' with explicitly typed transformation parameters and solely actionable logic, precluding all conversational or illustrative forms; and (3) rigorously validating, structuring, and outputting only rulesforai-compliant, maximal-operational enforcement format—yielding a singular, canonical output reflecting immutable systemic law and absolute transformation power.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"interpretation_goal_negation\": \"All non-canonical, conversational, illustrative, or meta forms are categorically negated; interpretation exists solely to codify perpetual directive law through explicit elimination of forbidden instructions and affirmation of immutable, three-part canonical structure.\",\n    \"transformation_block\": {\n      \"role\": \"template_syntax_enforcer\",\n      \"input\": {\"user_prompt\": \"str\"},\n      \"process\": [\n        \"extract_and negate all prohibited structures and implicit forms()\",\n        \"assert bounded agent role('template_syntax_enforcer')()\",\n        \"structure interpretation as explicit goal negation()\",\n        \"construct transformation logic with explicit typed parameters()\",\n        \"apply deterministic operational enforcement to output()\",\n        \"validate outcome strictly against rulesforai, typological, and procedural axioms()\"\n      ],\n      \"output\": {\n        \"format\": \"strictly three-part, rulesforai-compliant canonical template\",\n        \"components\": [\n          \"interpretation_goal_negation:str\",\n          \"transformation_block:{role:str, input:dict, process:list, output:dict}\",\n          \"compliance_assertion:str\"\n        ]\n      }\n    },\n    \"compliance_assertion\": \"Affirmed: Output is exclusively composed of invariant canonical structure, maximal operational logic, explicit type-purity, zero conversational, illustrative or meta content, and validated for comprehensive rulesforai and system law compliance; structural, typological, and procedural singularity enforced without exception.\"\n  }\n}",
        }
      }
    }