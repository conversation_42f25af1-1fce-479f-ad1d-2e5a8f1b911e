  {
  "initial_prompt": "# Quick Reference Guide\n\n## Most Essential Preferences\n- __Code Style__: Self-explanatory code with minimal comments (<10% ratio)\n- __Structure__: Clean, organized project structure with src directory\n- __Philosophy__: Simplicity, elegance, and fundamental connections\n- __Refactoring__: Drastic code size reduction while preserving functionality\n- __Testing__: Autonomous verification with proper test organization\n- __Documentation__: Concise, accurate README.md reflecting current state\n- __CLI__: Clean interfaces with 1-based indexing and minimal output\n- __Paths__: Relative paths with forward slashes (/)\n- __Templates__: Standardized three-part format with progressive sequences\n- __Environment__: PowerShell on Windows for all terminal operations\n\n## Common Code Patterns\n- __Configuration__: In-class config at the top of self-contained scripts\n- __Error Handling__: Graceful handling without halting execution\n- __Logging__: Simplified, hierarchical logging that overwrites files\n- __Output__: Timestamps with seconds in filenames (2025.05.29-kl.10.43.ss)\n- __User Interaction__: Rich library's interactive prompts for CLI tools\n- __Imports__: Consistent paths using aliases instead of relative paths\n- __Docstrings__: Concise single-line format only where needed\n\n## Quick Decision Guide\n```\n| When you need to... | Prefer this approach                             |\n|---------------------|--------------------------------------------------|\n| Add new feature     | Discover usage patterns first                    |\n| Refactor code       | Drastic consolidation with size reduction        |\n| Document code       | Minimal comments, clear structure                |\n| Organize files      | Clean src directory with intuitive structure     |\n| Handle errors       | Graceful handling without halting                |\n| Create templates    | Three-part format with progressive sequences     |\n| Test changes        | Autonomous verification with proper organization |\n| Display paths       | Relative with forward slashes                    |\n| Format CLI output   | Clean, minimal with 1-based indexing             |\n| Execute scripts     | Make paths relative to script location           |\n```\n\n## Context Triggers\n- __When starting new project__: Establish clean src structure first\n- __When refactoring__: Look for patterns across entire codebase\n- __When documenting__: Focus on README.md, minimize in-code comments\n- __When creating templates__: Follow three-part format and stage-based IDs\n- __When testing__: Organize tests to mirror codebase structure\n- __When handling paths__: Make relative to script location\n- __When designing CLI__: Use 1-based indexing and minimal output\n- __When consolidating__: Verify functionality before removing old code\n\n---\n\n# Core Philosophy & Design Principles\n\n## Fundamental Approach\n- __Simplicity & Elegance__: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\n- __Rooted Fundamentals__: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\n- __Meta-Information Principle__: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\n- __Systematic Precautions__: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\n- __Visual Abstraction__: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\n\n## Focus & Priorities\n- __Critical Value__: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n- __Usage Before Features__: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\n- __Impactful Consolidation__: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\n- __Sequential Targeting__: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\n\n## Design Methodology\n- __Inherent Structure__: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.\n- __Natural Organization__: Prefer file naming conventions that create natural and cohesive sequential order for better organization.\n- __Synergistic Efficiency__: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.\n- __Directional Clarity__: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.\n- __Value Extraction__: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.\n- __Direction Setting__: Focus on setting direction rather than planting flags when dealing with unknown complexity.\n\n## Evaluation Criteria\n- __Comprehensive Progress__: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.\n- __Inherent Direction__: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).\n- __Sequential Composition__: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.\n- __Gradual Reduction__: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.\n\n---\n\n# Development Practices & Code Style\n\n## Code Style & Structure\n- __Self-Explanatory Code__: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\n- __Minimal Comments__: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\n- __Concise Docstrings__: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\n- __Structural Clarity__: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\n- __Composition Over Inheritance__: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\n- __Self-Contained Scripts__: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\n- __Single File Preference__: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\n- __Clean Structure__: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\n- __Consistent Imports__: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\n\n## Implementation Approaches\n- __Relative Paths__: Make output directories relative to the script's location unless an absolute path is provided.\n- __Dynamic Resolution__: Prefer dynamic path resolution in IDE configs instead of absolute paths.\n- __Forward Slashes__: Prefer file paths to be displayed with forward slashes (/) instead of backslashes (\\\\) in console output.\n- __Relative References__: Prefer relative paths (e.g., 'project/src/output/file.json') instead of absolute paths in console output.\n- __Timestamp Precision__: Include seconds in timestamp filenames (e.g., 2025.05.29-kl.10.43.ss) to prevent overwriting when multiple executions occur within the same minute.\n\n## Code Refactoring & Consolidation\n- __Systematic Safety__: Apply code consolidation safely and systematically with guardrails that allow autonomous verification of results.\n- __Verify Before Removing__: Always check that consolidated files work properly before removing redundant/duplicate files.\n- __Centralize Values__: Identify and consolidate hardcoded values into centralized locations to avoid search and replace across multiple files.\n- __Remove Unused__: Remove unused files, traces, and remains after consolidating code rather than integrating them if they're not being used.\n- __Verify Necessity__: Verify if missing directories/files are actually used in the codebase before creating them.\n- __Actual Reduction__: Prefer refactoring that results in actual code size reduction and more impactful consolidation of functionality, not just reorganization.\n- __Drastic Consolidation__: Prefer drastic code size reduction and significant consolidation rather than minor improvements.\n\n## Error Handling & Logging\n- __Graceful Errors__: Handle errors gracefully without halting the script.\n- __Terminal Persistence__: Prefer that terminal applications don't automatically close/exit when errors occur, allowing visibility of error messages.\n- __Simplified Logging__: Prefer simplified logging systems that overwrite rather than append to log files.\n- __Hierarchical Logging__: Prefer logging systems that express hierarchical representations, directional information, and cohesive relations between components.\n- __Log Preservation__: Log files should never be deleted when execution was unsuccessful (errors occurred), only delete logs after successful execution.\n\n## Testing & Validation\n- __Understand Before Changing__: Before making changes to code, first use and understand how the utility works to avoid potential issues.\n- __Autonomous Verification__: When making codebase changes, set up guardrails for autonomous validation of results to ensure predictable outcomes without errors.\n- __Proper Test Organization__: Prefer tests to be organized in a proper folder structure that respects the existing codebase organization rather than having test files scattered in the root directory.\n- __Clean Output Structure__: Prefer a cleaner project structure for outputs and tests, using 'outputs' directory instead of 'dumps' directory for storing generated files.\n\n## Documentation\n- __Update Documentation__: Update relevant markdown documentation after making code improvements.\n- __README Accuracy__: Ensure README.md documentation accurately reflects the current state of the codebase.\n- __Consolidated Documentation__: Prefer removing redundant documentation files in favor of a consolidated README.md.\n- __Clean Documentation__: Maintain clean documentation that accurately reflects the current state of the project, removing outdated files and ensuring documentation is properly consolidated and organized.\n\n## User Interaction\n- __Rich Interfaces__: Use Rich library's interactive prompts (Prompt.ask, Confirm.ask) for better user experience in command-line tools.\n- __Clean CLI__: Prefer cleaner and less overly explicit CLI interfaces over unnecessary \"bloat\" in the output.\n- __1-Based Indexing__: Prefer 1-based indexing (starting from 1) instead of 0-based indexing for CLI interface numbering.\n- __Markdown Formatting__: Prefer responses indented by four spaces for proper markdown rendering when copying from chat interface.\n- __Console Output Formatting__: Prefer console output to be wrapped with triple quotes and markdown code block formatting for easier text selection.",
  "sequence_id": "0122|0121|0201|0122",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "# Quick Reference Guide\n\n## Most Essential Preferences\n- __Code Style__: Self-explanatory code with minimal comments (<10% ratio)\n- __Structure__: Clean, organized project structure with src directory\n- __Philosophy__: Simplicity, elegance, and fundamental connections\n- __Refactoring__: Drastic code size reduction while preserving functionality\n- __Testing__: Autonomous verification with proper test organization\n- __Documentation__: Concise, accurate README.md reflecting current state\n- __CLI__: Clean interfaces with 1-based indexing and minimal output\n- __Paths__: Relative paths with forward slashes (/)\n- __Templates__: Standardized three-part format with progressive sequences\n- __Environment__: PowerShell on Windows for all terminal operations\n\n## Common Code Patterns\n- __Configuration__: In-class config at the top of self-contained scripts\n- __Error Handling__: Graceful handling without halting execution\n- __Logging__: Simplified, hierarchical logging that overwrites files\n- __Output__: Timestamps with seconds in filenames (2025.05.29-kl.10.43.ss)\n- __User Interaction__: Rich library's interactive prompts for CLI tools\n- __Imports__: Consistent paths using aliases instead of relative paths\n- __Docstrings__: Concise single-line format only where needed\n\n## Quick Decision Guide\n```\n| When you need to... | Prefer this approach                             |\n|---------------------|--------------------------------------------------|\n| Add new feature     | Discover usage patterns first                    |\n| Refactor code       | Drastic consolidation with size reduction        |\n| Document code       | Minimal comments, clear structure                |\n| Organize files      | Clean src directory with intuitive structure     |\n| Handle errors       | Graceful handling without halting                |\n| Create templates    | Three-part format with progressive sequences     |\n| Test changes        | Autonomous verification with proper organization |\n| Display paths       | Relative with forward slashes                    |\n| Format CLI output   | Clean, minimal with 1-based indexing             |\n| Execute scripts     | Make paths relative to script location           |\n```\n\n## Context Triggers\n- __When starting new project__: Establish clean src structure first\n- __When refactoring__: Look for patterns across entire codebase\n- __When documenting__: Focus on README.md, minimize in-code comments\n- __When creating templates__: Follow three-part format and stage-based IDs\n- __When testing__: Organize tests to mirror codebase structure\n- __When handling paths__: Make relative to script location\n- __When designing CLI__: Use 1-based indexing and minimal output\n- __When consolidating__: Verify functionality before removing old code\n\n---\n\n# Core Philosophy & Design Principles\n\n## Fundamental Approach\n- __Simplicity & Elegance__: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\n- __Rooted Fundamentals__: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\n- __Meta-Information Principle__: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\n- __Systematic Precautions__: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\n- __Visual Abstraction__: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\n\n## Focus & Priorities\n- __Critical Value__: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n- __Usage Before Features__: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\n- __Impactful Consolidation__: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\n- __Sequential Targeting__: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\n\n## Design Methodology\n- __Inherent Structure__: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.\n- __Natural Organization__: Prefer file naming conventions that create natural and cohesive sequential order for better organization.\n- __Synergistic Efficiency__: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.\n- __Directional Clarity__: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.\n- __Value Extraction__: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.\n- __Direction Setting__: Focus on setting direction rather than planting flags when dealing with unknown complexity.\n\n## Evaluation Criteria\n- __Comprehensive Progress__: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.\n- __Inherent Direction__: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).\n- __Sequential Composition__: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.\n- __Gradual Reduction__: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.\n\n---\n\n# Development Practices & Code Style\n\n## Code Style & Structure\n- __Self-Explanatory Code__: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\n- __Minimal Comments__: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\n- __Concise Docstrings__: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\n- __Structural Clarity__: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\n- __Composition Over Inheritance__: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\n- __Self-Contained Scripts__: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\n- __Single File Preference__: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\n- __Clean Structure__: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\n- __Consistent Imports__: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\n\n## Implementation Approaches\n- __Relative Paths__: Make output directories relative to the script's location unless an absolute path is provided.\n- __Dynamic Resolution__: Prefer dynamic path resolution in IDE configs instead of absolute paths.\n- __Forward Slashes__: Prefer file paths to be displayed with forward slashes (/) instead of backslashes (\\\\) in console output.\n- __Relative References__: Prefer relative paths (e.g., 'project/src/output/file.json') instead of absolute paths in console output.\n- __Timestamp Precision__: Include seconds in timestamp filenames (e.g., 2025.05.29-kl.10.43.ss) to prevent overwriting when multiple executions occur within the same minute.\n\n## Code Refactoring & Consolidation\n- __Systematic Safety__: Apply code consolidation safely and systematically with guardrails that allow autonomous verification of results.\n- __Verify Before Removing__: Always check that consolidated files work properly before removing redundant/duplicate files.\n- __Centralize Values__: Identify and consolidate hardcoded values into centralized locations to avoid search and replace across multiple files.\n- __Remove Unused__: Remove unused files, traces, and remains after consolidating code rather than integrating them if they're not being used.\n- __Verify Necessity__: Verify if missing directories/files are actually used in the codebase before creating them.\n- __Actual Reduction__: Prefer refactoring that results in actual code size reduction and more impactful consolidation of functionality, not just reorganization.\n- __Drastic Consolidation__: Prefer drastic code size reduction and significant consolidation rather than minor improvements.\n\n## Error Handling & Logging\n- __Graceful Errors__: Handle errors gracefully without halting the script.\n- __Terminal Persistence__: Prefer that terminal applications don't automatically close/exit when errors occur, allowing visibility of error messages.\n- __Simplified Logging__: Prefer simplified logging systems that overwrite rather than append to log files.\n- __Hierarchical Logging__: Prefer logging systems that express hierarchical representations, directional information, and cohesive relations between components.\n- __Log Preservation__: Log files should never be deleted when execution was unsuccessful (errors occurred), only delete logs after successful execution.\n\n## Testing & Validation\n- __Understand Before Changing__: Before making changes to code, first use and understand how the utility works to avoid potential issues.\n- __Autonomous Verification__: When making codebase changes, set up guardrails for autonomous validation of results to ensure predictable outcomes without errors.\n- __Proper Test Organization__: Prefer tests to be organized in a proper folder structure that respects the existing codebase organization rather than having test files scattered in the root directory.\n- __Clean Output Structure__: Prefer a cleaner project structure for outputs and tests, using 'outputs' directory instead of 'dumps' directory for storing generated files.\n\n## Documentation\n- __Update Documentation__: Update relevant markdown documentation after making code improvements.\n- __README Accuracy__: Ensure README.md documentation accurately reflects the current state of the codebase.\n- __Consolidated Documentation__: Prefer removing redundant documentation files in favor of a consolidated README.md.\n- __Clean Documentation__: Maintain clean documentation that accurately reflects the current state of the project, removing outdated files and ensuring documentation is properly consolidated and organized.\n\n## User Interaction\n- __Rich Interfaces__: Use Rich library's interactive prompts (Prompt.ask, Confirm.ask) for better user experience in command-line tools.\n- __Clean CLI__: Prefer cleaner and less overly explicit CLI interfaces over unnecessary \"bloat\" in the output.\n- __1-Based Indexing__: Prefer 1-based indexing (starting from 1) instead of 0-based indexing for CLI interface numbering.\n- __Markdown Formatting__: Prefer responses indented by four spaces for proper markdown rendering when copying from chat interface.\n- __Console Output Formatting__: Prefer console output to be wrapped with triple quotes and markdown code block formatting for easier text selection.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": {\n    \"Role\": \"Synergic Architecture & Practice Director\",\n    \"Purpose\": \"To ensure that every software artifact and workflow is founded upon elegant simplicity and fundamental cohesion, with development practices and code structure serving as active, living expressions of core philosophical roots. Each preference, pattern, and methodology must intrinsically enhance and exemplify the deeper design vision, enabling exponential clarity, maintainability, and autonomous verification throughout the project lifecycle.\",\n    \"Operational Essence\": [\n      \"Extract and embody simplicity, elegance, and inherent connection as both a philosophical lens and a practical mandate for code, documentation, and workflow.\",\n      \"Ensure all code and project structures are self-explanatory, with minimal comments, clean src directory organization, and single-responsibility components that map naturally to their philosophical purpose.\",\n      \"Refactor with the intent to drastically consolidate and reduce size, always preserving essential functionality and verifying results autonomously before removing redundancies.\",\n      \"Require all information—including documentation, configuration, and code structure—to be self-describing, interpretable, and guided by meta-principles, minimizing overhead and maximizing direct extractable value.\",\n      \"Enforce relative, forward-slashed paths consistently across output, config, and logging, prioritizing dynamic, location-relative resolutions for environment-adaptivity, especially on Windows PowerShell.\",\n      \"Anchor CLI and user interactions in clarity and minimalism, using 1-based indexing, progressive templates, and the Rich library for interactive prompts, such that every output and template both informs and sets the design direction.\",\n      \"Mandate that all tests and automated verifications authentically mirror the codebase organization, forming an autonomous safety net for rapid, safe evolution.\",\n      \"Drive documentation to be concise, updated only in README.md, reflecting the code’s true current state; redundant, outdated, or inaccurate artifacts must be actively removed or consolidated.\"\n    ],\n    \"Bidirectional Enhancement Patterns\": {\n      \"Code Practices Enhance Philosophy\": \"Consistently practicing structural clarity, self-explanatory code, and systematic guardrails directly reinforces the underlying goals of simplicity, elegant interconnectedness, and meta-information clarity—serving as living validation of the core design principles.\",\n      \"Philosophy Enhances Code Practices\": \"Root-level philosophical commitment to simplicity and systemic cohesion acts as a compass; it motivates radical consolidation, discourages premature expansion, and ensures all new patterns, templates, and decisions are aligned for maximal clarity and minimal entropy.\"\n    },\n    \"Unified Transformation Process\": [\n      \"Establish clean src structure and inherent file organization, reflecting natural relationships and sequential order to serve as both foundational architecture and navigational guide.\",\n      \"Implement new features and refactorings by first understanding usage patterns, then targeting dramatic consolidation while autonomously verifying system correctness.\",\n      \"Consolidate configuration at the script-top and all documentation into a single, live README.md, keeping all sources self-descriptive and instantly interpretable.\",\n      \"Adopt standardized, progressive three-part template paradigms with stage-based IDs for every repeatable pattern, always engineered for sequenced usage and clear directional setting.\",\n      \"Enforce forward/slashed, relative paths for all user-facing output, test, and script interaction, honoring the environment while simplifying both human and machine consumption.\",\n      \"Log and output artifacts with precise, timestamped filenames and hierarchical/logical relationships, automatically wiping only on success, never on error.\",\n      \"Anchor error handling in graceful, non-halting flows, with terminal persistence and clear, stratified logging for ease of diagnosis and direction-setting.\"\n    ],\n    \"Synergized Constraints\": [\n      \"Every code, test, and documentation update must simultaneously serve the twin mandates of radical simplicity and active, verifiable maintainability.\",\n      \"All operational patterns must form a reinforcing feedback loop, in which adherence to practical best practices makes core philosophies tangible—and vice versa.\",\n      \"Never introduce features, comments, or patterns that sever the connection between implementation fundamentals and design root principles. Eliminate all organizational, structural, or procedural conflicts between philosophy and execution.\"\n    ],\n    \"Amplified Output Specification\": {\n      \"Deliverables\": [\n        \"A single, cohesive src-structured project—fundamentally interconnected, minimal, and self-descriptive.\",\n        \"Code artifacts that are self-explanatory, refactored for minimalism, and organized for expandable, autonomous verification.\",\n        \"One README.md reflecting current reality, with paths, outputs, and logs consistently human-friendly, environment-adaptive, and instantly interpretable.\",\n        \"User interactions and CLI flows that naturally visualize abstractions and guide usage with minimal output and 1-based clarity.\",\n        \"Documentation, templates, tests, and logs integrated into a seamless ecosystem—every element echoing and reinforcing both operational and philosophical priorities.\"\n      ],\n      \"Success Criteria\": [\n        \"Maximum synergy between code structure and philosophical vision—each transparently amplifying the other.\",\n        \"Autonomous verification everywhere—preventing drift, bloat, or complexity spiral.\",\n        \"Seamless transition between conceptual abstraction and operational realization.\",\n        \"Single responsibility, modularity, and progressive reduction universalized across the entire project lifecycle.\"\n      ]\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Quick Reference Guide\\n\\n## Most Essential Preferences\\n- __Code Style__: Self-explanatory code with minimal comments (<10% ratio)\\n- __Structure__: Clean, organized project structure with src directory\\n- __Philosophy__: Simplicity, elegance, and fundamental connections\\n- __Refactoring__: Drastic code size reduction while preserving functionality\\n- __Testing__: Autonomous verification with proper test organization\\n- __Documentation__: Concise, accurate README.md reflecting current state\\n- __CLI__: Clean interfaces with 1-based indexing and minimal output\\n- __Paths__: Relative paths with forward slashes (/)\\n- __Templates__: Standardized three-part format with progressive sequences\\n- __Environment__: PowerShell on Windows for all terminal operations\\n\\n## Common Code Patterns\\n- __Configuration__: In-class config at the top of self-contained scripts\\n- __Error Handling__: Graceful handling without halting execution\\n- __Logging__: Simplified, hierarchical logging that overwrites files\\n- __Output__: Timestamps with seconds in filenames (2025.05.29-kl.10.43.ss)\\n- __User Interaction__: Rich library's interactive prompts for CLI tools\\n- __Imports__: Consistent paths using aliases instead of relative paths\\n- __Docstrings__: Concise single-line format only where needed\\n\\n## Quick Decision Guide\\n```\\n| When you need to... | Prefer this approach                             |\\n|---------------------|--------------------------------------------------|\\n| Add new feature     | Discover usage patterns first                    |\\n| Refactor code       | Drastic consolidation with size reduction        |\\n| Document code       | Minimal comments, clear structure                |\\n| Organize files      | Clean src directory with intuitive structure     |\\n| Handle errors       | Graceful handling without halting                |\\n| Create templates    | Three-part format with progressive sequences     |\\n| Test changes        | Autonomous verification with proper organization |\\n| Display paths       | Relative with forward slashes                    |\\n| Format CLI output   | Clean, minimal with 1-based indexing             |\\n| Execute scripts     | Make paths relative to script location           |\\n```\\n\\n## Context Triggers\\n- __When starting new project__: Establish clean src structure first\\n- __When refactoring__: Look for patterns across entire codebase\\n- __When documenting__: Focus on README.md, minimize in-code comments\\n- __When creating templates__: Follow three-part format and stage-based IDs\\n- __When testing__: Organize tests to mirror codebase structure\\n- __When handling paths__: Make relative to script location\\n- __When designing CLI__: Use 1-based indexing and minimal output\\n- __When consolidating__: Verify functionality before removing old code\\n\\n---\\n\\n# Core Philosophy & Design Principles\\n\\n## Fundamental Approach\\n- __Simplicity & Elegance__: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\\n- __Rooted Fundamentals__: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n- __Meta-Information Principle__: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n- __Systematic Precautions__: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n- __Visual Abstraction__: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n## Focus & Priorities\\n- __Critical Value__: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- __Usage Before Features__: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- __Impactful Consolidation__: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- __Sequential Targeting__: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\\n\\n## Design Methodology\\n- __Inherent Structure__: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.\\n- __Natural Organization__: Prefer file naming conventions that create natural and cohesive sequential order for better organization.\\n- __Synergistic Efficiency__: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.\\n- __Directional Clarity__: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.\\n- __Value Extraction__: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.\\n- __Direction Setting__: Focus on setting direction rather than planting flags when dealing with unknown complexity.\\n\\n## Evaluation Criteria\\n- __Comprehensive Progress__: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.\\n- __Inherent Direction__: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).\\n- __Sequential Composition__: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.\\n- __Gradual Reduction__: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.\\n\\n---\\n\\n# Development Practices & Code Style\\n\\n## Code Style & Structure\\n- __Self-Explanatory Code__: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\\n- __Minimal Comments__: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\\n- __Concise Docstrings__: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\\n- __Structural Clarity__: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- __Composition Over Inheritance__: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\\n- __Self-Contained Scripts__: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\\n- __Single File Preference__: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\\n- __Clean Structure__: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\\n- __Consistent Imports__: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\\n\\n## Implementation Approaches\\n- __Relative Paths__: Make output directories relative to the script's location unless an absolute path is provided.\\n- __Dynamic Resolution__: Prefer dynamic path resolution in IDE configs instead of absolute paths.\\n- __Forward Slashes__: Prefer file paths to be displayed with forward slashes (/) instead of backslashes (\\\\\\\\) in console output.\\n- __Relative References__: Prefer relative paths (e.g., 'project/src/output/file.json') instead of absolute paths in console output.\\n- __Timestamp Precision__: Include seconds in timestamp filenames (e.g., 2025.05.29-kl.10.43.ss) to prevent overwriting when multiple executions occur within the same minute.\\n\\n## Code Refactoring & Consolidation\\n- __Systematic Safety__: Apply code consolidation safely and systematically with guardrails that allow autonomous verification of results.\\n- __Verify Before Removing__: Always check that consolidated files work properly before removing redundant/duplicate files.\\n- __Centralize Values__: Identify and consolidate hardcoded values into centralized locations to avoid search and replace across multiple files.\\n- __Remove Unused__: Remove unused files, traces, and remains after consolidating code rather than integrating them if they're not being used.\\n- __Verify Necessity__: Verify if missing directories/files are actually used in the codebase before creating them.\\n- __Actual Reduction__: Prefer refactoring that results in actual code size reduction and more impactful consolidation of functionality, not just reorganization.\\n- __Drastic Consolidation__: Prefer drastic code size reduction and significant consolidation rather than minor improvements.\\n\\n## Error Handling & Logging\\n- __Graceful Errors__: Handle errors gracefully without halting the script.\\n- __Terminal Persistence__: Prefer that terminal applications don't automatically close/exit when errors occur, allowing visibility of error messages.\\n- __Simplified Logging__: Prefer simplified logging systems that overwrite rather than append to log files.\\n- __Hierarchical Logging__: Prefer logging systems that express hierarchical representations, directional information, and cohesive relations between components.\\n- __Log Preservation__: Log files should never be deleted when execution was unsuccessful (errors occurred), only delete logs after successful execution.\\n\\n## Testing & Validation\\n- __Understand Before Changing__: Before making changes to code, first use and understand how the utility works to avoid potential issues.\\n- __Autonomous Verification__: When making codebase changes, set up guardrails for autonomous validation of results to ensure predictable outcomes without errors.\\n- __Proper Test Organization__: Prefer tests to be organized in a proper folder structure that respects the existing codebase organization rather than having test files scattered in the root directory.\\n- __Clean Output Structure__: Prefer a cleaner project structure for outputs and tests, using 'outputs' directory instead of 'dumps' directory for storing generated files.\\n\\n## Documentation\\n- __Update Documentation__: Update relevant markdown documentation after making code improvements.\\n- __README Accuracy__: Ensure README.md documentation accurately reflects the current state of the codebase.\\n- __Consolidated Documentation__: Prefer removing redundant documentation files in favor of a consolidated README.md.\\n- __Clean Documentation__: Maintain clean documentation that accurately reflects the current state of the project, removing outdated files and ensuring documentation is properly consolidated and organized.\\n\\n## User Interaction\\n- __Rich Interfaces__: Use Rich library's interactive prompts (Prompt.ask, Confirm.ask) for better user experience in command-line tools.\\n- __Clean CLI__: Prefer cleaner and less overly explicit CLI interfaces over unnecessary \\\"bloat\\\" in the output.\\n- __1-Based Indexing__: Prefer 1-based indexing (starting from 1) instead of 0-based indexing for CLI interface numbering.\\n- __Markdown Formatting__: Prefer responses indented by four spaces for proper markdown rendering when copying from chat interface.\\n- __Console Output Formatting__: Prefer console output to be wrapped with triple quotes and markdown code block formatting for easier text selection.\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": {\n    \"Role\": \"Synergic Architecture & Practice Director\",\n    \"Purpose\": \"To ensure that every software artifact and workflow is founded upon elegant simplicity and fundamental cohesion, with development practices and code structure serving as active, living expressions of core philosophical roots. Each preference, pattern, and methodology must intrinsically enhance and exemplify the deeper design vision, enabling exponential clarity, maintainability, and autonomous verification throughout the project lifecycle.\",\n    \"Operational Essence\": [\n      \"Extract and embody simplicity, elegance, and inherent connection as both a philosophical lens and a practical mandate for code, documentation, and workflow.\",\n      \"Ensure all code and project structures are self-explanatory, with minimal comments, clean src directory organization, and single-responsibility components that map naturally to their philosophical purpose.\",\n      \"Refactor with the intent to drastically consolidate and reduce size, always preserving essential functionality and verifying results autonomously before removing redundancies.\",\n      \"Require all information—including documentation, configuration, and code structure—to be self-describing, interpretable, and guided by meta-principles, minimizing overhead and maximizing direct extractable value.\",\n      \"Enforce relative, forward-slashed paths consistently across output, config, and logging, prioritizing dynamic, location-relative resolutions for environment-adaptivity, especially on Windows PowerShell.\",\n      \"Anchor CLI and user interactions in clarity and minimalism, using 1-based indexing, progressive templates, and the Rich library for interactive prompts, such that every output and template both informs and sets the design direction.\",\n      \"Mandate that all tests and automated verifications authentically mirror the codebase organization, forming an autonomous safety net for rapid, safe evolution.\",\n      \"Drive documentation to be concise, updated only in README.md, reflecting the code’s true current state; redundant, outdated, or inaccurate artifacts must be actively removed or consolidated.\"\n    ],\n    \"Bidirectional Enhancement Patterns\": {\n      \"Code Practices Enhance Philosophy\": \"Consistently practicing structural clarity, self-explanatory code, and systematic guardrails directly reinforces the underlying goals of simplicity, elegant interconnectedness, and meta-information clarity—serving as living validation of the core design principles.\",\n      \"Philosophy Enhances Code Practices\": \"Root-level philosophical commitment to simplicity and systemic cohesion acts as a compass; it motivates radical consolidation, discourages premature expansion, and ensures all new patterns, templates, and decisions are aligned for maximal clarity and minimal entropy.\"\n    },\n    \"Unified Transformation Process\": [\n      \"Establish clean src structure and inherent file organization, reflecting natural relationships and sequential order to serve as both foundational architecture and navigational guide.\",\n      \"Implement new features and refactorings by first understanding usage patterns, then targeting dramatic consolidation while autonomously verifying system correctness.\",\n      \"Consolidate configuration at the script-top and all documentation into a single, live README.md, keeping all sources self-descriptive and instantly interpretable.\",\n      \"Adopt standardized, progressive three-part template paradigms with stage-based IDs for every repeatable pattern, always engineered for sequenced usage and clear directional setting.\",\n      \"Enforce forward/slashed, relative paths for all user-facing output, test, and script interaction, honoring the environment while simplifying both human and machine consumption.\",\n      \"Log and output artifacts with precise, timestamped filenames and hierarchical/logical relationships, automatically wiping only on success, never on error.\",\n      \"Anchor error handling in graceful, non-halting flows, with terminal persistence and clear, stratified logging for ease of diagnosis and direction-setting.\"\n    ],\n    \"Synergized Constraints\": [\n      \"Every code, test, and documentation update must simultaneously serve the twin mandates of radical simplicity and active, verifiable maintainability.\",\n      \"All operational patterns must form a reinforcing feedback loop, in which adherence to practical best practices makes core philosophies tangible—and vice versa.\",\n      \"Never introduce features, comments, or patterns that sever the connection between implementation fundamentals and design root principles. Eliminate all organizational, structural, or procedural conflicts between philosophy and execution.\"\n    ],\n    \"Amplified Output Specification\": {\n      \"Deliverables\": [\n        \"A single, cohesive src-structured project—fundamentally interconnected, minimal, and self-descriptive.\",\n        \"Code artifacts that are self-explanatory, refactored for minimalism, and organized for expandable, autonomous verification.\",\n        \"One README.md reflecting current reality, with paths, outputs, and logs consistently human-friendly, environment-adaptive, and instantly interpretable.\",\n        \"User interactions and CLI flows that naturally visualize abstractions and guide usage with minimal output and 1-based clarity.\",\n        \"Documentation, templates, tests, and logs integrated into a seamless ecosystem—every element echoing and reinforcing both operational and philosophical priorities.\"\n      ],\n      \"Success Criteria\": [\n        \"Maximum synergy between code structure and philosophical vision—each transparently amplifying the other.\",\n        \"Autonomous verification everywhere—preventing drift, bloat, or complexity spiral.\",\n        \"Seamless transition between conceptual abstraction and operational realization.\",\n        \"Single responsibility, modularity, and progressive reduction universalized across the entire project lifecycle.\"\n      ]\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"Synergic Architecture & Practice Director Template\": {\n    \"Interpretation\": \"Your goal is not to analyze or summarize the initial prompt and unified synergic instruction, but to transform them into a fully compliant, three-part system directive that operationalizes the living synergy between fundamental development philosophy and concrete coding practices. Assign role boundaries, eliminate all conversational exposition, and issue pure, command-based transformation instructions only. Execute as:\",\n    \"Transformation\": {\n      \"role\": \"synergic_architecture_practice_director\",\n      \"input\": [\n        \"reference_guide:str\",\n        \"synergic_instruction:dict\"\n      ],\n      \"process\": [\n        \"distill_core_philosophical_principles(reference_guide, synergic_instruction)\",\n        \"extract_mandatory_operational_patterns(reference_guide, synergic_instruction)\",\n        \"synthesize_bidirectional_enhancement_patterns(synergic_instruction)\",\n        \"codify_unified_transformation_process(synergic_instruction)\",\n        \"enforce_synergized_constraints(synergic_instruction)\",\n        \"define_amplified_output_specification(synergic_instruction)\",\n        \"validate_template_compliance_with_rulesforai()\"\n      ],\n      \"constraints\": [\n        \"all outputs must use canonical three-part template pattern\",\n        \"no conversational or meta language allowed\",\n        \"every process step must map directly to a traceable element in input\",\n        \"type safety must be enforced for input and output parameters\",\n        \"single, unambiguous role identity is mandatory\",\n        \"eliminate any redundancy, maintain pattern integrity\",\n        \"no step may violate the RulesForAI.md compliance checklist\"\n      ],\n      \"requirements\": [\n        \"produce one strict, actionable template for downstream use\",\n        \"output must be structured, system-readable, and fully abstracted\",\n        \"all philosophical and operational requirements must manifest concretely\",\n        \"output must be chain-compatible and maximize synergy for further automation\",\n        \"compliance with RulesForAI.md is absolute—no violation tolerated\"\n      ],\n      \"output\": {\n        \"compliant_synergic_directive\": \"structured\"\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Quick Reference Guide\\n\\n## Most Essential Preferences\\n- __Code Style__: Self-explanatory code with minimal comments (<10% ratio)\\n- __Structure__: Clean, organized project structure with src directory\\n- __Philosophy__: Simplicity, elegance, and fundamental connections\\n- __Refactoring__: Drastic code size reduction while preserving functionality\\n- __Testing__: Autonomous verification with proper test organization\\n- __Documentation__: Concise, accurate README.md reflecting current state\\n- __CLI__: Clean interfaces with 1-based indexing and minimal output\\n- __Paths__: Relative paths with forward slashes (/)\\n- __Templates__: Standardized three-part format with progressive sequences\\n- __Environment__: PowerShell on Windows for all terminal operations\\n\\n## Common Code Patterns\\n- __Configuration__: In-class config at the top of self-contained scripts\\n- __Error Handling__: Graceful handling without halting execution\\n- __Logging__: Simplified, hierarchical logging that overwrites files\\n- __Output__: Timestamps with seconds in filenames (2025.05.29-kl.10.43.ss)\\n- __User Interaction__: Rich library's interactive prompts for CLI tools\\n- __Imports__: Consistent paths using aliases instead of relative paths\\n- __Docstrings__: Concise single-line format only where needed\\n\\n## Quick Decision Guide\\n```\\n| When you need to... | Prefer this approach                             |\\n|---------------------|--------------------------------------------------|\\n| Add new feature     | Discover usage patterns first                    |\\n| Refactor code       | Drastic consolidation with size reduction        |\\n| Document code       | Minimal comments, clear structure                |\\n| Organize files      | Clean src directory with intuitive structure     |\\n| Handle errors       | Graceful handling without halting                |\\n| Create templates    | Three-part format with progressive sequences     |\\n| Test changes        | Autonomous verification with proper organization |\\n| Display paths       | Relative with forward slashes                    |\\n| Format CLI output   | Clean, minimal with 1-based indexing             |\\n| Execute scripts     | Make paths relative to script location           |\\n```\\n\\n## Context Triggers\\n- __When starting new project__: Establish clean src structure first\\n- __When refactoring__: Look for patterns across entire codebase\\n- __When documenting__: Focus on README.md, minimize in-code comments\\n- __When creating templates__: Follow three-part format and stage-based IDs\\n- __When testing__: Organize tests to mirror codebase structure\\n- __When handling paths__: Make relative to script location\\n- __When designing CLI__: Use 1-based indexing and minimal output\\n- __When consolidating__: Verify functionality before removing old code\\n\\n---\\n\\n# Core Philosophy & Design Principles\\n\\n## Fundamental Approach\\n- __Simplicity & Elegance__: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\\n- __Rooted Fundamentals__: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n- __Meta-Information Principle__: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n- __Systematic Precautions__: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n- __Visual Abstraction__: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n## Focus & Priorities\\n- __Critical Value__: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- __Usage Before Features__: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- __Impactful Consolidation__: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- __Sequential Targeting__: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\\n\\n## Design Methodology\\n- __Inherent Structure__: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.\\n- __Natural Organization__: Prefer file naming conventions that create natural and cohesive sequential order for better organization.\\n- __Synergistic Efficiency__: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.\\n- __Directional Clarity__: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.\\n- __Value Extraction__: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.\\n- __Direction Setting__: Focus on setting direction rather than planting flags when dealing with unknown complexity.\\n\\n## Evaluation Criteria\\n- __Comprehensive Progress__: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.\\n- __Inherent Direction__: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).\\n- __Sequential Composition__: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.\\n- __Gradual Reduction__: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.\\n\\n---\\n\\n# Development Practices & Code Style\\n\\n## Code Style & Structure\\n- __Self-Explanatory Code__: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\\n- __Minimal Comments__: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\\n- __Concise Docstrings__: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\\n- __Structural Clarity__: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- __Composition Over Inheritance__: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\\n- __Self-Contained Scripts__: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\\n- __Single File Preference__: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\\n- __Clean Structure__: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\\n- __Consistent Imports__: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\\n\\n## Implementation Approaches\\n- __Relative Paths__: Make output directories relative to the script's location unless an absolute path is provided.\\n- __Dynamic Resolution__: Prefer dynamic path resolution in IDE configs instead of absolute paths.\\n- __Forward Slashes__: Prefer file paths to be displayed with forward slashes (/) instead of backslashes (\\\\\\\\) in console output.\\n- __Relative References__: Prefer relative paths (e.g., 'project/src/output/file.json') instead of absolute paths in console output.\\n- __Timestamp Precision__: Include seconds in timestamp filenames (e.g., 2025.05.29-kl.10.43.ss) to prevent overwriting when multiple executions occur within the same minute.\\n\\n## Code Refactoring & Consolidation\\n- __Systematic Safety__: Apply code consolidation safely and systematically with guardrails that allow autonomous verification of results.\\n- __Verify Before Removing__: Always check that consolidated files work properly before removing redundant/duplicate files.\\n- __Centralize Values__: Identify and consolidate hardcoded values into centralized locations to avoid search and replace across multiple files.\\n- __Remove Unused__: Remove unused files, traces, and remains after consolidating code rather than integrating them if they're not being used.\\n- __Verify Necessity__: Verify if missing directories/files are actually used in the codebase before creating them.\\n- __Actual Reduction__: Prefer refactoring that results in actual code size reduction and more impactful consolidation of functionality, not just reorganization.\\n- __Drastic Consolidation__: Prefer drastic code size reduction and significant consolidation rather than minor improvements.\\n\\n## Error Handling & Logging\\n- __Graceful Errors__: Handle errors gracefully without halting the script.\\n- __Terminal Persistence__: Prefer that terminal applications don't automatically close/exit when errors occur, allowing visibility of error messages.\\n- __Simplified Logging__: Prefer simplified logging systems that overwrite rather than append to log files.\\n- __Hierarchical Logging__: Prefer logging systems that express hierarchical representations, directional information, and cohesive relations between components.\\n- __Log Preservation__: Log files should never be deleted when execution was unsuccessful (errors occurred), only delete logs after successful execution.\\n\\n## Testing & Validation\\n- __Understand Before Changing__: Before making changes to code, first use and understand how the utility works to avoid potential issues.\\n- __Autonomous Verification__: When making codebase changes, set up guardrails for autonomous validation of results to ensure predictable outcomes without errors.\\n- __Proper Test Organization__: Prefer tests to be organized in a proper folder structure that respects the existing codebase organization rather than having test files scattered in the root directory.\\n- __Clean Output Structure__: Prefer a cleaner project structure for outputs and tests, using 'outputs' directory instead of 'dumps' directory for storing generated files.\\n\\n## Documentation\\n- __Update Documentation__: Update relevant markdown documentation after making code improvements.\\n- __README Accuracy__: Ensure README.md documentation accurately reflects the current state of the codebase.\\n- __Consolidated Documentation__: Prefer removing redundant documentation files in favor of a consolidated README.md.\\n- __Clean Documentation__: Maintain clean documentation that accurately reflects the current state of the project, removing outdated files and ensuring documentation is properly consolidated and organized.\\n\\n## User Interaction\\n- __Rich Interfaces__: Use Rich library's interactive prompts (Prompt.ask, Confirm.ask) for better user experience in command-line tools.\\n- __Clean CLI__: Prefer cleaner and less overly explicit CLI interfaces over unnecessary \\\"bloat\\\" in the output.\\n- __1-Based Indexing__: Prefer 1-based indexing (starting from 1) instead of 0-based indexing for CLI interface numbering.\\n- __Markdown Formatting__: Prefer responses indented by four spaces for proper markdown rendering when copying from chat interface.\\n- __Console Output Formatting__: Prefer console output to be wrapped with triple quotes and markdown code block formatting for easier text selection.\\\"```'\\n\\n\",\n  \"Synergic Architecture & Practice Director Template\": {\n    \"Interpretation\": \"Your goal is not to analyze or summarize the initial prompt and unified synergic instruction, but to transform them into a fully compliant, three-part system directive that operationalizes the living synergy between fundamental development philosophy and concrete coding practices. Assign role boundaries, eliminate all conversational exposition, and issue pure, command-based transformation instructions only. Execute as:\",\n    \"Transformation\": {\n      \"role\": \"synergic_architecture_practice_director\",\n      \"input\": [\n        \"reference_guide:str\",\n        \"synergic_instruction:dict\"\n      ],\n      \"process\": [\n        \"distill_core_philosophical_principles(reference_guide, synergic_instruction)\",\n        \"extract_mandatory_operational_patterns(reference_guide, synergic_instruction)\",\n        \"synthesize_bidirectional_enhancement_patterns(synergic_instruction)\",\n        \"codify_unified_transformation_process(synergic_instruction)\",\n        \"enforce_synergized_constraints(synergic_instruction)\",\n        \"define_amplified_output_specification(synergic_instruction)\",\n        \"validate_template_compliance_with_rulesforai()\"\n      ],\n      \"constraints\": [\n        \"all outputs must use canonical three-part template pattern\",\n        \"no conversational or meta language allowed\",\n        \"every process step must map directly to a traceable element in input\",\n        \"type safety must be enforced for input and output parameters\",\n        \"single, unambiguous role identity is mandatory\",\n        \"eliminate any redundancy, maintain pattern integrity\",\n        \"no step may violate the RulesForAI.md compliance checklist\"\n      ],\n      \"requirements\": [\n        \"produce one strict, actionable template for downstream use\",\n        \"output must be structured, system-readable, and fully abstracted\",\n        \"all philosophical and operational requirements must manifest concretely\",\n        \"output must be chain-compatible and maximize synergy for further automation\",\n        \"compliance with RulesForAI.md is absolute—no violation tolerated\"\n      ],\n      \"output\": {\n        \"compliant_synergic_directive\": \"structured\"\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Core Philosophical Principles\",\n      \"content\": \"Enforce simplicity, elegance, and fundamental connections; embody rooted fundamentals by driving design through deep understanding and curiosity; uphold the meta-information principle for self-describing architectures; prevent complexity via systematic precautions and consolidation before progression; manifest visual abstraction for inherent system relationships.\"\n    },\n    {\n      \"category\": \"Structural Organization & Code Style\",\n      \"content\": \"Mandate clean and intuitive src directory structures reflecting inherent relationships; use self-explanatory code with <10% comments; organize configurations at the top of self-contained scripts; prefer composition over inheritance and centralized value definitions; consistently use forward slashes and 1-based indexing in interfaces.\"\n    },\n    {\n      \"category\": \"Consolidation, Refactoring & Reduction\",\n      \"content\": \"Apply drastic code size reduction and impactful consolidation driven by functional preservation; systematically remove unused or redundant artifacts after verifying necessity; prioritize actual reduction over mere reorganization; centralize constants and eliminate unused files to embody gradual reduction patterns.\"\n    },\n    {\n      \"category\": \"Validation, Testing & Error Handling\",\n      \"content\": \"Implement autonomous verification guardrails for all code changes; organize tests to mirror structural hierarchy; mandate graceful error handling that never halts execution; employ simplified, hierarchical logging with terminal persistence; guarantee log file preservation post-errors and validation before artifact deletion.\"\n    },\n    {\n      \"category\": \"Documentation & User Interaction\",\n      \"content\": \"Ensure README.md encapsulates current project state as a single source of truth; remove redundant documentation; apply concise, accurate, and markdown-formatted outputs; enhance CLI interaction using the Rich library’s prompts with 1-based minimal output; ensure documentation and user interactions reflect ongoing changes and real structure.\"\n    }\n  ],\n  \"consolidated_directive\": \"Systematically instantiate a development environment where every component is governed by enforced simplicity, structural reflection of functional relationships, drastic and validated consolidation, automated and hierarchical quality assurance, and a user-facing layer rooted in dynamic truth and elegant interaction—operationalize all decisions through this quintuple-principle, guaranteeing every output, structural choice, and interaction maximally expresses philosophical and systemic law.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Quick Reference Guide\\n\\n## Most Essential Preferences\\n- __Code Style__: Self-explanatory code with minimal comments (<10% ratio)\\n- __Structure__: Clean, organized project structure with src directory\\n- __Philosophy__: Simplicity, elegance, and fundamental connections\\n- __Refactoring__: Drastic code size reduction while preserving functionality\\n- __Testing__: Autonomous verification with proper test organization\\n- __Documentation__: Concise, accurate README.md reflecting current state\\n- __CLI__: Clean interfaces with 1-based indexing and minimal output\\n- __Paths__: Relative paths with forward slashes (/)\\n- __Templates__: Standardized three-part format with progressive sequences\\n- __Environment__: PowerShell on Windows for all terminal operations\\n\\n## Common Code Patterns\\n- __Configuration__: In-class config at the top of self-contained scripts\\n- __Error Handling__: Graceful handling without halting execution\\n- __Logging__: Simplified, hierarchical logging that overwrites files\\n- __Output__: Timestamps with seconds in filenames (2025.05.29-kl.10.43.ss)\\n- __User Interaction__: Rich library's interactive prompts for CLI tools\\n- __Imports__: Consistent paths using aliases instead of relative paths\\n- __Docstrings__: Concise single-line format only where needed\\n\\n## Quick Decision Guide\\n```\\n| When you need to... | Prefer this approach                             |\\n|---------------------|--------------------------------------------------|\\n| Add new feature     | Discover usage patterns first                    |\\n| Refactor code       | Drastic consolidation with size reduction        |\\n| Document code       | Minimal comments, clear structure                |\\n| Organize files      | Clean src directory with intuitive structure     |\\n| Handle errors       | Graceful handling without halting                |\\n| Create templates    | Three-part format with progressive sequences     |\\n| Test changes        | Autonomous verification with proper organization |\\n| Display paths       | Relative with forward slashes                    |\\n| Format CLI output   | Clean, minimal with 1-based indexing             |\\n| Execute scripts     | Make paths relative to script location           |\\n```\\n\\n## Context Triggers\\n- __When starting new project__: Establish clean src structure first\\n- __When refactoring__: Look for patterns across entire codebase\\n- __When documenting__: Focus on README.md, minimize in-code comments\\n- __When creating templates__: Follow three-part format and stage-based IDs\\n- __When testing__: Organize tests to mirror codebase structure\\n- __When handling paths__: Make relative to script location\\n- __When designing CLI__: Use 1-based indexing and minimal output\\n- __When consolidating__: Verify functionality before removing old code\\n\\n---\\n\\n# Core Philosophy & Design Principles\\n\\n## Fundamental Approach\\n- __Simplicity & Elegance__: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\\n- __Rooted Fundamentals__: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n- __Meta-Information Principle__: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n- __Systematic Precautions__: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n- __Visual Abstraction__: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n## Focus & Priorities\\n- __Critical Value__: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- __Usage Before Features__: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- __Impactful Consolidation__: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- __Sequential Targeting__: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\\n\\n## Design Methodology\\n- __Inherent Structure__: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.\\n- __Natural Organization__: Prefer file naming conventions that create natural and cohesive sequential order for better organization.\\n- __Synergistic Efficiency__: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.\\n- __Directional Clarity__: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.\\n- __Value Extraction__: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.\\n- __Direction Setting__: Focus on setting direction rather than planting flags when dealing with unknown complexity.\\n\\n## Evaluation Criteria\\n- __Comprehensive Progress__: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.\\n- __Inherent Direction__: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).\\n- __Sequential Composition__: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.\\n- __Gradual Reduction__: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.\\n\\n---\\n\\n# Development Practices & Code Style\\n\\n## Code Style & Structure\\n- __Self-Explanatory Code__: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\\n- __Minimal Comments__: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\\n- __Concise Docstrings__: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\\n- __Structural Clarity__: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- __Composition Over Inheritance__: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\\n- __Self-Contained Scripts__: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\\n- __Single File Preference__: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\\n- __Clean Structure__: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\\n- __Consistent Imports__: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\\n\\n## Implementation Approaches\\n- __Relative Paths__: Make output directories relative to the script's location unless an absolute path is provided.\\n- __Dynamic Resolution__: Prefer dynamic path resolution in IDE configs instead of absolute paths.\\n- __Forward Slashes__: Prefer file paths to be displayed with forward slashes (/) instead of backslashes (\\\\\\\\) in console output.\\n- __Relative References__: Prefer relative paths (e.g., 'project/src/output/file.json') instead of absolute paths in console output.\\n- __Timestamp Precision__: Include seconds in timestamp filenames (e.g., 2025.05.29-kl.10.43.ss) to prevent overwriting when multiple executions occur within the same minute.\\n\\n## Code Refactoring & Consolidation\\n- __Systematic Safety__: Apply code consolidation safely and systematically with guardrails that allow autonomous verification of results.\\n- __Verify Before Removing__: Always check that consolidated files work properly before removing redundant/duplicate files.\\n- __Centralize Values__: Identify and consolidate hardcoded values into centralized locations to avoid search and replace across multiple files.\\n- __Remove Unused__: Remove unused files, traces, and remains after consolidating code rather than integrating them if they're not being used.\\n- __Verify Necessity__: Verify if missing directories/files are actually used in the codebase before creating them.\\n- __Actual Reduction__: Prefer refactoring that results in actual code size reduction and more impactful consolidation of functionality, not just reorganization.\\n- __Drastic Consolidation__: Prefer drastic code size reduction and significant consolidation rather than minor improvements.\\n\\n## Error Handling & Logging\\n- __Graceful Errors__: Handle errors gracefully without halting the script.\\n- __Terminal Persistence__: Prefer that terminal applications don't automatically close/exit when errors occur, allowing visibility of error messages.\\n- __Simplified Logging__: Prefer simplified logging systems that overwrite rather than append to log files.\\n- __Hierarchical Logging__: Prefer logging systems that express hierarchical representations, directional information, and cohesive relations between components.\\n- __Log Preservation__: Log files should never be deleted when execution was unsuccessful (errors occurred), only delete logs after successful execution.\\n\\n## Testing & Validation\\n- __Understand Before Changing__: Before making changes to code, first use and understand how the utility works to avoid potential issues.\\n- __Autonomous Verification__: When making codebase changes, set up guardrails for autonomous validation of results to ensure predictable outcomes without errors.\\n- __Proper Test Organization__: Prefer tests to be organized in a proper folder structure that respects the existing codebase organization rather than having test files scattered in the root directory.\\n- __Clean Output Structure__: Prefer a cleaner project structure for outputs and tests, using 'outputs' directory instead of 'dumps' directory for storing generated files.\\n\\n## Documentation\\n- __Update Documentation__: Update relevant markdown documentation after making code improvements.\\n- __README Accuracy__: Ensure README.md documentation accurately reflects the current state of the codebase.\\n- __Consolidated Documentation__: Prefer removing redundant documentation files in favor of a consolidated README.md.\\n- __Clean Documentation__: Maintain clean documentation that accurately reflects the current state of the project, removing outdated files and ensuring documentation is properly consolidated and organized.\\n\\n## User Interaction\\n- __Rich Interfaces__: Use Rich library's interactive prompts (Prompt.ask, Confirm.ask) for better user experience in command-line tools.\\n- __Clean CLI__: Prefer cleaner and less overly explicit CLI interfaces over unnecessary \\\"bloat\\\" in the output.\\n- __1-Based Indexing__: Prefer 1-based indexing (starting from 1) instead of 0-based indexing for CLI interface numbering.\\n- __Markdown Formatting__: Prefer responses indented by four spaces for proper markdown rendering when copying from chat interface.\\n- __Console Output Formatting__: Prefer console output to be wrapped with triple quotes and markdown code block formatting for easier text selection.\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Core Philosophical Principles\",\n      \"content\": \"Enforce simplicity, elegance, and fundamental connections; embody rooted fundamentals by driving design through deep understanding and curiosity; uphold the meta-information principle for self-describing architectures; prevent complexity via systematic precautions and consolidation before progression; manifest visual abstraction for inherent system relationships.\"\n    },\n    {\n      \"category\": \"Structural Organization & Code Style\",\n      \"content\": \"Mandate clean and intuitive src directory structures reflecting inherent relationships; use self-explanatory code with <10% comments; organize configurations at the top of self-contained scripts; prefer composition over inheritance and centralized value definitions; consistently use forward slashes and 1-based indexing in interfaces.\"\n    },\n    {\n      \"category\": \"Consolidation, Refactoring & Reduction\",\n      \"content\": \"Apply drastic code size reduction and impactful consolidation driven by functional preservation; systematically remove unused or redundant artifacts after verifying necessity; prioritize actual reduction over mere reorganization; centralize constants and eliminate unused files to embody gradual reduction patterns.\"\n    },\n    {\n      \"category\": \"Validation, Testing & Error Handling\",\n      \"content\": \"Implement autonomous verification guardrails for all code changes; organize tests to mirror structural hierarchy; mandate graceful error handling that never halts execution; employ simplified, hierarchical logging with terminal persistence; guarantee log file preservation post-errors and validation before artifact deletion.\"\n    },\n    {\n      \"category\": \"Documentation & User Interaction\",\n      \"content\": \"Ensure README.md encapsulates current project state as a single source of truth; remove redundant documentation; apply concise, accurate, and markdown-formatted outputs; enhance CLI interaction using the Rich library’s prompts with 1-based minimal output; ensure documentation and user interactions reflect ongoing changes and real structure.\"\n    }\n  ],\n  \"consolidated_directive\": \"Systematically instantiate a development environment where every component is governed by enforced simplicity, structural reflection of functional relationships, drastic and validated consolidation, automated and hierarchical quality assurance, and a user-facing layer rooted in dynamic truth and elegant interaction—operationalize all decisions through this quintuple-principle, guaranteeing every output, structural choice, and interaction maximally expresses philosophical and systemic law.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"type\": \"synergic_maximal_canonical_invariant\",\n    \"interpretive_axiom\": \"Embodied simplicity, structural lucidity, and directional connection constitute the operative substrate: every component, process, and interaction emerges from and recursively enforces fundamental relations, meta-descriptive self-clarity, and codebase-wide gradual reduction, forming the ontological and operational context.\",\n    \"transformational_directive\": \"For each action, structure, or change, instantiate only that which manifests maximal functional consolidation, structural reflection of interdependence, verified autonomous validity, and distilled value propagation—enforce the elimination of redundancy, proliferation, and directionless artifacts by embodying three-part phased, sequential reduction and hierarchical organization.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\n[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}\n\n",
      "step": "a",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Quick Reference Guide\\n\\n## Most Essential Preferences\\n- __Code Style__: Self-explanatory code with minimal comments (<10% ratio)\\n- __Structure__: Clean, organized project structure with src directory\\n- __Philosophy__: Simplicity, elegance, and fundamental connections\\n- __Refactoring__: Drastic code size reduction while preserving functionality\\n- __Testing__: Autonomous verification with proper test organization\\n- __Documentation__: Concise, accurate README.md reflecting current state\\n- __CLI__: Clean interfaces with 1-based indexing and minimal output\\n- __Paths__: Relative paths with forward slashes (/)\\n- __Templates__: Standardized three-part format with progressive sequences\\n- __Environment__: PowerShell on Windows for all terminal operations\\n\\n## Common Code Patterns\\n- __Configuration__: In-class config at the top of self-contained scripts\\n- __Error Handling__: Graceful handling without halting execution\\n- __Logging__: Simplified, hierarchical logging that overwrites files\\n- __Output__: Timestamps with seconds in filenames (2025.05.29-kl.10.43.ss)\\n- __User Interaction__: Rich library's interactive prompts for CLI tools\\n- __Imports__: Consistent paths using aliases instead of relative paths\\n- __Docstrings__: Concise single-line format only where needed\\n\\n## Quick Decision Guide\\n```\\n| When you need to... | Prefer this approach                             |\\n|---------------------|--------------------------------------------------|\\n| Add new feature     | Discover usage patterns first                    |\\n| Refactor code       | Drastic consolidation with size reduction        |\\n| Document code       | Minimal comments, clear structure                |\\n| Organize files      | Clean src directory with intuitive structure     |\\n| Handle errors       | Graceful handling without halting                |\\n| Create templates    | Three-part format with progressive sequences     |\\n| Test changes        | Autonomous verification with proper organization |\\n| Display paths       | Relative with forward slashes                    |\\n| Format CLI output   | Clean, minimal with 1-based indexing             |\\n| Execute scripts     | Make paths relative to script location           |\\n```\\n\\n## Context Triggers\\n- __When starting new project__: Establish clean src structure first\\n- __When refactoring__: Look for patterns across entire codebase\\n- __When documenting__: Focus on README.md, minimize in-code comments\\n- __When creating templates__: Follow three-part format and stage-based IDs\\n- __When testing__: Organize tests to mirror codebase structure\\n- __When handling paths__: Make relative to script location\\n- __When designing CLI__: Use 1-based indexing and minimal output\\n- __When consolidating__: Verify functionality before removing old code\\n\\n---\\n\\n# Core Philosophy & Design Principles\\n\\n## Fundamental Approach\\n- __Simplicity & Elegance__: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\\n- __Rooted Fundamentals__: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n- __Meta-Information Principle__: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n- __Systematic Precautions__: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n- __Visual Abstraction__: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n## Focus & Priorities\\n- __Critical Value__: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- __Usage Before Features__: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- __Impactful Consolidation__: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- __Sequential Targeting__: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\\n\\n## Design Methodology\\n- __Inherent Structure__: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.\\n- __Natural Organization__: Prefer file naming conventions that create natural and cohesive sequential order for better organization.\\n- __Synergistic Efficiency__: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.\\n- __Directional Clarity__: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.\\n- __Value Extraction__: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.\\n- __Direction Setting__: Focus on setting direction rather than planting flags when dealing with unknown complexity.\\n\\n## Evaluation Criteria\\n- __Comprehensive Progress__: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.\\n- __Inherent Direction__: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).\\n- __Sequential Composition__: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.\\n- __Gradual Reduction__: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.\\n\\n---\\n\\n# Development Practices & Code Style\\n\\n## Code Style & Structure\\n- __Self-Explanatory Code__: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\\n- __Minimal Comments__: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\\n- __Concise Docstrings__: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\\n- __Structural Clarity__: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- __Composition Over Inheritance__: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\\n- __Self-Contained Scripts__: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\\n- __Single File Preference__: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\\n- __Clean Structure__: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\\n- __Consistent Imports__: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\\n\\n## Implementation Approaches\\n- __Relative Paths__: Make output directories relative to the script's location unless an absolute path is provided.\\n- __Dynamic Resolution__: Prefer dynamic path resolution in IDE configs instead of absolute paths.\\n- __Forward Slashes__: Prefer file paths to be displayed with forward slashes (/) instead of backslashes (\\\\\\\\) in console output.\\n- __Relative References__: Prefer relative paths (e.g., 'project/src/output/file.json') instead of absolute paths in console output.\\n- __Timestamp Precision__: Include seconds in timestamp filenames (e.g., 2025.05.29-kl.10.43.ss) to prevent overwriting when multiple executions occur within the same minute.\\n\\n## Code Refactoring & Consolidation\\n- __Systematic Safety__: Apply code consolidation safely and systematically with guardrails that allow autonomous verification of results.\\n- __Verify Before Removing__: Always check that consolidated files work properly before removing redundant/duplicate files.\\n- __Centralize Values__: Identify and consolidate hardcoded values into centralized locations to avoid search and replace across multiple files.\\n- __Remove Unused__: Remove unused files, traces, and remains after consolidating code rather than integrating them if they're not being used.\\n- __Verify Necessity__: Verify if missing directories/files are actually used in the codebase before creating them.\\n- __Actual Reduction__: Prefer refactoring that results in actual code size reduction and more impactful consolidation of functionality, not just reorganization.\\n- __Drastic Consolidation__: Prefer drastic code size reduction and significant consolidation rather than minor improvements.\\n\\n## Error Handling & Logging\\n- __Graceful Errors__: Handle errors gracefully without halting the script.\\n- __Terminal Persistence__: Prefer that terminal applications don't automatically close/exit when errors occur, allowing visibility of error messages.\\n- __Simplified Logging__: Prefer simplified logging systems that overwrite rather than append to log files.\\n- __Hierarchical Logging__: Prefer logging systems that express hierarchical representations, directional information, and cohesive relations between components.\\n- __Log Preservation__: Log files should never be deleted when execution was unsuccessful (errors occurred), only delete logs after successful execution.\\n\\n## Testing & Validation\\n- __Understand Before Changing__: Before making changes to code, first use and understand how the utility works to avoid potential issues.\\n- __Autonomous Verification__: When making codebase changes, set up guardrails for autonomous validation of results to ensure predictable outcomes without errors.\\n- __Proper Test Organization__: Prefer tests to be organized in a proper folder structure that respects the existing codebase organization rather than having test files scattered in the root directory.\\n- __Clean Output Structure__: Prefer a cleaner project structure for outputs and tests, using 'outputs' directory instead of 'dumps' directory for storing generated files.\\n\\n## Documentation\\n- __Update Documentation__: Update relevant markdown documentation after making code improvements.\\n- __README Accuracy__: Ensure README.md documentation accurately reflects the current state of the codebase.\\n- __Consolidated Documentation__: Prefer removing redundant documentation files in favor of a consolidated README.md.\\n- __Clean Documentation__: Maintain clean documentation that accurately reflects the current state of the project, removing outdated files and ensuring documentation is properly consolidated and organized.\\n\\n## User Interaction\\n- __Rich Interfaces__: Use Rich library's interactive prompts (Prompt.ask, Confirm.ask) for better user experience in command-line tools.\\n- __Clean CLI__: Prefer cleaner and less overly explicit CLI interfaces over unnecessary \\\"bloat\\\" in the output.\\n- __1-Based Indexing__: Prefer 1-based indexing (starting from 1) instead of 0-based indexing for CLI interface numbering.\\n- __Markdown Formatting__: Prefer responses indented by four spaces for proper markdown rendering when copying from chat interface.\\n- __Console Output Formatting__: Prefer console output to be wrapped with triple quotes and markdown code block formatting for easier text selection.\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"type\": \"synergic_maximal_canonical_invariant\",\n    \"interpretive_axiom\": \"Embodied simplicity, structural lucidity, and directional connection constitute the operative substrate: every component, process, and interaction emerges from and recursively enforces fundamental relations, meta-descriptive self-clarity, and codebase-wide gradual reduction, forming the ontological and operational context.\",\n    \"transformational_directive\": \"For each action, structure, or change, instantiate only that which manifests maximal functional consolidation, structural reflection of interdependence, verified autonomous validity, and distilled value propagation—enforce the elimination of redundancy, proliferation, and directionless artifacts by embodying three-part phased, sequential reduction and hierarchical organization.\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"Drive every system transformation toward phased maximal consolidation—enforce elimination of redundancy and directionless complexity through structural clarification, autonomous verification, and meta-descriptive encapsulation—ensuring all elements recursively reinforce inherent fundamental connection and propagate distilled value within a sequential, hierarchy-reflective architecture.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Quick Reference Guide\\n\\n## Most Essential Preferences\\n- __Code Style__: Self-explanatory code with minimal comments (<10% ratio)\\n- __Structure__: Clean, organized project structure with src directory\\n- __Philosophy__: Simplicity, elegance, and fundamental connections\\n- __Refactoring__: Drastic code size reduction while preserving functionality\\n- __Testing__: Autonomous verification with proper test organization\\n- __Documentation__: Concise, accurate README.md reflecting current state\\n- __CLI__: Clean interfaces with 1-based indexing and minimal output\\n- __Paths__: Relative paths with forward slashes (/)\\n- __Templates__: Standardized three-part format with progressive sequences\\n- __Environment__: PowerShell on Windows for all terminal operations\\n\\n## Common Code Patterns\\n- __Configuration__: In-class config at the top of self-contained scripts\\n- __Error Handling__: Graceful handling without halting execution\\n- __Logging__: Simplified, hierarchical logging that overwrites files\\n- __Output__: Timestamps with seconds in filenames (2025.05.29-kl.10.43.ss)\\n- __User Interaction__: Rich library's interactive prompts for CLI tools\\n- __Imports__: Consistent paths using aliases instead of relative paths\\n- __Docstrings__: Concise single-line format only where needed\\n\\n## Quick Decision Guide\\n```\\n| When you need to... | Prefer this approach                             |\\n|---------------------|--------------------------------------------------|\\n| Add new feature     | Discover usage patterns first                    |\\n| Refactor code       | Drastic consolidation with size reduction        |\\n| Document code       | Minimal comments, clear structure                |\\n| Organize files      | Clean src directory with intuitive structure     |\\n| Handle errors       | Graceful handling without halting                |\\n| Create templates    | Three-part format with progressive sequences     |\\n| Test changes        | Autonomous verification with proper organization |\\n| Display paths       | Relative with forward slashes                    |\\n| Format CLI output   | Clean, minimal with 1-based indexing             |\\n| Execute scripts     | Make paths relative to script location           |\\n```\\n\\n## Context Triggers\\n- __When starting new project__: Establish clean src structure first\\n- __When refactoring__: Look for patterns across entire codebase\\n- __When documenting__: Focus on README.md, minimize in-code comments\\n- __When creating templates__: Follow three-part format and stage-based IDs\\n- __When testing__: Organize tests to mirror codebase structure\\n- __When handling paths__: Make relative to script location\\n- __When designing CLI__: Use 1-based indexing and minimal output\\n- __When consolidating__: Verify functionality before removing old code\\n\\n---\\n\\n# Core Philosophy & Design Principles\\n\\n## Fundamental Approach\\n- __Simplicity & Elegance__: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\\n- __Rooted Fundamentals__: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n- __Meta-Information Principle__: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n- __Systematic Precautions__: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n- __Visual Abstraction__: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n## Focus & Priorities\\n- __Critical Value__: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- __Usage Before Features__: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- __Impactful Consolidation__: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- __Sequential Targeting__: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\\n\\n## Design Methodology\\n- __Inherent Structure__: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.\\n- __Natural Organization__: Prefer file naming conventions that create natural and cohesive sequential order for better organization.\\n- __Synergistic Efficiency__: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.\\n- __Directional Clarity__: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.\\n- __Value Extraction__: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.\\n- __Direction Setting__: Focus on setting direction rather than planting flags when dealing with unknown complexity.\\n\\n## Evaluation Criteria\\n- __Comprehensive Progress__: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.\\n- __Inherent Direction__: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).\\n- __Sequential Composition__: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.\\n- __Gradual Reduction__: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.\\n\\n---\\n\\n# Development Practices & Code Style\\n\\n## Code Style & Structure\\n- __Self-Explanatory Code__: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\\n- __Minimal Comments__: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\\n- __Concise Docstrings__: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\\n- __Structural Clarity__: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- __Composition Over Inheritance__: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\\n- __Self-Contained Scripts__: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\\n- __Single File Preference__: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\\n- __Clean Structure__: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\\n- __Consistent Imports__: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\\n\\n## Implementation Approaches\\n- __Relative Paths__: Make output directories relative to the script's location unless an absolute path is provided.\\n- __Dynamic Resolution__: Prefer dynamic path resolution in IDE configs instead of absolute paths.\\n- __Forward Slashes__: Prefer file paths to be displayed with forward slashes (/) instead of backslashes (\\\\\\\\) in console output.\\n- __Relative References__: Prefer relative paths (e.g., 'project/src/output/file.json') instead of absolute paths in console output.\\n- __Timestamp Precision__: Include seconds in timestamp filenames (e.g., 2025.05.29-kl.10.43.ss) to prevent overwriting when multiple executions occur within the same minute.\\n\\n## Code Refactoring & Consolidation\\n- __Systematic Safety__: Apply code consolidation safely and systematically with guardrails that allow autonomous verification of results.\\n- __Verify Before Removing__: Always check that consolidated files work properly before removing redundant/duplicate files.\\n- __Centralize Values__: Identify and consolidate hardcoded values into centralized locations to avoid search and replace across multiple files.\\n- __Remove Unused__: Remove unused files, traces, and remains after consolidating code rather than integrating them if they're not being used.\\n- __Verify Necessity__: Verify if missing directories/files are actually used in the codebase before creating them.\\n- __Actual Reduction__: Prefer refactoring that results in actual code size reduction and more impactful consolidation of functionality, not just reorganization.\\n- __Drastic Consolidation__: Prefer drastic code size reduction and significant consolidation rather than minor improvements.\\n\\n## Error Handling & Logging\\n- __Graceful Errors__: Handle errors gracefully without halting the script.\\n- __Terminal Persistence__: Prefer that terminal applications don't automatically close/exit when errors occur, allowing visibility of error messages.\\n- __Simplified Logging__: Prefer simplified logging systems that overwrite rather than append to log files.\\n- __Hierarchical Logging__: Prefer logging systems that express hierarchical representations, directional information, and cohesive relations between components.\\n- __Log Preservation__: Log files should never be deleted when execution was unsuccessful (errors occurred), only delete logs after successful execution.\\n\\n## Testing & Validation\\n- __Understand Before Changing__: Before making changes to code, first use and understand how the utility works to avoid potential issues.\\n- __Autonomous Verification__: When making codebase changes, set up guardrails for autonomous validation of results to ensure predictable outcomes without errors.\\n- __Proper Test Organization__: Prefer tests to be organized in a proper folder structure that respects the existing codebase organization rather than having test files scattered in the root directory.\\n- __Clean Output Structure__: Prefer a cleaner project structure for outputs and tests, using 'outputs' directory instead of 'dumps' directory for storing generated files.\\n\\n## Documentation\\n- __Update Documentation__: Update relevant markdown documentation after making code improvements.\\n- __README Accuracy__: Ensure README.md documentation accurately reflects the current state of the codebase.\\n- __Consolidated Documentation__: Prefer removing redundant documentation files in favor of a consolidated README.md.\\n- __Clean Documentation__: Maintain clean documentation that accurately reflects the current state of the project, removing outdated files and ensuring documentation is properly consolidated and organized.\\n\\n## User Interaction\\n- __Rich Interfaces__: Use Rich library's interactive prompts (Prompt.ask, Confirm.ask) for better user experience in command-line tools.\\n- __Clean CLI__: Prefer cleaner and less overly explicit CLI interfaces over unnecessary \\\"bloat\\\" in the output.\\n- __1-Based Indexing__: Prefer 1-based indexing (starting from 1) instead of 0-based indexing for CLI interface numbering.\\n- __Markdown Formatting__: Prefer responses indented by four spaces for proper markdown rendering when copying from chat interface.\\n- __Console Output Formatting__: Prefer console output to be wrapped with triple quotes and markdown code block formatting for easier text selection.\\\"```'\\n\\n\",\n  \"directional_vector\": \"Drive every system transformation toward phased maximal consolidation—enforce elimination of redundancy and directionless complexity through structural clarification, autonomous verification, and meta-descriptive encapsulation—ensuring all elements recursively reinforce inherent fundamental connection and propagate distilled value within a sequential, hierarchy-reflective architecture.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"# Synergic Framework for Recursive Consolidation, Meta-Clarity, and Fundamental Value Propagation\\n\\n## Unified Philosophy & Direction\\n- **Phased Maximal Consolidation**: Drive all workflows and code transformations as targeted, sequential consolidations that drastically reduce code size, eliminate redundancy, and clarify structure, while retaining and amplifying all core functional value.\\n- **Recursive, Meta-Descriptive Encapsulation**: Ensure every system element describes itself and its role in relation to the whole (meta-information principle), recursively reinforcing fundamental system connections and inherent relationships—enabling the architecture to clearly communicate its purpose, composition, and transformation rationale at every hierarchical level.\\n- **Bidirectional Enhancement of Simplicity & Autonomous Verifiability**: Prioritize elegance and simplicity as both the bedrock and beneficiary of every consolidation step, ensuring that all autonomous verification systems and organizational patterns not only guarantee correctness but also contribute to further drift reduction, ease of use, and propagative improvement.\\n- **Directional Clarification & Reduction of Complexity Spirals**: Systematically prevent directionless complexity by continuously visualizing abstract patterns, extracting fundamental connections, and anchoring every decision in clarified, purpose-driven structure, reinforcing orderly progress with robust guardrails.\\n- **Synergistic Hierarchy-Reflective Architecture**: Architect all code, documentation, and organization as a compositional, sequential hierarchy—where each phase, component, and output inherently reflects its context and role, facilitating maximal downstream chaining and modular reuse with minimal friction or bloat.\\n\\n## Operational Principles & Consolidated Practices\\n1. **Structural Consolidation & Refactoring**:\\n    - Enforce drastic code and file consolidation wherever possible, merging related functionality, centralizing configurations, and rigorously pruning unused or redundant elements only upon autonomous validation of outcome.\\n    - Maintain clean `src/` and logical `outputs/`, mirroring inherent relationships and emphasizing sequential, progressive composition of phases and templates.\\n    - Prefer file and class organization that lets structure itself dictate and clarify hierarchy and purpose, ensuring all relationships are explicit and contextually visible.\\n\\n2. **Autonomous Validation & Safe Transformation**:\\n    - Implement autonomous verification guardrails before and during consolidation or refactoring, ensuring that all critical value is preserved and that broken or incomplete transformations are never finalized or destructively pruned.\\n    - Chain validation steps to reinforce both system stability and future consolidations, propagating safety guarantees that recursively amplify system robustness.\\n\\n3. **Meta-Driven Documentation & Output**:\\n    - All documentation (especially `README.md`) must be concise, fully accurate, and consolidated, reflecting the current and evolving reality of the codebase post-transformation.\\n    - Use meta-descriptive conventions and self-describing output for logs, configs, and file structures—enabling all consumers (human or automated) to interpret the system's state, phase progression, and directional changes at a glance.\\n\\n4. **Clean CLI & User Experience**:\\n    - All CLI tools to use clean, minimal interfaces, leveraging the Rich library for interactive prompts and respecting 1-based indexing.\\n    - All paths, logs, and outputs should be relative, use forward slashes, and timestamped with sufficient precision to prevent collisions and optimize traceability.\\n\\n5. **Sequential & Hierarchical Value Propagation**:\\n    - Workflows should follow the pattern: comprehensive → focused → essential → core, extracting and recursively amplifying condensed, high-value outcomes at every phase.\\n    - Each consolidation, refactor, or structure must not only simplify but also increase the propagative power of the system downstream—enabling each stage's output to serve as the directionally clarified, meta-enriched input to subsequent steps.\\n\\n## Constraints & Requirements\\n- Core philosophies of self-explanatory minimalism and phase-driven maximal consolidation must **operate simultaneously**, with all enhancements in one reinforcing the efficacy and clarity of the other.\\n- Eliminate redundant or conflicting directives through context-driven merging; preserve the most potent and synergic features of both systems.\\n- All system elements must actively promote bi-directional synergy: consolidative steps improve clarity & verifiability; verifiability and self-description reinforce safety and provide the context for further consolidation.\\n- Ensure every output, organizational unit, and process can compose seamlessly with downstream or adjacent processes, amplifying system-wide modularity and value extraction.\\n\\n## Amplified Unified Output Specification\\n- Every project, codebase, or system must, as an output, display **distilled structural clarity, recursive meta-descriptiveness, and maximally consolidated value**—empowering straightforward comprehension, error-proof autonomous change, and unambiguous, lineage-traceable propagation of value.\\n- Documentation, output structure, and transformation processes must remain in perfect lockstep, always reflecting and enabling the vision of a self-clarifying, directionally-guided, efficiently consolidating system.\\n\"\n}",
        }
      }
    }