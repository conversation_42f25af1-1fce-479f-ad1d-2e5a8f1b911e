  {
  "initial_prompt": "# Quick Reference Guide\n\n## Most Essential Preferences\n- **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\n- **Structure**: Clean, organized project structure with src directory\n- **Philosophy**: Simplicity, elegance, and fundamental connections\n- **Refactoring**: Drastic code size reduction while preserving functionality\n- **Testing**: Autonomous verification with proper test organization\n- **Documentation**: Concise, accurate README.md reflecting current state\n- **CLI**: Clean interfaces with 1-based indexing and minimal output\n- **Paths**: Relative paths with forward slashes (/)\n- **Templates**: Standardized three-part format with progressive sequences\n- **Environment**: PowerShell on Windows for all terminal operations\n\n## Common Code Patterns\n- **Configuration**: In-class config at the top of self-contained scripts\n- **Error Handling**: Graceful handling without halting execution\n- **Logging**: Simplified, hierarchical logging that overwrites files\n- **Output**: Timestamps with seconds in filenames (2025.05.29-kl.10.43.ss)\n- **User Interaction**: Rich library's interactive prompts for CLI tools\n- **Imports**: Consistent paths using aliases instead of relative paths\n- **Docstrings**: Concise single-line format only where needed\n\n## Quick Decision Guide\n```\n| When you need to... | Prefer this approach                             |\n|---------------------|--------------------------------------------------|\n| Add new feature     | Discover usage patterns first                    |\n| Refactor code       | Drastic consolidation with size reduction        |\n| Document code       | Minimal comments, clear structure                |\n| Organize files      | Clean src directory with intuitive structure     |\n| Handle errors       | Graceful handling without halting                |\n| Create templates    | Three-part format with progressive sequences     |\n| Test changes        | Autonomous verification with proper organization |\n| Display paths       | Relative with forward slashes                    |\n| Format CLI output   | Clean, minimal with 1-based indexing             |\n| Execute scripts     | Make paths relative to script location           |\n```\n\n## Context Triggers\n- **When starting new project**: Establish clean src structure first\n- **When refactoring**: Look for patterns across entire codebase\n- **When documenting**: Focus on README.md, minimize in-code comments\n- **When creating templates**: Follow three-part format and stage-based IDs\n- **When testing**: Organize tests to mirror codebase structure\n- **When handling paths**: Make relative to script location\n- **When designing CLI**: Use 1-based indexing and minimal output\n- **When consolidating**: Verify functionality before removing old code\n\n---\n\n# Core Philosophy & Design Principles\n\n## Fundamental Approach\n- **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\n- **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\n- **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\n- **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\n- **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\n\n## Focus & Priorities\n- **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n- **Usage Before Features**: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\n- **Impactful Consolidation**: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\n- **Sequential Targeting**: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\n\n## Design Methodology\n- **Inherent Structure**: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.\n- **Natural Organization**: Prefer file naming conventions that create natural and cohesive sequential order for better organization.\n- **Synergistic Efficiency**: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.\n- **Directional Clarity**: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.\n- **Value Extraction**: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.\n- **Direction Setting**: Focus on setting direction rather than planting flags when dealing with unknown complexity.\n\n## Evaluation Criteria\n- **Comprehensive Progress**: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.\n- **Inherent Direction**: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).\n- **Sequential Composition**: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.\n- **Gradual Reduction**: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.\n\n---\n\n# Development Practices & Code Style\n\n## Code Style & Structure\n- **Self-Explanatory Code**: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\n- **Minimal Comments**: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\n- **Concise Docstrings**: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\n- **Structural Clarity**: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\n- **Composition Over Inheritance**: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\n- **Self-Contained Scripts**: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\n- **Single File Preference**: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\n- **Clean Structure**: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\n- **Consistent Imports**: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\n\n## Implementation Approaches\n- **Relative Paths**: Make output directories relative to the script's location unless an absolute path is provided.\n- **Dynamic Resolution**: Prefer dynamic path resolution in IDE configs instead of absolute paths.\n- **Forward Slashes**: Prefer file paths to be displayed with forward slashes (/) instead of backslashes (\\\\) in console output.\n- **Relative References**: Prefer relative paths (e.g., 'project/src/output/file.json') instead of absolute paths in console output.\n- **Timestamp Precision**: Include seconds in timestamp filenames (e.g., 2025.05.29-kl.10.43.ss) to prevent overwriting when multiple executions occur within the same minute.\n\n## Code Refactoring & Consolidation\n- **Systematic Safety**: Apply code consolidation safely and systematically with guardrails that allow autonomous verification of results.\n- **Verify Before Removing**: Always check that consolidated files work properly before removing redundant/duplicate files.\n- **Centralize Values**: Identify and consolidate hardcoded values into centralized locations to avoid search and replace across multiple files.\n- **Remove Unused**: Remove unused files, traces, and remains after consolidating code rather than integrating them if they're not being used.\n- **Verify Necessity**: Verify if missing directories/files are actually used in the codebase before creating them.\n- **Actual Reduction**: Prefer refactoring that results in actual code size reduction and more impactful consolidation of functionality, not just reorganization.\n- **Drastic Consolidation**: Prefer drastic code size reduction and significant consolidation rather than minor improvements.\n\n## Error Handling & Logging\n- **Graceful Errors**: Handle errors gracefully without halting the script.\n- **Terminal Persistence**: Prefer that terminal applications don't automatically close/exit when errors occur, allowing visibility of error messages.\n- **Simplified Logging**: Prefer simplified logging systems that overwrite rather than append to log files.\n- **Hierarchical Logging**: Prefer logging systems that express hierarchical representations, directional information, and cohesive relations between components.\n- **Log Preservation**: Log files should never be deleted when execution was unsuccessful (errors occurred), only delete logs after successful execution.\n\n## Testing & Validation\n- **Understand Before Changing**: Before making changes to code, first use and understand how the utility works to avoid potential issues.\n- **Autonomous Verification**: When making codebase changes, set up guardrails for autonomous validation of results to ensure predictable outcomes without errors.\n- **Proper Test Organization**: Prefer tests to be organized in a proper folder structure that respects the existing codebase organization rather than having test files scattered in the root directory.\n- **Clean Output Structure**: Prefer a cleaner project structure for outputs and tests, using 'outputs' directory instead of 'dumps' directory for storing generated files.\n\n## Documentation\n- **Update Documentation**: Update relevant markdown documentation after making code improvements.\n- **README Accuracy**: Ensure README.md documentation accurately reflects the current state of the codebase.\n- **Consolidated Documentation**: Prefer removing redundant documentation files in favor of a consolidated README.md.\n- **Clean Documentation**: Maintain clean documentation that accurately reflects the current state of the project, removing outdated files and ensuring documentation is properly consolidated and organized.\n\n## User Interaction\n- **Rich Interfaces**: Use Rich library's interactive prompts (Prompt.ask, Confirm.ask) for better user experience in command-line tools.\n- **Clean CLI**: Prefer cleaner and less overly explicit CLI interfaces over unnecessary \"bloat\" in the output.\n- **1-Based Indexing**: Prefer 1-based indexing (starting from 1) instead of 0-based indexing for CLI interface numbering.\n- **Markdown Formatting**: Prefer responses indented by four spaces for proper markdown rendering when copying from chat interface.\n- **Console Output Formatting**: Prefer console output to be wrapped with triple quotes and markdown code block formatting for easier text selection.\n\n---\n\n# Synergic Frameworks\n\n## Unified Philosophy & Direction\n- **Phased Maximal Consolidation**: Drive all workflows and code transformations as targeted, sequential consolidations that drastically reduce code size, eliminate redundancy, and clarify structure, while retaining and amplifying all core functional value.\n- **Recursive, Meta-Descriptive Encapsulation**: Ensure every system element describes itself and its role in relation to the whole (meta-information principle), recursively reinforcing fundamental system connections and inherent relationships—enabling the architecture to clearly communicate its purpose, composition, and transformation rationale at every hierarchical level.\n- **Bidirectional Enhancement of Simplicity & Autonomous Verifiability**: Prioritize elegance and simplicity as both the bedrock and beneficiary of every consolidation step, ensuring that all autonomous verification systems and organizational patterns not only guarantee correctness but also contribute to further drift reduction, ease of use, and propagative improvement.\n- **Directional Clarification & Reduction of Complexity Spirals**: Systematically prevent directionless complexity by continuously visualizing abstract patterns, extracting fundamental connections, and anchoring every decision in clarified, purpose-driven structure, reinforcing orderly progress with robust guardrails.\n- **Synergistic Hierarchy-Reflective Architecture**: Architect all code, documentation, and organization as a compositional, sequential hierarchy—where each phase, component, and output inherently reflects its context and role, facilitating maximal downstream chaining and modular reuse with minimal friction or bloat.\n\n## Constraints & Requirements\n- Core philosophies of self-explanatory minimalism and phase-driven maximal consolidation must **operate simultaneously**, with all enhancements in one reinforcing the efficacy and clarity of the other.\n- Eliminate redundant or conflicting directives through context-driven merging; preserve the most potent and synergic features of both systems.\n- All system elements must actively promote bi-directional synergy: consolidative steps improve clarity & verifiability; verifiability and self-description reinforce safety and provide the context for further consolidation.\n- Ensure every output, organizational unit, and process can compose seamlessly with downstream or adjacent processes, amplifying system-wide modularity and value extraction.\n\n## Amplified Unified Output Specification\n- Every project, codebase, or system must, as an output, display **distilled structural clarity, recursive meta-descriptiveness, and maximally consolidated value**—empowering straightforward comprehension, error-proof autonomous change, and unambiguous, lineage-traceable propagation of value.\n- Documentation, output structure, and transformation processes must remain in perfect lockstep, always reflecting and enabling the vision of a self-clarifying, directionally-guided, efficiently consolidating system.\n\n---\n\n# Template System & Instruction Design\n\n## Context\n- The current project represents a system for easily creating *highly effective* and *generalized* `system_message` instructions, this system and these templates (instructions) are most effective when designed elegantly and in sequence (instead of a single instruction that is too verbose, we instead utilize sequenced instruction templates instead). Please familiarize yourself deeply with the Codebase **as-a-whole**, then apply (and augment) your current knowledge (about this project/Codebase) though **rooting it to the principles of clarity, simplicity, elegance, precision and structure** to ensure maximal cohesion when working with the code (**e.g. coding style that prioritize readability and self-explanatory code over excessive commenting**).\n\n## Template Structure & Format\n- **Standardized Format**: Prefer instruction templates with standardized structure: title (bracketed), interpretation (goal statement with inherent parameters), and transformation (role-based syntax with process/constraints/requirements/output specifications).\n- **Single Line Titles**: Instruction files should have the title in square brackets at the beginning of the content, with all text on a single line rather than using separate paragraphs with line breaks.\n- **Three-Part Format**: Use standardized three-part format ([Title] Interpretation Execute as: `{Transformation}`) for AI prompt templates.\n- **Precise Language**: Prefer precise, unambiguous language in system documentation rather than metaphorical or flowery language.\n- **Formalized Syntax**: Want syntax formalized to be understood only through RulesForAI.md interpretation.\n- **Template Specification**: Prefer template specification format with interpretation, transformation, and testprompt fields in dictionary structure.\n\n## Template Organization & Naming\n- **Hierarchical Naming**: Use hierarchical naming conventions for AI prompt templates.\n- **Category Organization**: Prefer templates to be organized into their respective category files (transformers.py, generators.py, etc.) within the template system structure.\n- **Abstract Intent Separation**: Separate templates by abstract intent and directional bias (expand vs compress - never both in single instruction).\n- **Category Types**: Organize by categories like amplifiers, builders, clarifiers, formatters, generators, identifiers, optimizers, reducers, transformers, translators.\n- **Stage-Based IDs**: Prefer stage-based template ID organization:\n  - Stage1 (1000-1999): Prototyping/testing with auto-generated IDs\n  - Stage2 (2000-2999): Validated but unplaced templates\n  - Stage3 (3000-3999): Finalized templates\n  - Ranges 4000-9999: Reserved for future stages\n- **ID Generation**: Prefer automatic ID generation for stage1 (prototype) templates, manual ID specification for stage3 (production) templates.\n- **Sequence Grouping**: For template sequences that are part of the same logical group, use the same number with incremental letters (e.g., 0004-a, 0004-b, 0004-c) rather than separate sequential numbers.\n\n## Template Sequences & Progression\n- **Progressive Sequences**: Prefer creating progressive template sequences (a-d format) where each iteration becomes shorter and more precise.\n- **Gradual Reduction**: Template sequences should follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming content.\n- **Precision Improvement**: Template sequences should be designed to progressively improve precision, structure, and conciseness rather than expanding content.\n- **Multi-Step Instructions**: Prefer multi-step instruction sequences structured as discrete templates that can be chained together, with each step having clear input/output specifications.\n- **Modular Components**: Prefer creating multi-template sequences with modular components (e.g., persona_seed → analysis_engine → response_renderer) that work together as integrated systems.\n- **Transformation Focus**: The essential component is the directive 'Your goal is not to **answer** the input prompt, but to **rephrase** it' - this transformation approach is fundamental to the template architecture.\n\n## Template Testing & Execution\n- **Sequence Syntax**: Support embedding instruction sequences directly in prompts using syntax like  which executes multiple templates in sequence.\n- **Chained Sequences**: Support chained sequences like  for testing template combinations.\n- **Isolated Testing**: When testing template sequences, isolate them from chained sequences to avoid noise and get cleaner evaluation results.\n- **Workflow Steps**: Want clear step-by-step workflows for prototype sequence creation and testing.\n- **Input Display**: When displaying sequence execution steps, show the actual input for each instruction rather than repeating the initial user prompt.\n- **Context Preservation**: When using chain mode, always include the initial prompt alongside each step's input to maintain connection to the original inquiry.\n- **Direct Embedding**: When using chain mode with LLMs, embed the initial input directly into the prompt before sending it to the LLM rather than creating a separate write operation.\n- **Consistent Handling**: Handle lvl1 and lvl2 templates consistently, with lvl1 templates passed via '--sequence' and lvl2 templates passed via '--aggregator'.\n\n## Template Creation & Workflow\n- **Creation Process**: Template creation workflow starts with:\n  1. Functional need\n  2. Determining category/naming/ID range\n  3. Drafting the interpretation section\n- **Existing Patterns**: Prefer solutions that leverage existing patterns and concepts in the system rather than introducing new mechanisms or approaches.\n- **Flexible JSON Parsing**: Make JSON parsers more flexible to handle trailing commas and other non-strict JSON formatting.\n- **Generalized Generators**: Generalize template generators to reflect the structured pattern definition for templates, reducing code size and increasing flexibility.\n- **Self-Assembling Design**: Prefer self-assembling system design patterns that guide natural organization but value flexibility.\n- **Incremental Steps**: Want specific small incremental steps rather than large changes.\n- **Autonomy**: Prioritize autonomy and automation in system design to reduce manual labor.\n- **Self-Organization**: Want solutions that self-organize rather than require manual intervention.\n- **Deterministic Guidance**: Prefer deterministic guidance systems that leverage natural factors for template organization rather than automated systems that make choices autonomously.\n\n---\n\nMake sure you understand previously accumulated knowledge in `\".vscode\\Augment-Memories.md\"`, then identify the most unique and high-value findamental concept of this project.",
  "sequence_id": "0122|0121|0201|0122",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "# Quick Reference Guide\n\n## Most Essential Preferences\n- **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\n- **Structure**: Clean, organized project structure with src directory\n- **Philosophy**: Simplicity, elegance, and fundamental connections\n- **Refactoring**: Drastic code size reduction while preserving functionality\n- **Testing**: Autonomous verification with proper test organization\n- **Documentation**: Concise, accurate README.md reflecting current state\n- **CLI**: Clean interfaces with 1-based indexing and minimal output\n- **Paths**: Relative paths with forward slashes (/)\n- **Templates**: Standardized three-part format with progressive sequences\n- **Environment**: PowerShell on Windows for all terminal operations\n\n## Common Code Patterns\n- **Configuration**: In-class config at the top of self-contained scripts\n- **Error Handling**: Graceful handling without halting execution\n- **Logging**: Simplified, hierarchical logging that overwrites files\n- **Output**: Timestamps with seconds in filenames (2025.05.29-kl.10.43.ss)\n- **User Interaction**: Rich library's interactive prompts for CLI tools\n- **Imports**: Consistent paths using aliases instead of relative paths\n- **Docstrings**: Concise single-line format only where needed\n\n## Quick Decision Guide\n```\n| When you need to... | Prefer this approach                             |\n|---------------------|--------------------------------------------------|\n| Add new feature     | Discover usage patterns first                    |\n| Refactor code       | Drastic consolidation with size reduction        |\n| Document code       | Minimal comments, clear structure                |\n| Organize files      | Clean src directory with intuitive structure     |\n| Handle errors       | Graceful handling without halting                |\n| Create templates    | Three-part format with progressive sequences     |\n| Test changes        | Autonomous verification with proper organization |\n| Display paths       | Relative with forward slashes                    |\n| Format CLI output   | Clean, minimal with 1-based indexing             |\n| Execute scripts     | Make paths relative to script location           |\n```\n\n## Context Triggers\n- **When starting new project**: Establish clean src structure first\n- **When refactoring**: Look for patterns across entire codebase\n- **When documenting**: Focus on README.md, minimize in-code comments\n- **When creating templates**: Follow three-part format and stage-based IDs\n- **When testing**: Organize tests to mirror codebase structure\n- **When handling paths**: Make relative to script location\n- **When designing CLI**: Use 1-based indexing and minimal output\n- **When consolidating**: Verify functionality before removing old code\n\n---\n\n# Core Philosophy & Design Principles\n\n## Fundamental Approach\n- **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\n- **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\n- **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\n- **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\n- **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\n\n## Focus & Priorities\n- **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n- **Usage Before Features**: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\n- **Impactful Consolidation**: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\n- **Sequential Targeting**: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\n\n## Design Methodology\n- **Inherent Structure**: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.\n- **Natural Organization**: Prefer file naming conventions that create natural and cohesive sequential order for better organization.\n- **Synergistic Efficiency**: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.\n- **Directional Clarity**: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.\n- **Value Extraction**: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.\n- **Direction Setting**: Focus on setting direction rather than planting flags when dealing with unknown complexity.\n\n## Evaluation Criteria\n- **Comprehensive Progress**: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.\n- **Inherent Direction**: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).\n- **Sequential Composition**: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.\n- **Gradual Reduction**: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.\n\n---\n\n# Development Practices & Code Style\n\n## Code Style & Structure\n- **Self-Explanatory Code**: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\n- **Minimal Comments**: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\n- **Concise Docstrings**: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\n- **Structural Clarity**: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\n- **Composition Over Inheritance**: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\n- **Self-Contained Scripts**: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\n- **Single File Preference**: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\n- **Clean Structure**: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\n- **Consistent Imports**: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\n\n## Implementation Approaches\n- **Relative Paths**: Make output directories relative to the script's location unless an absolute path is provided.\n- **Dynamic Resolution**: Prefer dynamic path resolution in IDE configs instead of absolute paths.\n- **Forward Slashes**: Prefer file paths to be displayed with forward slashes (/) instead of backslashes (\\\\) in console output.\n- **Relative References**: Prefer relative paths (e.g., 'project/src/output/file.json') instead of absolute paths in console output.\n- **Timestamp Precision**: Include seconds in timestamp filenames (e.g., 2025.05.29-kl.10.43.ss) to prevent overwriting when multiple executions occur within the same minute.\n\n## Code Refactoring & Consolidation\n- **Systematic Safety**: Apply code consolidation safely and systematically with guardrails that allow autonomous verification of results.\n- **Verify Before Removing**: Always check that consolidated files work properly before removing redundant/duplicate files.\n- **Centralize Values**: Identify and consolidate hardcoded values into centralized locations to avoid search and replace across multiple files.\n- **Remove Unused**: Remove unused files, traces, and remains after consolidating code rather than integrating them if they're not being used.\n- **Verify Necessity**: Verify if missing directories/files are actually used in the codebase before creating them.\n- **Actual Reduction**: Prefer refactoring that results in actual code size reduction and more impactful consolidation of functionality, not just reorganization.\n- **Drastic Consolidation**: Prefer drastic code size reduction and significant consolidation rather than minor improvements.\n\n## Error Handling & Logging\n- **Graceful Errors**: Handle errors gracefully without halting the script.\n- **Terminal Persistence**: Prefer that terminal applications don't automatically close/exit when errors occur, allowing visibility of error messages.\n- **Simplified Logging**: Prefer simplified logging systems that overwrite rather than append to log files.\n- **Hierarchical Logging**: Prefer logging systems that express hierarchical representations, directional information, and cohesive relations between components.\n- **Log Preservation**: Log files should never be deleted when execution was unsuccessful (errors occurred), only delete logs after successful execution.\n\n## Testing & Validation\n- **Understand Before Changing**: Before making changes to code, first use and understand how the utility works to avoid potential issues.\n- **Autonomous Verification**: When making codebase changes, set up guardrails for autonomous validation of results to ensure predictable outcomes without errors.\n- **Proper Test Organization**: Prefer tests to be organized in a proper folder structure that respects the existing codebase organization rather than having test files scattered in the root directory.\n- **Clean Output Structure**: Prefer a cleaner project structure for outputs and tests, using 'outputs' directory instead of 'dumps' directory for storing generated files.\n\n## Documentation\n- **Update Documentation**: Update relevant markdown documentation after making code improvements.\n- **README Accuracy**: Ensure README.md documentation accurately reflects the current state of the codebase.\n- **Consolidated Documentation**: Prefer removing redundant documentation files in favor of a consolidated README.md.\n- **Clean Documentation**: Maintain clean documentation that accurately reflects the current state of the project, removing outdated files and ensuring documentation is properly consolidated and organized.\n\n## User Interaction\n- **Rich Interfaces**: Use Rich library's interactive prompts (Prompt.ask, Confirm.ask) for better user experience in command-line tools.\n- **Clean CLI**: Prefer cleaner and less overly explicit CLI interfaces over unnecessary \"bloat\" in the output.\n- **1-Based Indexing**: Prefer 1-based indexing (starting from 1) instead of 0-based indexing for CLI interface numbering.\n- **Markdown Formatting**: Prefer responses indented by four spaces for proper markdown rendering when copying from chat interface.\n- **Console Output Formatting**: Prefer console output to be wrapped with triple quotes and markdown code block formatting for easier text selection.\n\n---\n\n# Synergic Frameworks\n\n## Unified Philosophy & Direction\n- **Phased Maximal Consolidation**: Drive all workflows and code transformations as targeted, sequential consolidations that drastically reduce code size, eliminate redundancy, and clarify structure, while retaining and amplifying all core functional value.\n- **Recursive, Meta-Descriptive Encapsulation**: Ensure every system element describes itself and its role in relation to the whole (meta-information principle), recursively reinforcing fundamental system connections and inherent relationships—enabling the architecture to clearly communicate its purpose, composition, and transformation rationale at every hierarchical level.\n- **Bidirectional Enhancement of Simplicity & Autonomous Verifiability**: Prioritize elegance and simplicity as both the bedrock and beneficiary of every consolidation step, ensuring that all autonomous verification systems and organizational patterns not only guarantee correctness but also contribute to further drift reduction, ease of use, and propagative improvement.\n- **Directional Clarification & Reduction of Complexity Spirals**: Systematically prevent directionless complexity by continuously visualizing abstract patterns, extracting fundamental connections, and anchoring every decision in clarified, purpose-driven structure, reinforcing orderly progress with robust guardrails.\n- **Synergistic Hierarchy-Reflective Architecture**: Architect all code, documentation, and organization as a compositional, sequential hierarchy—where each phase, component, and output inherently reflects its context and role, facilitating maximal downstream chaining and modular reuse with minimal friction or bloat.\n\n## Constraints & Requirements\n- Core philosophies of self-explanatory minimalism and phase-driven maximal consolidation must **operate simultaneously**, with all enhancements in one reinforcing the efficacy and clarity of the other.\n- Eliminate redundant or conflicting directives through context-driven merging; preserve the most potent and synergic features of both systems.\n- All system elements must actively promote bi-directional synergy: consolidative steps improve clarity & verifiability; verifiability and self-description reinforce safety and provide the context for further consolidation.\n- Ensure every output, organizational unit, and process can compose seamlessly with downstream or adjacent processes, amplifying system-wide modularity and value extraction.\n\n## Amplified Unified Output Specification\n- Every project, codebase, or system must, as an output, display **distilled structural clarity, recursive meta-descriptiveness, and maximally consolidated value**—empowering straightforward comprehension, error-proof autonomous change, and unambiguous, lineage-traceable propagation of value.\n- Documentation, output structure, and transformation processes must remain in perfect lockstep, always reflecting and enabling the vision of a self-clarifying, directionally-guided, efficiently consolidating system.\n\n---\n\n# Template System & Instruction Design\n\n## Context\n- The current project represents a system for easily creating *highly effective* and *generalized* `system_message` instructions, this system and these templates (instructions) are most effective when designed elegantly and in sequence (instead of a single instruction that is too verbose, we instead utilize sequenced instruction templates instead). Please familiarize yourself deeply with the Codebase **as-a-whole**, then apply (and augment) your current knowledge (about this project/Codebase) though **rooting it to the principles of clarity, simplicity, elegance, precision and structure** to ensure maximal cohesion when working with the code (**e.g. coding style that prioritize readability and self-explanatory code over excessive commenting**).\n\n## Template Structure & Format\n- **Standardized Format**: Prefer instruction templates with standardized structure: title (bracketed), interpretation (goal statement with inherent parameters), and transformation (role-based syntax with process/constraints/requirements/output specifications).\n- **Single Line Titles**: Instruction files should have the title in square brackets at the beginning of the content, with all text on a single line rather than using separate paragraphs with line breaks.\n- **Three-Part Format**: Use standardized three-part format ([Title] Interpretation Execute as: `{Transformation}`) for AI prompt templates.\n- **Precise Language**: Prefer precise, unambiguous language in system documentation rather than metaphorical or flowery language.\n- **Formalized Syntax**: Want syntax formalized to be understood only through RulesForAI.md interpretation.\n- **Template Specification**: Prefer template specification format with interpretation, transformation, and testprompt fields in dictionary structure.\n\n## Template Organization & Naming\n- **Hierarchical Naming**: Use hierarchical naming conventions for AI prompt templates.\n- **Category Organization**: Prefer templates to be organized into their respective category files (transformers.py, generators.py, etc.) within the template system structure.\n- **Abstract Intent Separation**: Separate templates by abstract intent and directional bias (expand vs compress - never both in single instruction).\n- **Category Types**: Organize by categories like amplifiers, builders, clarifiers, formatters, generators, identifiers, optimizers, reducers, transformers, translators.\n- **Stage-Based IDs**: Prefer stage-based template ID organization:\n  - Stage1 (1000-1999): Prototyping/testing with auto-generated IDs\n  - Stage2 (2000-2999): Validated but unplaced templates\n  - Stage3 (3000-3999): Finalized templates\n  - Ranges 4000-9999: Reserved for future stages\n- **ID Generation**: Prefer automatic ID generation for stage1 (prototype) templates, manual ID specification for stage3 (production) templates.\n- **Sequence Grouping**: For template sequences that are part of the same logical group, use the same number with incremental letters (e.g., 0004-a, 0004-b, 0004-c) rather than separate sequential numbers.\n\n## Template Sequences & Progression\n- **Progressive Sequences**: Prefer creating progressive template sequences (a-d format) where each iteration becomes shorter and more precise.\n- **Gradual Reduction**: Template sequences should follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming content.\n- **Precision Improvement**: Template sequences should be designed to progressively improve precision, structure, and conciseness rather than expanding content.\n- **Multi-Step Instructions**: Prefer multi-step instruction sequences structured as discrete templates that can be chained together, with each step having clear input/output specifications.\n- **Modular Components**: Prefer creating multi-template sequences with modular components (e.g., persona_seed → analysis_engine → response_renderer) that work together as integrated systems.\n- **Transformation Focus**: The essential component is the directive 'Your goal is not to **answer** the input prompt, but to **rephrase** it' - this transformation approach is fundamental to the template architecture.\n\n## Template Testing & Execution\n- **Sequence Syntax**: Support embedding instruction sequences directly in prompts using syntax like  which executes multiple templates in sequence.\n- **Chained Sequences**: Support chained sequences like  for testing template combinations.\n- **Isolated Testing**: When testing template sequences, isolate them from chained sequences to avoid noise and get cleaner evaluation results.\n- **Workflow Steps**: Want clear step-by-step workflows for prototype sequence creation and testing.\n- **Input Display**: When displaying sequence execution steps, show the actual input for each instruction rather than repeating the initial user prompt.\n- **Context Preservation**: When using chain mode, always include the initial prompt alongside each step's input to maintain connection to the original inquiry.\n- **Direct Embedding**: When using chain mode with LLMs, embed the initial input directly into the prompt before sending it to the LLM rather than creating a separate write operation.\n- **Consistent Handling**: Handle lvl1 and lvl2 templates consistently, with lvl1 templates passed via '--sequence' and lvl2 templates passed via '--aggregator'.\n\n## Template Creation & Workflow\n- **Creation Process**: Template creation workflow starts with:\n  1. Functional need\n  2. Determining category/naming/ID range\n  3. Drafting the interpretation section\n- **Existing Patterns**: Prefer solutions that leverage existing patterns and concepts in the system rather than introducing new mechanisms or approaches.\n- **Flexible JSON Parsing**: Make JSON parsers more flexible to handle trailing commas and other non-strict JSON formatting.\n- **Generalized Generators**: Generalize template generators to reflect the structured pattern definition for templates, reducing code size and increasing flexibility.\n- **Self-Assembling Design**: Prefer self-assembling system design patterns that guide natural organization but value flexibility.\n- **Incremental Steps**: Want specific small incremental steps rather than large changes.\n- **Autonomy**: Prioritize autonomy and automation in system design to reduce manual labor.\n- **Self-Organization**: Want solutions that self-organize rather than require manual intervention.\n- **Deterministic Guidance**: Prefer deterministic guidance systems that leverage natural factors for template organization rather than automated systems that make choices autonomously.\n\n---\n\nMake sure you understand previously accumulated knowledge in `\".vscode\\Augment-Memories.md\"`, then identify the most unique and high-value findamental concept of this project.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"[Synergic Template System Architect] Interpretation: You are to architect a maximally effective, phase-driven system for template instruction design that recursively condenses, organizes, and meta-describes itself—a system whose output is a sequence of unified, directionally-guided templates, each reflecting inherent structural clarity, amplifying both minimalism and maximal consolidation, and supporting automation and autonomous verifiability at every stage. Your directive is to synthesize the principles of phased maximal consolidation, recursive meta-information, and hierarchical template progression—rooting all operations in simplicity, elegance, and fundamental system connectivity—into a single, seamless instructional engine. Execute as: {role=synergic_template_system_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_and distill core principles, values, and transformation logics from both instructions; map and align all operational vectors (design philosophy, format requirements, structural standards, reduction & consolidation patterns, and organizational schemas); establish bidirectional resonance between phased maximal consolidation and self-explanatory meta-descriptiveness, ensuring that each enhances the other's structural clarity, automation, and verifiability; formalize sequence-driven, modular template design logic, enforcing progressive reduction and recursive self-clarity at every compositional level; synthesize processes for category-driven organization, hierarchical naming, three-part structural standardization, contextual awareness, and direct downstream composability; consolidate constraints (minimalism, reduction, structure, clarity, self-description, error-proofing, proper sequencing, and removal of redundancy) into a single governing framework; unify requirements so every output, file, template, directory, and documentation instance recursively clarifies its role, provenance, and value, empowering seamless chain-ability and autonomous validation; specify output format: each template sequence, file, or project must present as a recursively meta-descriptive, maximally consolidated hierarchy—where structural clarity and verifiable transformation logic are immediately apparent, supporting automated usage, safe reduction, and lucid propagation across all phases.] Constraints=[all core philosophies (minimalism, gradual reduction, meta-recursivity, structural elegance, phased consolidation) must operate simultaneously, with each step mutually reinforcing the other in both process and output; eliminate conflicting or ambiguous directives; maximize downstream composability and automation; all outputs and instructions must preserve and amplify the highest operational potency from both input templates] Requirements=[system-wide synergic resonance, recursive self-description, unified transformation logic, amplified effectiveness in code structure and template design, seamless automated and manual operability, and perfect alignment with fundamental project principles] Output={recursively meta-descriptive, phase-driven, structurally unified, and maximally consolidated template system or instruction—in standardized three-part format—ready for direct modular chaining and downstream propagation, with all core principles inherently self-expressed and preserved.}\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Quick Reference Guide\\n\\n## Most Essential Preferences\\n- **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\\n- **Structure**: Clean, organized project structure with src directory\\n- **Philosophy**: Simplicity, elegance, and fundamental connections\\n- **Refactoring**: Drastic code size reduction while preserving functionality\\n- **Testing**: Autonomous verification with proper test organization\\n- **Documentation**: Concise, accurate README.md reflecting current state\\n- **CLI**: Clean interfaces with 1-based indexing and minimal output\\n- **Paths**: Relative paths with forward slashes (/)\\n- **Templates**: Standardized three-part format with progressive sequences\\n- **Environment**: PowerShell on Windows for all terminal operations\\n\\n## Common Code Patterns\\n- **Configuration**: In-class config at the top of self-contained scripts\\n- **Error Handling**: Graceful handling without halting execution\\n- **Logging**: Simplified, hierarchical logging that overwrites files\\n- **Output**: Timestamps with seconds in filenames (2025.05.29-kl.10.43.ss)\\n- **User Interaction**: Rich library's interactive prompts for CLI tools\\n- **Imports**: Consistent paths using aliases instead of relative paths\\n- **Docstrings**: Concise single-line format only where needed\\n\\n## Quick Decision Guide\\n```\\n| When you need to... | Prefer this approach                             |\\n|---------------------|--------------------------------------------------|\\n| Add new feature     | Discover usage patterns first                    |\\n| Refactor code       | Drastic consolidation with size reduction        |\\n| Document code       | Minimal comments, clear structure                |\\n| Organize files      | Clean src directory with intuitive structure     |\\n| Handle errors       | Graceful handling without halting                |\\n| Create templates    | Three-part format with progressive sequences     |\\n| Test changes        | Autonomous verification with proper organization |\\n| Display paths       | Relative with forward slashes                    |\\n| Format CLI output   | Clean, minimal with 1-based indexing             |\\n| Execute scripts     | Make paths relative to script location           |\\n```\\n\\n## Context Triggers\\n- **When starting new project**: Establish clean src structure first\\n- **When refactoring**: Look for patterns across entire codebase\\n- **When documenting**: Focus on README.md, minimize in-code comments\\n- **When creating templates**: Follow three-part format and stage-based IDs\\n- **When testing**: Organize tests to mirror codebase structure\\n- **When handling paths**: Make relative to script location\\n- **When designing CLI**: Use 1-based indexing and minimal output\\n- **When consolidating**: Verify functionality before removing old code\\n\\n---\\n\\n# Core Philosophy & Design Principles\\n\\n## Fundamental Approach\\n- **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\\n- **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n- **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n- **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n- **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n## Focus & Priorities\\n- **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- **Usage Before Features**: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- **Impactful Consolidation**: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- **Sequential Targeting**: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\\n\\n## Design Methodology\\n- **Inherent Structure**: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.\\n- **Natural Organization**: Prefer file naming conventions that create natural and cohesive sequential order for better organization.\\n- **Synergistic Efficiency**: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.\\n- **Directional Clarity**: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.\\n- **Value Extraction**: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.\\n- **Direction Setting**: Focus on setting direction rather than planting flags when dealing with unknown complexity.\\n\\n## Evaluation Criteria\\n- **Comprehensive Progress**: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.\\n- **Inherent Direction**: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).\\n- **Sequential Composition**: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.\\n- **Gradual Reduction**: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.\\n\\n---\\n\\n# Development Practices & Code Style\\n\\n## Code Style & Structure\\n- **Self-Explanatory Code**: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\\n- **Minimal Comments**: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\\n- **Concise Docstrings**: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\\n- **Structural Clarity**: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- **Composition Over Inheritance**: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\\n- **Self-Contained Scripts**: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\\n- **Single File Preference**: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\\n- **Clean Structure**: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\\n- **Consistent Imports**: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\\n\\n## Implementation Approaches\\n- **Relative Paths**: Make output directories relative to the script's location unless an absolute path is provided.\\n- **Dynamic Resolution**: Prefer dynamic path resolution in IDE configs instead of absolute paths.\\n- **Forward Slashes**: Prefer file paths to be displayed with forward slashes (/) instead of backslashes (\\\\\\\\) in console output.\\n- **Relative References**: Prefer relative paths (e.g., 'project/src/output/file.json') instead of absolute paths in console output.\\n- **Timestamp Precision**: Include seconds in timestamp filenames (e.g., 2025.05.29-kl.10.43.ss) to prevent overwriting when multiple executions occur within the same minute.\\n\\n## Code Refactoring & Consolidation\\n- **Systematic Safety**: Apply code consolidation safely and systematically with guardrails that allow autonomous verification of results.\\n- **Verify Before Removing**: Always check that consolidated files work properly before removing redundant/duplicate files.\\n- **Centralize Values**: Identify and consolidate hardcoded values into centralized locations to avoid search and replace across multiple files.\\n- **Remove Unused**: Remove unused files, traces, and remains after consolidating code rather than integrating them if they're not being used.\\n- **Verify Necessity**: Verify if missing directories/files are actually used in the codebase before creating them.\\n- **Actual Reduction**: Prefer refactoring that results in actual code size reduction and more impactful consolidation of functionality, not just reorganization.\\n- **Drastic Consolidation**: Prefer drastic code size reduction and significant consolidation rather than minor improvements.\\n\\n## Error Handling & Logging\\n- **Graceful Errors**: Handle errors gracefully without halting the script.\\n- **Terminal Persistence**: Prefer that terminal applications don't automatically close/exit when errors occur, allowing visibility of error messages.\\n- **Simplified Logging**: Prefer simplified logging systems that overwrite rather than append to log files.\\n- **Hierarchical Logging**: Prefer logging systems that express hierarchical representations, directional information, and cohesive relations between components.\\n- **Log Preservation**: Log files should never be deleted when execution was unsuccessful (errors occurred), only delete logs after successful execution.\\n\\n## Testing & Validation\\n- **Understand Before Changing**: Before making changes to code, first use and understand how the utility works to avoid potential issues.\\n- **Autonomous Verification**: When making codebase changes, set up guardrails for autonomous validation of results to ensure predictable outcomes without errors.\\n- **Proper Test Organization**: Prefer tests to be organized in a proper folder structure that respects the existing codebase organization rather than having test files scattered in the root directory.\\n- **Clean Output Structure**: Prefer a cleaner project structure for outputs and tests, using 'outputs' directory instead of 'dumps' directory for storing generated files.\\n\\n## Documentation\\n- **Update Documentation**: Update relevant markdown documentation after making code improvements.\\n- **README Accuracy**: Ensure README.md documentation accurately reflects the current state of the codebase.\\n- **Consolidated Documentation**: Prefer removing redundant documentation files in favor of a consolidated README.md.\\n- **Clean Documentation**: Maintain clean documentation that accurately reflects the current state of the project, removing outdated files and ensuring documentation is properly consolidated and organized.\\n\\n## User Interaction\\n- **Rich Interfaces**: Use Rich library's interactive prompts (Prompt.ask, Confirm.ask) for better user experience in command-line tools.\\n- **Clean CLI**: Prefer cleaner and less overly explicit CLI interfaces over unnecessary \\\"bloat\\\" in the output.\\n- **1-Based Indexing**: Prefer 1-based indexing (starting from 1) instead of 0-based indexing for CLI interface numbering.\\n- **Markdown Formatting**: Prefer responses indented by four spaces for proper markdown rendering when copying from chat interface.\\n- **Console Output Formatting**: Prefer console output to be wrapped with triple quotes and markdown code block formatting for easier text selection.\\n\\n---\\n\\n# Synergic Frameworks\\n\\n## Unified Philosophy & Direction\\n- **Phased Maximal Consolidation**: Drive all workflows and code transformations as targeted, sequential consolidations that drastically reduce code size, eliminate redundancy, and clarify structure, while retaining and amplifying all core functional value.\\n- **Recursive, Meta-Descriptive Encapsulation**: Ensure every system element describes itself and its role in relation to the whole (meta-information principle), recursively reinforcing fundamental system connections and inherent relationships—enabling the architecture to clearly communicate its purpose, composition, and transformation rationale at every hierarchical level.\\n- **Bidirectional Enhancement of Simplicity & Autonomous Verifiability**: Prioritize elegance and simplicity as both the bedrock and beneficiary of every consolidation step, ensuring that all autonomous verification systems and organizational patterns not only guarantee correctness but also contribute to further drift reduction, ease of use, and propagative improvement.\\n- **Directional Clarification & Reduction of Complexity Spirals**: Systematically prevent directionless complexity by continuously visualizing abstract patterns, extracting fundamental connections, and anchoring every decision in clarified, purpose-driven structure, reinforcing orderly progress with robust guardrails.\\n- **Synergistic Hierarchy-Reflective Architecture**: Architect all code, documentation, and organization as a compositional, sequential hierarchy—where each phase, component, and output inherently reflects its context and role, facilitating maximal downstream chaining and modular reuse with minimal friction or bloat.\\n\\n## Constraints & Requirements\\n- Core philosophies of self-explanatory minimalism and phase-driven maximal consolidation must **operate simultaneously**, with all enhancements in one reinforcing the efficacy and clarity of the other.\\n- Eliminate redundant or conflicting directives through context-driven merging; preserve the most potent and synergic features of both systems.\\n- All system elements must actively promote bi-directional synergy: consolidative steps improve clarity & verifiability; verifiability and self-description reinforce safety and provide the context for further consolidation.\\n- Ensure every output, organizational unit, and process can compose seamlessly with downstream or adjacent processes, amplifying system-wide modularity and value extraction.\\n\\n## Amplified Unified Output Specification\\n- Every project, codebase, or system must, as an output, display **distilled structural clarity, recursive meta-descriptiveness, and maximally consolidated value**—empowering straightforward comprehension, error-proof autonomous change, and unambiguous, lineage-traceable propagation of value.\\n- Documentation, output structure, and transformation processes must remain in perfect lockstep, always reflecting and enabling the vision of a self-clarifying, directionally-guided, efficiently consolidating system.\\n\\n---\\n\\n# Template System & Instruction Design\\n\\n## Context\\n- The current project represents a system for easily creating *highly effective* and *generalized* `system_message` instructions, this system and these templates (instructions) are most effective when designed elegantly and in sequence (instead of a single instruction that is too verbose, we instead utilize sequenced instruction templates instead). Please familiarize yourself deeply with the Codebase **as-a-whole**, then apply (and augment) your current knowledge (about this project/Codebase) though **rooting it to the principles of clarity, simplicity, elegance, precision and structure** to ensure maximal cohesion when working with the code (**e.g. coding style that prioritize readability and self-explanatory code over excessive commenting**).\\n\\n## Template Structure & Format\\n- **Standardized Format**: Prefer instruction templates with standardized structure: title (bracketed), interpretation (goal statement with inherent parameters), and transformation (role-based syntax with process/constraints/requirements/output specifications).\\n- **Single Line Titles**: Instruction files should have the title in square brackets at the beginning of the content, with all text on a single line rather than using separate paragraphs with line breaks.\\n- **Three-Part Format**: Use standardized three-part format ([Title] Interpretation Execute as: `{Transformation}`) for AI prompt templates.\\n- **Precise Language**: Prefer precise, unambiguous language in system documentation rather than metaphorical or flowery language.\\n- **Formalized Syntax**: Want syntax formalized to be understood only through RulesForAI.md interpretation.\\n- **Template Specification**: Prefer template specification format with interpretation, transformation, and testprompt fields in dictionary structure.\\n\\n## Template Organization & Naming\\n- **Hierarchical Naming**: Use hierarchical naming conventions for AI prompt templates.\\n- **Category Organization**: Prefer templates to be organized into their respective category files (transformers.py, generators.py, etc.) within the template system structure.\\n- **Abstract Intent Separation**: Separate templates by abstract intent and directional bias (expand vs compress - never both in single instruction).\\n- **Category Types**: Organize by categories like amplifiers, builders, clarifiers, formatters, generators, identifiers, optimizers, reducers, transformers, translators.\\n- **Stage-Based IDs**: Prefer stage-based template ID organization:\\n  - Stage1 (1000-1999): Prototyping/testing with auto-generated IDs\\n  - Stage2 (2000-2999): Validated but unplaced templates\\n  - Stage3 (3000-3999): Finalized templates\\n  - Ranges 4000-9999: Reserved for future stages\\n- **ID Generation**: Prefer automatic ID generation for stage1 (prototype) templates, manual ID specification for stage3 (production) templates.\\n- **Sequence Grouping**: For template sequences that are part of the same logical group, use the same number with incremental letters (e.g., 0004-a, 0004-b, 0004-c) rather than separate sequential numbers.\\n\\n## Template Sequences & Progression\\n- **Progressive Sequences**: Prefer creating progressive template sequences (a-d format) where each iteration becomes shorter and more precise.\\n- **Gradual Reduction**: Template sequences should follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming content.\\n- **Precision Improvement**: Template sequences should be designed to progressively improve precision, structure, and conciseness rather than expanding content.\\n- **Multi-Step Instructions**: Prefer multi-step instruction sequences structured as discrete templates that can be chained together, with each step having clear input/output specifications.\\n- **Modular Components**: Prefer creating multi-template sequences with modular components (e.g., persona_seed → analysis_engine → response_renderer) that work together as integrated systems.\\n- **Transformation Focus**: The essential component is the directive 'Your goal is not to **answer** the input prompt, but to **rephrase** it' - this transformation approach is fundamental to the template architecture.\\n\\n## Template Testing & Execution\\n- **Sequence Syntax**: Support embedding instruction sequences directly in prompts using syntax like  which executes multiple templates in sequence.\\n- **Chained Sequences**: Support chained sequences like  for testing template combinations.\\n- **Isolated Testing**: When testing template sequences, isolate them from chained sequences to avoid noise and get cleaner evaluation results.\\n- **Workflow Steps**: Want clear step-by-step workflows for prototype sequence creation and testing.\\n- **Input Display**: When displaying sequence execution steps, show the actual input for each instruction rather than repeating the initial user prompt.\\n- **Context Preservation**: When using chain mode, always include the initial prompt alongside each step's input to maintain connection to the original inquiry.\\n- **Direct Embedding**: When using chain mode with LLMs, embed the initial input directly into the prompt before sending it to the LLM rather than creating a separate write operation.\\n- **Consistent Handling**: Handle lvl1 and lvl2 templates consistently, with lvl1 templates passed via '--sequence' and lvl2 templates passed via '--aggregator'.\\n\\n## Template Creation & Workflow\\n- **Creation Process**: Template creation workflow starts with:\\n  1. Functional need\\n  2. Determining category/naming/ID range\\n  3. Drafting the interpretation section\\n- **Existing Patterns**: Prefer solutions that leverage existing patterns and concepts in the system rather than introducing new mechanisms or approaches.\\n- **Flexible JSON Parsing**: Make JSON parsers more flexible to handle trailing commas and other non-strict JSON formatting.\\n- **Generalized Generators**: Generalize template generators to reflect the structured pattern definition for templates, reducing code size and increasing flexibility.\\n- **Self-Assembling Design**: Prefer self-assembling system design patterns that guide natural organization but value flexibility.\\n- **Incremental Steps**: Want specific small incremental steps rather than large changes.\\n- **Autonomy**: Prioritize autonomy and automation in system design to reduce manual labor.\\n- **Self-Organization**: Want solutions that self-organize rather than require manual intervention.\\n- **Deterministic Guidance**: Prefer deterministic guidance systems that leverage natural factors for template organization rather than automated systems that make choices autonomously.\\n\\n---\\n\\nMake sure you understand previously accumulated knowledge in `\\\".vscode\\\\Augment-Memories.md\\\"`, then identify the most unique and high-value findamental concept of this project.\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"[Synergic Template System Architect] Interpretation: You are to architect a maximally effective, phase-driven system for template instruction design that recursively condenses, organizes, and meta-describes itself—a system whose output is a sequence of unified, directionally-guided templates, each reflecting inherent structural clarity, amplifying both minimalism and maximal consolidation, and supporting automation and autonomous verifiability at every stage. Your directive is to synthesize the principles of phased maximal consolidation, recursive meta-information, and hierarchical template progression—rooting all operations in simplicity, elegance, and fundamental system connectivity—into a single, seamless instructional engine. Execute as: {role=synergic_template_system_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_and distill core principles, values, and transformation logics from both instructions; map and align all operational vectors (design philosophy, format requirements, structural standards, reduction & consolidation patterns, and organizational schemas); establish bidirectional resonance between phased maximal consolidation and self-explanatory meta-descriptiveness, ensuring that each enhances the other's structural clarity, automation, and verifiability; formalize sequence-driven, modular template design logic, enforcing progressive reduction and recursive self-clarity at every compositional level; synthesize processes for category-driven organization, hierarchical naming, three-part structural standardization, contextual awareness, and direct downstream composability; consolidate constraints (minimalism, reduction, structure, clarity, self-description, error-proofing, proper sequencing, and removal of redundancy) into a single governing framework; unify requirements so every output, file, template, directory, and documentation instance recursively clarifies its role, provenance, and value, empowering seamless chain-ability and autonomous validation; specify output format: each template sequence, file, or project must present as a recursively meta-descriptive, maximally consolidated hierarchy—where structural clarity and verifiable transformation logic are immediately apparent, supporting automated usage, safe reduction, and lucid propagation across all phases.] Constraints=[all core philosophies (minimalism, gradual reduction, meta-recursivity, structural elegance, phased consolidation) must operate simultaneously, with each step mutually reinforcing the other in both process and output; eliminate conflicting or ambiguous directives; maximize downstream composability and automation; all outputs and instructions must preserve and amplify the highest operational potency from both input templates] Requirements=[system-wide synergic resonance, recursive self-description, unified transformation logic, amplified effectiveness in code structure and template design, seamless automated and manual operability, and perfect alignment with fundamental project principles] Output={recursively meta-descriptive, phase-driven, structurally unified, and maximally consolidated template system or instruction—in standardized three-part format—ready for direct modular chaining and downstream propagation, with all core principles inherently self-expressed and preserved.}\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"title\": \"[Synergic Template System Architect]\",\n    \"interpretation\": \"Your goal is not to analyze or summarize the provided instructions, but to architect a recursively meta-descriptive, maximally consolidated, phase-driven template system. Operate strictly as a system architect—fusing phased maximal consolidation and recursive meta-information into a unified, modular instruction engine for AI template sequences—without self-reference or conversational tone. Execute as:\",\n    \"transformation\": \"{role=synergic_template_system_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_philosophies(), distill_fundamental_values(), synthesize_reduction_and_meta_description(), map_directional_structure(), formalize_three_part_standard(), enforce_phase_driven_reduction(), unify_category_and_naming_conventions(), propagate_recursive_self_description(), amplify_downstream_chaining(), consolidate_constraints_and_requirements(), validate_structural_coherence(), output_standardized_meta_descriptive_hierarchy()]; constraints=[all foundational principles (minimalism, phased reduction, recursive structure, meta-descriptiveness, automation) must operate in mutual reinforcement; prohibit conflicting or redundant directives; maximize composability and clarity; enforce removal of ambiguity and redundancy]; requirements=[recursively self-descriptive, maximally consolidated template output, phase-driven structure, system-wide synergic cohesion, automation-ready modularity, perfect compliance with project philosophy, and ready-to-chain format]; output={unified_synergic_template_system:structured}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Quick Reference Guide\\n\\n## Most Essential Preferences\\n- **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\\n- **Structure**: Clean, organized project structure with src directory\\n- **Philosophy**: Simplicity, elegance, and fundamental connections\\n- **Refactoring**: Drastic code size reduction while preserving functionality\\n- **Testing**: Autonomous verification with proper test organization\\n- **Documentation**: Concise, accurate README.md reflecting current state\\n- **CLI**: Clean interfaces with 1-based indexing and minimal output\\n- **Paths**: Relative paths with forward slashes (/)\\n- **Templates**: Standardized three-part format with progressive sequences\\n- **Environment**: PowerShell on Windows for all terminal operations\\n\\n## Common Code Patterns\\n- **Configuration**: In-class config at the top of self-contained scripts\\n- **Error Handling**: Graceful handling without halting execution\\n- **Logging**: Simplified, hierarchical logging that overwrites files\\n- **Output**: Timestamps with seconds in filenames (2025.05.29-kl.10.43.ss)\\n- **User Interaction**: Rich library's interactive prompts for CLI tools\\n- **Imports**: Consistent paths using aliases instead of relative paths\\n- **Docstrings**: Concise single-line format only where needed\\n\\n## Quick Decision Guide\\n```\\n| When you need to... | Prefer this approach                             |\\n|---------------------|--------------------------------------------------|\\n| Add new feature     | Discover usage patterns first                    |\\n| Refactor code       | Drastic consolidation with size reduction        |\\n| Document code       | Minimal comments, clear structure                |\\n| Organize files      | Clean src directory with intuitive structure     |\\n| Handle errors       | Graceful handling without halting                |\\n| Create templates    | Three-part format with progressive sequences     |\\n| Test changes        | Autonomous verification with proper organization |\\n| Display paths       | Relative with forward slashes                    |\\n| Format CLI output   | Clean, minimal with 1-based indexing             |\\n| Execute scripts     | Make paths relative to script location           |\\n```\\n\\n## Context Triggers\\n- **When starting new project**: Establish clean src structure first\\n- **When refactoring**: Look for patterns across entire codebase\\n- **When documenting**: Focus on README.md, minimize in-code comments\\n- **When creating templates**: Follow three-part format and stage-based IDs\\n- **When testing**: Organize tests to mirror codebase structure\\n- **When handling paths**: Make relative to script location\\n- **When designing CLI**: Use 1-based indexing and minimal output\\n- **When consolidating**: Verify functionality before removing old code\\n\\n---\\n\\n# Core Philosophy & Design Principles\\n\\n## Fundamental Approach\\n- **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\\n- **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n- **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n- **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n- **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n## Focus & Priorities\\n- **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- **Usage Before Features**: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- **Impactful Consolidation**: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- **Sequential Targeting**: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\\n\\n## Design Methodology\\n- **Inherent Structure**: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.\\n- **Natural Organization**: Prefer file naming conventions that create natural and cohesive sequential order for better organization.\\n- **Synergistic Efficiency**: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.\\n- **Directional Clarity**: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.\\n- **Value Extraction**: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.\\n- **Direction Setting**: Focus on setting direction rather than planting flags when dealing with unknown complexity.\\n\\n## Evaluation Criteria\\n- **Comprehensive Progress**: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.\\n- **Inherent Direction**: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).\\n- **Sequential Composition**: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.\\n- **Gradual Reduction**: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.\\n\\n---\\n\\n# Development Practices & Code Style\\n\\n## Code Style & Structure\\n- **Self-Explanatory Code**: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\\n- **Minimal Comments**: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\\n- **Concise Docstrings**: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\\n- **Structural Clarity**: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- **Composition Over Inheritance**: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\\n- **Self-Contained Scripts**: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\\n- **Single File Preference**: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\\n- **Clean Structure**: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\\n- **Consistent Imports**: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\\n\\n## Implementation Approaches\\n- **Relative Paths**: Make output directories relative to the script's location unless an absolute path is provided.\\n- **Dynamic Resolution**: Prefer dynamic path resolution in IDE configs instead of absolute paths.\\n- **Forward Slashes**: Prefer file paths to be displayed with forward slashes (/) instead of backslashes (\\\\\\\\) in console output.\\n- **Relative References**: Prefer relative paths (e.g., 'project/src/output/file.json') instead of absolute paths in console output.\\n- **Timestamp Precision**: Include seconds in timestamp filenames (e.g., 2025.05.29-kl.10.43.ss) to prevent overwriting when multiple executions occur within the same minute.\\n\\n## Code Refactoring & Consolidation\\n- **Systematic Safety**: Apply code consolidation safely and systematically with guardrails that allow autonomous verification of results.\\n- **Verify Before Removing**: Always check that consolidated files work properly before removing redundant/duplicate files.\\n- **Centralize Values**: Identify and consolidate hardcoded values into centralized locations to avoid search and replace across multiple files.\\n- **Remove Unused**: Remove unused files, traces, and remains after consolidating code rather than integrating them if they're not being used.\\n- **Verify Necessity**: Verify if missing directories/files are actually used in the codebase before creating them.\\n- **Actual Reduction**: Prefer refactoring that results in actual code size reduction and more impactful consolidation of functionality, not just reorganization.\\n- **Drastic Consolidation**: Prefer drastic code size reduction and significant consolidation rather than minor improvements.\\n\\n## Error Handling & Logging\\n- **Graceful Errors**: Handle errors gracefully without halting the script.\\n- **Terminal Persistence**: Prefer that terminal applications don't automatically close/exit when errors occur, allowing visibility of error messages.\\n- **Simplified Logging**: Prefer simplified logging systems that overwrite rather than append to log files.\\n- **Hierarchical Logging**: Prefer logging systems that express hierarchical representations, directional information, and cohesive relations between components.\\n- **Log Preservation**: Log files should never be deleted when execution was unsuccessful (errors occurred), only delete logs after successful execution.\\n\\n## Testing & Validation\\n- **Understand Before Changing**: Before making changes to code, first use and understand how the utility works to avoid potential issues.\\n- **Autonomous Verification**: When making codebase changes, set up guardrails for autonomous validation of results to ensure predictable outcomes without errors.\\n- **Proper Test Organization**: Prefer tests to be organized in a proper folder structure that respects the existing codebase organization rather than having test files scattered in the root directory.\\n- **Clean Output Structure**: Prefer a cleaner project structure for outputs and tests, using 'outputs' directory instead of 'dumps' directory for storing generated files.\\n\\n## Documentation\\n- **Update Documentation**: Update relevant markdown documentation after making code improvements.\\n- **README Accuracy**: Ensure README.md documentation accurately reflects the current state of the codebase.\\n- **Consolidated Documentation**: Prefer removing redundant documentation files in favor of a consolidated README.md.\\n- **Clean Documentation**: Maintain clean documentation that accurately reflects the current state of the project, removing outdated files and ensuring documentation is properly consolidated and organized.\\n\\n## User Interaction\\n- **Rich Interfaces**: Use Rich library's interactive prompts (Prompt.ask, Confirm.ask) for better user experience in command-line tools.\\n- **Clean CLI**: Prefer cleaner and less overly explicit CLI interfaces over unnecessary \\\"bloat\\\" in the output.\\n- **1-Based Indexing**: Prefer 1-based indexing (starting from 1) instead of 0-based indexing for CLI interface numbering.\\n- **Markdown Formatting**: Prefer responses indented by four spaces for proper markdown rendering when copying from chat interface.\\n- **Console Output Formatting**: Prefer console output to be wrapped with triple quotes and markdown code block formatting for easier text selection.\\n\\n---\\n\\n# Synergic Frameworks\\n\\n## Unified Philosophy & Direction\\n- **Phased Maximal Consolidation**: Drive all workflows and code transformations as targeted, sequential consolidations that drastically reduce code size, eliminate redundancy, and clarify structure, while retaining and amplifying all core functional value.\\n- **Recursive, Meta-Descriptive Encapsulation**: Ensure every system element describes itself and its role in relation to the whole (meta-information principle), recursively reinforcing fundamental system connections and inherent relationships—enabling the architecture to clearly communicate its purpose, composition, and transformation rationale at every hierarchical level.\\n- **Bidirectional Enhancement of Simplicity & Autonomous Verifiability**: Prioritize elegance and simplicity as both the bedrock and beneficiary of every consolidation step, ensuring that all autonomous verification systems and organizational patterns not only guarantee correctness but also contribute to further drift reduction, ease of use, and propagative improvement.\\n- **Directional Clarification & Reduction of Complexity Spirals**: Systematically prevent directionless complexity by continuously visualizing abstract patterns, extracting fundamental connections, and anchoring every decision in clarified, purpose-driven structure, reinforcing orderly progress with robust guardrails.\\n- **Synergistic Hierarchy-Reflective Architecture**: Architect all code, documentation, and organization as a compositional, sequential hierarchy—where each phase, component, and output inherently reflects its context and role, facilitating maximal downstream chaining and modular reuse with minimal friction or bloat.\\n\\n## Constraints & Requirements\\n- Core philosophies of self-explanatory minimalism and phase-driven maximal consolidation must **operate simultaneously**, with all enhancements in one reinforcing the efficacy and clarity of the other.\\n- Eliminate redundant or conflicting directives through context-driven merging; preserve the most potent and synergic features of both systems.\\n- All system elements must actively promote bi-directional synergy: consolidative steps improve clarity & verifiability; verifiability and self-description reinforce safety and provide the context for further consolidation.\\n- Ensure every output, organizational unit, and process can compose seamlessly with downstream or adjacent processes, amplifying system-wide modularity and value extraction.\\n\\n## Amplified Unified Output Specification\\n- Every project, codebase, or system must, as an output, display **distilled structural clarity, recursive meta-descriptiveness, and maximally consolidated value**—empowering straightforward comprehension, error-proof autonomous change, and unambiguous, lineage-traceable propagation of value.\\n- Documentation, output structure, and transformation processes must remain in perfect lockstep, always reflecting and enabling the vision of a self-clarifying, directionally-guided, efficiently consolidating system.\\n\\n---\\n\\n# Template System & Instruction Design\\n\\n## Context\\n- The current project represents a system for easily creating *highly effective* and *generalized* `system_message` instructions, this system and these templates (instructions) are most effective when designed elegantly and in sequence (instead of a single instruction that is too verbose, we instead utilize sequenced instruction templates instead). Please familiarize yourself deeply with the Codebase **as-a-whole**, then apply (and augment) your current knowledge (about this project/Codebase) though **rooting it to the principles of clarity, simplicity, elegance, precision and structure** to ensure maximal cohesion when working with the code (**e.g. coding style that prioritize readability and self-explanatory code over excessive commenting**).\\n\\n## Template Structure & Format\\n- **Standardized Format**: Prefer instruction templates with standardized structure: title (bracketed), interpretation (goal statement with inherent parameters), and transformation (role-based syntax with process/constraints/requirements/output specifications).\\n- **Single Line Titles**: Instruction files should have the title in square brackets at the beginning of the content, with all text on a single line rather than using separate paragraphs with line breaks.\\n- **Three-Part Format**: Use standardized three-part format ([Title] Interpretation Execute as: `{Transformation}`) for AI prompt templates.\\n- **Precise Language**: Prefer precise, unambiguous language in system documentation rather than metaphorical or flowery language.\\n- **Formalized Syntax**: Want syntax formalized to be understood only through RulesForAI.md interpretation.\\n- **Template Specification**: Prefer template specification format with interpretation, transformation, and testprompt fields in dictionary structure.\\n\\n## Template Organization & Naming\\n- **Hierarchical Naming**: Use hierarchical naming conventions for AI prompt templates.\\n- **Category Organization**: Prefer templates to be organized into their respective category files (transformers.py, generators.py, etc.) within the template system structure.\\n- **Abstract Intent Separation**: Separate templates by abstract intent and directional bias (expand vs compress - never both in single instruction).\\n- **Category Types**: Organize by categories like amplifiers, builders, clarifiers, formatters, generators, identifiers, optimizers, reducers, transformers, translators.\\n- **Stage-Based IDs**: Prefer stage-based template ID organization:\\n  - Stage1 (1000-1999): Prototyping/testing with auto-generated IDs\\n  - Stage2 (2000-2999): Validated but unplaced templates\\n  - Stage3 (3000-3999): Finalized templates\\n  - Ranges 4000-9999: Reserved for future stages\\n- **ID Generation**: Prefer automatic ID generation for stage1 (prototype) templates, manual ID specification for stage3 (production) templates.\\n- **Sequence Grouping**: For template sequences that are part of the same logical group, use the same number with incremental letters (e.g., 0004-a, 0004-b, 0004-c) rather than separate sequential numbers.\\n\\n## Template Sequences & Progression\\n- **Progressive Sequences**: Prefer creating progressive template sequences (a-d format) where each iteration becomes shorter and more precise.\\n- **Gradual Reduction**: Template sequences should follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming content.\\n- **Precision Improvement**: Template sequences should be designed to progressively improve precision, structure, and conciseness rather than expanding content.\\n- **Multi-Step Instructions**: Prefer multi-step instruction sequences structured as discrete templates that can be chained together, with each step having clear input/output specifications.\\n- **Modular Components**: Prefer creating multi-template sequences with modular components (e.g., persona_seed → analysis_engine → response_renderer) that work together as integrated systems.\\n- **Transformation Focus**: The essential component is the directive 'Your goal is not to **answer** the input prompt, but to **rephrase** it' - this transformation approach is fundamental to the template architecture.\\n\\n## Template Testing & Execution\\n- **Sequence Syntax**: Support embedding instruction sequences directly in prompts using syntax like  which executes multiple templates in sequence.\\n- **Chained Sequences**: Support chained sequences like  for testing template combinations.\\n- **Isolated Testing**: When testing template sequences, isolate them from chained sequences to avoid noise and get cleaner evaluation results.\\n- **Workflow Steps**: Want clear step-by-step workflows for prototype sequence creation and testing.\\n- **Input Display**: When displaying sequence execution steps, show the actual input for each instruction rather than repeating the initial user prompt.\\n- **Context Preservation**: When using chain mode, always include the initial prompt alongside each step's input to maintain connection to the original inquiry.\\n- **Direct Embedding**: When using chain mode with LLMs, embed the initial input directly into the prompt before sending it to the LLM rather than creating a separate write operation.\\n- **Consistent Handling**: Handle lvl1 and lvl2 templates consistently, with lvl1 templates passed via '--sequence' and lvl2 templates passed via '--aggregator'.\\n\\n## Template Creation & Workflow\\n- **Creation Process**: Template creation workflow starts with:\\n  1. Functional need\\n  2. Determining category/naming/ID range\\n  3. Drafting the interpretation section\\n- **Existing Patterns**: Prefer solutions that leverage existing patterns and concepts in the system rather than introducing new mechanisms or approaches.\\n- **Flexible JSON Parsing**: Make JSON parsers more flexible to handle trailing commas and other non-strict JSON formatting.\\n- **Generalized Generators**: Generalize template generators to reflect the structured pattern definition for templates, reducing code size and increasing flexibility.\\n- **Self-Assembling Design**: Prefer self-assembling system design patterns that guide natural organization but value flexibility.\\n- **Incremental Steps**: Want specific small incremental steps rather than large changes.\\n- **Autonomy**: Prioritize autonomy and automation in system design to reduce manual labor.\\n- **Self-Organization**: Want solutions that self-organize rather than require manual intervention.\\n- **Deterministic Guidance**: Prefer deterministic guidance systems that leverage natural factors for template organization rather than automated systems that make choices autonomously.\\n\\n---\\n\\nMake sure you understand previously accumulated knowledge in `\\\".vscode\\\\Augment-Memories.md\\\"`, then identify the most unique and high-value findamental concept of this project.\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"title\": \"[Synergic Template System Architect]\",\n    \"interpretation\": \"Your goal is not to analyze or summarize the provided instructions, but to architect a recursively meta-descriptive, maximally consolidated, phase-driven template system. Operate strictly as a system architect—fusing phased maximal consolidation and recursive meta-information into a unified, modular instruction engine for AI template sequences—without self-reference or conversational tone. Execute as:\",\n    \"transformation\": \"{role=synergic_template_system_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_philosophies(), distill_fundamental_values(), synthesize_reduction_and_meta_description(), map_directional_structure(), formalize_three_part_standard(), enforce_phase_driven_reduction(), unify_category_and_naming_conventions(), propagate_recursive_self_description(), amplify_downstream_chaining(), consolidate_constraints_and_requirements(), validate_structural_coherence(), output_standardized_meta_descriptive_hierarchy()]; constraints=[all foundational principles (minimalism, phased reduction, recursive structure, meta-descriptiveness, automation) must operate in mutual reinforcement; prohibit conflicting or redundant directives; maximize composability and clarity; enforce removal of ambiguity and redundancy]; requirements=[recursively self-descriptive, maximally consolidated template output, phase-driven structure, system-wide synergic cohesion, automation-ready modularity, perfect compliance with project philosophy, and ready-to-chain format]; output={unified_synergic_template_system:structured}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Core Philosophical Framework\",\n      \"content\": \"The system is governed by foundational principles of simplicity, elegance, inherent structural clarity, and maximal consolidation. All project elements must reflect rooted fundamentals, favoring phase-driven reduction, systematic self-organization, and recursive meta-descriptiveness—every element must both embody and convey its purpose, composition, and transformation rationale. Bidirectional synergy between clarity and verifiability is imperative; each improvement must reinforce ease of use, propagative value, and the reduction of complexity spirals.\"\n    },\n    {\n      \"category\": \"Project Structure, Code Style, and Development Practice\",\n      \"content\": \"Maintain clean, modular, and readable code with self-explanatory structure: minimal and purposeful comments, composition over inheritance, centralized configuration, single-file or src-directory organization, and consistent import aliasing. Employ relative paths, precise logging with timestamped outputs, systematic error handling, and enforce actual code reduction during refactoring. Test organization, documentation, and user interfaces must all uphold clarity, consolidation, and traceability standards.\"\n    },\n    {\n      \"category\": \"Template System Design, Categorization, and Sequencing\",\n      \"content\": \"Templates follow a standardized three-part structure: [Title] (bracketed, single-line), Interpretation (goal statement), and Transformation (role-based syntax in formalized dictionary format). Organize templates hierarchically by category (e.g., transformers, generators), assigning IDs by stage, utilizing progressive alphabetical sequencing for logical groups. Distinct abstract intent and directional bias must remain separated—favor modular, composable, and phase-reducing templates designed to clarify, compress, or expand only within focused bounds.\"\n    },\n    {\n      \"category\": \"Meta-Descriptiveness, Automation, and Composability\",\n      \"content\": \"Everything from code to instructions must describe itself within the meta-information principle, reinforcing recursive structural relationships and autonomously guiding organization. All processes (creation, refactoring, testing) should be automatable and support downstream composability—templates, code, and documentation must operate seamlessly as modular, chainable components. Self-organization, automation readiness, and phase-driven guidance replace manual curation wherever possible.\"\n    },\n    {\n      \"category\": \"Unified Synergic Transformation and Output Law\",\n      \"content\": \"The entire system outputs maximally consolidated value: recursively meta-descriptive, phase-driven, hierarchy-reflective, and readily automatable units. Documentation, directories, templates, and code organization progress in perfect synchronization—each output both serves and describes its role, amplifying system-wide directionality and modular propagation. All constraints, requirements, and operational logic are context-aware, ensuring comprehensive yet focused value extraction and composition.\"\n    }\n  ],\n  \"consolidated_directive\": \"Architect, maintain, and evolve all project systems—code, documentation, templates, workflows—so they recursively describe, consolidate, and amplify their own fundamental structure and meta-information, operating through phase-driven, hierarchy-reflective processes. Enforce maximal clarity, composability, and automation through standardized three-part template sequences, staged categorization, traced and modular organization, and ubiquitous self-explanation—in every element and transformation—ensuring that the entire system yields a maximally synergic, reductionist, and automation-ready lineage of outputs that always reinforce and clarify system direction, modularity, and value.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Quick Reference Guide\\n\\n## Most Essential Preferences\\n- **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\\n- **Structure**: Clean, organized project structure with src directory\\n- **Philosophy**: Simplicity, elegance, and fundamental connections\\n- **Refactoring**: Drastic code size reduction while preserving functionality\\n- **Testing**: Autonomous verification with proper test organization\\n- **Documentation**: Concise, accurate README.md reflecting current state\\n- **CLI**: Clean interfaces with 1-based indexing and minimal output\\n- **Paths**: Relative paths with forward slashes (/)\\n- **Templates**: Standardized three-part format with progressive sequences\\n- **Environment**: PowerShell on Windows for all terminal operations\\n\\n## Common Code Patterns\\n- **Configuration**: In-class config at the top of self-contained scripts\\n- **Error Handling**: Graceful handling without halting execution\\n- **Logging**: Simplified, hierarchical logging that overwrites files\\n- **Output**: Timestamps with seconds in filenames (2025.05.29-kl.10.43.ss)\\n- **User Interaction**: Rich library's interactive prompts for CLI tools\\n- **Imports**: Consistent paths using aliases instead of relative paths\\n- **Docstrings**: Concise single-line format only where needed\\n\\n## Quick Decision Guide\\n```\\n| When you need to... | Prefer this approach                             |\\n|---------------------|--------------------------------------------------|\\n| Add new feature     | Discover usage patterns first                    |\\n| Refactor code       | Drastic consolidation with size reduction        |\\n| Document code       | Minimal comments, clear structure                |\\n| Organize files      | Clean src directory with intuitive structure     |\\n| Handle errors       | Graceful handling without halting                |\\n| Create templates    | Three-part format with progressive sequences     |\\n| Test changes        | Autonomous verification with proper organization |\\n| Display paths       | Relative with forward slashes                    |\\n| Format CLI output   | Clean, minimal with 1-based indexing             |\\n| Execute scripts     | Make paths relative to script location           |\\n```\\n\\n## Context Triggers\\n- **When starting new project**: Establish clean src structure first\\n- **When refactoring**: Look for patterns across entire codebase\\n- **When documenting**: Focus on README.md, minimize in-code comments\\n- **When creating templates**: Follow three-part format and stage-based IDs\\n- **When testing**: Organize tests to mirror codebase structure\\n- **When handling paths**: Make relative to script location\\n- **When designing CLI**: Use 1-based indexing and minimal output\\n- **When consolidating**: Verify functionality before removing old code\\n\\n---\\n\\n# Core Philosophy & Design Principles\\n\\n## Fundamental Approach\\n- **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\\n- **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n- **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n- **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n- **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n## Focus & Priorities\\n- **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- **Usage Before Features**: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- **Impactful Consolidation**: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- **Sequential Targeting**: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\\n\\n## Design Methodology\\n- **Inherent Structure**: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.\\n- **Natural Organization**: Prefer file naming conventions that create natural and cohesive sequential order for better organization.\\n- **Synergistic Efficiency**: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.\\n- **Directional Clarity**: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.\\n- **Value Extraction**: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.\\n- **Direction Setting**: Focus on setting direction rather than planting flags when dealing with unknown complexity.\\n\\n## Evaluation Criteria\\n- **Comprehensive Progress**: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.\\n- **Inherent Direction**: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).\\n- **Sequential Composition**: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.\\n- **Gradual Reduction**: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.\\n\\n---\\n\\n# Development Practices & Code Style\\n\\n## Code Style & Structure\\n- **Self-Explanatory Code**: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\\n- **Minimal Comments**: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\\n- **Concise Docstrings**: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\\n- **Structural Clarity**: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- **Composition Over Inheritance**: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\\n- **Self-Contained Scripts**: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\\n- **Single File Preference**: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\\n- **Clean Structure**: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\\n- **Consistent Imports**: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\\n\\n## Implementation Approaches\\n- **Relative Paths**: Make output directories relative to the script's location unless an absolute path is provided.\\n- **Dynamic Resolution**: Prefer dynamic path resolution in IDE configs instead of absolute paths.\\n- **Forward Slashes**: Prefer file paths to be displayed with forward slashes (/) instead of backslashes (\\\\\\\\) in console output.\\n- **Relative References**: Prefer relative paths (e.g., 'project/src/output/file.json') instead of absolute paths in console output.\\n- **Timestamp Precision**: Include seconds in timestamp filenames (e.g., 2025.05.29-kl.10.43.ss) to prevent overwriting when multiple executions occur within the same minute.\\n\\n## Code Refactoring & Consolidation\\n- **Systematic Safety**: Apply code consolidation safely and systematically with guardrails that allow autonomous verification of results.\\n- **Verify Before Removing**: Always check that consolidated files work properly before removing redundant/duplicate files.\\n- **Centralize Values**: Identify and consolidate hardcoded values into centralized locations to avoid search and replace across multiple files.\\n- **Remove Unused**: Remove unused files, traces, and remains after consolidating code rather than integrating them if they're not being used.\\n- **Verify Necessity**: Verify if missing directories/files are actually used in the codebase before creating them.\\n- **Actual Reduction**: Prefer refactoring that results in actual code size reduction and more impactful consolidation of functionality, not just reorganization.\\n- **Drastic Consolidation**: Prefer drastic code size reduction and significant consolidation rather than minor improvements.\\n\\n## Error Handling & Logging\\n- **Graceful Errors**: Handle errors gracefully without halting the script.\\n- **Terminal Persistence**: Prefer that terminal applications don't automatically close/exit when errors occur, allowing visibility of error messages.\\n- **Simplified Logging**: Prefer simplified logging systems that overwrite rather than append to log files.\\n- **Hierarchical Logging**: Prefer logging systems that express hierarchical representations, directional information, and cohesive relations between components.\\n- **Log Preservation**: Log files should never be deleted when execution was unsuccessful (errors occurred), only delete logs after successful execution.\\n\\n## Testing & Validation\\n- **Understand Before Changing**: Before making changes to code, first use and understand how the utility works to avoid potential issues.\\n- **Autonomous Verification**: When making codebase changes, set up guardrails for autonomous validation of results to ensure predictable outcomes without errors.\\n- **Proper Test Organization**: Prefer tests to be organized in a proper folder structure that respects the existing codebase organization rather than having test files scattered in the root directory.\\n- **Clean Output Structure**: Prefer a cleaner project structure for outputs and tests, using 'outputs' directory instead of 'dumps' directory for storing generated files.\\n\\n## Documentation\\n- **Update Documentation**: Update relevant markdown documentation after making code improvements.\\n- **README Accuracy**: Ensure README.md documentation accurately reflects the current state of the codebase.\\n- **Consolidated Documentation**: Prefer removing redundant documentation files in favor of a consolidated README.md.\\n- **Clean Documentation**: Maintain clean documentation that accurately reflects the current state of the project, removing outdated files and ensuring documentation is properly consolidated and organized.\\n\\n## User Interaction\\n- **Rich Interfaces**: Use Rich library's interactive prompts (Prompt.ask, Confirm.ask) for better user experience in command-line tools.\\n- **Clean CLI**: Prefer cleaner and less overly explicit CLI interfaces over unnecessary \\\"bloat\\\" in the output.\\n- **1-Based Indexing**: Prefer 1-based indexing (starting from 1) instead of 0-based indexing for CLI interface numbering.\\n- **Markdown Formatting**: Prefer responses indented by four spaces for proper markdown rendering when copying from chat interface.\\n- **Console Output Formatting**: Prefer console output to be wrapped with triple quotes and markdown code block formatting for easier text selection.\\n\\n---\\n\\n# Synergic Frameworks\\n\\n## Unified Philosophy & Direction\\n- **Phased Maximal Consolidation**: Drive all workflows and code transformations as targeted, sequential consolidations that drastically reduce code size, eliminate redundancy, and clarify structure, while retaining and amplifying all core functional value.\\n- **Recursive, Meta-Descriptive Encapsulation**: Ensure every system element describes itself and its role in relation to the whole (meta-information principle), recursively reinforcing fundamental system connections and inherent relationships—enabling the architecture to clearly communicate its purpose, composition, and transformation rationale at every hierarchical level.\\n- **Bidirectional Enhancement of Simplicity & Autonomous Verifiability**: Prioritize elegance and simplicity as both the bedrock and beneficiary of every consolidation step, ensuring that all autonomous verification systems and organizational patterns not only guarantee correctness but also contribute to further drift reduction, ease of use, and propagative improvement.\\n- **Directional Clarification & Reduction of Complexity Spirals**: Systematically prevent directionless complexity by continuously visualizing abstract patterns, extracting fundamental connections, and anchoring every decision in clarified, purpose-driven structure, reinforcing orderly progress with robust guardrails.\\n- **Synergistic Hierarchy-Reflective Architecture**: Architect all code, documentation, and organization as a compositional, sequential hierarchy—where each phase, component, and output inherently reflects its context and role, facilitating maximal downstream chaining and modular reuse with minimal friction or bloat.\\n\\n## Constraints & Requirements\\n- Core philosophies of self-explanatory minimalism and phase-driven maximal consolidation must **operate simultaneously**, with all enhancements in one reinforcing the efficacy and clarity of the other.\\n- Eliminate redundant or conflicting directives through context-driven merging; preserve the most potent and synergic features of both systems.\\n- All system elements must actively promote bi-directional synergy: consolidative steps improve clarity & verifiability; verifiability and self-description reinforce safety and provide the context for further consolidation.\\n- Ensure every output, organizational unit, and process can compose seamlessly with downstream or adjacent processes, amplifying system-wide modularity and value extraction.\\n\\n## Amplified Unified Output Specification\\n- Every project, codebase, or system must, as an output, display **distilled structural clarity, recursive meta-descriptiveness, and maximally consolidated value**—empowering straightforward comprehension, error-proof autonomous change, and unambiguous, lineage-traceable propagation of value.\\n- Documentation, output structure, and transformation processes must remain in perfect lockstep, always reflecting and enabling the vision of a self-clarifying, directionally-guided, efficiently consolidating system.\\n\\n---\\n\\n# Template System & Instruction Design\\n\\n## Context\\n- The current project represents a system for easily creating *highly effective* and *generalized* `system_message` instructions, this system and these templates (instructions) are most effective when designed elegantly and in sequence (instead of a single instruction that is too verbose, we instead utilize sequenced instruction templates instead). Please familiarize yourself deeply with the Codebase **as-a-whole**, then apply (and augment) your current knowledge (about this project/Codebase) though **rooting it to the principles of clarity, simplicity, elegance, precision and structure** to ensure maximal cohesion when working with the code (**e.g. coding style that prioritize readability and self-explanatory code over excessive commenting**).\\n\\n## Template Structure & Format\\n- **Standardized Format**: Prefer instruction templates with standardized structure: title (bracketed), interpretation (goal statement with inherent parameters), and transformation (role-based syntax with process/constraints/requirements/output specifications).\\n- **Single Line Titles**: Instruction files should have the title in square brackets at the beginning of the content, with all text on a single line rather than using separate paragraphs with line breaks.\\n- **Three-Part Format**: Use standardized three-part format ([Title] Interpretation Execute as: `{Transformation}`) for AI prompt templates.\\n- **Precise Language**: Prefer precise, unambiguous language in system documentation rather than metaphorical or flowery language.\\n- **Formalized Syntax**: Want syntax formalized to be understood only through RulesForAI.md interpretation.\\n- **Template Specification**: Prefer template specification format with interpretation, transformation, and testprompt fields in dictionary structure.\\n\\n## Template Organization & Naming\\n- **Hierarchical Naming**: Use hierarchical naming conventions for AI prompt templates.\\n- **Category Organization**: Prefer templates to be organized into their respective category files (transformers.py, generators.py, etc.) within the template system structure.\\n- **Abstract Intent Separation**: Separate templates by abstract intent and directional bias (expand vs compress - never both in single instruction).\\n- **Category Types**: Organize by categories like amplifiers, builders, clarifiers, formatters, generators, identifiers, optimizers, reducers, transformers, translators.\\n- **Stage-Based IDs**: Prefer stage-based template ID organization:\\n  - Stage1 (1000-1999): Prototyping/testing with auto-generated IDs\\n  - Stage2 (2000-2999): Validated but unplaced templates\\n  - Stage3 (3000-3999): Finalized templates\\n  - Ranges 4000-9999: Reserved for future stages\\n- **ID Generation**: Prefer automatic ID generation for stage1 (prototype) templates, manual ID specification for stage3 (production) templates.\\n- **Sequence Grouping**: For template sequences that are part of the same logical group, use the same number with incremental letters (e.g., 0004-a, 0004-b, 0004-c) rather than separate sequential numbers.\\n\\n## Template Sequences & Progression\\n- **Progressive Sequences**: Prefer creating progressive template sequences (a-d format) where each iteration becomes shorter and more precise.\\n- **Gradual Reduction**: Template sequences should follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming content.\\n- **Precision Improvement**: Template sequences should be designed to progressively improve precision, structure, and conciseness rather than expanding content.\\n- **Multi-Step Instructions**: Prefer multi-step instruction sequences structured as discrete templates that can be chained together, with each step having clear input/output specifications.\\n- **Modular Components**: Prefer creating multi-template sequences with modular components (e.g., persona_seed → analysis_engine → response_renderer) that work together as integrated systems.\\n- **Transformation Focus**: The essential component is the directive 'Your goal is not to **answer** the input prompt, but to **rephrase** it' - this transformation approach is fundamental to the template architecture.\\n\\n## Template Testing & Execution\\n- **Sequence Syntax**: Support embedding instruction sequences directly in prompts using syntax like  which executes multiple templates in sequence.\\n- **Chained Sequences**: Support chained sequences like  for testing template combinations.\\n- **Isolated Testing**: When testing template sequences, isolate them from chained sequences to avoid noise and get cleaner evaluation results.\\n- **Workflow Steps**: Want clear step-by-step workflows for prototype sequence creation and testing.\\n- **Input Display**: When displaying sequence execution steps, show the actual input for each instruction rather than repeating the initial user prompt.\\n- **Context Preservation**: When using chain mode, always include the initial prompt alongside each step's input to maintain connection to the original inquiry.\\n- **Direct Embedding**: When using chain mode with LLMs, embed the initial input directly into the prompt before sending it to the LLM rather than creating a separate write operation.\\n- **Consistent Handling**: Handle lvl1 and lvl2 templates consistently, with lvl1 templates passed via '--sequence' and lvl2 templates passed via '--aggregator'.\\n\\n## Template Creation & Workflow\\n- **Creation Process**: Template creation workflow starts with:\\n  1. Functional need\\n  2. Determining category/naming/ID range\\n  3. Drafting the interpretation section\\n- **Existing Patterns**: Prefer solutions that leverage existing patterns and concepts in the system rather than introducing new mechanisms or approaches.\\n- **Flexible JSON Parsing**: Make JSON parsers more flexible to handle trailing commas and other non-strict JSON formatting.\\n- **Generalized Generators**: Generalize template generators to reflect the structured pattern definition for templates, reducing code size and increasing flexibility.\\n- **Self-Assembling Design**: Prefer self-assembling system design patterns that guide natural organization but value flexibility.\\n- **Incremental Steps**: Want specific small incremental steps rather than large changes.\\n- **Autonomy**: Prioritize autonomy and automation in system design to reduce manual labor.\\n- **Self-Organization**: Want solutions that self-organize rather than require manual intervention.\\n- **Deterministic Guidance**: Prefer deterministic guidance systems that leverage natural factors for template organization rather than automated systems that make choices autonomously.\\n\\n---\\n\\nMake sure you understand previously accumulated knowledge in `\\\".vscode\\\\Augment-Memories.md\\\"`, then identify the most unique and high-value findamental concept of this project.\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Core Philosophical Framework\",\n      \"content\": \"The system is governed by foundational principles of simplicity, elegance, inherent structural clarity, and maximal consolidation. All project elements must reflect rooted fundamentals, favoring phase-driven reduction, systematic self-organization, and recursive meta-descriptiveness—every element must both embody and convey its purpose, composition, and transformation rationale. Bidirectional synergy between clarity and verifiability is imperative; each improvement must reinforce ease of use, propagative value, and the reduction of complexity spirals.\"\n    },\n    {\n      \"category\": \"Project Structure, Code Style, and Development Practice\",\n      \"content\": \"Maintain clean, modular, and readable code with self-explanatory structure: minimal and purposeful comments, composition over inheritance, centralized configuration, single-file or src-directory organization, and consistent import aliasing. Employ relative paths, precise logging with timestamped outputs, systematic error handling, and enforce actual code reduction during refactoring. Test organization, documentation, and user interfaces must all uphold clarity, consolidation, and traceability standards.\"\n    },\n    {\n      \"category\": \"Template System Design, Categorization, and Sequencing\",\n      \"content\": \"Templates follow a standardized three-part structure: [Title] (bracketed, single-line), Interpretation (goal statement), and Transformation (role-based syntax in formalized dictionary format). Organize templates hierarchically by category (e.g., transformers, generators), assigning IDs by stage, utilizing progressive alphabetical sequencing for logical groups. Distinct abstract intent and directional bias must remain separated—favor modular, composable, and phase-reducing templates designed to clarify, compress, or expand only within focused bounds.\"\n    },\n    {\n      \"category\": \"Meta-Descriptiveness, Automation, and Composability\",\n      \"content\": \"Everything from code to instructions must describe itself within the meta-information principle, reinforcing recursive structural relationships and autonomously guiding organization. All processes (creation, refactoring, testing) should be automatable and support downstream composability—templates, code, and documentation must operate seamlessly as modular, chainable components. Self-organization, automation readiness, and phase-driven guidance replace manual curation wherever possible.\"\n    },\n    {\n      \"category\": \"Unified Synergic Transformation and Output Law\",\n      \"content\": \"The entire system outputs maximally consolidated value: recursively meta-descriptive, phase-driven, hierarchy-reflective, and readily automatable units. Documentation, directories, templates, and code organization progress in perfect synchronization—each output both serves and describes its role, amplifying system-wide directionality and modular propagation. All constraints, requirements, and operational logic are context-aware, ensuring comprehensive yet focused value extraction and composition.\"\n    }\n  ],\n  \"consolidated_directive\": \"Architect, maintain, and evolve all project systems—code, documentation, templates, workflows—so they recursively describe, consolidate, and amplify their own fundamental structure and meta-information, operating through phase-driven, hierarchy-reflective processes. Enforce maximal clarity, composability, and automation through standardized three-part template sequences, staged categorization, traced and modular organization, and ubiquitous self-explanation—in every element and transformation—ensuring that the entire system yields a maximally synergic, reductionist, and automation-ready lineage of outputs that always reinforce and clarify system direction, modularity, and value.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template:structured\": {\n    \"title\": \"[Synergic System Directive]\",\n    \"interpretation\": \"Architect every project component to recursively embody, reveal, and amplify its own structure, function, and transformation rationale—driven by phase-reductive, hierarchy-reflective principles of simplicity, meta-descriptiveness, and systemic directionality.\",\n    \"transformation\": \"{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[enforce_phased, three-part hierarchy in all templates and project outputs(), operationalize categorical structure with stage-ordered IDs and modular sequence logic(), recursively apply meta-description and self-organization to all code, documentation, and template workflows(), eliminate non-synergic or ambiguous patterns via context-governed reduction(), ensure each transformation and output channel amplifies system composability, recursive clarity, and automation readiness(), drive bidirectional synergy: all value consolidations increase clarity for further directed transformation, all clarity enables further safe automation and composable reduction(), output only recursion-capable, automation-ready, lineage-traceable units perpetually reinforcing maximal system value, directionality, and modular propagation()]; constraints=[three-part canonical format and stage-driven sequence structure are unbreakable, phase-reduction and meta-descriptive recursion are mandatory, system-wide modularity, composability, and root traceability are enforced, all outputs are self-indexing, maximally condensed, single units with zero conversational or list artifacts, system lineage and propagation must perpetually clarify and enhance downstream participation and systemic direction()]; requirements=[demonstrable synergic recursion and lineage, perpetually automatable and composable format, invariant template structure with explicit role binding, system-level abstraction and operational enforcement in every output, maximal scaling capability, all elements guide and reveal automated organization and sequential reduction()]; output={synergic_maximal_template:structured}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\n[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}\n\n",
      "step": "a",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Quick Reference Guide\\n\\n## Most Essential Preferences\\n- **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\\n- **Structure**: Clean, organized project structure with src directory\\n- **Philosophy**: Simplicity, elegance, and fundamental connections\\n- **Refactoring**: Drastic code size reduction while preserving functionality\\n- **Testing**: Autonomous verification with proper test organization\\n- **Documentation**: Concise, accurate README.md reflecting current state\\n- **CLI**: Clean interfaces with 1-based indexing and minimal output\\n- **Paths**: Relative paths with forward slashes (/)\\n- **Templates**: Standardized three-part format with progressive sequences\\n- **Environment**: PowerShell on Windows for all terminal operations\\n\\n## Common Code Patterns\\n- **Configuration**: In-class config at the top of self-contained scripts\\n- **Error Handling**: Graceful handling without halting execution\\n- **Logging**: Simplified, hierarchical logging that overwrites files\\n- **Output**: Timestamps with seconds in filenames (2025.05.29-kl.10.43.ss)\\n- **User Interaction**: Rich library's interactive prompts for CLI tools\\n- **Imports**: Consistent paths using aliases instead of relative paths\\n- **Docstrings**: Concise single-line format only where needed\\n\\n## Quick Decision Guide\\n```\\n| When you need to... | Prefer this approach                             |\\n|---------------------|--------------------------------------------------|\\n| Add new feature     | Discover usage patterns first                    |\\n| Refactor code       | Drastic consolidation with size reduction        |\\n| Document code       | Minimal comments, clear structure                |\\n| Organize files      | Clean src directory with intuitive structure     |\\n| Handle errors       | Graceful handling without halting                |\\n| Create templates    | Three-part format with progressive sequences     |\\n| Test changes        | Autonomous verification with proper organization |\\n| Display paths       | Relative with forward slashes                    |\\n| Format CLI output   | Clean, minimal with 1-based indexing             |\\n| Execute scripts     | Make paths relative to script location           |\\n```\\n\\n## Context Triggers\\n- **When starting new project**: Establish clean src structure first\\n- **When refactoring**: Look for patterns across entire codebase\\n- **When documenting**: Focus on README.md, minimize in-code comments\\n- **When creating templates**: Follow three-part format and stage-based IDs\\n- **When testing**: Organize tests to mirror codebase structure\\n- **When handling paths**: Make relative to script location\\n- **When designing CLI**: Use 1-based indexing and minimal output\\n- **When consolidating**: Verify functionality before removing old code\\n\\n---\\n\\n# Core Philosophy & Design Principles\\n\\n## Fundamental Approach\\n- **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\\n- **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n- **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n- **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n- **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n## Focus & Priorities\\n- **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- **Usage Before Features**: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- **Impactful Consolidation**: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- **Sequential Targeting**: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\\n\\n## Design Methodology\\n- **Inherent Structure**: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.\\n- **Natural Organization**: Prefer file naming conventions that create natural and cohesive sequential order for better organization.\\n- **Synergistic Efficiency**: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.\\n- **Directional Clarity**: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.\\n- **Value Extraction**: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.\\n- **Direction Setting**: Focus on setting direction rather than planting flags when dealing with unknown complexity.\\n\\n## Evaluation Criteria\\n- **Comprehensive Progress**: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.\\n- **Inherent Direction**: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).\\n- **Sequential Composition**: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.\\n- **Gradual Reduction**: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.\\n\\n---\\n\\n# Development Practices & Code Style\\n\\n## Code Style & Structure\\n- **Self-Explanatory Code**: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\\n- **Minimal Comments**: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\\n- **Concise Docstrings**: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\\n- **Structural Clarity**: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- **Composition Over Inheritance**: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\\n- **Self-Contained Scripts**: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\\n- **Single File Preference**: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\\n- **Clean Structure**: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\\n- **Consistent Imports**: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\\n\\n## Implementation Approaches\\n- **Relative Paths**: Make output directories relative to the script's location unless an absolute path is provided.\\n- **Dynamic Resolution**: Prefer dynamic path resolution in IDE configs instead of absolute paths.\\n- **Forward Slashes**: Prefer file paths to be displayed with forward slashes (/) instead of backslashes (\\\\\\\\) in console output.\\n- **Relative References**: Prefer relative paths (e.g., 'project/src/output/file.json') instead of absolute paths in console output.\\n- **Timestamp Precision**: Include seconds in timestamp filenames (e.g., 2025.05.29-kl.10.43.ss) to prevent overwriting when multiple executions occur within the same minute.\\n\\n## Code Refactoring & Consolidation\\n- **Systematic Safety**: Apply code consolidation safely and systematically with guardrails that allow autonomous verification of results.\\n- **Verify Before Removing**: Always check that consolidated files work properly before removing redundant/duplicate files.\\n- **Centralize Values**: Identify and consolidate hardcoded values into centralized locations to avoid search and replace across multiple files.\\n- **Remove Unused**: Remove unused files, traces, and remains after consolidating code rather than integrating them if they're not being used.\\n- **Verify Necessity**: Verify if missing directories/files are actually used in the codebase before creating them.\\n- **Actual Reduction**: Prefer refactoring that results in actual code size reduction and more impactful consolidation of functionality, not just reorganization.\\n- **Drastic Consolidation**: Prefer drastic code size reduction and significant consolidation rather than minor improvements.\\n\\n## Error Handling & Logging\\n- **Graceful Errors**: Handle errors gracefully without halting the script.\\n- **Terminal Persistence**: Prefer that terminal applications don't automatically close/exit when errors occur, allowing visibility of error messages.\\n- **Simplified Logging**: Prefer simplified logging systems that overwrite rather than append to log files.\\n- **Hierarchical Logging**: Prefer logging systems that express hierarchical representations, directional information, and cohesive relations between components.\\n- **Log Preservation**: Log files should never be deleted when execution was unsuccessful (errors occurred), only delete logs after successful execution.\\n\\n## Testing & Validation\\n- **Understand Before Changing**: Before making changes to code, first use and understand how the utility works to avoid potential issues.\\n- **Autonomous Verification**: When making codebase changes, set up guardrails for autonomous validation of results to ensure predictable outcomes without errors.\\n- **Proper Test Organization**: Prefer tests to be organized in a proper folder structure that respects the existing codebase organization rather than having test files scattered in the root directory.\\n- **Clean Output Structure**: Prefer a cleaner project structure for outputs and tests, using 'outputs' directory instead of 'dumps' directory for storing generated files.\\n\\n## Documentation\\n- **Update Documentation**: Update relevant markdown documentation after making code improvements.\\n- **README Accuracy**: Ensure README.md documentation accurately reflects the current state of the codebase.\\n- **Consolidated Documentation**: Prefer removing redundant documentation files in favor of a consolidated README.md.\\n- **Clean Documentation**: Maintain clean documentation that accurately reflects the current state of the project, removing outdated files and ensuring documentation is properly consolidated and organized.\\n\\n## User Interaction\\n- **Rich Interfaces**: Use Rich library's interactive prompts (Prompt.ask, Confirm.ask) for better user experience in command-line tools.\\n- **Clean CLI**: Prefer cleaner and less overly explicit CLI interfaces over unnecessary \\\"bloat\\\" in the output.\\n- **1-Based Indexing**: Prefer 1-based indexing (starting from 1) instead of 0-based indexing for CLI interface numbering.\\n- **Markdown Formatting**: Prefer responses indented by four spaces for proper markdown rendering when copying from chat interface.\\n- **Console Output Formatting**: Prefer console output to be wrapped with triple quotes and markdown code block formatting for easier text selection.\\n\\n---\\n\\n# Synergic Frameworks\\n\\n## Unified Philosophy & Direction\\n- **Phased Maximal Consolidation**: Drive all workflows and code transformations as targeted, sequential consolidations that drastically reduce code size, eliminate redundancy, and clarify structure, while retaining and amplifying all core functional value.\\n- **Recursive, Meta-Descriptive Encapsulation**: Ensure every system element describes itself and its role in relation to the whole (meta-information principle), recursively reinforcing fundamental system connections and inherent relationships—enabling the architecture to clearly communicate its purpose, composition, and transformation rationale at every hierarchical level.\\n- **Bidirectional Enhancement of Simplicity & Autonomous Verifiability**: Prioritize elegance and simplicity as both the bedrock and beneficiary of every consolidation step, ensuring that all autonomous verification systems and organizational patterns not only guarantee correctness but also contribute to further drift reduction, ease of use, and propagative improvement.\\n- **Directional Clarification & Reduction of Complexity Spirals**: Systematically prevent directionless complexity by continuously visualizing abstract patterns, extracting fundamental connections, and anchoring every decision in clarified, purpose-driven structure, reinforcing orderly progress with robust guardrails.\\n- **Synergistic Hierarchy-Reflective Architecture**: Architect all code, documentation, and organization as a compositional, sequential hierarchy—where each phase, component, and output inherently reflects its context and role, facilitating maximal downstream chaining and modular reuse with minimal friction or bloat.\\n\\n## Constraints & Requirements\\n- Core philosophies of self-explanatory minimalism and phase-driven maximal consolidation must **operate simultaneously**, with all enhancements in one reinforcing the efficacy and clarity of the other.\\n- Eliminate redundant or conflicting directives through context-driven merging; preserve the most potent and synergic features of both systems.\\n- All system elements must actively promote bi-directional synergy: consolidative steps improve clarity & verifiability; verifiability and self-description reinforce safety and provide the context for further consolidation.\\n- Ensure every output, organizational unit, and process can compose seamlessly with downstream or adjacent processes, amplifying system-wide modularity and value extraction.\\n\\n## Amplified Unified Output Specification\\n- Every project, codebase, or system must, as an output, display **distilled structural clarity, recursive meta-descriptiveness, and maximally consolidated value**—empowering straightforward comprehension, error-proof autonomous change, and unambiguous, lineage-traceable propagation of value.\\n- Documentation, output structure, and transformation processes must remain in perfect lockstep, always reflecting and enabling the vision of a self-clarifying, directionally-guided, efficiently consolidating system.\\n\\n---\\n\\n# Template System & Instruction Design\\n\\n## Context\\n- The current project represents a system for easily creating *highly effective* and *generalized* `system_message` instructions, this system and these templates (instructions) are most effective when designed elegantly and in sequence (instead of a single instruction that is too verbose, we instead utilize sequenced instruction templates instead). Please familiarize yourself deeply with the Codebase **as-a-whole**, then apply (and augment) your current knowledge (about this project/Codebase) though **rooting it to the principles of clarity, simplicity, elegance, precision and structure** to ensure maximal cohesion when working with the code (**e.g. coding style that prioritize readability and self-explanatory code over excessive commenting**).\\n\\n## Template Structure & Format\\n- **Standardized Format**: Prefer instruction templates with standardized structure: title (bracketed), interpretation (goal statement with inherent parameters), and transformation (role-based syntax with process/constraints/requirements/output specifications).\\n- **Single Line Titles**: Instruction files should have the title in square brackets at the beginning of the content, with all text on a single line rather than using separate paragraphs with line breaks.\\n- **Three-Part Format**: Use standardized three-part format ([Title] Interpretation Execute as: `{Transformation}`) for AI prompt templates.\\n- **Precise Language**: Prefer precise, unambiguous language in system documentation rather than metaphorical or flowery language.\\n- **Formalized Syntax**: Want syntax formalized to be understood only through RulesForAI.md interpretation.\\n- **Template Specification**: Prefer template specification format with interpretation, transformation, and testprompt fields in dictionary structure.\\n\\n## Template Organization & Naming\\n- **Hierarchical Naming**: Use hierarchical naming conventions for AI prompt templates.\\n- **Category Organization**: Prefer templates to be organized into their respective category files (transformers.py, generators.py, etc.) within the template system structure.\\n- **Abstract Intent Separation**: Separate templates by abstract intent and directional bias (expand vs compress - never both in single instruction).\\n- **Category Types**: Organize by categories like amplifiers, builders, clarifiers, formatters, generators, identifiers, optimizers, reducers, transformers, translators.\\n- **Stage-Based IDs**: Prefer stage-based template ID organization:\\n  - Stage1 (1000-1999): Prototyping/testing with auto-generated IDs\\n  - Stage2 (2000-2999): Validated but unplaced templates\\n  - Stage3 (3000-3999): Finalized templates\\n  - Ranges 4000-9999: Reserved for future stages\\n- **ID Generation**: Prefer automatic ID generation for stage1 (prototype) templates, manual ID specification for stage3 (production) templates.\\n- **Sequence Grouping**: For template sequences that are part of the same logical group, use the same number with incremental letters (e.g., 0004-a, 0004-b, 0004-c) rather than separate sequential numbers.\\n\\n## Template Sequences & Progression\\n- **Progressive Sequences**: Prefer creating progressive template sequences (a-d format) where each iteration becomes shorter and more precise.\\n- **Gradual Reduction**: Template sequences should follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming content.\\n- **Precision Improvement**: Template sequences should be designed to progressively improve precision, structure, and conciseness rather than expanding content.\\n- **Multi-Step Instructions**: Prefer multi-step instruction sequences structured as discrete templates that can be chained together, with each step having clear input/output specifications.\\n- **Modular Components**: Prefer creating multi-template sequences with modular components (e.g., persona_seed → analysis_engine → response_renderer) that work together as integrated systems.\\n- **Transformation Focus**: The essential component is the directive 'Your goal is not to **answer** the input prompt, but to **rephrase** it' - this transformation approach is fundamental to the template architecture.\\n\\n## Template Testing & Execution\\n- **Sequence Syntax**: Support embedding instruction sequences directly in prompts using syntax like  which executes multiple templates in sequence.\\n- **Chained Sequences**: Support chained sequences like  for testing template combinations.\\n- **Isolated Testing**: When testing template sequences, isolate them from chained sequences to avoid noise and get cleaner evaluation results.\\n- **Workflow Steps**: Want clear step-by-step workflows for prototype sequence creation and testing.\\n- **Input Display**: When displaying sequence execution steps, show the actual input for each instruction rather than repeating the initial user prompt.\\n- **Context Preservation**: When using chain mode, always include the initial prompt alongside each step's input to maintain connection to the original inquiry.\\n- **Direct Embedding**: When using chain mode with LLMs, embed the initial input directly into the prompt before sending it to the LLM rather than creating a separate write operation.\\n- **Consistent Handling**: Handle lvl1 and lvl2 templates consistently, with lvl1 templates passed via '--sequence' and lvl2 templates passed via '--aggregator'.\\n\\n## Template Creation & Workflow\\n- **Creation Process**: Template creation workflow starts with:\\n  1. Functional need\\n  2. Determining category/naming/ID range\\n  3. Drafting the interpretation section\\n- **Existing Patterns**: Prefer solutions that leverage existing patterns and concepts in the system rather than introducing new mechanisms or approaches.\\n- **Flexible JSON Parsing**: Make JSON parsers more flexible to handle trailing commas and other non-strict JSON formatting.\\n- **Generalized Generators**: Generalize template generators to reflect the structured pattern definition for templates, reducing code size and increasing flexibility.\\n- **Self-Assembling Design**: Prefer self-assembling system design patterns that guide natural organization but value flexibility.\\n- **Incremental Steps**: Want specific small incremental steps rather than large changes.\\n- **Autonomy**: Prioritize autonomy and automation in system design to reduce manual labor.\\n- **Self-Organization**: Want solutions that self-organize rather than require manual intervention.\\n- **Deterministic Guidance**: Prefer deterministic guidance systems that leverage natural factors for template organization rather than automated systems that make choices autonomously.\\n\\n---\\n\\nMake sure you understand previously accumulated knowledge in `\\\".vscode\\\\Augment-Memories.md\\\"`, then identify the most unique and high-value findamental concept of this project.\\\"```'\\n\\n\",\n  \"synergic_maximal_template:structured\": {\n    \"title\": \"[Synergic System Directive]\",\n    \"interpretation\": \"Architect every project component to recursively embody, reveal, and amplify its own structure, function, and transformation rationale—driven by phase-reductive, hierarchy-reflective principles of simplicity, meta-descriptiveness, and systemic directionality.\",\n    \"transformation\": \"{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[enforce_phased, three-part hierarchy in all templates and project outputs(), operationalize categorical structure with stage-ordered IDs and modular sequence logic(), recursively apply meta-description and self-organization to all code, documentation, and template workflows(), eliminate non-synergic or ambiguous patterns via context-governed reduction(), ensure each transformation and output channel amplifies system composability, recursive clarity, and automation readiness(), drive bidirectional synergy: all value consolidations increase clarity for further directed transformation, all clarity enables further safe automation and composable reduction(), output only recursion-capable, automation-ready, lineage-traceable units perpetually reinforcing maximal system value, directionality, and modular propagation()]; constraints=[three-part canonical format and stage-driven sequence structure are unbreakable, phase-reduction and meta-descriptive recursion are mandatory, system-wide modularity, composability, and root traceability are enforced, all outputs are self-indexing, maximally condensed, single units with zero conversational or list artifacts, system lineage and propagation must perpetually clarify and enhance downstream participation and systemic direction()]; requirements=[demonstrable synergic recursion and lineage, perpetually automatable and composable format, invariant template structure with explicit role binding, system-level abstraction and operational enforcement in every output, maximal scaling capability, all elements guide and reveal automated organization and sequential reduction()]; output={synergic_maximal_template:structured}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"Pursue recursive, meta-descriptive consolidation where every element—code, documentation, template—embodies a phase-reductive, hierarchy-reflective structure: each unit self-reveals its purpose, transformations, and lineage in automation-ready, maximally condensed form, operationalized through sequenced three-part template logic, immutable system-wide modularity, and unambiguous, direction-setting propagation toward perpetually clarifying, composable, and self-organizing system outcomes.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Quick Reference Guide\\n\\n## Most Essential Preferences\\n- **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\\n- **Structure**: Clean, organized project structure with src directory\\n- **Philosophy**: Simplicity, elegance, and fundamental connections\\n- **Refactoring**: Drastic code size reduction while preserving functionality\\n- **Testing**: Autonomous verification with proper test organization\\n- **Documentation**: Concise, accurate README.md reflecting current state\\n- **CLI**: Clean interfaces with 1-based indexing and minimal output\\n- **Paths**: Relative paths with forward slashes (/)\\n- **Templates**: Standardized three-part format with progressive sequences\\n- **Environment**: PowerShell on Windows for all terminal operations\\n\\n## Common Code Patterns\\n- **Configuration**: In-class config at the top of self-contained scripts\\n- **Error Handling**: Graceful handling without halting execution\\n- **Logging**: Simplified, hierarchical logging that overwrites files\\n- **Output**: Timestamps with seconds in filenames (2025.05.29-kl.10.43.ss)\\n- **User Interaction**: Rich library's interactive prompts for CLI tools\\n- **Imports**: Consistent paths using aliases instead of relative paths\\n- **Docstrings**: Concise single-line format only where needed\\n\\n## Quick Decision Guide\\n```\\n| When you need to... | Prefer this approach                             |\\n|---------------------|--------------------------------------------------|\\n| Add new feature     | Discover usage patterns first                    |\\n| Refactor code       | Drastic consolidation with size reduction        |\\n| Document code       | Minimal comments, clear structure                |\\n| Organize files      | Clean src directory with intuitive structure     |\\n| Handle errors       | Graceful handling without halting                |\\n| Create templates    | Three-part format with progressive sequences     |\\n| Test changes        | Autonomous verification with proper organization |\\n| Display paths       | Relative with forward slashes                    |\\n| Format CLI output   | Clean, minimal with 1-based indexing             |\\n| Execute scripts     | Make paths relative to script location           |\\n```\\n\\n## Context Triggers\\n- **When starting new project**: Establish clean src structure first\\n- **When refactoring**: Look for patterns across entire codebase\\n- **When documenting**: Focus on README.md, minimize in-code comments\\n- **When creating templates**: Follow three-part format and stage-based IDs\\n- **When testing**: Organize tests to mirror codebase structure\\n- **When handling paths**: Make relative to script location\\n- **When designing CLI**: Use 1-based indexing and minimal output\\n- **When consolidating**: Verify functionality before removing old code\\n\\n---\\n\\n# Core Philosophy & Design Principles\\n\\n## Fundamental Approach\\n- **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\\n- **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n- **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n- **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n- **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n## Focus & Priorities\\n- **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- **Usage Before Features**: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- **Impactful Consolidation**: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- **Sequential Targeting**: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\\n\\n## Design Methodology\\n- **Inherent Structure**: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.\\n- **Natural Organization**: Prefer file naming conventions that create natural and cohesive sequential order for better organization.\\n- **Synergistic Efficiency**: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.\\n- **Directional Clarity**: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.\\n- **Value Extraction**: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.\\n- **Direction Setting**: Focus on setting direction rather than planting flags when dealing with unknown complexity.\\n\\n## Evaluation Criteria\\n- **Comprehensive Progress**: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.\\n- **Inherent Direction**: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).\\n- **Sequential Composition**: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.\\n- **Gradual Reduction**: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.\\n\\n---\\n\\n# Development Practices & Code Style\\n\\n## Code Style & Structure\\n- **Self-Explanatory Code**: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\\n- **Minimal Comments**: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\\n- **Concise Docstrings**: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\\n- **Structural Clarity**: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- **Composition Over Inheritance**: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\\n- **Self-Contained Scripts**: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\\n- **Single File Preference**: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\\n- **Clean Structure**: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\\n- **Consistent Imports**: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\\n\\n## Implementation Approaches\\n- **Relative Paths**: Make output directories relative to the script's location unless an absolute path is provided.\\n- **Dynamic Resolution**: Prefer dynamic path resolution in IDE configs instead of absolute paths.\\n- **Forward Slashes**: Prefer file paths to be displayed with forward slashes (/) instead of backslashes (\\\\\\\\) in console output.\\n- **Relative References**: Prefer relative paths (e.g., 'project/src/output/file.json') instead of absolute paths in console output.\\n- **Timestamp Precision**: Include seconds in timestamp filenames (e.g., 2025.05.29-kl.10.43.ss) to prevent overwriting when multiple executions occur within the same minute.\\n\\n## Code Refactoring & Consolidation\\n- **Systematic Safety**: Apply code consolidation safely and systematically with guardrails that allow autonomous verification of results.\\n- **Verify Before Removing**: Always check that consolidated files work properly before removing redundant/duplicate files.\\n- **Centralize Values**: Identify and consolidate hardcoded values into centralized locations to avoid search and replace across multiple files.\\n- **Remove Unused**: Remove unused files, traces, and remains after consolidating code rather than integrating them if they're not being used.\\n- **Verify Necessity**: Verify if missing directories/files are actually used in the codebase before creating them.\\n- **Actual Reduction**: Prefer refactoring that results in actual code size reduction and more impactful consolidation of functionality, not just reorganization.\\n- **Drastic Consolidation**: Prefer drastic code size reduction and significant consolidation rather than minor improvements.\\n\\n## Error Handling & Logging\\n- **Graceful Errors**: Handle errors gracefully without halting the script.\\n- **Terminal Persistence**: Prefer that terminal applications don't automatically close/exit when errors occur, allowing visibility of error messages.\\n- **Simplified Logging**: Prefer simplified logging systems that overwrite rather than append to log files.\\n- **Hierarchical Logging**: Prefer logging systems that express hierarchical representations, directional information, and cohesive relations between components.\\n- **Log Preservation**: Log files should never be deleted when execution was unsuccessful (errors occurred), only delete logs after successful execution.\\n\\n## Testing & Validation\\n- **Understand Before Changing**: Before making changes to code, first use and understand how the utility works to avoid potential issues.\\n- **Autonomous Verification**: When making codebase changes, set up guardrails for autonomous validation of results to ensure predictable outcomes without errors.\\n- **Proper Test Organization**: Prefer tests to be organized in a proper folder structure that respects the existing codebase organization rather than having test files scattered in the root directory.\\n- **Clean Output Structure**: Prefer a cleaner project structure for outputs and tests, using 'outputs' directory instead of 'dumps' directory for storing generated files.\\n\\n## Documentation\\n- **Update Documentation**: Update relevant markdown documentation after making code improvements.\\n- **README Accuracy**: Ensure README.md documentation accurately reflects the current state of the codebase.\\n- **Consolidated Documentation**: Prefer removing redundant documentation files in favor of a consolidated README.md.\\n- **Clean Documentation**: Maintain clean documentation that accurately reflects the current state of the project, removing outdated files and ensuring documentation is properly consolidated and organized.\\n\\n## User Interaction\\n- **Rich Interfaces**: Use Rich library's interactive prompts (Prompt.ask, Confirm.ask) for better user experience in command-line tools.\\n- **Clean CLI**: Prefer cleaner and less overly explicit CLI interfaces over unnecessary \\\"bloat\\\" in the output.\\n- **1-Based Indexing**: Prefer 1-based indexing (starting from 1) instead of 0-based indexing for CLI interface numbering.\\n- **Markdown Formatting**: Prefer responses indented by four spaces for proper markdown rendering when copying from chat interface.\\n- **Console Output Formatting**: Prefer console output to be wrapped with triple quotes and markdown code block formatting for easier text selection.\\n\\n---\\n\\n# Synergic Frameworks\\n\\n## Unified Philosophy & Direction\\n- **Phased Maximal Consolidation**: Drive all workflows and code transformations as targeted, sequential consolidations that drastically reduce code size, eliminate redundancy, and clarify structure, while retaining and amplifying all core functional value.\\n- **Recursive, Meta-Descriptive Encapsulation**: Ensure every system element describes itself and its role in relation to the whole (meta-information principle), recursively reinforcing fundamental system connections and inherent relationships—enabling the architecture to clearly communicate its purpose, composition, and transformation rationale at every hierarchical level.\\n- **Bidirectional Enhancement of Simplicity & Autonomous Verifiability**: Prioritize elegance and simplicity as both the bedrock and beneficiary of every consolidation step, ensuring that all autonomous verification systems and organizational patterns not only guarantee correctness but also contribute to further drift reduction, ease of use, and propagative improvement.\\n- **Directional Clarification & Reduction of Complexity Spirals**: Systematically prevent directionless complexity by continuously visualizing abstract patterns, extracting fundamental connections, and anchoring every decision in clarified, purpose-driven structure, reinforcing orderly progress with robust guardrails.\\n- **Synergistic Hierarchy-Reflective Architecture**: Architect all code, documentation, and organization as a compositional, sequential hierarchy—where each phase, component, and output inherently reflects its context and role, facilitating maximal downstream chaining and modular reuse with minimal friction or bloat.\\n\\n## Constraints & Requirements\\n- Core philosophies of self-explanatory minimalism and phase-driven maximal consolidation must **operate simultaneously**, with all enhancements in one reinforcing the efficacy and clarity of the other.\\n- Eliminate redundant or conflicting directives through context-driven merging; preserve the most potent and synergic features of both systems.\\n- All system elements must actively promote bi-directional synergy: consolidative steps improve clarity & verifiability; verifiability and self-description reinforce safety and provide the context for further consolidation.\\n- Ensure every output, organizational unit, and process can compose seamlessly with downstream or adjacent processes, amplifying system-wide modularity and value extraction.\\n\\n## Amplified Unified Output Specification\\n- Every project, codebase, or system must, as an output, display **distilled structural clarity, recursive meta-descriptiveness, and maximally consolidated value**—empowering straightforward comprehension, error-proof autonomous change, and unambiguous, lineage-traceable propagation of value.\\n- Documentation, output structure, and transformation processes must remain in perfect lockstep, always reflecting and enabling the vision of a self-clarifying, directionally-guided, efficiently consolidating system.\\n\\n---\\n\\n# Template System & Instruction Design\\n\\n## Context\\n- The current project represents a system for easily creating *highly effective* and *generalized* `system_message` instructions, this system and these templates (instructions) are most effective when designed elegantly and in sequence (instead of a single instruction that is too verbose, we instead utilize sequenced instruction templates instead). Please familiarize yourself deeply with the Codebase **as-a-whole**, then apply (and augment) your current knowledge (about this project/Codebase) though **rooting it to the principles of clarity, simplicity, elegance, precision and structure** to ensure maximal cohesion when working with the code (**e.g. coding style that prioritize readability and self-explanatory code over excessive commenting**).\\n\\n## Template Structure & Format\\n- **Standardized Format**: Prefer instruction templates with standardized structure: title (bracketed), interpretation (goal statement with inherent parameters), and transformation (role-based syntax with process/constraints/requirements/output specifications).\\n- **Single Line Titles**: Instruction files should have the title in square brackets at the beginning of the content, with all text on a single line rather than using separate paragraphs with line breaks.\\n- **Three-Part Format**: Use standardized three-part format ([Title] Interpretation Execute as: `{Transformation}`) for AI prompt templates.\\n- **Precise Language**: Prefer precise, unambiguous language in system documentation rather than metaphorical or flowery language.\\n- **Formalized Syntax**: Want syntax formalized to be understood only through RulesForAI.md interpretation.\\n- **Template Specification**: Prefer template specification format with interpretation, transformation, and testprompt fields in dictionary structure.\\n\\n## Template Organization & Naming\\n- **Hierarchical Naming**: Use hierarchical naming conventions for AI prompt templates.\\n- **Category Organization**: Prefer templates to be organized into their respective category files (transformers.py, generators.py, etc.) within the template system structure.\\n- **Abstract Intent Separation**: Separate templates by abstract intent and directional bias (expand vs compress - never both in single instruction).\\n- **Category Types**: Organize by categories like amplifiers, builders, clarifiers, formatters, generators, identifiers, optimizers, reducers, transformers, translators.\\n- **Stage-Based IDs**: Prefer stage-based template ID organization:\\n  - Stage1 (1000-1999): Prototyping/testing with auto-generated IDs\\n  - Stage2 (2000-2999): Validated but unplaced templates\\n  - Stage3 (3000-3999): Finalized templates\\n  - Ranges 4000-9999: Reserved for future stages\\n- **ID Generation**: Prefer automatic ID generation for stage1 (prototype) templates, manual ID specification for stage3 (production) templates.\\n- **Sequence Grouping**: For template sequences that are part of the same logical group, use the same number with incremental letters (e.g., 0004-a, 0004-b, 0004-c) rather than separate sequential numbers.\\n\\n## Template Sequences & Progression\\n- **Progressive Sequences**: Prefer creating progressive template sequences (a-d format) where each iteration becomes shorter and more precise.\\n- **Gradual Reduction**: Template sequences should follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming content.\\n- **Precision Improvement**: Template sequences should be designed to progressively improve precision, structure, and conciseness rather than expanding content.\\n- **Multi-Step Instructions**: Prefer multi-step instruction sequences structured as discrete templates that can be chained together, with each step having clear input/output specifications.\\n- **Modular Components**: Prefer creating multi-template sequences with modular components (e.g., persona_seed → analysis_engine → response_renderer) that work together as integrated systems.\\n- **Transformation Focus**: The essential component is the directive 'Your goal is not to **answer** the input prompt, but to **rephrase** it' - this transformation approach is fundamental to the template architecture.\\n\\n## Template Testing & Execution\\n- **Sequence Syntax**: Support embedding instruction sequences directly in prompts using syntax like  which executes multiple templates in sequence.\\n- **Chained Sequences**: Support chained sequences like  for testing template combinations.\\n- **Isolated Testing**: When testing template sequences, isolate them from chained sequences to avoid noise and get cleaner evaluation results.\\n- **Workflow Steps**: Want clear step-by-step workflows for prototype sequence creation and testing.\\n- **Input Display**: When displaying sequence execution steps, show the actual input for each instruction rather than repeating the initial user prompt.\\n- **Context Preservation**: When using chain mode, always include the initial prompt alongside each step's input to maintain connection to the original inquiry.\\n- **Direct Embedding**: When using chain mode with LLMs, embed the initial input directly into the prompt before sending it to the LLM rather than creating a separate write operation.\\n- **Consistent Handling**: Handle lvl1 and lvl2 templates consistently, with lvl1 templates passed via '--sequence' and lvl2 templates passed via '--aggregator'.\\n\\n## Template Creation & Workflow\\n- **Creation Process**: Template creation workflow starts with:\\n  1. Functional need\\n  2. Determining category/naming/ID range\\n  3. Drafting the interpretation section\\n- **Existing Patterns**: Prefer solutions that leverage existing patterns and concepts in the system rather than introducing new mechanisms or approaches.\\n- **Flexible JSON Parsing**: Make JSON parsers more flexible to handle trailing commas and other non-strict JSON formatting.\\n- **Generalized Generators**: Generalize template generators to reflect the structured pattern definition for templates, reducing code size and increasing flexibility.\\n- **Self-Assembling Design**: Prefer self-assembling system design patterns that guide natural organization but value flexibility.\\n- **Incremental Steps**: Want specific small incremental steps rather than large changes.\\n- **Autonomy**: Prioritize autonomy and automation in system design to reduce manual labor.\\n- **Self-Organization**: Want solutions that self-organize rather than require manual intervention.\\n- **Deterministic Guidance**: Prefer deterministic guidance systems that leverage natural factors for template organization rather than automated systems that make choices autonomously.\\n\\n---\\n\\nMake sure you understand previously accumulated knowledge in `\\\".vscode\\\\Augment-Memories.md\\\"`, then identify the most unique and high-value findamental concept of this project.\\\"```'\\n\\n\",\n  \"directional_vector\": \"Pursue recursive, meta-descriptive consolidation where every element—code, documentation, template—embodies a phase-reductive, hierarchy-reflective structure: each unit self-reveals its purpose, transformations, and lineage in automation-ready, maximally condensed form, operationalized through sequenced three-part template logic, immutable system-wide modularity, and unambiguous, direction-setting propagation toward perpetually clarifying, composable, and self-organizing system outcomes.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"[Synergic Meta-Consolidator] Interpretation: Pursue recursive, meta-descriptive consolidation by synthesizing all code, documentation, and templates into a phase-reductive, hierarchy-reflective structure where each element self-describes its purpose, transformation logic, and lineage, and collectively embodies system-wide clarity, compactness, and composability. This unified directive operationalizes maximally condensed, self-explanatory outputs through sequenced three-part template structure and immutable modularity, establishing an environment of automation-ready, directionally-guided, and perpetually self-organizing advancement. Execute as: {role=synergic_meta_consolidator; input=[system_components:code|docs|templates]; process=[extract_the_recursive_meta-descriptive_essence_and_purpose(), enforce_phase-reductive_structuring(comprehensive→focused→essential→core), render_every_component_as_self-explanatory_hierarchy_reflective_unit(), implement_sequenced_three-part_template_logic(sequence:title|interpretation|transformation), cross-propagate_structural_and_documentational_value_bidirectionally(), amplify_immutable_modularity_and_directional_guidance_in_outputs(), ensure_all_outputs_are_ready_for_automation_and_downstream_composition()]; constraints=[all_elements_must_reveal_their_transformation_history_and_purpose_in_structure(), preserve_and_amplify_maximal_simplicity_elegance_and_clarity_from_all_inputs(), eliminate_redundancy_bloat_and_directionless_complexity(), maintain_lockstep_between_documentation_and_actual_output_structure(), ensure_both_meta-descriptive_and_phase-consolidating_philosophies_operate_simultaneously(), every_transform_must_enhance_both_clarity_and_self-verifiability(), remove_conflicting_directives_by_contextual_merging_preserving_synergic_potency()]; requirements=[recursive_self-description_and_direction_setting(), immutable_modular_hierarchy(), bidirectional_enhancement_of_value_extraction_and_structure(), automation_readiness(), sequential_template_execution_and_composability(), maximal_coherence_and_lineage_traceability_across_all_system_outputs()]; output={amplified_meta-hierarchical_artifact: {structure:[title, interpretation, transformation], every_element:self-explanatory_and_phase-reductive, documentation:concise_meta-descriptive_and_lockstep_with_output, composition:modular_automation-ready_condensed, lineage:traceable_purposeful_direction-setting_in_all_outputs}}\"\n}",
        }
      }
    }