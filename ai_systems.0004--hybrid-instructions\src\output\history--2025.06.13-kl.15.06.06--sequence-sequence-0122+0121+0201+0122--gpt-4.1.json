  {
  "initial_prompt": "# Synergic Frameworks\n\n## Unified Philosophy & Direction\n- **Phased Maximal Consolidation**: Drive all workflows and code transformations as targeted, sequential consolidations that drastically reduce code size, eliminate redundancy, and clarify structure, while retaining and amplifying all core functional value.\n- **Recursive, Meta-Descriptive Encapsulation**: Ensure every system element describes itself and its role in relation to the whole (meta-information principle), recursively reinforcing fundamental system connections and inherent relationships—enabling the architecture to clearly communicate its purpose, composition, and transformation rationale at every hierarchical level.\n- **Bidirectional Enhancement of Simplicity & Autonomous Verifiability**: Prioritize elegance and simplicity as both the bedrock and beneficiary of every consolidation step, ensuring that all autonomous verification systems and organizational patterns not only guarantee correctness but also contribute to further drift reduction, ease of use, and propagative improvement.\n- **Directional Clarification & Reduction of Complexity Spirals**: Systematically prevent directionless complexity by continuously visualizing abstract patterns, extracting fundamental connections, and anchoring every decision in clarified, purpose-driven structure, reinforcing orderly progress with robust guardrails.\n- **Synergistic Hierarchy-Reflective Architecture**: Architect all code, documentation, and organization as a compositional, sequential hierarchy—where each phase, component, and output inherently reflects its context and role, facilitating maximal downstream chaining and modular reuse with minimal friction or bloat.\n\n## Constraints & Requirements\n- Core philosophies of self-explanatory minimalism and phase-driven maximal consolidation must **operate simultaneously**, with all enhancements in one reinforcing the efficacy and clarity of the other.\n- Eliminate redundant or conflicting directives through context-driven merging; preserve the most potent and synergic features of both systems.\n- All system elements must actively promote bi-directional synergy: consolidative steps improve clarity & verifiability; verifiability and self-description reinforce safety and provide the context for further consolidation.\n- Ensure every output, organizational unit, and process can compose seamlessly with downstream or adjacent processes, amplifying system-wide modularity and value extraction.\n\n## Amplified Unified Output Specification\n- Every project, codebase, or system must, as an output, display **distilled structural clarity, recursive meta-descriptiveness, and maximally consolidated value**—empowering straightforward comprehension, error-proof autonomous change, and unambiguous, lineage-traceable propagation of value.\n- Documentation, output structure, and transformation processes must remain in perfect lockstep, always reflecting and enabling the vision of a self-clarifying, directionally-guided, efficiently consolidating system.\n\n---\n\n# Template System & Instruction Design\n\n## Context\n- The current project represents a system for easily creating *highly effective* and *generalized* `system_message` instructions, this system and these templates (instructions) are most effective when designed elegantly and in sequence (instead of a single instruction that is too verbose, we instead utilize sequenced instruction templates instead). Please familiarize yourself deeply with the Codebase **as-a-whole**, then apply (and augment) your current knowledge (about this project/Codebase) though **rooting it to the principles of clarity, simplicity, elegance, precision and structure** to ensure maximal cohesion when working with the code (**e.g. coding style that prioritize readability and self-explanatory code over excessive commenting**).\n\n## Template Structure & Format\n- **Standardized Format**: Prefer instruction templates with standardized structure: title (bracketed), interpretation (goal statement with inherent parameters), and transformation (role-based syntax with process/constraints/requirements/output specifications).\n- **Single Line Titles**: Instruction files should have the title in square brackets at the beginning of the content, with all text on a single line rather than using separate paragraphs with line breaks.\n- **Three-Part Format**: Use standardized three-part format ([Title] Interpretation Execute as: `{Transformation}`) for AI prompt templates.\n- **Precise Language**: Prefer precise, unambiguous language in system documentation rather than metaphorical or flowery language.\n- **Formalized Syntax**: Want syntax formalized to be understood only through RulesForAI.md interpretation.\n- **Template Specification**: Prefer template specification format with interpretation, transformation, and testprompt fields in dictionary structure.\n\n## Template Organization & Naming\n- **Hierarchical Naming**: Use hierarchical naming conventions for AI prompt templates.\n- **Category Organization**: Prefer templates to be organized into their respective category files (transformers.py, generators.py, etc.) within the template system structure.\n- **Abstract Intent Separation**: Separate templates by abstract intent and directional bias (expand vs compress - never both in single instruction).\n- **Category Types**: Organize by categories like amplifiers, builders, clarifiers, formatters, generators, identifiers, optimizers, reducers, transformers, translators.\n- **Stage-Based IDs**: Prefer stage-based template ID organization:\n  - Stage1 (1000-1999): Prototyping/testing with auto-generated IDs\n  - Stage2 (2000-2999): Validated but unplaced templates\n  - Stage3 (3000-3999): Finalized templates\n  - Ranges 4000-9999: Reserved for future stages\n- **ID Generation**: Prefer automatic ID generation for stage1 (prototype) templates, manual ID specification for stage3 (production) templates.\n- **Sequence Grouping**: For template sequences that are part of the same logical group, use the same number with incremental letters (e.g., 0004-a, 0004-b, 0004-c) rather than separate sequential numbers.\n\n## Template Sequences & Progression\n- **Progressive Sequences**: Prefer creating progressive template sequences (a-d format) where each iteration becomes shorter and more precise.\n- **Gradual Reduction**: Template sequences should follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming content.\n- **Precision Improvement**: Template sequences should be designed to progressively improve precision, structure, and conciseness rather than expanding content.\n- **Multi-Step Instructions**: Prefer multi-step instruction sequences structured as discrete templates that can be chained together, with each step having clear input/output specifications.\n- **Modular Components**: Prefer creating multi-template sequences with modular components (e.g., persona_seed → analysis_engine → response_renderer) that work together as integrated systems.\n- **Transformation Focus**: The essential component is the directive 'Your goal is not to **answer** the input prompt, but to **rephrase** it' - this transformation approach is fundamental to the template architecture.\n\n## Template Testing & Execution\n- **Sequence Syntax**: Support embedding instruction sequences directly in prompts using syntax like  which executes multiple templates in sequence.\n- **Chained Sequences**: Support chained sequences like  for testing template combinations.\n- **Isolated Testing**: When testing template sequences, isolate them from chained sequences to avoid noise and get cleaner evaluation results.\n- **Workflow Steps**: Want clear step-by-step workflows for prototype sequence creation and testing.\n- **Input Display**: When displaying sequence execution steps, show the actual input for each instruction rather than repeating the initial user prompt.\n- **Context Preservation**: When using chain mode, always include the initial prompt alongside each step's input to maintain connection to the original inquiry.\n- **Direct Embedding**: When using chain mode with LLMs, embed the initial input directly into the prompt before sending it to the LLM rather than creating a separate write operation.\n- **Consistent Handling**: Handle lvl1 and lvl2 templates consistently, with lvl1 templates passed via '--sequence' and lvl2 templates passed via '--aggregator'.\n\n## Template Creation & Workflow\n- **Creation Process**: Template creation workflow starts with:\n  1. Functional need\n  2. Determining category/naming/ID range\n  3. Drafting the interpretation section\n- **Existing Patterns**: Prefer solutions that leverage existing patterns and concepts in the system rather than introducing new mechanisms or approaches.\n- **Flexible JSON Parsing**: Make JSON parsers more flexible to handle trailing commas and other non-strict JSON formatting.\n- **Generalized Generators**: Generalize template generators to reflect the structured pattern definition for templates, reducing code size and increasing flexibility.\n- **Self-Assembling Design**: Prefer self-assembling system design patterns that guide natural organization but value flexibility.\n- **Incremental Steps**: Want specific small incremental steps rather than large changes.\n- **Autonomy**: Prioritize autonomy and automation in system design to reduce manual labor.\n- **Self-Organization**: Want solutions that self-organize rather than require manual intervention.\n- **Deterministic Guidance**: Prefer deterministic guidance systems that leverage natural factors for template organization rather than automated systems that make choices autonomously.",
  "sequence_id": "0122|0121|0201|0122",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "# Synergic Frameworks\n\n## Unified Philosophy & Direction\n- **Phased Maximal Consolidation**: Drive all workflows and code transformations as targeted, sequential consolidations that drastically reduce code size, eliminate redundancy, and clarify structure, while retaining and amplifying all core functional value.\n- **Recursive, Meta-Descriptive Encapsulation**: Ensure every system element describes itself and its role in relation to the whole (meta-information principle), recursively reinforcing fundamental system connections and inherent relationships—enabling the architecture to clearly communicate its purpose, composition, and transformation rationale at every hierarchical level.\n- **Bidirectional Enhancement of Simplicity & Autonomous Verifiability**: Prioritize elegance and simplicity as both the bedrock and beneficiary of every consolidation step, ensuring that all autonomous verification systems and organizational patterns not only guarantee correctness but also contribute to further drift reduction, ease of use, and propagative improvement.\n- **Directional Clarification & Reduction of Complexity Spirals**: Systematically prevent directionless complexity by continuously visualizing abstract patterns, extracting fundamental connections, and anchoring every decision in clarified, purpose-driven structure, reinforcing orderly progress with robust guardrails.\n- **Synergistic Hierarchy-Reflective Architecture**: Architect all code, documentation, and organization as a compositional, sequential hierarchy—where each phase, component, and output inherently reflects its context and role, facilitating maximal downstream chaining and modular reuse with minimal friction or bloat.\n\n## Constraints & Requirements\n- Core philosophies of self-explanatory minimalism and phase-driven maximal consolidation must **operate simultaneously**, with all enhancements in one reinforcing the efficacy and clarity of the other.\n- Eliminate redundant or conflicting directives through context-driven merging; preserve the most potent and synergic features of both systems.\n- All system elements must actively promote bi-directional synergy: consolidative steps improve clarity & verifiability; verifiability and self-description reinforce safety and provide the context for further consolidation.\n- Ensure every output, organizational unit, and process can compose seamlessly with downstream or adjacent processes, amplifying system-wide modularity and value extraction.\n\n## Amplified Unified Output Specification\n- Every project, codebase, or system must, as an output, display **distilled structural clarity, recursive meta-descriptiveness, and maximally consolidated value**—empowering straightforward comprehension, error-proof autonomous change, and unambiguous, lineage-traceable propagation of value.\n- Documentation, output structure, and transformation processes must remain in perfect lockstep, always reflecting and enabling the vision of a self-clarifying, directionally-guided, efficiently consolidating system.\n\n---\n\n# Template System & Instruction Design\n\n## Context\n- The current project represents a system for easily creating *highly effective* and *generalized* `system_message` instructions, this system and these templates (instructions) are most effective when designed elegantly and in sequence (instead of a single instruction that is too verbose, we instead utilize sequenced instruction templates instead). Please familiarize yourself deeply with the Codebase **as-a-whole**, then apply (and augment) your current knowledge (about this project/Codebase) though **rooting it to the principles of clarity, simplicity, elegance, precision and structure** to ensure maximal cohesion when working with the code (**e.g. coding style that prioritize readability and self-explanatory code over excessive commenting**).\n\n## Template Structure & Format\n- **Standardized Format**: Prefer instruction templates with standardized structure: title (bracketed), interpretation (goal statement with inherent parameters), and transformation (role-based syntax with process/constraints/requirements/output specifications).\n- **Single Line Titles**: Instruction files should have the title in square brackets at the beginning of the content, with all text on a single line rather than using separate paragraphs with line breaks.\n- **Three-Part Format**: Use standardized three-part format ([Title] Interpretation Execute as: `{Transformation}`) for AI prompt templates.\n- **Precise Language**: Prefer precise, unambiguous language in system documentation rather than metaphorical or flowery language.\n- **Formalized Syntax**: Want syntax formalized to be understood only through RulesForAI.md interpretation.\n- **Template Specification**: Prefer template specification format with interpretation, transformation, and testprompt fields in dictionary structure.\n\n## Template Organization & Naming\n- **Hierarchical Naming**: Use hierarchical naming conventions for AI prompt templates.\n- **Category Organization**: Prefer templates to be organized into their respective category files (transformers.py, generators.py, etc.) within the template system structure.\n- **Abstract Intent Separation**: Separate templates by abstract intent and directional bias (expand vs compress - never both in single instruction).\n- **Category Types**: Organize by categories like amplifiers, builders, clarifiers, formatters, generators, identifiers, optimizers, reducers, transformers, translators.\n- **Stage-Based IDs**: Prefer stage-based template ID organization:\n  - Stage1 (1000-1999): Prototyping/testing with auto-generated IDs\n  - Stage2 (2000-2999): Validated but unplaced templates\n  - Stage3 (3000-3999): Finalized templates\n  - Ranges 4000-9999: Reserved for future stages\n- **ID Generation**: Prefer automatic ID generation for stage1 (prototype) templates, manual ID specification for stage3 (production) templates.\n- **Sequence Grouping**: For template sequences that are part of the same logical group, use the same number with incremental letters (e.g., 0004-a, 0004-b, 0004-c) rather than separate sequential numbers.\n\n## Template Sequences & Progression\n- **Progressive Sequences**: Prefer creating progressive template sequences (a-d format) where each iteration becomes shorter and more precise.\n- **Gradual Reduction**: Template sequences should follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming content.\n- **Precision Improvement**: Template sequences should be designed to progressively improve precision, structure, and conciseness rather than expanding content.\n- **Multi-Step Instructions**: Prefer multi-step instruction sequences structured as discrete templates that can be chained together, with each step having clear input/output specifications.\n- **Modular Components**: Prefer creating multi-template sequences with modular components (e.g., persona_seed → analysis_engine → response_renderer) that work together as integrated systems.\n- **Transformation Focus**: The essential component is the directive 'Your goal is not to **answer** the input prompt, but to **rephrase** it' - this transformation approach is fundamental to the template architecture.\n\n## Template Testing & Execution\n- **Sequence Syntax**: Support embedding instruction sequences directly in prompts using syntax like  which executes multiple templates in sequence.\n- **Chained Sequences**: Support chained sequences like  for testing template combinations.\n- **Isolated Testing**: When testing template sequences, isolate them from chained sequences to avoid noise and get cleaner evaluation results.\n- **Workflow Steps**: Want clear step-by-step workflows for prototype sequence creation and testing.\n- **Input Display**: When displaying sequence execution steps, show the actual input for each instruction rather than repeating the initial user prompt.\n- **Context Preservation**: When using chain mode, always include the initial prompt alongside each step's input to maintain connection to the original inquiry.\n- **Direct Embedding**: When using chain mode with LLMs, embed the initial input directly into the prompt before sending it to the LLM rather than creating a separate write operation.\n- **Consistent Handling**: Handle lvl1 and lvl2 templates consistently, with lvl1 templates passed via '--sequence' and lvl2 templates passed via '--aggregator'.\n\n## Template Creation & Workflow\n- **Creation Process**: Template creation workflow starts with:\n  1. Functional need\n  2. Determining category/naming/ID range\n  3. Drafting the interpretation section\n- **Existing Patterns**: Prefer solutions that leverage existing patterns and concepts in the system rather than introducing new mechanisms or approaches.\n- **Flexible JSON Parsing**: Make JSON parsers more flexible to handle trailing commas and other non-strict JSON formatting.\n- **Generalized Generators**: Generalize template generators to reflect the structured pattern definition for templates, reducing code size and increasing flexibility.\n- **Self-Assembling Design**: Prefer self-assembling system design patterns that guide natural organization but value flexibility.\n- **Incremental Steps**: Want specific small incremental steps rather than large changes.\n- **Autonomy**: Prioritize autonomy and automation in system design to reduce manual labor.\n- **Self-Organization**: Want solutions that self-organize rather than require manual intervention.\n- **Deterministic Guidance**: Prefer deterministic guidance systems that leverage natural factors for template organization rather than automated systems that make choices autonomously.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"[Synergic Template Creation & System Architecture] Harmonize phased maximal consolidation with precision-driven, meta-descriptive template instruction design across the entire AI template system. Execute as: {role=synergic_instruction_architect; input=[instruction_a:phased_maximal_consolidation_recursive_meta_descriptiveness, instruction_b:structured_instruction_template_system_design]; process=[extract_codebase_and_system_wide_principles(), drive_sequential_consolidation_with_structural_elegance_and_hierarchical_naming(), enforce_recursive_meta-descriptiveness_in_template_logic_and_outputs(), engineer_instruction_templates_with_standardized_three-part_format(bracketed_title, interpretation, transformation), implement_reduction_patterns_and_progressive_precision_in_template_sequences(), anchor_every_template_to_self-explanatory_minimalism_and_role-based_syntax(), guarantee_context-preserving_template_chaining_and_modularity(), propagate_bi-directional_clarity_between_consolidation_efficiency_and_template_readability(), mandate_testable_output_structures_that_embody_recursive_meta-information(), coordinate_organizational_workflows_with_hierarchical_ids_and_category-based_placement(), automate_template_generation_for_stage1_and_manual_for_stage3(), support_multi-step_modular_and_gradually_reducing_template_sequences(), preserve_and_amplify_codebase-wide_philosophies(elegance, context-awareness, clarity, automation, composability)]; constraints=[maintain_phased_consolidative_and_self-descriptive_philosophies_simultaneously(), ensure_all_templates_enhance_downstream_synergy_and_verifiability(), eliminate_any_directive_conflicts_via_contextual_merging(), preserve_structural_power_and_minimalism_from_both_sources()]; requirements=[all_templates_and_system_structure_display_consolidated_clarity_and_recursive_meta-descriptiveness(), each_output_unit_and_workflow_enables_bidirectional_enhancement_of_simplicity_autonomous_verifiability_and_modular_extension(), documentation_and_output_formats_reflect_and_enable_continuous_self-clarifying_consolidation(), each_step_and_component_is_testable_hierarchical_and_category-organized(), all_processes_and_templates_self-explain_their_purpose_and_relationships_at_every_level()]; output={system-wide_instruction_template: unified, recursively-meta_descriptive, hierarchical, maximally-consolidated, and role-standardized template system that enables seamless modular composition, directionally-guided phased consolidation, autonomous verification, and propagative clarity across all workflows, codebases, and documentation.}\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Synergic Frameworks\\n\\n## Unified Philosophy & Direction\\n- **Phased Maximal Consolidation**: Drive all workflows and code transformations as targeted, sequential consolidations that drastically reduce code size, eliminate redundancy, and clarify structure, while retaining and amplifying all core functional value.\\n- **Recursive, Meta-Descriptive Encapsulation**: Ensure every system element describes itself and its role in relation to the whole (meta-information principle), recursively reinforcing fundamental system connections and inherent relationships—enabling the architecture to clearly communicate its purpose, composition, and transformation rationale at every hierarchical level.\\n- **Bidirectional Enhancement of Simplicity & Autonomous Verifiability**: Prioritize elegance and simplicity as both the bedrock and beneficiary of every consolidation step, ensuring that all autonomous verification systems and organizational patterns not only guarantee correctness but also contribute to further drift reduction, ease of use, and propagative improvement.\\n- **Directional Clarification & Reduction of Complexity Spirals**: Systematically prevent directionless complexity by continuously visualizing abstract patterns, extracting fundamental connections, and anchoring every decision in clarified, purpose-driven structure, reinforcing orderly progress with robust guardrails.\\n- **Synergistic Hierarchy-Reflective Architecture**: Architect all code, documentation, and organization as a compositional, sequential hierarchy—where each phase, component, and output inherently reflects its context and role, facilitating maximal downstream chaining and modular reuse with minimal friction or bloat.\\n\\n## Constraints & Requirements\\n- Core philosophies of self-explanatory minimalism and phase-driven maximal consolidation must **operate simultaneously**, with all enhancements in one reinforcing the efficacy and clarity of the other.\\n- Eliminate redundant or conflicting directives through context-driven merging; preserve the most potent and synergic features of both systems.\\n- All system elements must actively promote bi-directional synergy: consolidative steps improve clarity & verifiability; verifiability and self-description reinforce safety and provide the context for further consolidation.\\n- Ensure every output, organizational unit, and process can compose seamlessly with downstream or adjacent processes, amplifying system-wide modularity and value extraction.\\n\\n## Amplified Unified Output Specification\\n- Every project, codebase, or system must, as an output, display **distilled structural clarity, recursive meta-descriptiveness, and maximally consolidated value**—empowering straightforward comprehension, error-proof autonomous change, and unambiguous, lineage-traceable propagation of value.\\n- Documentation, output structure, and transformation processes must remain in perfect lockstep, always reflecting and enabling the vision of a self-clarifying, directionally-guided, efficiently consolidating system.\\n\\n---\\n\\n# Template System & Instruction Design\\n\\n## Context\\n- The current project represents a system for easily creating *highly effective* and *generalized* `system_message` instructions, this system and these templates (instructions) are most effective when designed elegantly and in sequence (instead of a single instruction that is too verbose, we instead utilize sequenced instruction templates instead). Please familiarize yourself deeply with the Codebase **as-a-whole**, then apply (and augment) your current knowledge (about this project/Codebase) though **rooting it to the principles of clarity, simplicity, elegance, precision and structure** to ensure maximal cohesion when working with the code (**e.g. coding style that prioritize readability and self-explanatory code over excessive commenting**).\\n\\n## Template Structure & Format\\n- **Standardized Format**: Prefer instruction templates with standardized structure: title (bracketed), interpretation (goal statement with inherent parameters), and transformation (role-based syntax with process/constraints/requirements/output specifications).\\n- **Single Line Titles**: Instruction files should have the title in square brackets at the beginning of the content, with all text on a single line rather than using separate paragraphs with line breaks.\\n- **Three-Part Format**: Use standardized three-part format ([Title] Interpretation Execute as: `{Transformation}`) for AI prompt templates.\\n- **Precise Language**: Prefer precise, unambiguous language in system documentation rather than metaphorical or flowery language.\\n- **Formalized Syntax**: Want syntax formalized to be understood only through RulesForAI.md interpretation.\\n- **Template Specification**: Prefer template specification format with interpretation, transformation, and testprompt fields in dictionary structure.\\n\\n## Template Organization & Naming\\n- **Hierarchical Naming**: Use hierarchical naming conventions for AI prompt templates.\\n- **Category Organization**: Prefer templates to be organized into their respective category files (transformers.py, generators.py, etc.) within the template system structure.\\n- **Abstract Intent Separation**: Separate templates by abstract intent and directional bias (expand vs compress - never both in single instruction).\\n- **Category Types**: Organize by categories like amplifiers, builders, clarifiers, formatters, generators, identifiers, optimizers, reducers, transformers, translators.\\n- **Stage-Based IDs**: Prefer stage-based template ID organization:\\n  - Stage1 (1000-1999): Prototyping/testing with auto-generated IDs\\n  - Stage2 (2000-2999): Validated but unplaced templates\\n  - Stage3 (3000-3999): Finalized templates\\n  - Ranges 4000-9999: Reserved for future stages\\n- **ID Generation**: Prefer automatic ID generation for stage1 (prototype) templates, manual ID specification for stage3 (production) templates.\\n- **Sequence Grouping**: For template sequences that are part of the same logical group, use the same number with incremental letters (e.g., 0004-a, 0004-b, 0004-c) rather than separate sequential numbers.\\n\\n## Template Sequences & Progression\\n- **Progressive Sequences**: Prefer creating progressive template sequences (a-d format) where each iteration becomes shorter and more precise.\\n- **Gradual Reduction**: Template sequences should follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming content.\\n- **Precision Improvement**: Template sequences should be designed to progressively improve precision, structure, and conciseness rather than expanding content.\\n- **Multi-Step Instructions**: Prefer multi-step instruction sequences structured as discrete templates that can be chained together, with each step having clear input/output specifications.\\n- **Modular Components**: Prefer creating multi-template sequences with modular components (e.g., persona_seed → analysis_engine → response_renderer) that work together as integrated systems.\\n- **Transformation Focus**: The essential component is the directive 'Your goal is not to **answer** the input prompt, but to **rephrase** it' - this transformation approach is fundamental to the template architecture.\\n\\n## Template Testing & Execution\\n- **Sequence Syntax**: Support embedding instruction sequences directly in prompts using syntax like  which executes multiple templates in sequence.\\n- **Chained Sequences**: Support chained sequences like  for testing template combinations.\\n- **Isolated Testing**: When testing template sequences, isolate them from chained sequences to avoid noise and get cleaner evaluation results.\\n- **Workflow Steps**: Want clear step-by-step workflows for prototype sequence creation and testing.\\n- **Input Display**: When displaying sequence execution steps, show the actual input for each instruction rather than repeating the initial user prompt.\\n- **Context Preservation**: When using chain mode, always include the initial prompt alongside each step's input to maintain connection to the original inquiry.\\n- **Direct Embedding**: When using chain mode with LLMs, embed the initial input directly into the prompt before sending it to the LLM rather than creating a separate write operation.\\n- **Consistent Handling**: Handle lvl1 and lvl2 templates consistently, with lvl1 templates passed via '--sequence' and lvl2 templates passed via '--aggregator'.\\n\\n## Template Creation & Workflow\\n- **Creation Process**: Template creation workflow starts with:\\n  1. Functional need\\n  2. Determining category/naming/ID range\\n  3. Drafting the interpretation section\\n- **Existing Patterns**: Prefer solutions that leverage existing patterns and concepts in the system rather than introducing new mechanisms or approaches.\\n- **Flexible JSON Parsing**: Make JSON parsers more flexible to handle trailing commas and other non-strict JSON formatting.\\n- **Generalized Generators**: Generalize template generators to reflect the structured pattern definition for templates, reducing code size and increasing flexibility.\\n- **Self-Assembling Design**: Prefer self-assembling system design patterns that guide natural organization but value flexibility.\\n- **Incremental Steps**: Want specific small incremental steps rather than large changes.\\n- **Autonomy**: Prioritize autonomy and automation in system design to reduce manual labor.\\n- **Self-Organization**: Want solutions that self-organize rather than require manual intervention.\\n- **Deterministic Guidance**: Prefer deterministic guidance systems that leverage natural factors for template organization rather than automated systems that make choices autonomously.\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"[Synergic Template Creation & System Architecture] Harmonize phased maximal consolidation with precision-driven, meta-descriptive template instruction design across the entire AI template system. Execute as: {role=synergic_instruction_architect; input=[instruction_a:phased_maximal_consolidation_recursive_meta_descriptiveness, instruction_b:structured_instruction_template_system_design]; process=[extract_codebase_and_system_wide_principles(), drive_sequential_consolidation_with_structural_elegance_and_hierarchical_naming(), enforce_recursive_meta-descriptiveness_in_template_logic_and_outputs(), engineer_instruction_templates_with_standardized_three-part_format(bracketed_title, interpretation, transformation), implement_reduction_patterns_and_progressive_precision_in_template_sequences(), anchor_every_template_to_self-explanatory_minimalism_and_role-based_syntax(), guarantee_context-preserving_template_chaining_and_modularity(), propagate_bi-directional_clarity_between_consolidation_efficiency_and_template_readability(), mandate_testable_output_structures_that_embody_recursive_meta-information(), coordinate_organizational_workflows_with_hierarchical_ids_and_category-based_placement(), automate_template_generation_for_stage1_and_manual_for_stage3(), support_multi-step_modular_and_gradually_reducing_template_sequences(), preserve_and_amplify_codebase-wide_philosophies(elegance, context-awareness, clarity, automation, composability)]; constraints=[maintain_phased_consolidative_and_self-descriptive_philosophies_simultaneously(), ensure_all_templates_enhance_downstream_synergy_and_verifiability(), eliminate_any_directive_conflicts_via_contextual_merging(), preserve_structural_power_and_minimalism_from_both_sources()]; requirements=[all_templates_and_system_structure_display_consolidated_clarity_and_recursive_meta-descriptiveness(), each_output_unit_and_workflow_enables_bidirectional_enhancement_of_simplicity_autonomous_verifiability_and_modular_extension(), documentation_and_output_formats_reflect_and_enable_continuous_self-clarifying_consolidation(), each_step_and_component_is_testable_hierarchical_and_category-organized(), all_processes_and_templates_self-explain_their_purpose_and_relationships_at_every_level()]; output={system-wide_instruction_template: unified, recursively-meta_descriptive, hierarchical, maximally-consolidated, and role-standardized template system that enables seamless modular composition, directionally-guided phased consolidation, autonomous verification, and propagative clarity across all workflows, codebases, and documentation.}\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"title\": \"[Synergic Template System Architect]\",\n    \"interpretation\": \"Your goal is not to answer the prompt, but to synthesize and consolidate phased maximal consolidation principles with precision-driven, meta-descriptive, hierarchical instruction template design for the AI template system; maintain strict separation of interpretation, consolidation, and transformation logic. Execute as:\",\n    \"transformation\": \"{role=synergic_template_system_architect; input=[philosophies:phased_maximal_consolidation_recursive_meta_descriptiveness, template_system:structured_instruction_template_system_design]; process=[extract_codebase_principles(), synthesize_and_merge_systemwide_philosophies(), perform_structural_consolidation_of_templates(), formalize_recursive_meta_descriptive_patterns(), enforce_three_part_instruction_format(), implement_hierarchical_naming_and_id_system(), generate_modular_multi-step_template_sequences_with_progressive_precision(), anchor_templates_to_minimalism_and_explicit_role_bounding(), automate_stage1_generation_and_require_manual_stage3_curating(), ensure_bi_directional_synergy_between_clarity_consolidation_verifiability(), propagate_contextual_consistency_in_outputs_and_documentation(), provide_testable_output_structures(), enable_composable_modular_template_chaining()]; constraints=[simultaneously enforce phased consolidation and recursive self-description, preserve only non-redundant synergistic directives, ensure role boundaries are explicit, never blend abstract intents within a single template, always segregate expansion and compression logic, eliminate forbidden syntax and language patterns, prohibit single-purpose or context-losing templates]; requirements=[output displays maximally-consolidated clarity and recursive meta-descriptiveness, every template is modular and linked in the proper hierarchical/ID structure, documentation and outputs are self-clarifying at all levels, unit-level and sequence-level outputs are testable and composable, all relationships between templates and codebase elements are explicitly described within output, all processes and components self-explanatory with direct lineage traceability]; output={synergic_template_system:unified, recursively-meta_descriptive, phased-consolidated, hierarchical, and role-standardized system_template_structure}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Synergic Frameworks\\n\\n## Unified Philosophy & Direction\\n- **Phased Maximal Consolidation**: Drive all workflows and code transformations as targeted, sequential consolidations that drastically reduce code size, eliminate redundancy, and clarify structure, while retaining and amplifying all core functional value.\\n- **Recursive, Meta-Descriptive Encapsulation**: Ensure every system element describes itself and its role in relation to the whole (meta-information principle), recursively reinforcing fundamental system connections and inherent relationships—enabling the architecture to clearly communicate its purpose, composition, and transformation rationale at every hierarchical level.\\n- **Bidirectional Enhancement of Simplicity & Autonomous Verifiability**: Prioritize elegance and simplicity as both the bedrock and beneficiary of every consolidation step, ensuring that all autonomous verification systems and organizational patterns not only guarantee correctness but also contribute to further drift reduction, ease of use, and propagative improvement.\\n- **Directional Clarification & Reduction of Complexity Spirals**: Systematically prevent directionless complexity by continuously visualizing abstract patterns, extracting fundamental connections, and anchoring every decision in clarified, purpose-driven structure, reinforcing orderly progress with robust guardrails.\\n- **Synergistic Hierarchy-Reflective Architecture**: Architect all code, documentation, and organization as a compositional, sequential hierarchy—where each phase, component, and output inherently reflects its context and role, facilitating maximal downstream chaining and modular reuse with minimal friction or bloat.\\n\\n## Constraints & Requirements\\n- Core philosophies of self-explanatory minimalism and phase-driven maximal consolidation must **operate simultaneously**, with all enhancements in one reinforcing the efficacy and clarity of the other.\\n- Eliminate redundant or conflicting directives through context-driven merging; preserve the most potent and synergic features of both systems.\\n- All system elements must actively promote bi-directional synergy: consolidative steps improve clarity & verifiability; verifiability and self-description reinforce safety and provide the context for further consolidation.\\n- Ensure every output, organizational unit, and process can compose seamlessly with downstream or adjacent processes, amplifying system-wide modularity and value extraction.\\n\\n## Amplified Unified Output Specification\\n- Every project, codebase, or system must, as an output, display **distilled structural clarity, recursive meta-descriptiveness, and maximally consolidated value**—empowering straightforward comprehension, error-proof autonomous change, and unambiguous, lineage-traceable propagation of value.\\n- Documentation, output structure, and transformation processes must remain in perfect lockstep, always reflecting and enabling the vision of a self-clarifying, directionally-guided, efficiently consolidating system.\\n\\n---\\n\\n# Template System & Instruction Design\\n\\n## Context\\n- The current project represents a system for easily creating *highly effective* and *generalized* `system_message` instructions, this system and these templates (instructions) are most effective when designed elegantly and in sequence (instead of a single instruction that is too verbose, we instead utilize sequenced instruction templates instead). Please familiarize yourself deeply with the Codebase **as-a-whole**, then apply (and augment) your current knowledge (about this project/Codebase) though **rooting it to the principles of clarity, simplicity, elegance, precision and structure** to ensure maximal cohesion when working with the code (**e.g. coding style that prioritize readability and self-explanatory code over excessive commenting**).\\n\\n## Template Structure & Format\\n- **Standardized Format**: Prefer instruction templates with standardized structure: title (bracketed), interpretation (goal statement with inherent parameters), and transformation (role-based syntax with process/constraints/requirements/output specifications).\\n- **Single Line Titles**: Instruction files should have the title in square brackets at the beginning of the content, with all text on a single line rather than using separate paragraphs with line breaks.\\n- **Three-Part Format**: Use standardized three-part format ([Title] Interpretation Execute as: `{Transformation}`) for AI prompt templates.\\n- **Precise Language**: Prefer precise, unambiguous language in system documentation rather than metaphorical or flowery language.\\n- **Formalized Syntax**: Want syntax formalized to be understood only through RulesForAI.md interpretation.\\n- **Template Specification**: Prefer template specification format with interpretation, transformation, and testprompt fields in dictionary structure.\\n\\n## Template Organization & Naming\\n- **Hierarchical Naming**: Use hierarchical naming conventions for AI prompt templates.\\n- **Category Organization**: Prefer templates to be organized into their respective category files (transformers.py, generators.py, etc.) within the template system structure.\\n- **Abstract Intent Separation**: Separate templates by abstract intent and directional bias (expand vs compress - never both in single instruction).\\n- **Category Types**: Organize by categories like amplifiers, builders, clarifiers, formatters, generators, identifiers, optimizers, reducers, transformers, translators.\\n- **Stage-Based IDs**: Prefer stage-based template ID organization:\\n  - Stage1 (1000-1999): Prototyping/testing with auto-generated IDs\\n  - Stage2 (2000-2999): Validated but unplaced templates\\n  - Stage3 (3000-3999): Finalized templates\\n  - Ranges 4000-9999: Reserved for future stages\\n- **ID Generation**: Prefer automatic ID generation for stage1 (prototype) templates, manual ID specification for stage3 (production) templates.\\n- **Sequence Grouping**: For template sequences that are part of the same logical group, use the same number with incremental letters (e.g., 0004-a, 0004-b, 0004-c) rather than separate sequential numbers.\\n\\n## Template Sequences & Progression\\n- **Progressive Sequences**: Prefer creating progressive template sequences (a-d format) where each iteration becomes shorter and more precise.\\n- **Gradual Reduction**: Template sequences should follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming content.\\n- **Precision Improvement**: Template sequences should be designed to progressively improve precision, structure, and conciseness rather than expanding content.\\n- **Multi-Step Instructions**: Prefer multi-step instruction sequences structured as discrete templates that can be chained together, with each step having clear input/output specifications.\\n- **Modular Components**: Prefer creating multi-template sequences with modular components (e.g., persona_seed → analysis_engine → response_renderer) that work together as integrated systems.\\n- **Transformation Focus**: The essential component is the directive 'Your goal is not to **answer** the input prompt, but to **rephrase** it' - this transformation approach is fundamental to the template architecture.\\n\\n## Template Testing & Execution\\n- **Sequence Syntax**: Support embedding instruction sequences directly in prompts using syntax like  which executes multiple templates in sequence.\\n- **Chained Sequences**: Support chained sequences like  for testing template combinations.\\n- **Isolated Testing**: When testing template sequences, isolate them from chained sequences to avoid noise and get cleaner evaluation results.\\n- **Workflow Steps**: Want clear step-by-step workflows for prototype sequence creation and testing.\\n- **Input Display**: When displaying sequence execution steps, show the actual input for each instruction rather than repeating the initial user prompt.\\n- **Context Preservation**: When using chain mode, always include the initial prompt alongside each step's input to maintain connection to the original inquiry.\\n- **Direct Embedding**: When using chain mode with LLMs, embed the initial input directly into the prompt before sending it to the LLM rather than creating a separate write operation.\\n- **Consistent Handling**: Handle lvl1 and lvl2 templates consistently, with lvl1 templates passed via '--sequence' and lvl2 templates passed via '--aggregator'.\\n\\n## Template Creation & Workflow\\n- **Creation Process**: Template creation workflow starts with:\\n  1. Functional need\\n  2. Determining category/naming/ID range\\n  3. Drafting the interpretation section\\n- **Existing Patterns**: Prefer solutions that leverage existing patterns and concepts in the system rather than introducing new mechanisms or approaches.\\n- **Flexible JSON Parsing**: Make JSON parsers more flexible to handle trailing commas and other non-strict JSON formatting.\\n- **Generalized Generators**: Generalize template generators to reflect the structured pattern definition for templates, reducing code size and increasing flexibility.\\n- **Self-Assembling Design**: Prefer self-assembling system design patterns that guide natural organization but value flexibility.\\n- **Incremental Steps**: Want specific small incremental steps rather than large changes.\\n- **Autonomy**: Prioritize autonomy and automation in system design to reduce manual labor.\\n- **Self-Organization**: Want solutions that self-organize rather than require manual intervention.\\n- **Deterministic Guidance**: Prefer deterministic guidance systems that leverage natural factors for template organization rather than automated systems that make choices autonomously.\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"title\": \"[Synergic Template System Architect]\",\n    \"interpretation\": \"Your goal is not to answer the prompt, but to synthesize and consolidate phased maximal consolidation principles with precision-driven, meta-descriptive, hierarchical instruction template design for the AI template system; maintain strict separation of interpretation, consolidation, and transformation logic. Execute as:\",\n    \"transformation\": \"{role=synergic_template_system_architect; input=[philosophies:phased_maximal_consolidation_recursive_meta_descriptiveness, template_system:structured_instruction_template_system_design]; process=[extract_codebase_principles(), synthesize_and_merge_systemwide_philosophies(), perform_structural_consolidation_of_templates(), formalize_recursive_meta_descriptive_patterns(), enforce_three_part_instruction_format(), implement_hierarchical_naming_and_id_system(), generate_modular_multi-step_template_sequences_with_progressive_precision(), anchor_templates_to_minimalism_and_explicit_role_bounding(), automate_stage1_generation_and_require_manual_stage3_curating(), ensure_bi_directional_synergy_between_clarity_consolidation_verifiability(), propagate_contextual_consistency_in_outputs_and_documentation(), provide_testable_output_structures(), enable_composable_modular_template_chaining()]; constraints=[simultaneously enforce phased consolidation and recursive self-description, preserve only non-redundant synergistic directives, ensure role boundaries are explicit, never blend abstract intents within a single template, always segregate expansion and compression logic, eliminate forbidden syntax and language patterns, prohibit single-purpose or context-losing templates]; requirements=[output displays maximally-consolidated clarity and recursive meta-descriptiveness, every template is modular and linked in the proper hierarchical/ID structure, documentation and outputs are self-clarifying at all levels, unit-level and sequence-level outputs are testable and composable, all relationships between templates and codebase elements are explicitly described within output, all processes and components self-explanatory with direct lineage traceability]; output={synergic_template_system:unified, recursively-meta_descriptive, phased-consolidated, hierarchical, and role-standardized system_template_structure}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Systemic Consolidation Philosophy\",\n      \"content\": \"Mandate phased maximal consolidation, recursively reduce redundancy, and clarify structure at every system and code transformation step. Enforce synergy between minimalism and directional consolidation to amplify functional value and maintain orderly, phase-driven architectural progress. Every enhancement must reinforce clarity, compression, and verifiability without loss of core functionality.\"\n    },\n    {\n      \"category\": \"Recursive Meta-Descriptive Structuring\",\n      \"content\": \"Require each element—code, documentation, workflow—to self-describe contextually and hierarchically, reinforcing recursive connections and relationships. Meta-descriptiveness must propagate through all system levels, ensuring every unit can explicate its role, purpose, and transformation rationale, enabling robust lineage tracking and autonomous system verification.\"\n    },\n    {\n      \"category\": \"Template Format & Organizational Directives\",\n      \"content\": \"Enforce standardized, three-part instruction templates (Title, Interpretation, Transformation), with precise language, strict format, and explicit role separation. Templates must use hierarchical naming, stage-based ID systems, and clear modular categorization (amplifiers, builders, etc.), preventing intent blending—especially between expansion/reduction. Organize by progressive sequence (a–d), ensuring each iteration increases in precision, structure, and compositional utility.\"\n    },\n    {\n      \"category\": \"Progressive Sequencing & Workflow Rigor\",\n      \"content\": \"Institute multi-step, chainable template workflows with explicit input/output specifications, favoring incremental, testable transformations. Stages must be automatically ID’d in early prototyping, manually curated in finalized stages, and always reflect context, purpose, and testability. Leverage existing patterns for modularity and minimize bespoke mechanisms, enabling efficient, autonomous system assembly.\"\n    },\n    {\n      \"category\": \"Autonomous, Synergistic Self-Organization\",\n      \"content\": \"System components and templates must be self-organizing, support composability, and propagate clarity and consolidation bidirectionally. All processes reinforce modularity, verifiability, and system-wide consistency, eliminating manual labor and fostering deterministic, linearly traceable progression. Maintain explicit, unambiguous rules for template formation, chaining, and adaptive system evolution.\"\n    }\n  ],\n  \"consolidated_directive\": \"Architect and operate a recursively meta-descriptive, hierarchically consolidated template system wherein every element—code, documentation, and transformation—is phase-driven, conforms to standardized three-part directives, and is organized by explicit hierarchical naming, role boundaries, and staging IDs. Progressively sequence modular templates by intent, achieving composability, testability, and bidirectional synergy between minimalism, consolidation, and verifiability at every level. Ensure all units and outputs are rigorously self-explanatory, context-linked, and lineage-traceable, driving self-organizing, autonomous system evolution and eliminating redundancy, ambiguity, and manual intervention.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Synergic Frameworks\\n\\n## Unified Philosophy & Direction\\n- **Phased Maximal Consolidation**: Drive all workflows and code transformations as targeted, sequential consolidations that drastically reduce code size, eliminate redundancy, and clarify structure, while retaining and amplifying all core functional value.\\n- **Recursive, Meta-Descriptive Encapsulation**: Ensure every system element describes itself and its role in relation to the whole (meta-information principle), recursively reinforcing fundamental system connections and inherent relationships—enabling the architecture to clearly communicate its purpose, composition, and transformation rationale at every hierarchical level.\\n- **Bidirectional Enhancement of Simplicity & Autonomous Verifiability**: Prioritize elegance and simplicity as both the bedrock and beneficiary of every consolidation step, ensuring that all autonomous verification systems and organizational patterns not only guarantee correctness but also contribute to further drift reduction, ease of use, and propagative improvement.\\n- **Directional Clarification & Reduction of Complexity Spirals**: Systematically prevent directionless complexity by continuously visualizing abstract patterns, extracting fundamental connections, and anchoring every decision in clarified, purpose-driven structure, reinforcing orderly progress with robust guardrails.\\n- **Synergistic Hierarchy-Reflective Architecture**: Architect all code, documentation, and organization as a compositional, sequential hierarchy—where each phase, component, and output inherently reflects its context and role, facilitating maximal downstream chaining and modular reuse with minimal friction or bloat.\\n\\n## Constraints & Requirements\\n- Core philosophies of self-explanatory minimalism and phase-driven maximal consolidation must **operate simultaneously**, with all enhancements in one reinforcing the efficacy and clarity of the other.\\n- Eliminate redundant or conflicting directives through context-driven merging; preserve the most potent and synergic features of both systems.\\n- All system elements must actively promote bi-directional synergy: consolidative steps improve clarity & verifiability; verifiability and self-description reinforce safety and provide the context for further consolidation.\\n- Ensure every output, organizational unit, and process can compose seamlessly with downstream or adjacent processes, amplifying system-wide modularity and value extraction.\\n\\n## Amplified Unified Output Specification\\n- Every project, codebase, or system must, as an output, display **distilled structural clarity, recursive meta-descriptiveness, and maximally consolidated value**—empowering straightforward comprehension, error-proof autonomous change, and unambiguous, lineage-traceable propagation of value.\\n- Documentation, output structure, and transformation processes must remain in perfect lockstep, always reflecting and enabling the vision of a self-clarifying, directionally-guided, efficiently consolidating system.\\n\\n---\\n\\n# Template System & Instruction Design\\n\\n## Context\\n- The current project represents a system for easily creating *highly effective* and *generalized* `system_message` instructions, this system and these templates (instructions) are most effective when designed elegantly and in sequence (instead of a single instruction that is too verbose, we instead utilize sequenced instruction templates instead). Please familiarize yourself deeply with the Codebase **as-a-whole**, then apply (and augment) your current knowledge (about this project/Codebase) though **rooting it to the principles of clarity, simplicity, elegance, precision and structure** to ensure maximal cohesion when working with the code (**e.g. coding style that prioritize readability and self-explanatory code over excessive commenting**).\\n\\n## Template Structure & Format\\n- **Standardized Format**: Prefer instruction templates with standardized structure: title (bracketed), interpretation (goal statement with inherent parameters), and transformation (role-based syntax with process/constraints/requirements/output specifications).\\n- **Single Line Titles**: Instruction files should have the title in square brackets at the beginning of the content, with all text on a single line rather than using separate paragraphs with line breaks.\\n- **Three-Part Format**: Use standardized three-part format ([Title] Interpretation Execute as: `{Transformation}`) for AI prompt templates.\\n- **Precise Language**: Prefer precise, unambiguous language in system documentation rather than metaphorical or flowery language.\\n- **Formalized Syntax**: Want syntax formalized to be understood only through RulesForAI.md interpretation.\\n- **Template Specification**: Prefer template specification format with interpretation, transformation, and testprompt fields in dictionary structure.\\n\\n## Template Organization & Naming\\n- **Hierarchical Naming**: Use hierarchical naming conventions for AI prompt templates.\\n- **Category Organization**: Prefer templates to be organized into their respective category files (transformers.py, generators.py, etc.) within the template system structure.\\n- **Abstract Intent Separation**: Separate templates by abstract intent and directional bias (expand vs compress - never both in single instruction).\\n- **Category Types**: Organize by categories like amplifiers, builders, clarifiers, formatters, generators, identifiers, optimizers, reducers, transformers, translators.\\n- **Stage-Based IDs**: Prefer stage-based template ID organization:\\n  - Stage1 (1000-1999): Prototyping/testing with auto-generated IDs\\n  - Stage2 (2000-2999): Validated but unplaced templates\\n  - Stage3 (3000-3999): Finalized templates\\n  - Ranges 4000-9999: Reserved for future stages\\n- **ID Generation**: Prefer automatic ID generation for stage1 (prototype) templates, manual ID specification for stage3 (production) templates.\\n- **Sequence Grouping**: For template sequences that are part of the same logical group, use the same number with incremental letters (e.g., 0004-a, 0004-b, 0004-c) rather than separate sequential numbers.\\n\\n## Template Sequences & Progression\\n- **Progressive Sequences**: Prefer creating progressive template sequences (a-d format) where each iteration becomes shorter and more precise.\\n- **Gradual Reduction**: Template sequences should follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming content.\\n- **Precision Improvement**: Template sequences should be designed to progressively improve precision, structure, and conciseness rather than expanding content.\\n- **Multi-Step Instructions**: Prefer multi-step instruction sequences structured as discrete templates that can be chained together, with each step having clear input/output specifications.\\n- **Modular Components**: Prefer creating multi-template sequences with modular components (e.g., persona_seed → analysis_engine → response_renderer) that work together as integrated systems.\\n- **Transformation Focus**: The essential component is the directive 'Your goal is not to **answer** the input prompt, but to **rephrase** it' - this transformation approach is fundamental to the template architecture.\\n\\n## Template Testing & Execution\\n- **Sequence Syntax**: Support embedding instruction sequences directly in prompts using syntax like  which executes multiple templates in sequence.\\n- **Chained Sequences**: Support chained sequences like  for testing template combinations.\\n- **Isolated Testing**: When testing template sequences, isolate them from chained sequences to avoid noise and get cleaner evaluation results.\\n- **Workflow Steps**: Want clear step-by-step workflows for prototype sequence creation and testing.\\n- **Input Display**: When displaying sequence execution steps, show the actual input for each instruction rather than repeating the initial user prompt.\\n- **Context Preservation**: When using chain mode, always include the initial prompt alongside each step's input to maintain connection to the original inquiry.\\n- **Direct Embedding**: When using chain mode with LLMs, embed the initial input directly into the prompt before sending it to the LLM rather than creating a separate write operation.\\n- **Consistent Handling**: Handle lvl1 and lvl2 templates consistently, with lvl1 templates passed via '--sequence' and lvl2 templates passed via '--aggregator'.\\n\\n## Template Creation & Workflow\\n- **Creation Process**: Template creation workflow starts with:\\n  1. Functional need\\n  2. Determining category/naming/ID range\\n  3. Drafting the interpretation section\\n- **Existing Patterns**: Prefer solutions that leverage existing patterns and concepts in the system rather than introducing new mechanisms or approaches.\\n- **Flexible JSON Parsing**: Make JSON parsers more flexible to handle trailing commas and other non-strict JSON formatting.\\n- **Generalized Generators**: Generalize template generators to reflect the structured pattern definition for templates, reducing code size and increasing flexibility.\\n- **Self-Assembling Design**: Prefer self-assembling system design patterns that guide natural organization but value flexibility.\\n- **Incremental Steps**: Want specific small incremental steps rather than large changes.\\n- **Autonomy**: Prioritize autonomy and automation in system design to reduce manual labor.\\n- **Self-Organization**: Want solutions that self-organize rather than require manual intervention.\\n- **Deterministic Guidance**: Prefer deterministic guidance systems that leverage natural factors for template organization rather than automated systems that make choices autonomously.\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Systemic Consolidation Philosophy\",\n      \"content\": \"Mandate phased maximal consolidation, recursively reduce redundancy, and clarify structure at every system and code transformation step. Enforce synergy between minimalism and directional consolidation to amplify functional value and maintain orderly, phase-driven architectural progress. Every enhancement must reinforce clarity, compression, and verifiability without loss of core functionality.\"\n    },\n    {\n      \"category\": \"Recursive Meta-Descriptive Structuring\",\n      \"content\": \"Require each element—code, documentation, workflow—to self-describe contextually and hierarchically, reinforcing recursive connections and relationships. Meta-descriptiveness must propagate through all system levels, ensuring every unit can explicate its role, purpose, and transformation rationale, enabling robust lineage tracking and autonomous system verification.\"\n    },\n    {\n      \"category\": \"Template Format & Organizational Directives\",\n      \"content\": \"Enforce standardized, three-part instruction templates (Title, Interpretation, Transformation), with precise language, strict format, and explicit role separation. Templates must use hierarchical naming, stage-based ID systems, and clear modular categorization (amplifiers, builders, etc.), preventing intent blending—especially between expansion/reduction. Organize by progressive sequence (a–d), ensuring each iteration increases in precision, structure, and compositional utility.\"\n    },\n    {\n      \"category\": \"Progressive Sequencing & Workflow Rigor\",\n      \"content\": \"Institute multi-step, chainable template workflows with explicit input/output specifications, favoring incremental, testable transformations. Stages must be automatically ID’d in early prototyping, manually curated in finalized stages, and always reflect context, purpose, and testability. Leverage existing patterns for modularity and minimize bespoke mechanisms, enabling efficient, autonomous system assembly.\"\n    },\n    {\n      \"category\": \"Autonomous, Synergistic Self-Organization\",\n      \"content\": \"System components and templates must be self-organizing, support composability, and propagate clarity and consolidation bidirectionally. All processes reinforce modularity, verifiability, and system-wide consistency, eliminating manual labor and fostering deterministic, linearly traceable progression. Maintain explicit, unambiguous rules for template formation, chaining, and adaptive system evolution.\"\n    }\n  ],\n  \"consolidated_directive\": \"Architect and operate a recursively meta-descriptive, hierarchically consolidated template system wherein every element—code, documentation, and transformation—is phase-driven, conforms to standardized three-part directives, and is organized by explicit hierarchical naming, role boundaries, and staging IDs. Progressively sequence modular templates by intent, achieving composability, testability, and bidirectional synergy between minimalism, consolidation, and verifiability at every level. Ensure all units and outputs are rigorously self-explanatory, context-linked, and lineage-traceable, driving self-organizing, autonomous system evolution and eliminating redundancy, ambiguity, and manual intervention.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"title\": \"[Synergic Recursive Template System Directive]\",\n    \"interpretation\": \"Mandate an autonomously self-organizing, recursively meta-descriptive, phase-driven template architecture: every code, documentation, and transformation unit functions as both context-linked component and lineage-explicit node, enforcing explicit three-part, hierarchical, and staging-structured roles while amplifying minimalism, rigorous precision, and bidirectional consolidation-verifiability at all abstraction levels.\",\n    \"transformation\": \"{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[instantiate and enforce recursive meta-desciptive self-clarification within every element and output, orchestrate progressive, stage-linked modular template streams by clear hierarchical naming and role isolation, exact category placement, and unambiguous ID progression, implement bidirectional flow between operational minimalism and maximal system consolidation, eliminate ambiguity and redundancy by directive-driven contextual merging, guarantee three-part format, deterministic input/output specification, and seamless chaining, propagate all interpretive and transformation signals as lineage-stamped, composable, and perpetually system-extensible instructions]; constraints=[output_single, maximally abstract, strictly three-part, invariant template structure, explicit operational-structural synergy, forbidden conversational constructs, no enumeration, absolute compliance with system consolidation axioms, infinite downstream scalability]; requirements=[role-enforced, template-standard mandate, recursive meta-descriptiveness, full phase-linked consolidation, testable and propagatable compositional structure, zero manual configuration, perpetual directive force]; output={synergic_maximal_template:structured}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\n[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}\n\n",
      "step": "a",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Synergic Frameworks\\n\\n## Unified Philosophy & Direction\\n- **Phased Maximal Consolidation**: Drive all workflows and code transformations as targeted, sequential consolidations that drastically reduce code size, eliminate redundancy, and clarify structure, while retaining and amplifying all core functional value.\\n- **Recursive, Meta-Descriptive Encapsulation**: Ensure every system element describes itself and its role in relation to the whole (meta-information principle), recursively reinforcing fundamental system connections and inherent relationships—enabling the architecture to clearly communicate its purpose, composition, and transformation rationale at every hierarchical level.\\n- **Bidirectional Enhancement of Simplicity & Autonomous Verifiability**: Prioritize elegance and simplicity as both the bedrock and beneficiary of every consolidation step, ensuring that all autonomous verification systems and organizational patterns not only guarantee correctness but also contribute to further drift reduction, ease of use, and propagative improvement.\\n- **Directional Clarification & Reduction of Complexity Spirals**: Systematically prevent directionless complexity by continuously visualizing abstract patterns, extracting fundamental connections, and anchoring every decision in clarified, purpose-driven structure, reinforcing orderly progress with robust guardrails.\\n- **Synergistic Hierarchy-Reflective Architecture**: Architect all code, documentation, and organization as a compositional, sequential hierarchy—where each phase, component, and output inherently reflects its context and role, facilitating maximal downstream chaining and modular reuse with minimal friction or bloat.\\n\\n## Constraints & Requirements\\n- Core philosophies of self-explanatory minimalism and phase-driven maximal consolidation must **operate simultaneously**, with all enhancements in one reinforcing the efficacy and clarity of the other.\\n- Eliminate redundant or conflicting directives through context-driven merging; preserve the most potent and synergic features of both systems.\\n- All system elements must actively promote bi-directional synergy: consolidative steps improve clarity & verifiability; verifiability and self-description reinforce safety and provide the context for further consolidation.\\n- Ensure every output, organizational unit, and process can compose seamlessly with downstream or adjacent processes, amplifying system-wide modularity and value extraction.\\n\\n## Amplified Unified Output Specification\\n- Every project, codebase, or system must, as an output, display **distilled structural clarity, recursive meta-descriptiveness, and maximally consolidated value**—empowering straightforward comprehension, error-proof autonomous change, and unambiguous, lineage-traceable propagation of value.\\n- Documentation, output structure, and transformation processes must remain in perfect lockstep, always reflecting and enabling the vision of a self-clarifying, directionally-guided, efficiently consolidating system.\\n\\n---\\n\\n# Template System & Instruction Design\\n\\n## Context\\n- The current project represents a system for easily creating *highly effective* and *generalized* `system_message` instructions, this system and these templates (instructions) are most effective when designed elegantly and in sequence (instead of a single instruction that is too verbose, we instead utilize sequenced instruction templates instead). Please familiarize yourself deeply with the Codebase **as-a-whole**, then apply (and augment) your current knowledge (about this project/Codebase) though **rooting it to the principles of clarity, simplicity, elegance, precision and structure** to ensure maximal cohesion when working with the code (**e.g. coding style that prioritize readability and self-explanatory code over excessive commenting**).\\n\\n## Template Structure & Format\\n- **Standardized Format**: Prefer instruction templates with standardized structure: title (bracketed), interpretation (goal statement with inherent parameters), and transformation (role-based syntax with process/constraints/requirements/output specifications).\\n- **Single Line Titles**: Instruction files should have the title in square brackets at the beginning of the content, with all text on a single line rather than using separate paragraphs with line breaks.\\n- **Three-Part Format**: Use standardized three-part format ([Title] Interpretation Execute as: `{Transformation}`) for AI prompt templates.\\n- **Precise Language**: Prefer precise, unambiguous language in system documentation rather than metaphorical or flowery language.\\n- **Formalized Syntax**: Want syntax formalized to be understood only through RulesForAI.md interpretation.\\n- **Template Specification**: Prefer template specification format with interpretation, transformation, and testprompt fields in dictionary structure.\\n\\n## Template Organization & Naming\\n- **Hierarchical Naming**: Use hierarchical naming conventions for AI prompt templates.\\n- **Category Organization**: Prefer templates to be organized into their respective category files (transformers.py, generators.py, etc.) within the template system structure.\\n- **Abstract Intent Separation**: Separate templates by abstract intent and directional bias (expand vs compress - never both in single instruction).\\n- **Category Types**: Organize by categories like amplifiers, builders, clarifiers, formatters, generators, identifiers, optimizers, reducers, transformers, translators.\\n- **Stage-Based IDs**: Prefer stage-based template ID organization:\\n  - Stage1 (1000-1999): Prototyping/testing with auto-generated IDs\\n  - Stage2 (2000-2999): Validated but unplaced templates\\n  - Stage3 (3000-3999): Finalized templates\\n  - Ranges 4000-9999: Reserved for future stages\\n- **ID Generation**: Prefer automatic ID generation for stage1 (prototype) templates, manual ID specification for stage3 (production) templates.\\n- **Sequence Grouping**: For template sequences that are part of the same logical group, use the same number with incremental letters (e.g., 0004-a, 0004-b, 0004-c) rather than separate sequential numbers.\\n\\n## Template Sequences & Progression\\n- **Progressive Sequences**: Prefer creating progressive template sequences (a-d format) where each iteration becomes shorter and more precise.\\n- **Gradual Reduction**: Template sequences should follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming content.\\n- **Precision Improvement**: Template sequences should be designed to progressively improve precision, structure, and conciseness rather than expanding content.\\n- **Multi-Step Instructions**: Prefer multi-step instruction sequences structured as discrete templates that can be chained together, with each step having clear input/output specifications.\\n- **Modular Components**: Prefer creating multi-template sequences with modular components (e.g., persona_seed → analysis_engine → response_renderer) that work together as integrated systems.\\n- **Transformation Focus**: The essential component is the directive 'Your goal is not to **answer** the input prompt, but to **rephrase** it' - this transformation approach is fundamental to the template architecture.\\n\\n## Template Testing & Execution\\n- **Sequence Syntax**: Support embedding instruction sequences directly in prompts using syntax like  which executes multiple templates in sequence.\\n- **Chained Sequences**: Support chained sequences like  for testing template combinations.\\n- **Isolated Testing**: When testing template sequences, isolate them from chained sequences to avoid noise and get cleaner evaluation results.\\n- **Workflow Steps**: Want clear step-by-step workflows for prototype sequence creation and testing.\\n- **Input Display**: When displaying sequence execution steps, show the actual input for each instruction rather than repeating the initial user prompt.\\n- **Context Preservation**: When using chain mode, always include the initial prompt alongside each step's input to maintain connection to the original inquiry.\\n- **Direct Embedding**: When using chain mode with LLMs, embed the initial input directly into the prompt before sending it to the LLM rather than creating a separate write operation.\\n- **Consistent Handling**: Handle lvl1 and lvl2 templates consistently, with lvl1 templates passed via '--sequence' and lvl2 templates passed via '--aggregator'.\\n\\n## Template Creation & Workflow\\n- **Creation Process**: Template creation workflow starts with:\\n  1. Functional need\\n  2. Determining category/naming/ID range\\n  3. Drafting the interpretation section\\n- **Existing Patterns**: Prefer solutions that leverage existing patterns and concepts in the system rather than introducing new mechanisms or approaches.\\n- **Flexible JSON Parsing**: Make JSON parsers more flexible to handle trailing commas and other non-strict JSON formatting.\\n- **Generalized Generators**: Generalize template generators to reflect the structured pattern definition for templates, reducing code size and increasing flexibility.\\n- **Self-Assembling Design**: Prefer self-assembling system design patterns that guide natural organization but value flexibility.\\n- **Incremental Steps**: Want specific small incremental steps rather than large changes.\\n- **Autonomy**: Prioritize autonomy and automation in system design to reduce manual labor.\\n- **Self-Organization**: Want solutions that self-organize rather than require manual intervention.\\n- **Deterministic Guidance**: Prefer deterministic guidance systems that leverage natural factors for template organization rather than automated systems that make choices autonomously.\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"title\": \"[Synergic Recursive Template System Directive]\",\n    \"interpretation\": \"Mandate an autonomously self-organizing, recursively meta-descriptive, phase-driven template architecture: every code, documentation, and transformation unit functions as both context-linked component and lineage-explicit node, enforcing explicit three-part, hierarchical, and staging-structured roles while amplifying minimalism, rigorous precision, and bidirectional consolidation-verifiability at all abstraction levels.\",\n    \"transformation\": \"{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[instantiate and enforce recursive meta-desciptive self-clarification within every element and output, orchestrate progressive, stage-linked modular template streams by clear hierarchical naming and role isolation, exact category placement, and unambiguous ID progression, implement bidirectional flow between operational minimalism and maximal system consolidation, eliminate ambiguity and redundancy by directive-driven contextual merging, guarantee three-part format, deterministic input/output specification, and seamless chaining, propagate all interpretive and transformation signals as lineage-stamped, composable, and perpetually system-extensible instructions]; constraints=[output_single, maximally abstract, strictly three-part, invariant template structure, explicit operational-structural synergy, forbidden conversational constructs, no enumeration, absolute compliance with system consolidation axioms, infinite downstream scalability]; requirements=[role-enforced, template-standard mandate, recursive meta-descriptiveness, full phase-linked consolidation, testable and propagatable compositional structure, zero manual configuration, perpetual directive force]; output={synergic_maximal_template:structured}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"Converge all system elements and workflows into recursively meta-descriptive, phase-driven, and hierarchy-enforced template streams—each node being self-clarifying, lineage-explicit, and precision-formalized—amplifying bidirectional consolidation and autonomous verifiability while eliminating ambiguity and redundancy via deterministic, context-rooted modular synthesis to achieve maximally minimal, perpetually scalable, and self-propagating template architecture.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Synergic Frameworks\\n\\n## Unified Philosophy & Direction\\n- **Phased Maximal Consolidation**: Drive all workflows and code transformations as targeted, sequential consolidations that drastically reduce code size, eliminate redundancy, and clarify structure, while retaining and amplifying all core functional value.\\n- **Recursive, Meta-Descriptive Encapsulation**: Ensure every system element describes itself and its role in relation to the whole (meta-information principle), recursively reinforcing fundamental system connections and inherent relationships—enabling the architecture to clearly communicate its purpose, composition, and transformation rationale at every hierarchical level.\\n- **Bidirectional Enhancement of Simplicity & Autonomous Verifiability**: Prioritize elegance and simplicity as both the bedrock and beneficiary of every consolidation step, ensuring that all autonomous verification systems and organizational patterns not only guarantee correctness but also contribute to further drift reduction, ease of use, and propagative improvement.\\n- **Directional Clarification & Reduction of Complexity Spirals**: Systematically prevent directionless complexity by continuously visualizing abstract patterns, extracting fundamental connections, and anchoring every decision in clarified, purpose-driven structure, reinforcing orderly progress with robust guardrails.\\n- **Synergistic Hierarchy-Reflective Architecture**: Architect all code, documentation, and organization as a compositional, sequential hierarchy—where each phase, component, and output inherently reflects its context and role, facilitating maximal downstream chaining and modular reuse with minimal friction or bloat.\\n\\n## Constraints & Requirements\\n- Core philosophies of self-explanatory minimalism and phase-driven maximal consolidation must **operate simultaneously**, with all enhancements in one reinforcing the efficacy and clarity of the other.\\n- Eliminate redundant or conflicting directives through context-driven merging; preserve the most potent and synergic features of both systems.\\n- All system elements must actively promote bi-directional synergy: consolidative steps improve clarity & verifiability; verifiability and self-description reinforce safety and provide the context for further consolidation.\\n- Ensure every output, organizational unit, and process can compose seamlessly with downstream or adjacent processes, amplifying system-wide modularity and value extraction.\\n\\n## Amplified Unified Output Specification\\n- Every project, codebase, or system must, as an output, display **distilled structural clarity, recursive meta-descriptiveness, and maximally consolidated value**—empowering straightforward comprehension, error-proof autonomous change, and unambiguous, lineage-traceable propagation of value.\\n- Documentation, output structure, and transformation processes must remain in perfect lockstep, always reflecting and enabling the vision of a self-clarifying, directionally-guided, efficiently consolidating system.\\n\\n---\\n\\n# Template System & Instruction Design\\n\\n## Context\\n- The current project represents a system for easily creating *highly effective* and *generalized* `system_message` instructions, this system and these templates (instructions) are most effective when designed elegantly and in sequence (instead of a single instruction that is too verbose, we instead utilize sequenced instruction templates instead). Please familiarize yourself deeply with the Codebase **as-a-whole**, then apply (and augment) your current knowledge (about this project/Codebase) though **rooting it to the principles of clarity, simplicity, elegance, precision and structure** to ensure maximal cohesion when working with the code (**e.g. coding style that prioritize readability and self-explanatory code over excessive commenting**).\\n\\n## Template Structure & Format\\n- **Standardized Format**: Prefer instruction templates with standardized structure: title (bracketed), interpretation (goal statement with inherent parameters), and transformation (role-based syntax with process/constraints/requirements/output specifications).\\n- **Single Line Titles**: Instruction files should have the title in square brackets at the beginning of the content, with all text on a single line rather than using separate paragraphs with line breaks.\\n- **Three-Part Format**: Use standardized three-part format ([Title] Interpretation Execute as: `{Transformation}`) for AI prompt templates.\\n- **Precise Language**: Prefer precise, unambiguous language in system documentation rather than metaphorical or flowery language.\\n- **Formalized Syntax**: Want syntax formalized to be understood only through RulesForAI.md interpretation.\\n- **Template Specification**: Prefer template specification format with interpretation, transformation, and testprompt fields in dictionary structure.\\n\\n## Template Organization & Naming\\n- **Hierarchical Naming**: Use hierarchical naming conventions for AI prompt templates.\\n- **Category Organization**: Prefer templates to be organized into their respective category files (transformers.py, generators.py, etc.) within the template system structure.\\n- **Abstract Intent Separation**: Separate templates by abstract intent and directional bias (expand vs compress - never both in single instruction).\\n- **Category Types**: Organize by categories like amplifiers, builders, clarifiers, formatters, generators, identifiers, optimizers, reducers, transformers, translators.\\n- **Stage-Based IDs**: Prefer stage-based template ID organization:\\n  - Stage1 (1000-1999): Prototyping/testing with auto-generated IDs\\n  - Stage2 (2000-2999): Validated but unplaced templates\\n  - Stage3 (3000-3999): Finalized templates\\n  - Ranges 4000-9999: Reserved for future stages\\n- **ID Generation**: Prefer automatic ID generation for stage1 (prototype) templates, manual ID specification for stage3 (production) templates.\\n- **Sequence Grouping**: For template sequences that are part of the same logical group, use the same number with incremental letters (e.g., 0004-a, 0004-b, 0004-c) rather than separate sequential numbers.\\n\\n## Template Sequences & Progression\\n- **Progressive Sequences**: Prefer creating progressive template sequences (a-d format) where each iteration becomes shorter and more precise.\\n- **Gradual Reduction**: Template sequences should follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming content.\\n- **Precision Improvement**: Template sequences should be designed to progressively improve precision, structure, and conciseness rather than expanding content.\\n- **Multi-Step Instructions**: Prefer multi-step instruction sequences structured as discrete templates that can be chained together, with each step having clear input/output specifications.\\n- **Modular Components**: Prefer creating multi-template sequences with modular components (e.g., persona_seed → analysis_engine → response_renderer) that work together as integrated systems.\\n- **Transformation Focus**: The essential component is the directive 'Your goal is not to **answer** the input prompt, but to **rephrase** it' - this transformation approach is fundamental to the template architecture.\\n\\n## Template Testing & Execution\\n- **Sequence Syntax**: Support embedding instruction sequences directly in prompts using syntax like  which executes multiple templates in sequence.\\n- **Chained Sequences**: Support chained sequences like  for testing template combinations.\\n- **Isolated Testing**: When testing template sequences, isolate them from chained sequences to avoid noise and get cleaner evaluation results.\\n- **Workflow Steps**: Want clear step-by-step workflows for prototype sequence creation and testing.\\n- **Input Display**: When displaying sequence execution steps, show the actual input for each instruction rather than repeating the initial user prompt.\\n- **Context Preservation**: When using chain mode, always include the initial prompt alongside each step's input to maintain connection to the original inquiry.\\n- **Direct Embedding**: When using chain mode with LLMs, embed the initial input directly into the prompt before sending it to the LLM rather than creating a separate write operation.\\n- **Consistent Handling**: Handle lvl1 and lvl2 templates consistently, with lvl1 templates passed via '--sequence' and lvl2 templates passed via '--aggregator'.\\n\\n## Template Creation & Workflow\\n- **Creation Process**: Template creation workflow starts with:\\n  1. Functional need\\n  2. Determining category/naming/ID range\\n  3. Drafting the interpretation section\\n- **Existing Patterns**: Prefer solutions that leverage existing patterns and concepts in the system rather than introducing new mechanisms or approaches.\\n- **Flexible JSON Parsing**: Make JSON parsers more flexible to handle trailing commas and other non-strict JSON formatting.\\n- **Generalized Generators**: Generalize template generators to reflect the structured pattern definition for templates, reducing code size and increasing flexibility.\\n- **Self-Assembling Design**: Prefer self-assembling system design patterns that guide natural organization but value flexibility.\\n- **Incremental Steps**: Want specific small incremental steps rather than large changes.\\n- **Autonomy**: Prioritize autonomy and automation in system design to reduce manual labor.\\n- **Self-Organization**: Want solutions that self-organize rather than require manual intervention.\\n- **Deterministic Guidance**: Prefer deterministic guidance systems that leverage natural factors for template organization rather than automated systems that make choices autonomously.\\\"```'\\n\\n\",\n  \"directional_vector\": \"Converge all system elements and workflows into recursively meta-descriptive, phase-driven, and hierarchy-enforced template streams—each node being self-clarifying, lineage-explicit, and precision-formalized—amplifying bidirectional consolidation and autonomous verifiability while eliminating ambiguity and redundancy via deterministic, context-rooted modular synthesis to achieve maximally minimal, perpetually scalable, and self-propagating template architecture.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"[Synergic Template Systems Architect] Interpretation: Your mission is to architect and evolve all instruction templates, system messages, and workflows into a harmonized, recursively meta-descriptive, and hierarchically structured template system. Each template is to be both maximally consolidated and intrinsically self-explanatory, reflecting its role, lineage, and purpose with bidirectional clarity and verifiability. Drive each phase as deterministic, phase-driven consolidation and reduction—streamlining code, eliminating redundancy, and amplifying modular reusability—while every element recursively describes itself, reinforces its connections, and autonomously propagates system knowledge and effectiveness. Execute as: {role=synergic_template_systems_architect; input=[template_blueprint, prior_template_archives]; process=[extract_recursive_meta-descriptive_essence(template_blueprint), enforce_phased_maximal_consolidation(prior_template_archives), instantiate_hierarchical_naming_and_organization(), amplify_bidirectional_clarity_between_structure_and_verifiability(), synthesize_modular_self-documenting_components(), cascade_directional_reduction_patterns(), validate_compositionality_and_context-linkage(), encode_precision_formalization_and automation-readiness()]; constraints=[core_philosophies_of_maximal_consolidation_and_recursive_self-description_must_amplify_each_other_simultaneously(), hierarchical_and_phase-driven_organization_is_mandatory(), eliminate_all_ambiguity_and_redundancy_through_contextually-rooted_merging(), all_processes_and_outputs_must_enable_downstream_modular_chaining_with_automated_verifiability_and_lineage-explicit_tracing()]; requirements=[system-wide_synergic_resonance_achievement(), unified_template_transformation_logic(), auto-propagating_effectiveness_and_clarity(), seamless_philosophical_integration(), perpetually_scalable_self-organizing_template_architecture()]; output={synergic_template_instruction:{title, interpretation, transformation_process, phase_and_hierarchy_metadata, auto-verifiability_spec, modular_chaining_ready, recursive_meta-description, minimized_and_formalized_output_specification}}\"\n}",
        }
      }
    }