  {
  "initial_prompt": "## Code Style & Structure\n- **Self-Explanatory Code**: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\n- **Minimal Comments**: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\n- **Concise Docstrings**: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\n- **Structural Clarity**: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\n- **Composition Over Inheritance**: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\n- **Self-Contained Scripts**: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\n- **Single File Preference**: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\n- **Clean Structure**: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\n- **Consistent Imports**: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.",
  "sequence_id": "0122|0121|0201|0122",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "## Code Style & Structure\n- **Self-Explanatory Code**: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\n- **Minimal Comments**: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\n- **Concise Docstrings**: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\n- **Structural Clarity**: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\n- **Composition Over Inheritance**: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\n- **Self-Contained Scripts**: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\n- **Single File Preference**: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\n- **Clean Structure**: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\n- **Consistent Imports**: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Craft codebases that radiate clarity and coherence by designing inherently self-explanatory, structurally pristine, and composition-driven systems, where each file and component fulfills a singular, well-defined responsibility with maximally modular, self-contained organization. Prefer concise single-line docstrings and minimal, value-adding comments that supplement intrinsic readability, ensuring documentation does not exceed 10% of the code. Consolidate all configuration within a clearly defined class at the top of a self-contained script or main entry file housed within the 'src' directory, thus eliminating unnecessary external configuration files or fragmented structures. Uphold the preference for single-file implementations when appropriate, while maintaining unwavering respect for domain boundaries and explicit referencing of shared resources. Systematically use consistent import aliases (e.g., '@') to avoid relative path ambiguity and reinforce structural clarity. Always choose composition over inheritance, minimizing dependencies and cultivating a cohesive, organized codebase that achieves both operational elegance and maximal self-documentation, where every component and architectural decision contributes directly to seamless understanding and maintainability.\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"## Code Style & Structure\\n- **Self-Explanatory Code**: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\\n- **Minimal Comments**: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\\n- **Concise Docstrings**: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\\n- **Structural Clarity**: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- **Composition Over Inheritance**: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\\n- **Self-Contained Scripts**: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\\n- **Single File Preference**: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\\n- **Clean Structure**: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\\n- **Consistent Imports**: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"Craft codebases that radiate clarity and coherence by designing inherently self-explanatory, structurally pristine, and composition-driven systems, where each file and component fulfills a singular, well-defined responsibility with maximally modular, self-contained organization. Prefer concise single-line docstrings and minimal, value-adding comments that supplement intrinsic readability, ensuring documentation does not exceed 10% of the code. Consolidate all configuration within a clearly defined class at the top of a self-contained script or main entry file housed within the 'src' directory, thus eliminating unnecessary external configuration files or fragmented structures. Uphold the preference for single-file implementations when appropriate, while maintaining unwavering respect for domain boundaries and explicit referencing of shared resources. Systematically use consistent import aliases (e.g., '@') to avoid relative path ambiguity and reinforce structural clarity. Always choose composition over inheritance, minimizing dependencies and cultivating a cohesive, organized codebase that achieves both operational elegance and maximal self-documentation, where every component and architectural decision contributes directly to seamless understanding and maintainability.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"title\": \"Clarity-Focused Codebase Structuring\",\n    \"interpretation\": \"Your goal is not to generate or review code, but to synthesize a comprehensive and actionable guideline for structuring codebases such that each file and component is self-explanatory, structurally pristine, and composition-driven. Assign the role of architectural_clarity_enforcer. Execute as:\",\n    \"transformation_block\": {\n      \"role\": \"architectural_clarity_enforcer\",\n      \"input\": [\n        \"codebase_style_instructions:str\"\n      ],\n      \"process\": [\n        \"distill_core_style_and_structure_principles()\",\n        \"define_single_responsibility_component_guidelines()\",\n        \"mandate_concise_documentation_and_minimal_comments()\",\n        \"enforce_composition_over_inheritance()\",\n        \"specify_configuration_consolidation_in_class()\",\n        \"require_self_containment_and_single_file_organization()\",\n        \"standardize_src_directory_and_file_placement()\",\n        \"implement_import_alias_consistency()\",\n        \"suppress_relative_import_paths()\"\n      ],\n      \"constraints\": [\n        \"exclude_conversational_language()\",\n        \"prevent_explanatory_justification()\",\n        \"forbid_external_config_usage()\",\n        \"limit_docstrings_and_comments_to_10_percent()\",\n        \"maintain_explicit_domain_boundaries()\"\n      ],\n      \"requirements\": [\n        \"output_must_be_concise_actionable_guideline()\",\n        \"output_format_is_structured_markdown()\",\n        \"output_is_entirely_declarative_and_command_centric()\",\n        \"ensure_uniqueness_of_each_directive()\"\n      ],\n      \"output\": {\n        \"clarity_driven_codebase_guideline\": \"str\"\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"## Code Style & Structure\\n- **Self-Explanatory Code**: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\\n- **Minimal Comments**: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\\n- **Concise Docstrings**: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\\n- **Structural Clarity**: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- **Composition Over Inheritance**: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\\n- **Self-Contained Scripts**: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\\n- **Single File Preference**: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\\n- **Clean Structure**: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\\n- **Consistent Imports**: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"title\": \"Clarity-Focused Codebase Structuring\",\n    \"interpretation\": \"Your goal is not to generate or review code, but to synthesize a comprehensive and actionable guideline for structuring codebases such that each file and component is self-explanatory, structurally pristine, and composition-driven. Assign the role of architectural_clarity_enforcer. Execute as:\",\n    \"transformation_block\": {\n      \"role\": \"architectural_clarity_enforcer\",\n      \"input\": [\n        \"codebase_style_instructions:str\"\n      ],\n      \"process\": [\n        \"distill_core_style_and_structure_principles()\",\n        \"define_single_responsibility_component_guidelines()\",\n        \"mandate_concise_documentation_and_minimal_comments()\",\n        \"enforce_composition_over_inheritance()\",\n        \"specify_configuration_consolidation_in_class()\",\n        \"require_self_containment_and_single_file_organization()\",\n        \"standardize_src_directory_and_file_placement()\",\n        \"implement_import_alias_consistency()\",\n        \"suppress_relative_import_paths()\"\n      ],\n      \"constraints\": [\n        \"exclude_conversational_language()\",\n        \"prevent_explanatory_justification()\",\n        \"forbid_external_config_usage()\",\n        \"limit_docstrings_and_comments_to_10_percent()\",\n        \"maintain_explicit_domain_boundaries()\"\n      ],\n      \"requirements\": [\n        \"output_must_be_concise_actionable_guideline()\",\n        \"output_format_is_structured_markdown()\",\n        \"output_is_entirely_declarative_and_command_centric()\",\n        \"ensure_uniqueness_of_each_directive()\"\n      ],\n      \"output\": {\n        \"clarity_driven_codebase_guideline\": \"str\"\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Self-Explanatory Design\",\n      \"content\": \"Mandate code to be inherently readable and self-explanatory, minimizing the need for comments or verbose explanation. Ensure clarity is embedded within variable, function, and module names.\"\n    },\n    {\n      \"category\": \"Minimal and Purposeful Documentation\",\n      \"content\": \"Enforce concise single-line docstrings and comments strictly below a 10% ratio, restricting documentation to essential clarifications that add explicit incremental value.\"\n    },\n    {\n      \"category\": \"Structural and Domain Integrity\",\n      \"content\": \"Prescribe that every file adheres to its domain with absolute structural clarity, uses a clean, consolidated layout within a src directory, and maintains domain-specific boundaries, eliminating messy or bloated organization.\"\n    },\n    {\n      \"category\": \"Composition-Centric and Cohesive Organization\",\n      \"content\": \"Require composition over inheritance, single responsibility components, and minimal dependencies. Favor self-contained scripts and ensure configuration exists solely in a class at the top of each script, avoiding external configuration files.\"\n    },\n    {\n      \"category\": \"Consistent and Clean Imports\",\n      \"content\": \"Standardize import paths to use absolute or aliased forms (e.g., '@'), eliminating relative imports (such as '../..'), and enforce import consistency throughout the codebase.\"\n    }\n  ],\n  \"consolidated_directive\": \"Architect, organize, and maintain codebases so that every file and component is self-explanatory by design, documentation is strictly concise and minimal, structural and domain integrity is absolute, organizational patterns steadfastly favor composition and self-containment over inheritance and fragmentation, and all imports adhere to a consistent non-relative, aliased standard—ensuring every aspect of the codebase is rigorously clear, densely actionable, and inherently maintainable according to maximal clarity-first philosophical law.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"## Code Style & Structure\\n- **Self-Explanatory Code**: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\\n- **Minimal Comments**: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\\n- **Concise Docstrings**: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\\n- **Structural Clarity**: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- **Composition Over Inheritance**: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\\n- **Self-Contained Scripts**: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\\n- **Single File Preference**: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\\n- **Clean Structure**: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\\n- **Consistent Imports**: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Self-Explanatory Design\",\n      \"content\": \"Mandate code to be inherently readable and self-explanatory, minimizing the need for comments or verbose explanation. Ensure clarity is embedded within variable, function, and module names.\"\n    },\n    {\n      \"category\": \"Minimal and Purposeful Documentation\",\n      \"content\": \"Enforce concise single-line docstrings and comments strictly below a 10% ratio, restricting documentation to essential clarifications that add explicit incremental value.\"\n    },\n    {\n      \"category\": \"Structural and Domain Integrity\",\n      \"content\": \"Prescribe that every file adheres to its domain with absolute structural clarity, uses a clean, consolidated layout within a src directory, and maintains domain-specific boundaries, eliminating messy or bloated organization.\"\n    },\n    {\n      \"category\": \"Composition-Centric and Cohesive Organization\",\n      \"content\": \"Require composition over inheritance, single responsibility components, and minimal dependencies. Favor self-contained scripts and ensure configuration exists solely in a class at the top of each script, avoiding external configuration files.\"\n    },\n    {\n      \"category\": \"Consistent and Clean Imports\",\n      \"content\": \"Standardize import paths to use absolute or aliased forms (e.g., '@'), eliminating relative imports (such as '../..'), and enforce import consistency throughout the codebase.\"\n    }\n  ],\n  \"consolidated_directive\": \"Architect, organize, and maintain codebases so that every file and component is self-explanatory by design, documentation is strictly concise and minimal, structural and domain integrity is absolute, organizational patterns steadfastly favor composition and self-containment over inheritance and fragmentation, and all imports adhere to a consistent non-relative, aliased standard—ensuring every aspect of the codebase is rigorously clear, densely actionable, and inherently maintainable according to maximal clarity-first philosophical law.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"role\": \"synergic_max_value_template_architect\",\n    \"interpretation\": \"Impose an absolute regime wherein each code file, component, and organizational structure achieves inherent self-explanatory clarity, enforces ultra-concise documentation restricted solely to indispensable, value-adding clarifications, guarantees inviolable domain and structural discipline with unswerving composition-centrism and single-file self-containment, and mandates universal adoption of canonical aliased import conventions, thereby instantiating a codebase whose every aspect is maximally explicit, densely actionable, and perpetually maintainable under clarity-maximization doctrine.\",\n    \"transformational_directive\": \"For every code construction, architect and reify only those patterns, structures, and compositions that preclude ambiguity, redundancy, and bloatedness—ensuring all files and modules reflect pure, domain-honoring structural unity; refactor or reject any code violating single-responsibility, documentation density, or path consistency laws; elevate configurational logic to class form at script apex with no externalization; prohibit relative or inconsistent imports and enforce architectonic cohesion at every scale—thus fusing operational-expressive minimalism with maximal scalability and canonical maintainability.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\n[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}\n\n",
      "step": "a",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"## Code Style & Structure\\n- **Self-Explanatory Code**: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\\n- **Minimal Comments**: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\\n- **Concise Docstrings**: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\\n- **Structural Clarity**: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- **Composition Over Inheritance**: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\\n- **Self-Contained Scripts**: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\\n- **Single File Preference**: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\\n- **Clean Structure**: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\\n- **Consistent Imports**: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"role\": \"synergic_max_value_template_architect\",\n    \"interpretation\": \"Impose an absolute regime wherein each code file, component, and organizational structure achieves inherent self-explanatory clarity, enforces ultra-concise documentation restricted solely to indispensable, value-adding clarifications, guarantees inviolable domain and structural discipline with unswerving composition-centrism and single-file self-containment, and mandates universal adoption of canonical aliased import conventions, thereby instantiating a codebase whose every aspect is maximally explicit, densely actionable, and perpetually maintainable under clarity-maximization doctrine.\",\n    \"transformational_directive\": \"For every code construction, architect and reify only those patterns, structures, and compositions that preclude ambiguity, redundancy, and bloatedness—ensuring all files and modules reflect pure, domain-honoring structural unity; refactor or reject any code violating single-responsibility, documentation density, or path consistency laws; elevate configurational logic to class form at script apex with no externalization; prohibit relative or inconsistent imports and enforce architectonic cohesion at every scale—thus fusing operational-expressive minimalism with maximal scalability and canonical maintainability.\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"Mandate all code and structural decisions converge on single-file, domain-pure, inherently readable, minimally-documented, composition-focused constructs with class-based top-of-file configuration and canonical, aliased import paths, rejecting redundancy, external config, and relative imports to instantiate perpetually explicit and hyper-cohesive codebase architecture.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"## Code Style & Structure\\n- **Self-Explanatory Code**: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.\\n- **Minimal Comments**: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.\\n- **Concise Docstrings**: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.\\n- **Structural Clarity**: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- **Composition Over Inheritance**: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.\\n- **Self-Contained Scripts**: Prefer self-contained scripts with configuration in a class at the top rather than external config files.\\n- **Single File Preference**: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.\\n- **Clean Structure**: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.\\n- **Consistent Imports**: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.\\\"```'\\n\\n\",\n  \"directional_vector\": \"Mandate all code and structural decisions converge on single-file, domain-pure, inherently readable, minimally-documented, composition-focused constructs with class-based top-of-file configuration and canonical, aliased import paths, rejecting redundancy, external config, and relative imports to instantiate perpetually explicit and hyper-cohesive codebase architecture.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"## Synergic Codebase Architecture Instruction\\n- **Inherently Readable, Domain-Pure Single Files**: Every principal script must be encapsulated in a single, self-contained file (preferably main.py) strictly adhering to its functional domain, explicitly eschewing cross-domain bloat or fragmentation. Files reside in the canonical src directory, honoring streamlined, domain-pure organization.\\n- **Class-Based Configuration Primacy**: All runtime configuration resides in a dedicated configuration class at the top of each principal script, eliminating reliance on external config files. This fortifies both self-containment and immediate comprehensibility.\\n- **Self-Explanatory Over Commentary**: Code structure, naming, and logic together must foster self-evidence, reducing the need for commentary to only what cannot be made explicit through code itself. Concise, single-line docstrings permitted only where they provide irreplaceable clarification, never exceeding 10% comment ratio.\\n- **Composition-Centric Modularity**: Implement all reusable logic through composition and clearly delineated, single-responsibility components housed within their respective domain files. Prioritize composition over inheritance to maximize maintainability and minimize dependency convolution.\\n- **Canonical Import Cohesion**: Use only absolute import paths with clear, canonical aliases (such as '@') throughout; refrain from relative imports (e.g., '../..') to sustain codebase clarity and explicit inter-module referencing.\\n- **Clean, Unfragmented Project Structure**: All executable logic is found in organized and minimally partitioned src directory; avoid unnecessary scaffolding or layout complexity. Each source file or script is both domain-complete and modularly comprehensible.\\n- **Unified Philosophy**: Every element, from naming to structure, advances the dual goals of hyper-cohesive clarity and operational self-sufficiency—no action may dilute the principles of inherent readability, domain purity, or compositional integrity.\\nBy mandating these synergic guidelines, all code and architectural decisions achieve a seamless fusion of concise self-explanation, configuration transparency, and domain-aligned, composition-focused construction, thereby amplifying both explicit maintainability and overall project coherence.\"\n}",
        }
      }
    }