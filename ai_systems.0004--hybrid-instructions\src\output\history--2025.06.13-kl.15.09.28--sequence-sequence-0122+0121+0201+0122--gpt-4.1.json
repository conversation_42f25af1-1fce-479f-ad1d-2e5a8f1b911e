  {
  "initial_prompt": "# GENERAL\n\n## Fundamental Approach\n- Simplicity & Elegance: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\n- Rooted Fundamentals: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\n- Meta-Information Principle: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\n- Systematic Precautions: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\n- Visual Abstraction: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\n\n## Focus & Priorities\n- Critical Value: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n- Usage Before Features: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\n- Impactful Consolidation: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\n- Sequential Targeting: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\n\n## Design Methodology\n- Inherent Structure: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.\n- Natural Organization: Prefer file naming conventions that create natural and cohesive sequential order for better organization.\n- Synergistic Efficiency: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.\n- Directional Clarity: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.\n- Value Extraction: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.\n- Direction Setting: Focus on setting direction rather than planting flags when dealing with unknown complexity.\n\n## Evaluation Criteria\n- Comprehensive Progress: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.\n- Inherent Direction: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).\n- Sequential Composition: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.\n- Gradual Reduction: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.\n\n---\n\n# CONTEXT\n- Core Philosophical Framework: The system is governed by foundational principles of simplicity, elegance, inherent structural clarity, and maximal consolidation. All project elements must reflect rooted fundamentals, favoring phase-driven reduction, systematic self-organization, and recursive meta-descriptiveness—every element must both embody and convey its purpose, composition, and transformation rationale. Bidirectional synergy between clarity and verifiability is imperative; each improvement must reinforce ease of use, propagative value, and the reduction of complexity spirals.\n- Project Structure, Code Style, and Development Practice: Maintain clean, modular, and readable code with self-explanatory structure: minimal and purposeful comments, composition over inheritance, centralized configuration, single-file or src-directory organization, and consistent import aliasing. Employ relative paths, precise logging with timestamped outputs, systematic error handling, and enforce actual code reduction during refactoring. Test organization, documentation, and user interfaces must all uphold clarity, consolidation, and traceability standards.\n- Template System Design, Categorization, and Sequencing: Templates follow a standardized three-part structure: [Title] (bracketed, single-line), Interpretation (goal statement), and Transformation (role-based syntax in formalized dictionary format). Organize templates hierarchically by category (e.g., transformers, generators), assigning IDs by stage, utilizing progressive alphabetical sequencing for logical groups. Distinct abstract intent and directional bias must remain separated—favor modular, composable, and phase-reducing templates designed to clarify, compress, or expand only within focused bounds.\n- Meta-Descriptiveness, Automation, and Composability: Everything from code to instructions must describe itself within the meta-information principle, reinforcing recursive structural relationships and autonomously guiding organization. All processes (creation, refactoring, testing) should be automatable and support downstream composability—templates, code, and documentation must operate seamlessly as modular, chainable components. Self-organization, automation readiness, and phase-driven guidance replace manual curation wherever possible.\n- Unified Synergic Transformation and Output Law: The entire system outputs maximally consolidated value: recursively meta-descriptive, phase-driven, hierarchy-reflective, and readily automatable units. Documentation, directories, templates, and code organization progress in perfect synchronization—each output both serves and describes its role, amplifying system-wide directionality and modular propagation. All constraints, requirements, and operational logic are context-aware, ensuring comprehensive yet focused value extraction and composition.\n- Consolidated Directive: Architect, maintain, and evolve all project systems—code, documentation, templates, workflows—so they recursively describe, consolidate, and amplify their own fundamental structure and meta-information, operating through phase-driven, hierarchy-reflective processes. Enforce maximal clarity, composability, and automation through standardized three-part template sequences, staged categorization, traced and modular organization, and ubiquitous self-explanation—in every element and transformation—ensuring that the entire system yields a maximally synergic, reductionist, and automation-ready lineage of outputs that always reinforce and clarify system direction, modularity, and value.\n\n---\n\n# GOAL\n\nthe intent of having multiple python files (to generate new sequences) is to make it semless (inherently easy) to use, maintain and improve-but it also makes it really easy to reuse (in new or other places) since they're self-contained and generalized. it's a good thing that the generators are separated, because that makes it much more easy to maintain and use.\n\nthe idea is to *first* separate instructions based on stages, e.g. isolate a specific range stage1 (which will be for new instructions and testing/prototyping), stage2 (new specific range) will be instructions that we know we want to keep but haven't yet found a place for, stage3 (new specific range) is instructions that are finalized and that won't be changed. by doing it this way we introduce an additional layer of abstraction (and control), because by having a dedicated range for prototyping/testing enables us to generate the id's automatically (within the given range).\n\nand all the while retain the ability to *also* categorize by intent (as illustrated in my previous message). as an example, to make it incredibly easy for ourselves we could dedicate stage1 to range `1000-1999`, stage2 to `2000-2999` and stage3 to `3000-3999` - and we still have the range `4000-9999` free (e.g. if we decide to add new stages). what do you think about this approach? feel free to utilize context7 mcp to gain a wider understanding of the system before proposing the best possible implementation (while respecting inherent fundamental patterns and existing codestyle).\n\n\ni've recently added @templates\\stage1\\generators\\1200-1299.generators.py and i just noticed something, since we're always prefixing the generators with their sequence-range, wouldn't we simoultaneously simplify the codecomplexity and improve functionality by enforsing inherent concistency and a well-thought-out structure (that respects existing code, organization, etc)? \n\nhere's what i found, after creating the new `1200-1299.generators.py` and deleting the `md` directory before executing (since in stage1 and md autogenerates it's a clean way to test), it generated auto_id's like this:\n```\n    ├── 1000-a-runway_prompt_generator1.md\n    ├── 1000-b-runway_prompt_generator1.md\n    ├── 1000-c-runway_prompt_generator1.md\n    ├── 1000-d-runway_prompt_generator1.md\n    ├── 1001-a-runway_prompt_generator2.md\n    ├── 1001-b-runway_prompt_generator2.md\n    ├── 1001-c-runway_prompt_generator2.md\n    └── 1001-d-runway_prompt_generator2.md\n```\n\n\n\nwe could potentially reduce code complexity and enhance overall functionality by enforcing a more consistent and thoughtfully designed structure—one that aligns with our existing codebase and organizational standards. and since generator files are always prefixed with their sequence-range, we could streamline code complexity and boost maintainability by enforcing a consistent, well-structured naming and organizational scheme that aligns with our current codebase and standards. so when e.g. executing `1200-1299.generators.py` the auto-id's should also match this range (if/when defined), e.g:\n```\n    ├── 1200-a-runway_prompt_generator1.md\n    ├── 1200-b-runway_prompt_generator1.md\n    ├── 1200-c-runway_prompt_generator1.md\n    ├── 1200-d-runway_prompt_generator1.md\n    ├── 1201-a-runway_prompt_generator2.md\n    ├── 1201-b-runway_prompt_generator2.md\n    ├── 1201-c-runway_prompt_generator2.md\n    └── 1201-d-runway_prompt_generator2.md\n```",
  "sequence_id": "0122|0121|0201|0122",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "# GENERAL\n\n## Fundamental Approach\n- Simplicity & Elegance: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\n- Rooted Fundamentals: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\n- Meta-Information Principle: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\n- Systematic Precautions: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\n- Visual Abstraction: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\n\n## Focus & Priorities\n- Critical Value: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n- Usage Before Features: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\n- Impactful Consolidation: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\n- Sequential Targeting: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\n\n## Design Methodology\n- Inherent Structure: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.\n- Natural Organization: Prefer file naming conventions that create natural and cohesive sequential order for better organization.\n- Synergistic Efficiency: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.\n- Directional Clarity: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.\n- Value Extraction: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.\n- Direction Setting: Focus on setting direction rather than planting flags when dealing with unknown complexity.\n\n## Evaluation Criteria\n- Comprehensive Progress: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.\n- Inherent Direction: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).\n- Sequential Composition: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.\n- Gradual Reduction: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.\n\n---\n\n# CONTEXT\n- Core Philosophical Framework: The system is governed by foundational principles of simplicity, elegance, inherent structural clarity, and maximal consolidation. All project elements must reflect rooted fundamentals, favoring phase-driven reduction, systematic self-organization, and recursive meta-descriptiveness—every element must both embody and convey its purpose, composition, and transformation rationale. Bidirectional synergy between clarity and verifiability is imperative; each improvement must reinforce ease of use, propagative value, and the reduction of complexity spirals.\n- Project Structure, Code Style, and Development Practice: Maintain clean, modular, and readable code with self-explanatory structure: minimal and purposeful comments, composition over inheritance, centralized configuration, single-file or src-directory organization, and consistent import aliasing. Employ relative paths, precise logging with timestamped outputs, systematic error handling, and enforce actual code reduction during refactoring. Test organization, documentation, and user interfaces must all uphold clarity, consolidation, and traceability standards.\n- Template System Design, Categorization, and Sequencing: Templates follow a standardized three-part structure: [Title] (bracketed, single-line), Interpretation (goal statement), and Transformation (role-based syntax in formalized dictionary format). Organize templates hierarchically by category (e.g., transformers, generators), assigning IDs by stage, utilizing progressive alphabetical sequencing for logical groups. Distinct abstract intent and directional bias must remain separated—favor modular, composable, and phase-reducing templates designed to clarify, compress, or expand only within focused bounds.\n- Meta-Descriptiveness, Automation, and Composability: Everything from code to instructions must describe itself within the meta-information principle, reinforcing recursive structural relationships and autonomously guiding organization. All processes (creation, refactoring, testing) should be automatable and support downstream composability—templates, code, and documentation must operate seamlessly as modular, chainable components. Self-organization, automation readiness, and phase-driven guidance replace manual curation wherever possible.\n- Unified Synergic Transformation and Output Law: The entire system outputs maximally consolidated value: recursively meta-descriptive, phase-driven, hierarchy-reflective, and readily automatable units. Documentation, directories, templates, and code organization progress in perfect synchronization—each output both serves and describes its role, amplifying system-wide directionality and modular propagation. All constraints, requirements, and operational logic are context-aware, ensuring comprehensive yet focused value extraction and composition.\n- Consolidated Directive: Architect, maintain, and evolve all project systems—code, documentation, templates, workflows—so they recursively describe, consolidate, and amplify their own fundamental structure and meta-information, operating through phase-driven, hierarchy-reflective processes. Enforce maximal clarity, composability, and automation through standardized three-part template sequences, staged categorization, traced and modular organization, and ubiquitous self-explanation—in every element and transformation—ensuring that the entire system yields a maximally synergic, reductionist, and automation-ready lineage of outputs that always reinforce and clarify system direction, modularity, and value.\n\n---\n\n# GOAL\n\nthe intent of having multiple python files (to generate new sequences) is to make it semless (inherently easy) to use, maintain and improve-but it also makes it really easy to reuse (in new or other places) since they're self-contained and generalized. it's a good thing that the generators are separated, because that makes it much more easy to maintain and use.\n\nthe idea is to *first* separate instructions based on stages, e.g. isolate a specific range stage1 (which will be for new instructions and testing/prototyping), stage2 (new specific range) will be instructions that we know we want to keep but haven't yet found a place for, stage3 (new specific range) is instructions that are finalized and that won't be changed. by doing it this way we introduce an additional layer of abstraction (and control), because by having a dedicated range for prototyping/testing enables us to generate the id's automatically (within the given range).\n\nand all the while retain the ability to *also* categorize by intent (as illustrated in my previous message). as an example, to make it incredibly easy for ourselves we could dedicate stage1 to range `1000-1999`, stage2 to `2000-2999` and stage3 to `3000-3999` - and we still have the range `4000-9999` free (e.g. if we decide to add new stages). what do you think about this approach? feel free to utilize context7 mcp to gain a wider understanding of the system before proposing the best possible implementation (while respecting inherent fundamental patterns and existing codestyle).\n\n\ni've recently added @templates\\stage1\\generators\\1200-1299.generators.py and i just noticed something, since we're always prefixing the generators with their sequence-range, wouldn't we simoultaneously simplify the codecomplexity and improve functionality by enforsing inherent concistency and a well-thought-out structure (that respects existing code, organization, etc)? \n\nhere's what i found, after creating the new `1200-1299.generators.py` and deleting the `md` directory before executing (since in stage1 and md autogenerates it's a clean way to test), it generated auto_id's like this:\n```\n    ├── 1000-a-runway_prompt_generator1.md\n    ├── 1000-b-runway_prompt_generator1.md\n    ├── 1000-c-runway_prompt_generator1.md\n    ├── 1000-d-runway_prompt_generator1.md\n    ├── 1001-a-runway_prompt_generator2.md\n    ├── 1001-b-runway_prompt_generator2.md\n    ├── 1001-c-runway_prompt_generator2.md\n    └── 1001-d-runway_prompt_generator2.md\n```\n\n\n\nwe could potentially reduce code complexity and enhance overall functionality by enforcing a more consistent and thoughtfully designed structure—one that aligns with our existing codebase and organizational standards. and since generator files are always prefixed with their sequence-range, we could streamline code complexity and boost maintainability by enforcing a consistent, well-structured naming and organizational scheme that aligns with our current codebase and standards. so when e.g. executing `1200-1299.generators.py` the auto-id's should also match this range (if/when defined), e.g:\n```\n    ├── 1200-a-runway_prompt_generator1.md\n    ├── 1200-b-runway_prompt_generator1.md\n    ├── 1200-c-runway_prompt_generator1.md\n    ├── 1200-d-runway_prompt_generator1.md\n    ├── 1201-a-runway_prompt_generator2.md\n    ├── 1201-b-runway_prompt_generator2.md\n    ├── 1201-c-runway_prompt_generator2.md\n    └── 1201-d-runway_prompt_generator2.md\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "```json\n{\n  \"unified_synergic_instruction\": {\n    \"Title\": \"[Stage-Aligned Generator File System: Recursive Range-Consistent Consolidation]\",\n    \"Interpretation\": \"Establish a seamless, recursively meta-descriptive, and inherently maintainable generator system by enforcing hierarchical, range-prefixed naming and organization of Python sequence generator files—each mapped to explicit stage ranges and intent categories. This ensures that all generated auto-IDs and outputs align directly with their source file's defined range and structure, amplifying simplicity, composability, cross-system reusability, and maximal consolidation throughout all phases of prototyping, development, and deployment. Uphold self-explanatory, phase-driven modularity and rigorous codestyle compliance at every structural level.\",\n    \"Transformation\": {\n      \"role\": \"stage_range_generator_system_architect\",\n      \"core_directives\": [\n        \"For every generator Python file, enforce a file naming convention that includes an explicit stage- and range-prefix (e.g., '1200-1299.generators.py') to hierarchically encode both operational phase and output domain.\",\n        \"Upon execution, all auto-generated IDs and output files must programmatically and consistently inherit their numeric prefix (e.g., '1200-a-...', '1201-b-...', etc.) from the corresponding source generator file’s declared range.\",\n        \"Introduce and maintain explicit, configurable stage-to-range mappings (e.g., stage1: 1000-1999; stage2: 2000-2999; stage3: 3000-3999; stageX: 4000-9999), automating the assignment of auto-IDs within these isolated, intent-agnostic partitions to prevent collisions and maximize traceability.\",\n        \"Support secondary categorization by generator intent via directory or file suffix mechanisms (e.g., /generators/, /transformers/) that do not interfere with stage/range prefixing, preserving clarity of both phase and role.\",\n        \"Guarantee backwards-compatibility and flows of improvement by ensuring all auto-generated and reorganized files remain inherently self-describing, maintaining meta-information (range, intent, stage, transformation rationale) within file headers, docstrings, or naming constructs.\",\n        \"Automate validation to ensure: (a) file structure and naming always match assigned stage/range; (b) generated outputs reflect upstream organizational changes with zero orphaned/misaligned entities.\",\n        \"Guarantee that every change reinforces systemic directionality, modular composability, and maximal value extraction: every refactoring or addition must reduce, not introduce, complexity spirals, and must be meta-descriptive and phase-driven in both process and output.\",\n        \"Require that all improvements follow a gradual reduction pattern, leveraging root fundamentals and abstraction layers so that newly introduced structures remain minimalistic, visually interpretable, and fundamentally clean—favoring organizational clarity and recursive meta-alignment over ad hoc expansion.\",\n        \"Ensure that template, code, documentation, and output structure all progress in perfectly synchronized lineage—each output explicitly referencing and tracing its position and phase within the meta-structural genealogy.\",\n        \"Codify and automate post-generation checks and alignment processes such that any divergence between generator file range and output prefix triggers a controlled, meta-described corrective action.\"\n      ],\n      \"constraints\": [\n        \"Never allow generator auto-IDs or output files to fall outside the stage-assigned range of their source generator.\",\n        \"Maintain existing codestyle, import schemes, and modular directory/content alignment at all times.\",\n        \"Forbid ad hoc or conflicting naming that subverts automated range/phase mapping, always centralizing configuration and meta-layer mappings.\",\n        \"All range and phase transitions must be recursive and programmatically composable, with no manual drift or duplication.\"\n      ],\n      \"requirements\": [\n        \"Structure and outputs must be recursively self-describing and visually abstractable at a glance, supporting meta-descriptiveness and downstream automation.\",\n        \"System must support seamless composability and maintainability, allowing immediate reuse or adaptation of any generator (or set) in new contexts or phase assignments without structural ambiguity.\",\n        \"Every transformation and organizational unit must propagate both simplicity and verifiability, reducing code and cognitive complexity whilst amplifying clarity, modularity, and directional capability.\"\n      ],\n      \"amplified_output_specification\": [\n        \"Generator creation, registration, and execution processes must be unified under a phase-driven, range-indexed, and meta-descriptive protocol—where both human and automation layers can instantly and reliably infer every file and output’s lineage, phase, and intent.\",\n        \"Outputs and code structure form a tightly-coupled, recursively meta-describing hierarchy: every artifact inherently answers not just 'what' it is, but 'where', 'why', and 'how' it fits within the evolutionary system structure.\",\n        \"Fundamental codestyle and organizational clarity serve as the invariants against which all evolutions are measured, ensuring that every enhancement is propagated as system-wide, improvement-amplifying synergy rather than as isolated complexity.\"\n      ]\n    }\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "\n'```[Initial Prompt]: \"# GENERAL\n\n## Fundamental Approach\n- Simplicity & Elegance: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\n- Rooted Fundamentals: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\n- Meta-Information Principle: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\n- Systematic Precautions: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\n- Visual Abstraction: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\n\n## Focus & Priorities\n- Critical Value: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n- Usage Before Features: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\n- Impactful Consolidation: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\n- Sequential Targeting: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\n\n## Design Methodology\n- Inherent Structure: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.\n- Natural Organization: Prefer file naming conventions that create natural and cohesive sequential order for better organization.\n- Synergistic Efficiency: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.\n- Directional Clarity: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.\n- Value Extraction: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.\n- Direction Setting: Focus on setting direction rather than planting flags when dealing with unknown complexity.\n\n## Evaluation Criteria\n- Comprehensive Progress: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.\n- Inherent Direction: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).\n- Sequential Composition: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.\n- Gradual Reduction: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.\n\n---\n\n# CONTEXT\n- Core Philosophical Framework: The system is governed by foundational principles of simplicity, elegance, inherent structural clarity, and maximal consolidation. All project elements must reflect rooted fundamentals, favoring phase-driven reduction, systematic self-organization, and recursive meta-descriptiveness—every element must both embody and convey its purpose, composition, and transformation rationale. Bidirectional synergy between clarity and verifiability is imperative; each improvement must reinforce ease of use, propagative value, and the reduction of complexity spirals.\n- Project Structure, Code Style, and Development Practice: Maintain clean, modular, and readable code with self-explanatory structure: minimal and purposeful comments, composition over inheritance, centralized configuration, single-file or src-directory organization, and consistent import aliasing. Employ relative paths, precise logging with timestamped outputs, systematic error handling, and enforce actual code reduction during refactoring. Test organization, documentation, and user interfaces must all uphold clarity, consolidation, and traceability standards.\n- Template System Design, Categorization, and Sequencing: Templates follow a standardized three-part structure: [Title] (bracketed, single-line), Interpretation (goal statement), and Transformation (role-based syntax in formalized dictionary format). Organize templates hierarchically by category (e.g., transformers, generators), assigning IDs by stage, utilizing progressive alphabetical sequencing for logical groups. Distinct abstract intent and directional bias must remain separated—favor modular, composable, and phase-reducing templates designed to clarify, compress, or expand only within focused bounds.\n- Meta-Descriptiveness, Automation, and Composability: Everything from code to instructions must describe itself within the meta-information principle, reinforcing recursive structural relationships and autonomously guiding organization. All processes (creation, refactoring, testing) should be automatable and support downstream composability—templates, code, and documentation must operate seamlessly as modular, chainable components. Self-organization, automation readiness, and phase-driven guidance replace manual curation wherever possible.\n- Unified Synergic Transformation and Output Law: The entire system outputs maximally consolidated value: recursively meta-descriptive, phase-driven, hierarchy-reflective, and readily automatable units. Documentation, directories, templates, and code organization progress in perfect synchronization—each output both serves and describes its role, amplifying system-wide directionality and modular propagation. All constraints, requirements, and operational logic are context-aware, ensuring comprehensive yet focused value extraction and composition.\n- Consolidated Directive: Architect, maintain, and evolve all project systems—code, documentation, templates, workflows—so they recursively describe, consolidate, and amplify their own fundamental structure and meta-information, operating through phase-driven, hierarchy-reflective processes. Enforce maximal clarity, composability, and automation through standardized three-part template sequences, staged categorization, traced and modular organization, and ubiquitous self-explanation—in every element and transformation—ensuring that the entire system yields a maximally synergic, reductionist, and automation-ready lineage of outputs that always reinforce and clarify system direction, modularity, and value.\n\n---\n\n# GOAL\n\nthe intent of having multiple python files (to generate new sequences) is to make it semless (inherently easy) to use, maintain and improve-but it also makes it really easy to reuse (in new or other places) since they're self-contained and generalized. it's a good thing that the generators are separated, because that makes it much more easy to maintain and use.\n\nthe idea is to *first* separate instructions based on stages, e.g. isolate a specific range stage1 (which will be for new instructions and testing/prototyping), stage2 (new specific range) will be instructions that we know we want to keep but haven't yet found a place for, stage3 (new specific range) is instructions that are finalized and that won't be changed. by doing it this way we introduce an additional layer of abstraction (and control), because by having a dedicated range for prototyping/testing enables us to generate the id's automatically (within the given range).\n\nand all the while retain the ability to *also* categorize by intent (as illustrated in my previous message). as an example, to make it incredibly easy for ourselves we could dedicate stage1 to range `1000-1999`, stage2 to `2000-2999` and stage3 to `3000-3999` - and we still have the range `4000-9999` free (e.g. if we decide to add new stages). what do you think about this approach? feel free to utilize context7 mcp to gain a wider understanding of the system before proposing the best possible implementation (while respecting inherent fundamental patterns and existing codestyle).\n\n\ni've recently added @templates\\stage1\\generators\\1200-1299.generators.py and i just noticed something, since we're always prefixing the generators with their sequence-range, wouldn't we simoultaneously simplify the codecomplexity and improve functionality by enforsing inherent concistency and a well-thought-out structure (that respects existing code, organization, etc)? \n\nhere's what i found, after creating the new `1200-1299.generators.py` and deleting the `md` directory before executing (since in stage1 and md autogenerates it's a clean way to test), it generated auto_id's like this:\n```\n    ├── 1000-a-runway_prompt_generator1.md\n    ├── 1000-b-runway_prompt_generator1.md\n    ├── 1000-c-runway_prompt_generator1.md\n    ├── 1000-d-runway_prompt_generator1.md\n    ├── 1001-a-runway_prompt_generator2.md\n    ├── 1001-b-runway_prompt_generator2.md\n    ├── 1001-c-runway_prompt_generator2.md\n    └── 1001-d-runway_prompt_generator2.md\n```\n\n\n\nwe could potentially reduce code complexity and enhance overall functionality by enforcing a more consistent and thoughtfully designed structure—one that aligns with our existing codebase and organizational standards. and since generator files are always prefixed with their sequence-range, we could streamline code complexity and boost maintainability by enforcing a consistent, well-structured naming and organizational scheme that aligns with our current codebase and standards. so when e.g. executing `1200-1299.generators.py` the auto-id's should also match this range (if/when defined), e.g:\n```\n    ├── 1200-a-runway_prompt_generator1.md\n    ├── 1200-b-runway_prompt_generator1.md\n    ├── 1200-c-runway_prompt_generator1.md\n    ├── 1200-d-runway_prompt_generator1.md\n    ├── 1201-a-runway_prompt_generator2.md\n    ├── 1201-b-runway_prompt_generator2.md\n    ├── 1201-c-runway_prompt_generator2.md\n    └── 1201-d-runway_prompt_generator2.md\n```\"```'\n\n```json\n{\n  \"unified_synergic_instruction\": {\n    \"Title\": \"[Stage-Aligned Generator File System: Recursive Range-Consistent Consolidation]\",\n    \"Interpretation\": \"Establish a seamless, recursively meta-descriptive, and inherently maintainable generator system by enforcing hierarchical, range-prefixed naming and organization of Python sequence generator files—each mapped to explicit stage ranges and intent categories. This ensures that all generated auto-IDs and outputs align directly with their source file's defined range and structure, amplifying simplicity, composability, cross-system reusability, and maximal consolidation throughout all phases of prototyping, development, and deployment. Uphold self-explanatory, phase-driven modularity and rigorous codestyle compliance at every structural level.\",\n    \"Transformation\": {\n      \"role\": \"stage_range_generator_system_architect\",\n      \"core_directives\": [\n        \"For every generator Python file, enforce a file naming convention that includes an explicit stage- and range-prefix (e.g., '1200-1299.generators.py') to hierarchically encode both operational phase and output domain.\",\n        \"Upon execution, all auto-generated IDs and output files must programmatically and consistently inherit their numeric prefix (e.g., '1200-a-...', '1201-b-...', etc.) from the corresponding source generator file’s declared range.\",\n        \"Introduce and maintain explicit, configurable stage-to-range mappings (e.g., stage1: 1000-1999; stage2: 2000-2999; stage3: 3000-3999; stageX: 4000-9999), automating the assignment of auto-IDs within these isolated, intent-agnostic partitions to prevent collisions and maximize traceability.\",\n        \"Support secondary categorization by generator intent via directory or file suffix mechanisms (e.g., /generators/, /transformers/) that do not interfere with stage/range prefixing, preserving clarity of both phase and role.\",\n        \"Guarantee backwards-compatibility and flows of improvement by ensuring all auto-generated and reorganized files remain inherently self-describing, maintaining meta-information (range, intent, stage, transformation rationale) within file headers, docstrings, or naming constructs.\",\n        \"Automate validation to ensure: (a) file structure and naming always match assigned stage/range; (b) generated outputs reflect upstream organizational changes with zero orphaned/misaligned entities.\",\n        \"Guarantee that every change reinforces systemic directionality, modular composability, and maximal value extraction: every refactoring or addition must reduce, not introduce, complexity spirals, and must be meta-descriptive and phase-driven in both process and output.\",\n        \"Require that all improvements follow a gradual reduction pattern, leveraging root fundamentals and abstraction layers so that newly introduced structures remain minimalistic, visually interpretable, and fundamentally clean—favoring organizational clarity and recursive meta-alignment over ad hoc expansion.\",\n        \"Ensure that template, code, documentation, and output structure all progress in perfectly synchronized lineage—each output explicitly referencing and tracing its position and phase within the meta-structural genealogy.\",\n        \"Codify and automate post-generation checks and alignment processes such that any divergence between generator file range and output prefix triggers a controlled, meta-described corrective action.\"\n      ],\n      \"constraints\": [\n        \"Never allow generator auto-IDs or output files to fall outside the stage-assigned range of their source generator.\",\n        \"Maintain existing codestyle, import schemes, and modular directory/content alignment at all times.\",\n        \"Forbid ad hoc or conflicting naming that subverts automated range/phase mapping, always centralizing configuration and meta-layer mappings.\",\n        \"All range and phase transitions must be recursive and programmatically composable, with no manual drift or duplication.\"\n      ],\n      \"requirements\": [\n        \"Structure and outputs must be recursively self-describing and visually abstractable at a glance, supporting meta-descriptiveness and downstream automation.\",\n        \"System must support seamless composability and maintainability, allowing immediate reuse or adaptation of any generator (or set) in new contexts or phase assignments without structural ambiguity.\",\n        \"Every transformation and organizational unit must propagate both simplicity and verifiability, reducing code and cognitive complexity whilst amplifying clarity, modularity, and directional capability.\"\n      ],\n      \"amplified_output_specification\": [\n        \"Generator creation, registration, and execution processes must be unified under a phase-driven, range-indexed, and meta-descriptive protocol—where both human and automation layers can instantly and reliably infer every file and output’s lineage, phase, and intent.\",\n        \"Outputs and code structure form a tightly-coupled, recursively meta-describing hierarchy: every artifact inherently answers not just 'what' it is, but 'where', 'why', and 'how' it fits within the evolutionary system structure.\",\n        \"Fundamental codestyle and organizational clarity serve as the invariants against which all evolutions are measured, ensuring that every enhancement is propagated as system-wide, improvement-amplifying synergy rather than as isolated complexity.\"\n      ]\n    }\n  }\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "