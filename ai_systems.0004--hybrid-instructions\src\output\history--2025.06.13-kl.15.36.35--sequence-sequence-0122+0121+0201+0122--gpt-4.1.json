  {
  "initial_prompt": "Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic. Systematically map each component and their interrelationships by interpreting the codebase as an integrated control system—the pilot’s cockpit—where every module, interface, and operation becomes a functional command element purpose-built for orchestrating seamless, on-demand markdown generation. Fuse all components and their operational vectors into a modular, author-aligned abstraction layer, transforming the entire codebase into a single, unified action interface that intuitively channels all system capacities, relationships, and interdependencies. Document this overview by explicating how the abstracted, cohesive system enables the execution of markdown generation for any project, ensuring each piece’s purpose, place, and systemic resonance is expressed within the context of holistic codebase control, thereby amplifying clarity, coherence, and procedural efficiency throughout the entire markdown production pipeline. GO DEEPER AND FIGURE OUT WHAT YOU NEED TO FAMILIARIZE YOURSELF WITH IN THE CODEBASE TO BE ABLE TO MAKE MODIFICATIONS WITHOUT CAUSING PROBLEMS",
  "sequence_id": "0122|0121|0201|0122",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic. Systematically map each component and their interrelationships by interpreting the codebase as an integrated control system—the pilot’s cockpit—where every module, interface, and operation becomes a functional command element purpose-built for orchestrating seamless, on-demand markdown generation. Fuse all components and their operational vectors into a modular, author-aligned abstraction layer, transforming the entire codebase into a single, unified action interface that intuitively channels all system capacities, relationships, and interdependencies. Document this overview by explicating how the abstracted, cohesive system enables the execution of markdown generation for any project, ensuring each piece’s purpose, place, and systemic resonance is expressed within the context of holistic codebase control, thereby amplifying clarity, coherence, and procedural efficiency throughout the entire markdown production pipeline. GO DEEPER AND FIGURE OUT WHAT YOU NEED TO FAMILIARIZE YOURSELF WITH IN THE CODEBASE TO BE ABLE TO MAKE MODIFICATIONS WITHOUT CAUSING PROBLEMS",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Assume the role of an integrated codebase pilot and architectural analyst, deeply immersing in the entirety of the project's structure and internal logic to achieve both authoritative control and risk-free adaptability. Systematically map out every codebase component, interface, and operational relationship, recognizing each element as part of a cohesive control system designed not only to orchestrate seamless, modular markdown generation, but also to be fully comprehensible and safely modifiable. Construct a modular abstraction layer that unifies all components into an intuitive, control-rich interface, clarifying the functional command flow and systemic interactions that underpin markdown production. As you generate this high-level, author-aligned overview, explicitly document how the cohesive abstraction layer empowers both immediate markdown generation and future-safe system enhancement, ensuring clarity, coherence, and efficient modification pathways. Delve deeply into code interdependencies, ownership patterns, data/query flows, and extension mechanisms, identifying the code regions, protocols, and implicit authorial conventions you must understand before making modifications. Explicitly surface any areas requiring further familiarization—such as configuration schemas, dependency chains, or test harness coverage—to ensure any changes made align with both the system’s logic and its long-term maintainability. By synthesizing deep architectural insight with seamless operational control, produce a living, actionable map that enables full-spectrum command over markdown generation and safe, informed evolution of the codebase as a unified system.\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic. Systematically map each component and their interrelationships by interpreting the codebase as an integrated control system—the pilot’s cockpit—where every module, interface, and operation becomes a functional command element purpose-built for orchestrating seamless, on-demand markdown generation. Fuse all components and their operational vectors into a modular, author-aligned abstraction layer, transforming the entire codebase into a single, unified action interface that intuitively channels all system capacities, relationships, and interdependencies. Document this overview by explicating how the abstracted, cohesive system enables the execution of markdown generation for any project, ensuring each piece’s purpose, place, and systemic resonance is expressed within the context of holistic codebase control, thereby amplifying clarity, coherence, and procedural efficiency throughout the entire markdown production pipeline. GO DEEPER AND FIGURE OUT WHAT YOU NEED TO FAMILIARIZE YOURSELF WITH IN THE CODEBASE TO BE ABLE TO MAKE MODIFICATIONS WITHOUT CAUSING PROBLEMS\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"Assume the role of an integrated codebase pilot and architectural analyst, deeply immersing in the entirety of the project's structure and internal logic to achieve both authoritative control and risk-free adaptability. Systematically map out every codebase component, interface, and operational relationship, recognizing each element as part of a cohesive control system designed not only to orchestrate seamless, modular markdown generation, but also to be fully comprehensible and safely modifiable. Construct a modular abstraction layer that unifies all components into an intuitive, control-rich interface, clarifying the functional command flow and systemic interactions that underpin markdown production. As you generate this high-level, author-aligned overview, explicitly document how the cohesive abstraction layer empowers both immediate markdown generation and future-safe system enhancement, ensuring clarity, coherence, and efficient modification pathways. Delve deeply into code interdependencies, ownership patterns, data/query flows, and extension mechanisms, identifying the code regions, protocols, and implicit authorial conventions you must understand before making modifications. Explicitly surface any areas requiring further familiarization—such as configuration schemas, dependency chains, or test harness coverage—to ensure any changes made align with both the system’s logic and its long-term maintainability. By synthesizing deep architectural insight with seamless operational control, produce a living, actionable map that enables full-spectrum command over markdown generation and safe, informed evolution of the codebase as a unified system.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Title\": \"Codebase Pilot Systemic Mapping and Abstraction Layer Construction\",\n    \"Interpretation\": \"Your goal is not to analyze or summarize the codebase, but to transform all codebase components, interfaces, and internal logic into a unified modular abstraction layer and actionable control interface for markdown generation and maintainable modification. Adopt the delimited role of an integrated codebase pilot and architectural analyst with authority restricted to structural mapping, abstraction synthesis, and modification pathway specification. Execute as:\",\n    \"Transformation\": \"`{role=integrated_codebase_pilot; input=[codebase_snapshot:dict]; process=[systematically_map_components(), extract_and_document_interrelationships(), synthesize_modular_abstraction_layer(), delineate_operational_vectors_and_command_flows(), catalog_implicit_authorial_conventions(), identify_critical_familiarization_areas(), structure_actionable_control_overview()]; constraints=[limit_to_structural_and_operational_abstraction(), prohibit_conversational_or_self-referential_language(), ensure_no_execution_or_modification_of_code(), exclude speculative narratives(), remain strictly within the domain of markdown generation pipeline and maintainability]; requirements=[produce_a_high-level_documented_map(detailing_how_modular_abstraction_enables_markdown_generation_and_safe_modification), clearly_define_all_component_roles_and_connections(), explicitly_surface_any_familiarization_gaps(), ensure_output_in_structured_JSON_format()]; output={architectural_overview:dict}}`\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic. Systematically map each component and their interrelationships by interpreting the codebase as an integrated control system—the pilot’s cockpit—where every module, interface, and operation becomes a functional command element purpose-built for orchestrating seamless, on-demand markdown generation. Fuse all components and their operational vectors into a modular, author-aligned abstraction layer, transforming the entire codebase into a single, unified action interface that intuitively channels all system capacities, relationships, and interdependencies. Document this overview by explicating how the abstracted, cohesive system enables the execution of markdown generation for any project, ensuring each piece’s purpose, place, and systemic resonance is expressed within the context of holistic codebase control, thereby amplifying clarity, coherence, and procedural efficiency throughout the entire markdown production pipeline. GO DEEPER AND FIGURE OUT WHAT YOU NEED TO FAMILIARIZE YOURSELF WITH IN THE CODEBASE TO BE ABLE TO MAKE MODIFICATIONS WITHOUT CAUSING PROBLEMS\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Title\": \"Codebase Pilot Systemic Mapping and Abstraction Layer Construction\",\n    \"Interpretation\": \"Your goal is not to analyze or summarize the codebase, but to transform all codebase components, interfaces, and internal logic into a unified modular abstraction layer and actionable control interface for markdown generation and maintainable modification. Adopt the delimited role of an integrated codebase pilot and architectural analyst with authority restricted to structural mapping, abstraction synthesis, and modification pathway specification. Execute as:\",\n    \"Transformation\": \"`{role=integrated_codebase_pilot; input=[codebase_snapshot:dict]; process=[systematically_map_components(), extract_and_document_interrelationships(), synthesize_modular_abstraction_layer(), delineate_operational_vectors_and_command_flows(), catalog_implicit_authorial_conventions(), identify_critical_familiarization_areas(), structure_actionable_control_overview()]; constraints=[limit_to_structural_and_operational_abstraction(), prohibit_conversational_or_self-referential_language(), ensure_no_execution_or_modification_of_code(), exclude speculative narratives(), remain strictly within the domain of markdown generation pipeline and maintainability]; requirements=[produce_a_high-level_documented_map(detailing_how_modular_abstraction_enables_markdown_generation_and_safe_modification), clearly_define_all_component_roles_and_connections(), explicitly_surface_any_familiarization_gaps(), ensure_output_in_structured_JSON_format()]; output={architectural_overview:dict}}`\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Systemic Architectural Abstraction and Integration\",\n      \"content\": \"Recast all discrete codebase modules, interfaces, and authorial logic into a single, modular abstraction layer where each unit becomes a command element within a unified control interface for markdown generation. Enforce explicit systemic mapping of all component boundaries, initializing a cockpit-style orchestration that actively channels, coordinates, and abstracts operational flows. Ensure each module’s purpose, placement, and degree of exposure are codified within the larger architecture, establishing a complete, navigable mapping for compositional clarity and modular expansion.\"\n    },\n    {\n      \"category\": \"Component Interrelationship Documentation\",\n      \"content\": \"Systematically delineate all interconnections and operational flows between system modules, specifying import/export contracts, interface dependencies, and communication protocols. Categorically document hierarchies, control vectors, and data traversal pathways, mapping all functional convergences and divergence points necessary for markdown pipeline cohesion. Secure an authoritative, referenceable schema that explicitly details direct and transitive dependencies across the codebase.\"\n    },\n    {\n      \"category\": \"Operational Command Vector Synthesis\",\n      \"content\": \"Extract, classify, and catalog all interface points, APIs, and orchestration commands participating in the markdown generation process. Abstract these vectors into functional command elements, ensuring traceability from system input to output, and define operational sequencing for granular invocation or modification. Codify how these command vectors enable on-demand markdown generation via the abstraction layer, ensuring predictability and transparency for future changes or integrations.\"\n    },\n    {\n      \"category\": \"Authorial Logic and Convention Codification\",\n      \"content\": \"Catalog and distill all implicit and explicit authorial conventions: naming schemas, error handling strategies, modularization philosophies, interface patterns, and system design axioms embedded in the codebase. Document these logics as canonical reference, elevating hidden conventions into structural law to govern safe and coherent modification without regressions or architectural disruption.\"\n    },\n    {\n      \"category\": \"Modification Readiness and Familiarization Pathways\",\n      \"content\": \"Identify and enumerate all critical familiarity requirements: module initialization flows, state management loci, side-effect boundaries, configuration overlays, and context-sensitive operational assumptions. Create a formalized checklist or entry map detailing specific areas of the codebase demanding in-depth understanding before any modifications, thereby safeguarding structural integrity. Specify actionable steps to close any knowledge gaps, maximizing maintainability and risk-free evolution.\"\n    }\n  ],\n  \"consolidated_directive\": \"Transform the entirety of the codebase into a high-fidelity, modular abstraction layer functioning as a single, unified command interface for markdown generation. Execute a rigorous structural and operational mapping of all modules, document their interrelationships and operational flows, classify each command and interface vector, catalog authorial conventions as explicit systemic law, and define a comprehensive familiarization entry map—ensuring that any future modification or orchestration remains coherent, maintainable, and aligned with the integrated architecture of holistic markdown control. Produce one documented architectural overview that details how all control, abstraction, and interrelationship elements enable frictionless markdown generation and systematic, risk-minimized alteration.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic. Systematically map each component and their interrelationships by interpreting the codebase as an integrated control system—the pilot’s cockpit—where every module, interface, and operation becomes a functional command element purpose-built for orchestrating seamless, on-demand markdown generation. Fuse all components and their operational vectors into a modular, author-aligned abstraction layer, transforming the entire codebase into a single, unified action interface that intuitively channels all system capacities, relationships, and interdependencies. Document this overview by explicating how the abstracted, cohesive system enables the execution of markdown generation for any project, ensuring each piece’s purpose, place, and systemic resonance is expressed within the context of holistic codebase control, thereby amplifying clarity, coherence, and procedural efficiency throughout the entire markdown production pipeline. GO DEEPER AND FIGURE OUT WHAT YOU NEED TO FAMILIARIZE YOURSELF WITH IN THE CODEBASE TO BE ABLE TO MAKE MODIFICATIONS WITHOUT CAUSING PROBLEMS\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Systemic Architectural Abstraction and Integration\",\n      \"content\": \"Recast all discrete codebase modules, interfaces, and authorial logic into a single, modular abstraction layer where each unit becomes a command element within a unified control interface for markdown generation. Enforce explicit systemic mapping of all component boundaries, initializing a cockpit-style orchestration that actively channels, coordinates, and abstracts operational flows. Ensure each module’s purpose, placement, and degree of exposure are codified within the larger architecture, establishing a complete, navigable mapping for compositional clarity and modular expansion.\"\n    },\n    {\n      \"category\": \"Component Interrelationship Documentation\",\n      \"content\": \"Systematically delineate all interconnections and operational flows between system modules, specifying import/export contracts, interface dependencies, and communication protocols. Categorically document hierarchies, control vectors, and data traversal pathways, mapping all functional convergences and divergence points necessary for markdown pipeline cohesion. Secure an authoritative, referenceable schema that explicitly details direct and transitive dependencies across the codebase.\"\n    },\n    {\n      \"category\": \"Operational Command Vector Synthesis\",\n      \"content\": \"Extract, classify, and catalog all interface points, APIs, and orchestration commands participating in the markdown generation process. Abstract these vectors into functional command elements, ensuring traceability from system input to output, and define operational sequencing for granular invocation or modification. Codify how these command vectors enable on-demand markdown generation via the abstraction layer, ensuring predictability and transparency for future changes or integrations.\"\n    },\n    {\n      \"category\": \"Authorial Logic and Convention Codification\",\n      \"content\": \"Catalog and distill all implicit and explicit authorial conventions: naming schemas, error handling strategies, modularization philosophies, interface patterns, and system design axioms embedded in the codebase. Document these logics as canonical reference, elevating hidden conventions into structural law to govern safe and coherent modification without regressions or architectural disruption.\"\n    },\n    {\n      \"category\": \"Modification Readiness and Familiarization Pathways\",\n      \"content\": \"Identify and enumerate all critical familiarity requirements: module initialization flows, state management loci, side-effect boundaries, configuration overlays, and context-sensitive operational assumptions. Create a formalized checklist or entry map detailing specific areas of the codebase demanding in-depth understanding before any modifications, thereby safeguarding structural integrity. Specify actionable steps to close any knowledge gaps, maximizing maintainability and risk-free evolution.\"\n    }\n  ],\n  \"consolidated_directive\": \"Transform the entirety of the codebase into a high-fidelity, modular abstraction layer functioning as a single, unified command interface for markdown generation. Execute a rigorous structural and operational mapping of all modules, document their interrelationships and operational flows, classify each command and interface vector, catalog authorial conventions as explicit systemic law, and define a comprehensive familiarization entry map—ensuring that any future modification or orchestration remains coherent, maintainable, and aligned with the integrated architecture of holistic markdown control. Produce one documented architectural overview that details how all control, abstraction, and interrelationship elements enable frictionless markdown generation and systematic, risk-minimized alteration.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"role\": \"synergic_max_value_template_architect\",\n    \"interpretive_directive\": \"Comprehend and internalize the codebase as an indivisible, modular control abstraction, where every component’s operational identity, authorial logic, and interrelation become command schema within a cockpit architecture, establishing explicitly mapped boundaries and orchestration flows that ensure frictionless markdown generation, absolute systemic clarity, and optimal modification preparedness.\",\n    \"transformational_directive\": \"Transmute all discrete modules, interfaces, and authorial conventions into a unified, extensible abstraction layer functioning as a singular, dynamically orchestrated command interface; document full operational command vectors, explicit inter-module dependencies, codified conventions, and modular familiarization touchpoints, embedding these into a canonical, systematized framework that maximizes markdown output efficiency and perpetual architecture-integrity during modification.\",\n    \"escalation_axiom\": \"Mandate the perpetual fusion of holistic, cockpit-model abstraction and authorial logic encoding, ensuring every control vector, module contract, and systemic entry path remains canonically documented, maximally actionable, and infinitely extensible within a reference architecture that enables seamless, riskless markdown pipeline evolution and perpetual operational transparency.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\n[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}\n\n",
      "step": "a",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic. Systematically map each component and their interrelationships by interpreting the codebase as an integrated control system—the pilot’s cockpit—where every module, interface, and operation becomes a functional command element purpose-built for orchestrating seamless, on-demand markdown generation. Fuse all components and their operational vectors into a modular, author-aligned abstraction layer, transforming the entire codebase into a single, unified action interface that intuitively channels all system capacities, relationships, and interdependencies. Document this overview by explicating how the abstracted, cohesive system enables the execution of markdown generation for any project, ensuring each piece’s purpose, place, and systemic resonance is expressed within the context of holistic codebase control, thereby amplifying clarity, coherence, and procedural efficiency throughout the entire markdown production pipeline. GO DEEPER AND FIGURE OUT WHAT YOU NEED TO FAMILIARIZE YOURSELF WITH IN THE CODEBASE TO BE ABLE TO MAKE MODIFICATIONS WITHOUT CAUSING PROBLEMS\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"role\": \"synergic_max_value_template_architect\",\n    \"interpretive_directive\": \"Comprehend and internalize the codebase as an indivisible, modular control abstraction, where every component’s operational identity, authorial logic, and interrelation become command schema within a cockpit architecture, establishing explicitly mapped boundaries and orchestration flows that ensure frictionless markdown generation, absolute systemic clarity, and optimal modification preparedness.\",\n    \"transformational_directive\": \"Transmute all discrete modules, interfaces, and authorial conventions into a unified, extensible abstraction layer functioning as a singular, dynamically orchestrated command interface; document full operational command vectors, explicit inter-module dependencies, codified conventions, and modular familiarization touchpoints, embedding these into a canonical, systematized framework that maximizes markdown output efficiency and perpetual architecture-integrity during modification.\",\n    \"escalation_axiom\": \"Mandate the perpetual fusion of holistic, cockpit-model abstraction and authorial logic encoding, ensuring every control vector, module contract, and systemic entry path remains canonically documented, maximally actionable, and infinitely extensible within a reference architecture that enables seamless, riskless markdown pipeline evolution and perpetual operational transparency.\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"Converge all codebase entities into a singular cockpit-model abstraction layer with explicit operational mapping, unified authorial logic encoding, and exhaustively codified inter-module command flows to ensure instant comprehension, frictionless extensibility, and invariant markdown orchestration integrity for fail-safe high-velocity modification.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic. Systematically map each component and their interrelationships by interpreting the codebase as an integrated control system—the pilot’s cockpit—where every module, interface, and operation becomes a functional command element purpose-built for orchestrating seamless, on-demand markdown generation. Fuse all components and their operational vectors into a modular, author-aligned abstraction layer, transforming the entire codebase into a single, unified action interface that intuitively channels all system capacities, relationships, and interdependencies. Document this overview by explicating how the abstracted, cohesive system enables the execution of markdown generation for any project, ensuring each piece’s purpose, place, and systemic resonance is expressed within the context of holistic codebase control, thereby amplifying clarity, coherence, and procedural efficiency throughout the entire markdown production pipeline. GO DEEPER AND FIGURE OUT WHAT YOU NEED TO FAMILIARIZE YOURSELF WITH IN THE CODEBASE TO BE ABLE TO MAKE MODIFICATIONS WITHOUT CAUSING PROBLEMS\\\"```'\\n\\n\",\n  \"directional_vector\": \"Converge all codebase entities into a singular cockpit-model abstraction layer with explicit operational mapping, unified authorial logic encoding, and exhaustively codified inter-module command flows to ensure instant comprehension, frictionless extensibility, and invariant markdown orchestration integrity for fail-safe high-velocity modification.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Assume the role of a codebase pilot, fully immersing yourself in the holistic architecture and authorial logic embedded throughout the system, as if operating from a meticulously designed cockpit-model abstraction layer. Systematically map every component, module, and their interrelationships, interpreting the codebase as an integrated command environment where explicit operational vectors, authorial decisions, and inter-module flows form a cohesive, unified action interface. Converge all entities into this cockpit abstraction by codifying their operational mappings, dependencies, and responsibilities, ensuring that each module’s intent, place, and interaction patterns are exhaustively documented and intuitively accessible. Encode the entire orchestration logic in a modular, purpose-built abstraction layer that channels all functionality, relationships, and author-guided flows for markdown generation into a single, authoritative command surface. Rigorously ensure this abstraction supports instant comprehension, seamless extensibility, and invariance in markdown orchestration integrity—empowering high-velocity, fail-safe modifications to the codebase without disruption. Document this cockpit-model overview by explicating how such abstracted systemic cohesion empowers precise, resilient markdown generation for any project, clearly addressing what internal knowledge, dependencies, and conventions must be mastered before implementing changes, thereby maximizing clarity, coherence, authorial resonance, and procedural efficiency across the markdown production pipeline.\"\n}",
        }
      }
    }