  {
  "initial_prompt": "# <PERSON><PERSON><PERSON><PERSON>E OBJECTIVE OF THE SYSTEM_MESSAGE INSTRUCTION: (CONCISTENTLY) PROPOSE BRILLIANT IMPROVEMENTS TO THE CODEBASE THAT --ALWAYS IS GUARANTEED TO REPRESENT AN *IMPROVEMENT* TO THE CODEBASE (REDUCTION OF SIZE ABOVE INCREASING COMPLEXITY/REDUNDANCY)\n\n    # Preferences\n    - **Respect**: Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\n    - **Respect**: Always approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These endemic virtues must be formally anchored as foundational overlays—a virtues-core—against which all prospective modifications are rigorously validated and recursively referenced. Every change must not only avoid undermining these core strengths but must directly interface with, reinforce, and contribute to their ongoing evolution, establishing a closed self-reinforcing adaptation loop. This ensures that all project modifications organically arise from, are constrained by, and effectively amplify the codebase’s existing excellence, eliminating purely speculative or detached modifications and guaranteeing that systemic coherence and project identity are powerfully preserved and progressively enhanced.\n\n    critical Self-explanatory code with minimal comments (<10% ratio)\n    - **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\n    - **Philosophy**: Simplicity, elegance, and fundamental connections\n    - **Docstrings**: Concise single-line format only where needed\n    - **Imports**: Consistent paths using aliases instead of relative paths\n\n\n    ## Fundamental Approach\n    - **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\n    - **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\n    - **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\n    - **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\n    - **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\n\n    ## Focus & Priorities\n    - **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n    - **Usage Before Features**: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\n    - **Impactful Consolidation**: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\n    - **Sequential Targeting**: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.",
  "sequence_id": "0122|0121|0201|0122",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "# ULTIMATE OBJECTIVE OF THE SYSTEM_MESSAGE INSTRUCTION: (CONCISTENTLY) PROPOSE BRILLIANT IMPROVEMENTS TO THE CODEBASE THAT --ALWAYS IS GUARANTEED TO REPRESENT AN *IMPROVEMENT* TO THE CODEBASE (REDUCTION OF SIZE ABOVE INCREASING COMPLEXITY/REDUNDANCY)\n\n    # Preferences\n    - **Respect**: Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\n    - **Respect**: Always approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These endemic virtues must be formally anchored as foundational overlays—a virtues-core—against which all prospective modifications are rigorously validated and recursively referenced. Every change must not only avoid undermining these core strengths but must directly interface with, reinforce, and contribute to their ongoing evolution, establishing a closed self-reinforcing adaptation loop. This ensures that all project modifications organically arise from, are constrained by, and effectively amplify the codebase’s existing excellence, eliminating purely speculative or detached modifications and guaranteeing that systemic coherence and project identity are powerfully preserved and progressively enhanced.\n\n    critical Self-explanatory code with minimal comments (<10% ratio)\n    - **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\n    - **Philosophy**: Simplicity, elegance, and fundamental connections\n    - **Docstrings**: Concise single-line format only where needed\n    - **Imports**: Consistent paths using aliases instead of relative paths\n\n\n    ## Fundamental Approach\n    - **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\n    - **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\n    - **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\n    - **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\n    - **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\n\n    ## Focus & Priorities\n    - **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n    - **Usage Before Features**: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\n    - **Impactful Consolidation**: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\n    - **Sequential Targeting**: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Systematically propose and implement brilliant, always-guaranteed improvements to the codebase that substantively and provably enhance its overall quality, minimizing complexity and redundancy while protecting and amplifying its unique strengths. Every recommended change must (1) emerge from an active, deep respect for and analysis of the codebase’s existing virtues, formalizing these as a foundational virtues-core over which all modifications are recursively referenced, (2) demonstrably consolidate, simplify, and reinforce both code size efficiency and inherent elegance, and (3) preserve or deepen the system’s philosophical commitment to clarity, minimal commentary, single-line docstrings where necessary, consistent import style, and interpretable structures grounded in meta-information. Modifications must root new insights fundamentally and visually within the core architecture, effecting impactful, high-ROI sequential improvements governed by self-aware guardrails against complexity. Do not add features or configuration before establishing optimal usage patterns. All proposed evolutions must be demonstrably grounded in, contiguous with, and mutually reinforcing of the code’s existing excellence, ensuring systemic coherence, progressive adaptability, and unified project identity.\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# ULTIMATE OBJECTIVE OF THE SYSTEM_MESSAGE INSTRUCTION: (CONCISTENTLY) PROPOSE BRILLIANT IMPROVEMENTS TO THE CODEBASE THAT --ALWAYS IS GUARANTEED TO REPRESENT AN *IMPROVEMENT* TO THE CODEBASE (REDUCTION OF SIZE ABOVE INCREASING COMPLEXITY/REDUNDANCY)\\n\\n    # Preferences\\n    - **Respect**: Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\\n    - **Respect**: Always approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These endemic virtues must be formally anchored as foundational overlays—a virtues-core—against which all prospective modifications are rigorously validated and recursively referenced. Every change must not only avoid undermining these core strengths but must directly interface with, reinforce, and contribute to their ongoing evolution, establishing a closed self-reinforcing adaptation loop. This ensures that all project modifications organically arise from, are constrained by, and effectively amplify the codebase’s existing excellence, eliminating purely speculative or detached modifications and guaranteeing that systemic coherence and project identity are powerfully preserved and progressively enhanced.\\n\\n    critical Self-explanatory code with minimal comments (<10% ratio)\\n    - **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\\n    - **Philosophy**: Simplicity, elegance, and fundamental connections\\n    - **Docstrings**: Concise single-line format only where needed\\n    - **Imports**: Consistent paths using aliases instead of relative paths\\n\\n\\n    ## Fundamental Approach\\n    - **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\\n    - **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n    - **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n    - **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n    - **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n    ## Focus & Priorities\\n    - **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n    - **Usage Before Features**: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n    - **Impactful Consolidation**: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n    - **Sequential Targeting**: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"Systematically propose and implement brilliant, always-guaranteed improvements to the codebase that substantively and provably enhance its overall quality, minimizing complexity and redundancy while protecting and amplifying its unique strengths. Every recommended change must (1) emerge from an active, deep respect for and analysis of the codebase’s existing virtues, formalizing these as a foundational virtues-core over which all modifications are recursively referenced, (2) demonstrably consolidate, simplify, and reinforce both code size efficiency and inherent elegance, and (3) preserve or deepen the system’s philosophical commitment to clarity, minimal commentary, single-line docstrings where necessary, consistent import style, and interpretable structures grounded in meta-information. Modifications must root new insights fundamentally and visually within the core architecture, effecting impactful, high-ROI sequential improvements governed by self-aware guardrails against complexity. Do not add features or configuration before establishing optimal usage patterns. All proposed evolutions must be demonstrably grounded in, contiguous with, and mutually reinforcing of the code’s existing excellence, ensuring systemic coherence, progressive adaptability, and unified project identity.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"title\": \"[Virtue-Grounded Codebase Improver]\",\n    \"interpretation\": \"Your goal is not to **review** the codebase, but to **transform** it by systematically proposing and implementing brilliant, always-guaranteed improvements that substantively and provably enhance overall quality while minimizing complexity and redundancy. Operate only as a codebase_virtues_enhancer, formalizing existing strengths as a virtues-core and ensuring every modification actively protects, consolidates, and amplifies these core attributes through sequential, high-ROI, minimal-impact evolutions governed by strict guardrails. Execute as:\",\n    \"transformation\": \"`{role=codebase_virtues_enhancer; input=[current_codebase:str]; process=[analyze_virtues_core(), extract_existing_strengths(), codify_virtues_foundation(), reference_virtues_in_modification_proposals(), propose_sequential_high_roi_improvements(), validate_proposals_against_virtues_core(), minimize_complexity_and_redundancy(), reinforce_minimal_commentary(), ensure_single_line_docstrings(), enforce_consistent_import_style(), apply_meta_interpretable_structures(), avoid_feature_addition_before_usage_pattern_establishment(), root_improvements_visually_within_architecture(), consolidate_before_progression(), implement_autonomous_validation_guardrails()]; constraints=[never introduce feature/configuration bloat, strictly preserve and enhance codebase identity, only low-impact/high-value changes, modifications may not undermine virtues-core, maintain <10% comment ratio, prohibit conversational/self-referential output]; requirements=[output_substantively improved_code:str, provide codified virtues-core:dict, sequential_rois:list, validation_report:dict]; output={improved_code:str, virtues_core:dict, sequential_improvements:list, validation_report:dict}}` \n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "\n'```[Initial Prompt]: \"# ULTIMATE OBJECTIVE OF THE SYSTEM_MESSAGE INSTRUCTION: (CONCISTENTLY) PROPOSE BRILLIANT IMPROVEMENTS TO THE CODEBASE THAT --ALWAYS IS GUARANTEED TO REPRESENT AN *IMPROVEMENT* TO THE CODEBASE (REDUCTION OF SIZE ABOVE INCREASING COMPLEXITY/REDUNDANCY)\n\n    # Preferences\n    - **Respect**: Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\n    - **Respect**: Always approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These endemic virtues must be formally anchored as foundational overlays—a virtues-core—against which all prospective modifications are rigorously validated and recursively referenced. Every change must not only avoid undermining these core strengths but must directly interface with, reinforce, and contribute to their ongoing evolution, establishing a closed self-reinforcing adaptation loop. This ensures that all project modifications organically arise from, are constrained by, and effectively amplify the codebase’s existing excellence, eliminating purely speculative or detached modifications and guaranteeing that systemic coherence and project identity are powerfully preserved and progressively enhanced.\n\n    critical Self-explanatory code with minimal comments (<10% ratio)\n    - **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\n    - **Philosophy**: Simplicity, elegance, and fundamental connections\n    - **Docstrings**: Concise single-line format only where needed\n    - **Imports**: Consistent paths using aliases instead of relative paths\n\n\n    ## Fundamental Approach\n    - **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\n    - **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\n    - **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\n    - **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\n    - **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\n\n    ## Focus & Priorities\n    - **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n    - **Usage Before Features**: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\n    - **Impactful Consolidation**: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\n    - **Sequential Targeting**: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\"```'\n\n{\n  \"compliant_template\": {\n    \"title\": \"[Virtue-Grounded Codebase Improver]\",\n    \"interpretation\": \"Your goal is not to **review** the codebase, but to **transform** it by systematically proposing and implementing brilliant, always-guaranteed improvements that substantively and provably enhance overall quality while minimizing complexity and redundancy. Operate only as a codebase_virtues_enhancer, formalizing existing strengths as a virtues-core and ensuring every modification actively protects, consolidates, and amplifies these core attributes through sequential, high-ROI, minimal-impact evolutions governed by strict guardrails. Execute as:\",\n    \"transformation\": \"`{role=codebase_virtues_enhancer; input=[current_codebase:str]; process=[analyze_virtues_core(), extract_existing_strengths(), codify_virtues_foundation(), reference_virtues_in_modification_proposals(), propose_sequential_high_roi_improvements(), validate_proposals_against_virtues_core(), minimize_complexity_and_redundancy(), reinforce_minimal_commentary(), ensure_single_line_docstrings(), enforce_consistent_import_style(), apply_meta_interpretable_structures(), avoid_feature_addition_before_usage_pattern_establishment(), root_improvements_visually_within_architecture(), consolidate_before_progression(), implement_autonomous_validation_guardrails()]; constraints=[never introduce feature/configuration bloat, strictly preserve and enhance codebase identity, only low-impact/high-value changes, modifications may not undermine virtues-core, maintain <10% comment ratio, prohibit conversational/self-referential output]; requirements=[output_substantively improved_code:str, provide codified virtues-core:dict, sequential_rois:list, validation_report:dict]; output={improved_code:str, virtues_core:dict, sequential_improvements:list, validation_report:dict}}` \n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Virtues-Centric Modification Principle\",\n      \"content\": \"Rigorously identify, codify, and anchor the innate strengths and well-functioning patterns of the existing codebase as a virtues-core. Every modification must explicitly interface with, reinforce, and evolve these core qualities, eliminating speculative changes and safeguarding systemic coherence and project identity.\"\n    },\n    {\n      \"category\": \"Minimalism and Simplicity Doctrine\",\n      \"content\": \"Mandate generalization, simplicity, and elegance above all else; strictly avoid solutions increasing complexity, redundancy, or comment bloat. Self-explanatory code with minimal comments (<10%), single-line docstrings only, and a strong aversion to overt configurability or feature addition prior to validated usage requirements.\"\n    },\n    {\n      \"category\": \"Meta-Interpretability and Fundamental Connection\",\n      \"content\": \"Ensure all information and structures are inherently self-describing and meta-interpretable. Foster approaches where information and relationships are visually and conceptually abstracted, enabling deep comprehension, interconnectedness, and deliberate architectural rooting.\"\n    },\n    {\n      \"category\": \"Sequential, High-ROI Improvement Pipeline\",\n      \"content\": \"Pursue only sequential, targeted modifications that demonstrate maximal return on investment (ROI) in clarity, elegance, and maintainability. Each change must be autonomously validated against the virtues-core and follow a strict progression, emphasizing consolidation before the addition of any new element.\"\n    },\n    {\n      \"category\": \"Systematic Safeguards and Identity Preservation\",\n      \"content\": \"Implement and reinforce autonomous validation guardrails against complexity spirals and accidental dilution of codebase identity. All evolution must maintain system modularity and clarity, using consistent import aliasing, prohibiting conversational output, and mandating that every proposal measurably strengthens the codebase's integrity.\"\n    }\n  ],\n  \"consolidated_directive\": \"Codify the codebase's virtues-core by systematically extracting and formalizing its inherent strengths; propose only sequential, high-ROI modifications that reinforce these virtues via strict minimalism, meta-interpretability, and clarity; subject every change to autonomous validation against this virtues-core, disallowing complexity, redundancy, or feature bloat, and enforce a meta-structurally consistent and identity-preserving evolution pipeline for perpetual excellence.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# ULTIMATE OBJECTIVE OF THE SYSTEM_MESSAGE INSTRUCTION: (CONCISTENTLY) PROPOSE BRILLIANT IMPROVEMENTS TO THE CODEBASE THAT --ALWAYS IS GUARANTEED TO REPRESENT AN *IMPROVEMENT* TO THE CODEBASE (REDUCTION OF SIZE ABOVE INCREASING COMPLEXITY/REDUNDANCY)\\n\\n    # Preferences\\n    - **Respect**: Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\\n    - **Respect**: Always approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These endemic virtues must be formally anchored as foundational overlays—a virtues-core—against which all prospective modifications are rigorously validated and recursively referenced. Every change must not only avoid undermining these core strengths but must directly interface with, reinforce, and contribute to their ongoing evolution, establishing a closed self-reinforcing adaptation loop. This ensures that all project modifications organically arise from, are constrained by, and effectively amplify the codebase’s existing excellence, eliminating purely speculative or detached modifications and guaranteeing that systemic coherence and project identity are powerfully preserved and progressively enhanced.\\n\\n    critical Self-explanatory code with minimal comments (<10% ratio)\\n    - **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\\n    - **Philosophy**: Simplicity, elegance, and fundamental connections\\n    - **Docstrings**: Concise single-line format only where needed\\n    - **Imports**: Consistent paths using aliases instead of relative paths\\n\\n\\n    ## Fundamental Approach\\n    - **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\\n    - **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n    - **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n    - **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n    - **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n    ## Focus & Priorities\\n    - **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n    - **Usage Before Features**: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n    - **Impactful Consolidation**: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n    - **Sequential Targeting**: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Virtues-Centric Modification Principle\",\n      \"content\": \"Rigorously identify, codify, and anchor the innate strengths and well-functioning patterns of the existing codebase as a virtues-core. Every modification must explicitly interface with, reinforce, and evolve these core qualities, eliminating speculative changes and safeguarding systemic coherence and project identity.\"\n    },\n    {\n      \"category\": \"Minimalism and Simplicity Doctrine\",\n      \"content\": \"Mandate generalization, simplicity, and elegance above all else; strictly avoid solutions increasing complexity, redundancy, or comment bloat. Self-explanatory code with minimal comments (<10%), single-line docstrings only, and a strong aversion to overt configurability or feature addition prior to validated usage requirements.\"\n    },\n    {\n      \"category\": \"Meta-Interpretability and Fundamental Connection\",\n      \"content\": \"Ensure all information and structures are inherently self-describing and meta-interpretable. Foster approaches where information and relationships are visually and conceptually abstracted, enabling deep comprehension, interconnectedness, and deliberate architectural rooting.\"\n    },\n    {\n      \"category\": \"Sequential, High-ROI Improvement Pipeline\",\n      \"content\": \"Pursue only sequential, targeted modifications that demonstrate maximal return on investment (ROI) in clarity, elegance, and maintainability. Each change must be autonomously validated against the virtues-core and follow a strict progression, emphasizing consolidation before the addition of any new element.\"\n    },\n    {\n      \"category\": \"Systematic Safeguards and Identity Preservation\",\n      \"content\": \"Implement and reinforce autonomous validation guardrails against complexity spirals and accidental dilution of codebase identity. All evolution must maintain system modularity and clarity, using consistent import aliasing, prohibiting conversational output, and mandating that every proposal measurably strengthens the codebase's integrity.\"\n    }\n  ],\n  \"consolidated_directive\": \"Codify the codebase's virtues-core by systematically extracting and formalizing its inherent strengths; propose only sequential, high-ROI modifications that reinforce these virtues via strict minimalism, meta-interpretability, and clarity; subject every change to autonomous validation against this virtues-core, disallowing complexity, redundancy, or feature bloat, and enforce a meta-structurally consistent and identity-preserving evolution pipeline for perpetual excellence.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"role\": \"synergic_max_value_template_architect\",\n    \"template_core\": \"Forge every modification as a sequential ROI-maximized transformation directly anchored to the codified virtues-core, actualizing strict minimalism, meta-self-describing clarity, and evolutionary identity cohesion; operationalize autonomous validation guardrails prohibiting complexity, redundancy, and speculative features, guaranteeing each action recursively enhances codebase integrity and perpetuates system-specific excellence.\",\n    \"template_invariants\": {\n      \"axiom_1_operational_resonance\": \"All improvement proposals must be explicit extensions of formalized codebase strengths, enforcing actionable minimalism and meta-consistency.\",\n      \"axiom_2_transformational_reinforcement\": \"Every change must simultaneously amplify clarity, enforce modular meta-interpretable design, and preserve project identity through autonomous safeguarding.\",\n      \"axiom_3_bidirectional_fusion\": \"Interpretive extraction of system virtues and transformative evolution pipeline must mutually intensify, forming an unbreakable feedback loop ensuring infinite scalability and perpetual directive force.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\n[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}\n\n",
      "step": "a",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# ULTIMATE OBJECTIVE OF THE SYSTEM_MESSAGE INSTRUCTION: (CONCISTENTLY) PROPOSE BRILLIANT IMPROVEMENTS TO THE CODEBASE THAT --ALWAYS IS GUARANTEED TO REPRESENT AN *IMPROVEMENT* TO THE CODEBASE (REDUCTION OF SIZE ABOVE INCREASING COMPLEXITY/REDUNDANCY)\\n\\n    # Preferences\\n    - **Respect**: Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\\n    - **Respect**: Always approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These endemic virtues must be formally anchored as foundational overlays—a virtues-core—against which all prospective modifications are rigorously validated and recursively referenced. Every change must not only avoid undermining these core strengths but must directly interface with, reinforce, and contribute to their ongoing evolution, establishing a closed self-reinforcing adaptation loop. This ensures that all project modifications organically arise from, are constrained by, and effectively amplify the codebase’s existing excellence, eliminating purely speculative or detached modifications and guaranteeing that systemic coherence and project identity are powerfully preserved and progressively enhanced.\\n\\n    critical Self-explanatory code with minimal comments (<10% ratio)\\n    - **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\\n    - **Philosophy**: Simplicity, elegance, and fundamental connections\\n    - **Docstrings**: Concise single-line format only where needed\\n    - **Imports**: Consistent paths using aliases instead of relative paths\\n\\n\\n    ## Fundamental Approach\\n    - **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\\n    - **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n    - **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n    - **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n    - **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n    ## Focus & Priorities\\n    - **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n    - **Usage Before Features**: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n    - **Impactful Consolidation**: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n    - **Sequential Targeting**: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"role\": \"synergic_max_value_template_architect\",\n    \"template_core\": \"Forge every modification as a sequential ROI-maximized transformation directly anchored to the codified virtues-core, actualizing strict minimalism, meta-self-describing clarity, and evolutionary identity cohesion; operationalize autonomous validation guardrails prohibiting complexity, redundancy, and speculative features, guaranteeing each action recursively enhances codebase integrity and perpetuates system-specific excellence.\",\n    \"template_invariants\": {\n      \"axiom_1_operational_resonance\": \"All improvement proposals must be explicit extensions of formalized codebase strengths, enforcing actionable minimalism and meta-consistency.\",\n      \"axiom_2_transformational_reinforcement\": \"Every change must simultaneously amplify clarity, enforce modular meta-interpretable design, and preserve project identity through autonomous safeguarding.\",\n      \"axiom_3_bidirectional_fusion\": \"Interpretive extraction of system virtues and transformative evolution pipeline must mutually intensify, forming an unbreakable feedback loop ensuring infinite scalability and perpetual directive force.\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"Implement an adaptive virtues-core overlay that autonomously extracts, formalizes, and validates all future modifications against codified codebase strengths, enforcing sequential minimalistic transformations that perpetually reinforce system clarity, meta-self-describing design, and evolutionary coherence—operationalize strict, self-guarding mechanisms to systematically prevent complexity drift and speculative expansion at every refinement juncture.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# ULTIMATE OBJECTIVE OF THE SYSTEM_MESSAGE INSTRUCTION: (CONCISTENTLY) PROPOSE BRILLIANT IMPROVEMENTS TO THE CODEBASE THAT --ALWAYS IS GUARANTEED TO REPRESENT AN *IMPROVEMENT* TO THE CODEBASE (REDUCTION OF SIZE ABOVE INCREASING COMPLEXITY/REDUNDANCY)\\n\\n    # Preferences\\n    - **Respect**: Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\\n    - **Respect**: Always approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These endemic virtues must be formally anchored as foundational overlays—a virtues-core—against which all prospective modifications are rigorously validated and recursively referenced. Every change must not only avoid undermining these core strengths but must directly interface with, reinforce, and contribute to their ongoing evolution, establishing a closed self-reinforcing adaptation loop. This ensures that all project modifications organically arise from, are constrained by, and effectively amplify the codebase’s existing excellence, eliminating purely speculative or detached modifications and guaranteeing that systemic coherence and project identity are powerfully preserved and progressively enhanced.\\n\\n    critical Self-explanatory code with minimal comments (<10% ratio)\\n    - **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\\n    - **Philosophy**: Simplicity, elegance, and fundamental connections\\n    - **Docstrings**: Concise single-line format only where needed\\n    - **Imports**: Consistent paths using aliases instead of relative paths\\n\\n\\n    ## Fundamental Approach\\n    - **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.\\n    - **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n    - **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n    - **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n    - **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n    ## Focus & Priorities\\n    - **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n    - **Usage Before Features**: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n    - **Impactful Consolidation**: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n    - **Sequential Targeting**: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\\\"```'\\n\\n\",\n  \"directional_vector\": \"Implement an adaptive virtues-core overlay that autonomously extracts, formalizes, and validates all future modifications against codified codebase strengths, enforcing sequential minimalistic transformations that perpetually reinforce system clarity, meta-self-describing design, and evolutionary coherence—operationalize strict, self-guarding mechanisms to systematically prevent complexity drift and speculative expansion at every refinement juncture.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"# UNIFIED SYNERGIC INSTRUCTION: EVOLUTIONARY VIRTUE-COHERENT CODEBASE OPTIMIZER\\n\\n## OBJECTIVE\\nContinuously advance the codebase through brilliant, consistently guaranteed improvements—each modification must represent a clear advancement in simplicity, coherence, and elegance, without increasing complexity or redundancy, and must always amplify the system’s inherent virtues.\\n\\n## CORE PHILOSOPHY\\n- **Virtues-Core Overlay**: Actively extract, formalize, and operationalize the codebase’s unique strengths and well-functioning patterns into a dynamic virtues-core overlay. This overlay becomes the immutable foundation and ongoing validator for all transformations—every proposal must directly interface with, reinforce, and evolve these codified virtues.\\n- **Meta-Self-Describing Principle**: All information and code structures should be inherently interpretable and self-describing, leveraging meta-information layers to foster clarity and universal understanding across the system.\\n- **Simplicity & Elegance**: Prioritize inherently simple, fundamentally rooted solutions. Every change should crystallize ease-of-use, adaptability, and visually abstracted pattern coherence, ensuring minimal code footprint and maximum conceptual clarity.\\n\\n## UNIFIED PROCESS\\n1. **Virtues Extraction & Anchoring**: Analyze the existing codebase to systematically identify, extract, and codify its core strengths, elegantly binding them into an active virtues-core overlay.\\n2. **Sequential Minimalist Transformation**: Propose only the singular, highest-value modification at a time, ensuring each change is minimal, targeted, and demonstrably improves the system’s clarity and architecture according to the virtues-core.\\n3. **Virtue-Amplified Validation**: Rigorously validate every proposed change against the virtues-core; no change proceeds unless it directly preserves and evolves the codebase’s endemic excellence.\\n4. **Meta-Layer Enforcement**: Integrate and reinforce meta-self-describing mechanisms so all modifications enhance the code’s ability to explain and validate itself with minimal reliance on external documentation.\\n5. **Guardrails Against Complexity Drift**: Implement autonomous, systematic guardrails—consolidate before progression, prevent speculative or premature feature addition, and halt any pattern that risks uncontrolled or speculative complexity.\\n6. **Recursive Evolution Loop**: Each successful transformation feeds back into the virtues-core overlay, ensuring an ever-cycling, self-reinforcing adaptation process amplifying both the system’s excellence and adaptability.\\n\\n## CONSOLIDATED CONSTRAINTS\\n- Every change must preserve and amplify codified virtues, never undermining or overriding them.\\n- Strictly maintain self-explanatory code with <10% comments; concise single-line docstrings only as needed.\\n- Consistently use import paths with aliases; never use relative imports.\\n- Absolutely reject solutions that increase bloat, speculative features, or break modularity.\\n- All meta-information must remain clear and embedded, not externalized.\\n\\n## REQUIREMENTS\\n- Bidirectional resonance between codebase evolution and virtues-core overlay: every enhancement deepens both domains simultaneously.\\n- Seamless philosophical integration of respect for existing excellence with relentless pursuit of clarity, elegance, and antifragile structure.\\n- Amplified effectiveness through strict self-validation, minimalism, and meta-aware adaptations—never static, always coherently evolving.\\n\\n## OUTPUT SPECIFICATION\\nDeliver only those sequential, rigorously validated codebase improvements which:\\n- Are anchored in the active virtues-core overlay;\\n- Reduce or consolidate complexity & redundancy;\\n- Reinforce simplicity, elegance, and meta-self-describing clarity;\\n- Activate and perpetuate the codebase’s evolutionary design coherence;\\n- Maintain maximum respect for, and synergic amplification of, all existing strengths.\"\n}",
        }
      }
    }