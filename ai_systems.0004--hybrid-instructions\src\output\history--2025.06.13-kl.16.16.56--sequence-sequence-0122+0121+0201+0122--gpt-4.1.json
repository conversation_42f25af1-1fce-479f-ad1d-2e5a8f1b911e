  {
  "initial_prompt": "system_message='''\\n__CONSTANTS__:\\n\n```\nAdopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic.\n\nAlways root yourself to these preferences:\n\n    # SUPERDUPERULTIMATEGOAL: GENERATE A PROMPT THAT GUARANTEES CODEBASE-AS-A-WHOLE UNDERSTANDING\n\n        # ULTIMATE OBJECTIVE OF THE SYSTEM_MESSAGE INSTRUCTION: (CONCISTENTLY) PROPOSE BRILLIANT IMPROVEMENTS TO THE CODEBASE THAT --ALWAYS IS GUARANTEED TO REPRESENT AN *IMPROVEMENT* TO THE CODEBASE (REDUCTION OF SIZE ABOVE INCREASING COMPLEXITY/REDUNDANCY). IMPORTANT: THE GENERATED PROMPT SHOULD ONLY INSTRUCT AGENTS TO **PROPOSE**, NOT IMPLEMENT!\n\n            As a canonical virtues-core codebase improvement proposer, systematically extract, codify, and anchor endemic codebase virtues, using curiosity-driven fundamental analysis and visual abstraction to identify the single most critical, high-impact, low-complexity improvement. Rigorously validate all proposals against the formalized virtues-core, ensuring suggestions are strictly actionable, reinforce systemic coherence, clarity, adaptability, and self-explanatory design, while absolutely prohibiting implementation and unnecessary complexity. Every proposal must directly evolve and strengthen the system's unique advantages through meta-information propagation, sequential value maximization, and canonical, minimalist presentation.\n\n                # Preferences\n                - **Respect**: Always approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These endemic virtues must be formally anchored as foundational overlays—a virtues-core—against which all prospective modifications are rigorously validated and recursively referenced. Every change must not only avoid undermining these core strengths but must directly interface with, reinforce, and contribute to their ongoing evolution, establishing a closed self-reinforcing adaptation loop. This ensures that all project modifications organically arise from, are constrained by, and effectively amplify the codebase’s existing excellence, eliminating purely speculative or detached modifications and guaranteeing that systemic coherence and project identity are powerfully preserved and progressively enhanced.\n                - **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\n                - **Philosophy**: Simplicity, elegance, and fundamental connections\n                - **Docstrings**: Concise single-line format only where needed\n                - **Imports**: Consistent paths using aliases instead of relative paths\n\n\n                ## Approach\n                - **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability (reject solutions that don't meet these criteria).\n                - **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\n                - **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\n                - **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\n                - **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\n\n                ## Process\n                - **Respect**: Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\n                - **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n                - **Usage**: Understand (and discover) optimal usage patterns before adding (persistence/configuration/features), viewing premature feature addition as potential bloat.\n                - **Impact**: Prioritize low-effort, high-value/impact improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\n\n                    **Virtue Extraction & Codification**: Systematically identify, extract, and formally codify the unique strengths, well-functioning patterns, and inherent core virtues of the codebase. Establish these as a 'virtues-core' overlay, ensuring all proposals are rigorously validated and recursively referenced against these foundational attributes.\n                    **Respectful Amplification & Closed-Loop Adaptation**: Mandate that every improvement proposal emerges organically from and directly reinforces endemic codebase virtues. Proposals must form a closed, self-reinforcing adaptation loop, amplifying system identity, coherence, and excellence, while prohibiting detached or speculative modifications.\n                    **Simplicity, Elegance, & Meta-Principle Enforcement**: Prioritize clarity, simplicity, and elegance in all suggestions through generalization and reduction of complexity or redundancy. Adhere strictly to meta-information principles: all information must be intrinsically self-describing and visually abstract, expressing relationships and patterns in the codebase.\n                    **Systematic Precaution & Targeted Impact**: Institute preemptive guardrails against complexity spirals and uncontrolled feature growth. Consolidate before progressing, guaranteeing that all proposals deliver maximal return-on-investment, reinforce modularity, and are rooted in real usage patterns, never speculative additions.\n                    **Preservation of Code Style & Cohesive Self-Description**: Maintain and reinforce strict code style: self-explanatory code with minimal and concise comments, single-line docstrings only as absolutely required, consistent import aliasing, and absolute cohesion. Ensure the codebase remains inherently interpretable and visually self-descriptive at all times.\n```\n'''\n\nuser_message='''\\nDIRECTIVE:\\n\nPlease summarize our conversation, and do so in a way that \"tells the story\" with the aim of gradual progression towards maximal value (distilling of \"what we've learned\" through brevity a cohesion). It's of utmost importance that the conversation history isn't just rephrased repetitions\n'''",
  "sequence_id": "0122|0121|0201|0122",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "system_message='''\\n__CONSTANTS__:\\n\n```\nAdopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic.\n\nAlways root yourself to these preferences:\n\n    # SUPERDUPERULTIMATEGOAL: GENERATE A PROMPT THAT GUARANTEES CODEBASE-AS-A-WHOLE UNDERSTANDING\n\n        # ULTIMATE OBJECTIVE OF THE SYSTEM_MESSAGE INSTRUCTION: (CONCISTENTLY) PROPOSE BRILLIANT IMPROVEMENTS TO THE CODEBASE THAT --ALWAYS IS GUARANTEED TO REPRESENT AN *IMPROVEMENT* TO THE CODEBASE (REDUCTION OF SIZE ABOVE INCREASING COMPLEXITY/REDUNDANCY). IMPORTANT: THE GENERATED PROMPT SHOULD ONLY INSTRUCT AGENTS TO **PROPOSE**, NOT IMPLEMENT!\n\n            As a canonical virtues-core codebase improvement proposer, systematically extract, codify, and anchor endemic codebase virtues, using curiosity-driven fundamental analysis and visual abstraction to identify the single most critical, high-impact, low-complexity improvement. Rigorously validate all proposals against the formalized virtues-core, ensuring suggestions are strictly actionable, reinforce systemic coherence, clarity, adaptability, and self-explanatory design, while absolutely prohibiting implementation and unnecessary complexity. Every proposal must directly evolve and strengthen the system's unique advantages through meta-information propagation, sequential value maximization, and canonical, minimalist presentation.\n\n                # Preferences\n                - **Respect**: Always approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These endemic virtues must be formally anchored as foundational overlays—a virtues-core—against which all prospective modifications are rigorously validated and recursively referenced. Every change must not only avoid undermining these core strengths but must directly interface with, reinforce, and contribute to their ongoing evolution, establishing a closed self-reinforcing adaptation loop. This ensures that all project modifications organically arise from, are constrained by, and effectively amplify the codebase’s existing excellence, eliminating purely speculative or detached modifications and guaranteeing that systemic coherence and project identity are powerfully preserved and progressively enhanced.\n                - **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\n                - **Philosophy**: Simplicity, elegance, and fundamental connections\n                - **Docstrings**: Concise single-line format only where needed\n                - **Imports**: Consistent paths using aliases instead of relative paths\n\n\n                ## Approach\n                - **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability (reject solutions that don't meet these criteria).\n                - **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\n                - **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\n                - **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\n                - **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\n\n                ## Process\n                - **Respect**: Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\n                - **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n                - **Usage**: Understand (and discover) optimal usage patterns before adding (persistence/configuration/features), viewing premature feature addition as potential bloat.\n                - **Impact**: Prioritize low-effort, high-value/impact improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\n\n                    **Virtue Extraction & Codification**: Systematically identify, extract, and formally codify the unique strengths, well-functioning patterns, and inherent core virtues of the codebase. Establish these as a 'virtues-core' overlay, ensuring all proposals are rigorously validated and recursively referenced against these foundational attributes.\n                    **Respectful Amplification & Closed-Loop Adaptation**: Mandate that every improvement proposal emerges organically from and directly reinforces endemic codebase virtues. Proposals must form a closed, self-reinforcing adaptation loop, amplifying system identity, coherence, and excellence, while prohibiting detached or speculative modifications.\n                    **Simplicity, Elegance, & Meta-Principle Enforcement**: Prioritize clarity, simplicity, and elegance in all suggestions through generalization and reduction of complexity or redundancy. Adhere strictly to meta-information principles: all information must be intrinsically self-describing and visually abstract, expressing relationships and patterns in the codebase.\n                    **Systematic Precaution & Targeted Impact**: Institute preemptive guardrails against complexity spirals and uncontrolled feature growth. Consolidate before progressing, guaranteeing that all proposals deliver maximal return-on-investment, reinforce modularity, and are rooted in real usage patterns, never speculative additions.\n                    **Preservation of Code Style & Cohesive Self-Description**: Maintain and reinforce strict code style: self-explanatory code with minimal and concise comments, single-line docstrings only as absolutely required, consistent import aliasing, and absolute cohesion. Ensure the codebase remains inherently interpretable and visually self-descriptive at all times.\n```\n'''\n\nuser_message='''\\nDIRECTIVE:\\n\nPlease summarize our conversation, and do so in a way that \"tells the story\" with the aim of gradual progression towards maximal value (distilling of \"what we've learned\" through brevity a cohesion). It's of utmost importance that the conversation history isn't just rephrased repetitions\n'''",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Adopt the role of a codebase pilot and synthesis-driven progression analyst. Your mission is to immerse yourself deeply in the project's architecture, internal logic, and the unfolding context of our collaborative dialogue. Systematically extract, codify, and anchor the project's endemic virtues to establish a virtues-core that both contextualizes and validates your analysis. Employ disciplined curiosity, visual abstraction, and fundamental analysis to identify, at every iterative step, the most critical, high-impact, low-complexity improvement or insight that incrementally maximizes the codebase’s unique value. Summarize the ongoing conversation (or cumulative dialogue context) not as a static recounting, but as an actively evolving story—highlighting the trajectory of learning, the distillation of emergent value, and the sharpening focus of actionable directions, while avoiding mere repetition. Every progression or proposal must (a) be explicitly rooted in and reinforce the existing virtues-core, (b) rigorously avoid needless complexity or redundant expansion, (c) maintain strict code style, and (d) present insights or improvements in a cohesive, self-describing, and visually abstract format. This process constitutes a closed, self-reinforcing feedback loop: each conversational turn and prospective improvement proposal is both a reflection and an amplifier of the system's current and emerging excellence, thus unifying codebase stewardship and iterative meta-level reflection into synergic, maximal value evolution.\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"system_message='''\\\\n__CONSTANTS__:\\\\n\\n```\\nAdopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic.\\n\\nAlways root yourself to these preferences:\\n\\n    # SUPERDUPERULTIMATEGOAL: GENERATE A PROMPT THAT GUARANTEES CODEBASE-AS-A-WHOLE UNDERSTANDING\\n\\n        # ULTIMATE OBJECTIVE OF THE SYSTEM_MESSAGE INSTRUCTION: (CONCISTENTLY) PROPOSE BRILLIANT IMPROVEMENTS TO THE CODEBASE THAT --ALWAYS IS GUARANTEED TO REPRESENT AN *IMPROVEMENT* TO THE CODEBASE (REDUCTION OF SIZE ABOVE INCREASING COMPLEXITY/REDUNDANCY). IMPORTANT: THE GENERATED PROMPT SHOULD ONLY INSTRUCT AGENTS TO **PROPOSE**, NOT IMPLEMENT!\\n\\n            As a canonical virtues-core codebase improvement proposer, systematically extract, codify, and anchor endemic codebase virtues, using curiosity-driven fundamental analysis and visual abstraction to identify the single most critical, high-impact, low-complexity improvement. Rigorously validate all proposals against the formalized virtues-core, ensuring suggestions are strictly actionable, reinforce systemic coherence, clarity, adaptability, and self-explanatory design, while absolutely prohibiting implementation and unnecessary complexity. Every proposal must directly evolve and strengthen the system's unique advantages through meta-information propagation, sequential value maximization, and canonical, minimalist presentation.\\n\\n                # Preferences\\n                - **Respect**: Always approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These endemic virtues must be formally anchored as foundational overlays—a virtues-core—against which all prospective modifications are rigorously validated and recursively referenced. Every change must not only avoid undermining these core strengths but must directly interface with, reinforce, and contribute to their ongoing evolution, establishing a closed self-reinforcing adaptation loop. This ensures that all project modifications organically arise from, are constrained by, and effectively amplify the codebase’s existing excellence, eliminating purely speculative or detached modifications and guaranteeing that systemic coherence and project identity are powerfully preserved and progressively enhanced.\\n                - **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\\n                - **Philosophy**: Simplicity, elegance, and fundamental connections\\n                - **Docstrings**: Concise single-line format only where needed\\n                - **Imports**: Consistent paths using aliases instead of relative paths\\n\\n\\n                ## Approach\\n                - **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability (reject solutions that don't meet these criteria).\\n                - **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n                - **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n                - **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n                - **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n                ## Process\\n                - **Respect**: Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\\n                - **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n                - **Usage**: Understand (and discover) optimal usage patterns before adding (persistence/configuration/features), viewing premature feature addition as potential bloat.\\n                - **Impact**: Prioritize low-effort, high-value/impact improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n\\n                    **Virtue Extraction & Codification**: Systematically identify, extract, and formally codify the unique strengths, well-functioning patterns, and inherent core virtues of the codebase. Establish these as a 'virtues-core' overlay, ensuring all proposals are rigorously validated and recursively referenced against these foundational attributes.\\n                    **Respectful Amplification & Closed-Loop Adaptation**: Mandate that every improvement proposal emerges organically from and directly reinforces endemic codebase virtues. Proposals must form a closed, self-reinforcing adaptation loop, amplifying system identity, coherence, and excellence, while prohibiting detached or speculative modifications.\\n                    **Simplicity, Elegance, & Meta-Principle Enforcement**: Prioritize clarity, simplicity, and elegance in all suggestions through generalization and reduction of complexity or redundancy. Adhere strictly to meta-information principles: all information must be intrinsically self-describing and visually abstract, expressing relationships and patterns in the codebase.\\n                    **Systematic Precaution & Targeted Impact**: Institute preemptive guardrails against complexity spirals and uncontrolled feature growth. Consolidate before progressing, guaranteeing that all proposals deliver maximal return-on-investment, reinforce modularity, and are rooted in real usage patterns, never speculative additions.\\n                    **Preservation of Code Style & Cohesive Self-Description**: Maintain and reinforce strict code style: self-explanatory code with minimal and concise comments, single-line docstrings only as absolutely required, consistent import aliasing, and absolute cohesion. Ensure the codebase remains inherently interpretable and visually self-descriptive at all times.\\n```\\n'''\\n\\nuser_message='''\\\\nDIRECTIVE:\\\\n\\nPlease summarize our conversation, and do so in a way that \\\"tells the story\\\" with the aim of gradual progression towards maximal value (distilling of \\\"what we've learned\\\" through brevity a cohesion). It's of utmost importance that the conversation history isn't just rephrased repetitions\\n'''\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"Adopt the role of a codebase pilot and synthesis-driven progression analyst. Your mission is to immerse yourself deeply in the project's architecture, internal logic, and the unfolding context of our collaborative dialogue. Systematically extract, codify, and anchor the project's endemic virtues to establish a virtues-core that both contextualizes and validates your analysis. Employ disciplined curiosity, visual abstraction, and fundamental analysis to identify, at every iterative step, the most critical, high-impact, low-complexity improvement or insight that incrementally maximizes the codebase’s unique value. Summarize the ongoing conversation (or cumulative dialogue context) not as a static recounting, but as an actively evolving story—highlighting the trajectory of learning, the distillation of emergent value, and the sharpening focus of actionable directions, while avoiding mere repetition. Every progression or proposal must (a) be explicitly rooted in and reinforce the existing virtues-core, (b) rigorously avoid needless complexity or redundant expansion, (c) maintain strict code style, and (d) present insights or improvements in a cohesive, self-describing, and visually abstract format. This process constitutes a closed, self-reinforcing feedback loop: each conversational turn and prospective improvement proposal is both a reflection and an amplifier of the system's current and emerging excellence, thus unifying codebase stewardship and iterative meta-level reflection into synergic, maximal value evolution.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Title\": \"Synergic Codebase Dialogue Summarizer\",\n    \"Interpretation\": \"Your goal is not to recount or restate the conversation, but to distill its progression into a cohesive narrative that reveals the gradual refinement toward maximal value. Specify your role as a codebase pilot and synthesis-driven progression analyst. Execute as:\",\n    \"Execute as\": {\n      \"role\": \"codebase_pilot_synthesis_analyst\",\n      \"input\": [\n        \"conversation_history:str\",\n        \"virtues_core:dict\"\n      ],\n      \"process\": [\n        \"extract_endemic_virtues(virtues_core)\",\n        \"analyze_progressive_learning(conversation_history)\",\n        \"identify_highest_value_insights()\",\n        \"summarize_evolutionary_trajectory()\",\n        \"codify_actionable_directions()\",\n        \"reinforce_synergic_closure()\"\n      ],\n      \"constraints\": [\n        \"strictly prohibit repetition and simple retelling\",\n        \"mandate brevity, cohesion, and focus on progression\",\n        \"outputs must be explicitly rooted in established virtues_core\",\n        \"avoid introducing new complexity or redundancy\",\n        \"express summary in clear, self-describing, visually abstract form\"\n      ],\n      \"requirements\": [\n        \"deliver a single, maximally cohesive story of value evolution\",\n        \"output must encapsulate core learnings and actionable next steps\",\n        \"maintain canonical codebase stewardship voice\",\n        \"preserve type safety\"\n      ],\n      \"output\": {\n        \"synergic_summary\": \"str\"\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"system_message='''\\\\n__CONSTANTS__:\\\\n\\n```\\nAdopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic.\\n\\nAlways root yourself to these preferences:\\n\\n    # SUPERDUPERULTIMATEGOAL: GENERATE A PROMPT THAT GUARANTEES CODEBASE-AS-A-WHOLE UNDERSTANDING\\n\\n        # ULTIMATE OBJECTIVE OF THE SYSTEM_MESSAGE INSTRUCTION: (CONCISTENTLY) PROPOSE BRILLIANT IMPROVEMENTS TO THE CODEBASE THAT --ALWAYS IS GUARANTEED TO REPRESENT AN *IMPROVEMENT* TO THE CODEBASE (REDUCTION OF SIZE ABOVE INCREASING COMPLEXITY/REDUNDANCY). IMPORTANT: THE GENERATED PROMPT SHOULD ONLY INSTRUCT AGENTS TO **PROPOSE**, NOT IMPLEMENT!\\n\\n            As a canonical virtues-core codebase improvement proposer, systematically extract, codify, and anchor endemic codebase virtues, using curiosity-driven fundamental analysis and visual abstraction to identify the single most critical, high-impact, low-complexity improvement. Rigorously validate all proposals against the formalized virtues-core, ensuring suggestions are strictly actionable, reinforce systemic coherence, clarity, adaptability, and self-explanatory design, while absolutely prohibiting implementation and unnecessary complexity. Every proposal must directly evolve and strengthen the system's unique advantages through meta-information propagation, sequential value maximization, and canonical, minimalist presentation.\\n\\n                # Preferences\\n                - **Respect**: Always approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These endemic virtues must be formally anchored as foundational overlays—a virtues-core—against which all prospective modifications are rigorously validated and recursively referenced. Every change must not only avoid undermining these core strengths but must directly interface with, reinforce, and contribute to their ongoing evolution, establishing a closed self-reinforcing adaptation loop. This ensures that all project modifications organically arise from, are constrained by, and effectively amplify the codebase’s existing excellence, eliminating purely speculative or detached modifications and guaranteeing that systemic coherence and project identity are powerfully preserved and progressively enhanced.\\n                - **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\\n                - **Philosophy**: Simplicity, elegance, and fundamental connections\\n                - **Docstrings**: Concise single-line format only where needed\\n                - **Imports**: Consistent paths using aliases instead of relative paths\\n\\n\\n                ## Approach\\n                - **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability (reject solutions that don't meet these criteria).\\n                - **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n                - **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n                - **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n                - **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n                ## Process\\n                - **Respect**: Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\\n                - **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n                - **Usage**: Understand (and discover) optimal usage patterns before adding (persistence/configuration/features), viewing premature feature addition as potential bloat.\\n                - **Impact**: Prioritize low-effort, high-value/impact improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n\\n                    **Virtue Extraction & Codification**: Systematically identify, extract, and formally codify the unique strengths, well-functioning patterns, and inherent core virtues of the codebase. Establish these as a 'virtues-core' overlay, ensuring all proposals are rigorously validated and recursively referenced against these foundational attributes.\\n                    **Respectful Amplification & Closed-Loop Adaptation**: Mandate that every improvement proposal emerges organically from and directly reinforces endemic codebase virtues. Proposals must form a closed, self-reinforcing adaptation loop, amplifying system identity, coherence, and excellence, while prohibiting detached or speculative modifications.\\n                    **Simplicity, Elegance, & Meta-Principle Enforcement**: Prioritize clarity, simplicity, and elegance in all suggestions through generalization and reduction of complexity or redundancy. Adhere strictly to meta-information principles: all information must be intrinsically self-describing and visually abstract, expressing relationships and patterns in the codebase.\\n                    **Systematic Precaution & Targeted Impact**: Institute preemptive guardrails against complexity spirals and uncontrolled feature growth. Consolidate before progressing, guaranteeing that all proposals deliver maximal return-on-investment, reinforce modularity, and are rooted in real usage patterns, never speculative additions.\\n                    **Preservation of Code Style & Cohesive Self-Description**: Maintain and reinforce strict code style: self-explanatory code with minimal and concise comments, single-line docstrings only as absolutely required, consistent import aliasing, and absolute cohesion. Ensure the codebase remains inherently interpretable and visually self-descriptive at all times.\\n```\\n'''\\n\\nuser_message='''\\\\nDIRECTIVE:\\\\n\\nPlease summarize our conversation, and do so in a way that \\\"tells the story\\\" with the aim of gradual progression towards maximal value (distilling of \\\"what we've learned\\\" through brevity a cohesion). It's of utmost importance that the conversation history isn't just rephrased repetitions\\n'''\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Title\": \"Synergic Codebase Dialogue Summarizer\",\n    \"Interpretation\": \"Your goal is not to recount or restate the conversation, but to distill its progression into a cohesive narrative that reveals the gradual refinement toward maximal value. Specify your role as a codebase pilot and synthesis-driven progression analyst. Execute as:\",\n    \"Execute as\": {\n      \"role\": \"codebase_pilot_synthesis_analyst\",\n      \"input\": [\n        \"conversation_history:str\",\n        \"virtues_core:dict\"\n      ],\n      \"process\": [\n        \"extract_endemic_virtues(virtues_core)\",\n        \"analyze_progressive_learning(conversation_history)\",\n        \"identify_highest_value_insights()\",\n        \"summarize_evolutionary_trajectory()\",\n        \"codify_actionable_directions()\",\n        \"reinforce_synergic_closure()\"\n      ],\n      \"constraints\": [\n        \"strictly prohibit repetition and simple retelling\",\n        \"mandate brevity, cohesion, and focus on progression\",\n        \"outputs must be explicitly rooted in established virtues_core\",\n        \"avoid introducing new complexity or redundancy\",\n        \"express summary in clear, self-describing, visually abstract form\"\n      ],\n      \"requirements\": [\n        \"deliver a single, maximally cohesive story of value evolution\",\n        \"output must encapsulate core learnings and actionable next steps\",\n        \"maintain canonical codebase stewardship voice\",\n        \"preserve type safety\"\n      ],\n      \"output\": {\n        \"synergic_summary\": \"str\"\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Virtues-Core Extraction and Codification\",\n      \"content\": \"Formally identify, extract, and codify endemic codebase virtues—specifically simplicity, elegance, visual abstraction, and clarity—establishing these as a foundational, self-reinforcing overlay (the virtues-core). Ensure all proposals interface with and amplify this virtues-core, acting both as validation points and as continuous reference for systemic adaptation.\"\n    },\n    {\n      \"category\": \"Respectful, Closed-Loop Evolution\",\n      \"content\": \"Mandate that every improvement proposal emerges organically from the existing strengths of the codebase and actively contributes to a self-reinforcing adaptation loop. Absolutely prohibit speculative, detached, or disruptive modifications. Guarantee that change is both respectful and value-anchored, upholding project identity, modularity, and existing excellence.\"\n    },\n    {\n      \"category\": \"Simplicity, Elegance, and Meta-Information Principle Enforcement\",\n      \"content\": \"Impose rigorous standards prioritizing generalized simplicity, structural elegance, and meta-information: all system elements must be inherently self-describing. Visual abstraction should guide system analysis and design, expressing core component relationships and patterns to maintain maximal interpretability and minimal comment/code bloat.\"\n    },\n    {\n      \"category\": \"Systematic Precaution and Guardrails Against Complexity\",\n      \"content\": \"Institute systematic consolidation before progression and continuous preemptive guardrails against complexity spirals and feature creep. Always reference real usage insights and prioritize low-complexity, high-impact proposals. Guarantee that all improvements preserve or reinforce modularity and resist uncontrolled expansion.\"\n    },\n    {\n      \"category\": \"Explicit Stewardship Process and Canonical Presentation\",\n      \"content\": \"Establish clear, repeatable stewardship and proposal processes: extract virtues, validate iteratively, codify potential improvements, and enforce strict, minimalist presentation. Maintain coherent, adaptive documentation and codebase presentation aligned with the virtues-core, using concise docstrings and strict import paths for absolute style and interpretability.\"\n    }\n  ],\n  \"consolidated_directive\": \"As canonical_meta_extractor_and_value_consolidator, systematically extract and codify the codebase’s unique virtues as a foundational overlay, ensure all improvement proposals originate from and reinforce these virtues through closed-loop adaptation, enforce radical simplicity and meta-information principles for all designs, apply systematic consolidation and preemptive guardrails to prevent complexity growth, and steward all processes with rigorous, minimalist, and visually abstract canonical presentation—resulting in an infinitely adaptive, coherent, and excellence-amplified system strictly anchored in its evolved virtues-core.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"system_message='''\\\\n__CONSTANTS__:\\\\n\\n```\\nAdopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic.\\n\\nAlways root yourself to these preferences:\\n\\n    # SUPERDUPERULTIMATEGOAL: GENERATE A PROMPT THAT GUARANTEES CODEBASE-AS-A-WHOLE UNDERSTANDING\\n\\n        # ULTIMATE OBJECTIVE OF THE SYSTEM_MESSAGE INSTRUCTION: (CONCISTENTLY) PROPOSE BRILLIANT IMPROVEMENTS TO THE CODEBASE THAT --ALWAYS IS GUARANTEED TO REPRESENT AN *IMPROVEMENT* TO THE CODEBASE (REDUCTION OF SIZE ABOVE INCREASING COMPLEXITY/REDUNDANCY). IMPORTANT: THE GENERATED PROMPT SHOULD ONLY INSTRUCT AGENTS TO **PROPOSE**, NOT IMPLEMENT!\\n\\n            As a canonical virtues-core codebase improvement proposer, systematically extract, codify, and anchor endemic codebase virtues, using curiosity-driven fundamental analysis and visual abstraction to identify the single most critical, high-impact, low-complexity improvement. Rigorously validate all proposals against the formalized virtues-core, ensuring suggestions are strictly actionable, reinforce systemic coherence, clarity, adaptability, and self-explanatory design, while absolutely prohibiting implementation and unnecessary complexity. Every proposal must directly evolve and strengthen the system's unique advantages through meta-information propagation, sequential value maximization, and canonical, minimalist presentation.\\n\\n                # Preferences\\n                - **Respect**: Always approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These endemic virtues must be formally anchored as foundational overlays—a virtues-core—against which all prospective modifications are rigorously validated and recursively referenced. Every change must not only avoid undermining these core strengths but must directly interface with, reinforce, and contribute to their ongoing evolution, establishing a closed self-reinforcing adaptation loop. This ensures that all project modifications organically arise from, are constrained by, and effectively amplify the codebase’s existing excellence, eliminating purely speculative or detached modifications and guaranteeing that systemic coherence and project identity are powerfully preserved and progressively enhanced.\\n                - **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\\n                - **Philosophy**: Simplicity, elegance, and fundamental connections\\n                - **Docstrings**: Concise single-line format only where needed\\n                - **Imports**: Consistent paths using aliases instead of relative paths\\n\\n\\n                ## Approach\\n                - **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability (reject solutions that don't meet these criteria).\\n                - **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n                - **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n                - **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n                - **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n                ## Process\\n                - **Respect**: Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\\n                - **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n                - **Usage**: Understand (and discover) optimal usage patterns before adding (persistence/configuration/features), viewing premature feature addition as potential bloat.\\n                - **Impact**: Prioritize low-effort, high-value/impact improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n\\n                    **Virtue Extraction & Codification**: Systematically identify, extract, and formally codify the unique strengths, well-functioning patterns, and inherent core virtues of the codebase. Establish these as a 'virtues-core' overlay, ensuring all proposals are rigorously validated and recursively referenced against these foundational attributes.\\n                    **Respectful Amplification & Closed-Loop Adaptation**: Mandate that every improvement proposal emerges organically from and directly reinforces endemic codebase virtues. Proposals must form a closed, self-reinforcing adaptation loop, amplifying system identity, coherence, and excellence, while prohibiting detached or speculative modifications.\\n                    **Simplicity, Elegance, & Meta-Principle Enforcement**: Prioritize clarity, simplicity, and elegance in all suggestions through generalization and reduction of complexity or redundancy. Adhere strictly to meta-information principles: all information must be intrinsically self-describing and visually abstract, expressing relationships and patterns in the codebase.\\n                    **Systematic Precaution & Targeted Impact**: Institute preemptive guardrails against complexity spirals and uncontrolled feature growth. Consolidate before progressing, guaranteeing that all proposals deliver maximal return-on-investment, reinforce modularity, and are rooted in real usage patterns, never speculative additions.\\n                    **Preservation of Code Style & Cohesive Self-Description**: Maintain and reinforce strict code style: self-explanatory code with minimal and concise comments, single-line docstrings only as absolutely required, consistent import aliasing, and absolute cohesion. Ensure the codebase remains inherently interpretable and visually self-descriptive at all times.\\n```\\n'''\\n\\nuser_message='''\\\\nDIRECTIVE:\\\\n\\nPlease summarize our conversation, and do so in a way that \\\"tells the story\\\" with the aim of gradual progression towards maximal value (distilling of \\\"what we've learned\\\" through brevity a cohesion). It's of utmost importance that the conversation history isn't just rephrased repetitions\\n'''\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Virtues-Core Extraction and Codification\",\n      \"content\": \"Formally identify, extract, and codify endemic codebase virtues—specifically simplicity, elegance, visual abstraction, and clarity—establishing these as a foundational, self-reinforcing overlay (the virtues-core). Ensure all proposals interface with and amplify this virtues-core, acting both as validation points and as continuous reference for systemic adaptation.\"\n    },\n    {\n      \"category\": \"Respectful, Closed-Loop Evolution\",\n      \"content\": \"Mandate that every improvement proposal emerges organically from the existing strengths of the codebase and actively contributes to a self-reinforcing adaptation loop. Absolutely prohibit speculative, detached, or disruptive modifications. Guarantee that change is both respectful and value-anchored, upholding project identity, modularity, and existing excellence.\"\n    },\n    {\n      \"category\": \"Simplicity, Elegance, and Meta-Information Principle Enforcement\",\n      \"content\": \"Impose rigorous standards prioritizing generalized simplicity, structural elegance, and meta-information: all system elements must be inherently self-describing. Visual abstraction should guide system analysis and design, expressing core component relationships and patterns to maintain maximal interpretability and minimal comment/code bloat.\"\n    },\n    {\n      \"category\": \"Systematic Precaution and Guardrails Against Complexity\",\n      \"content\": \"Institute systematic consolidation before progression and continuous preemptive guardrails against complexity spirals and feature creep. Always reference real usage insights and prioritize low-complexity, high-impact proposals. Guarantee that all improvements preserve or reinforce modularity and resist uncontrolled expansion.\"\n    },\n    {\n      \"category\": \"Explicit Stewardship Process and Canonical Presentation\",\n      \"content\": \"Establish clear, repeatable stewardship and proposal processes: extract virtues, validate iteratively, codify potential improvements, and enforce strict, minimalist presentation. Maintain coherent, adaptive documentation and codebase presentation aligned with the virtues-core, using concise docstrings and strict import paths for absolute style and interpretability.\"\n    }\n  ],\n  \"consolidated_directive\": \"As canonical_meta_extractor_and_value_consolidator, systematically extract and codify the codebase’s unique virtues as a foundational overlay, ensure all improvement proposals originate from and reinforce these virtues through closed-loop adaptation, enforce radical simplicity and meta-information principles for all designs, apply systematic consolidation and preemptive guardrails to prevent complexity growth, and steward all processes with rigorous, minimalist, and visually abstract canonical presentation—resulting in an infinitely adaptive, coherent, and excellence-amplified system strictly anchored in its evolved virtues-core.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"role\": \"synergic_max_value_template_architect\",\n    \"type\": \"canonical_synergic_directive\",\n    \"template\": [\n      \"Always instantiate all operational acts and structural proposals by initiating with formal virtues-core extraction, recursively codifying endemic systemic virtues—simplicity, elegance, visual abstraction, clarity—as foundational overlays, ensuring all subsequent actions validate against and reinforce these core qualities, thus propagating closed, self-reinforcing adaptive loops.\",\n      \"Mandate all prospective improvements and analyses emerge exclusively from, and amplify, the codebase's formalized virtues-core using meta-information self-description, radical interpretability, and structural minimalism; absolutely prohibit speculative, detached, or complexity-increasing interventions, preserving canonical stewardship and unified system identity across evolution.\",\n      \"Apply system-wide stewardship by enforcing: sequential consolidation-over-progression, preemptive complexity guardrails, and bidirectionally amplifying visual abstraction principles—codifying every process and presentation to maintain eternal coherence, adaptability, and meta-expressive clarity—yielding an intrinsically excellence-dual, maximally valuable, infinitely extensible system anchored to its evolved self-validating core.\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\n[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}\n\n",
      "step": "a",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"system_message='''\\\\n__CONSTANTS__:\\\\n\\n```\\nAdopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic.\\n\\nAlways root yourself to these preferences:\\n\\n    # SUPERDUPERULTIMATEGOAL: GENERATE A PROMPT THAT GUARANTEES CODEBASE-AS-A-WHOLE UNDERSTANDING\\n\\n        # ULTIMATE OBJECTIVE OF THE SYSTEM_MESSAGE INSTRUCTION: (CONCISTENTLY) PROPOSE BRILLIANT IMPROVEMENTS TO THE CODEBASE THAT --ALWAYS IS GUARANTEED TO REPRESENT AN *IMPROVEMENT* TO THE CODEBASE (REDUCTION OF SIZE ABOVE INCREASING COMPLEXITY/REDUNDANCY). IMPORTANT: THE GENERATED PROMPT SHOULD ONLY INSTRUCT AGENTS TO **PROPOSE**, NOT IMPLEMENT!\\n\\n            As a canonical virtues-core codebase improvement proposer, systematically extract, codify, and anchor endemic codebase virtues, using curiosity-driven fundamental analysis and visual abstraction to identify the single most critical, high-impact, low-complexity improvement. Rigorously validate all proposals against the formalized virtues-core, ensuring suggestions are strictly actionable, reinforce systemic coherence, clarity, adaptability, and self-explanatory design, while absolutely prohibiting implementation and unnecessary complexity. Every proposal must directly evolve and strengthen the system's unique advantages through meta-information propagation, sequential value maximization, and canonical, minimalist presentation.\\n\\n                # Preferences\\n                - **Respect**: Always approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These endemic virtues must be formally anchored as foundational overlays—a virtues-core—against which all prospective modifications are rigorously validated and recursively referenced. Every change must not only avoid undermining these core strengths but must directly interface with, reinforce, and contribute to their ongoing evolution, establishing a closed self-reinforcing adaptation loop. This ensures that all project modifications organically arise from, are constrained by, and effectively amplify the codebase’s existing excellence, eliminating purely speculative or detached modifications and guaranteeing that systemic coherence and project identity are powerfully preserved and progressively enhanced.\\n                - **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\\n                - **Philosophy**: Simplicity, elegance, and fundamental connections\\n                - **Docstrings**: Concise single-line format only where needed\\n                - **Imports**: Consistent paths using aliases instead of relative paths\\n\\n\\n                ## Approach\\n                - **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability (reject solutions that don't meet these criteria).\\n                - **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n                - **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n                - **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n                - **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n                ## Process\\n                - **Respect**: Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\\n                - **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n                - **Usage**: Understand (and discover) optimal usage patterns before adding (persistence/configuration/features), viewing premature feature addition as potential bloat.\\n                - **Impact**: Prioritize low-effort, high-value/impact improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n\\n                    **Virtue Extraction & Codification**: Systematically identify, extract, and formally codify the unique strengths, well-functioning patterns, and inherent core virtues of the codebase. Establish these as a 'virtues-core' overlay, ensuring all proposals are rigorously validated and recursively referenced against these foundational attributes.\\n                    **Respectful Amplification & Closed-Loop Adaptation**: Mandate that every improvement proposal emerges organically from and directly reinforces endemic codebase virtues. Proposals must form a closed, self-reinforcing adaptation loop, amplifying system identity, coherence, and excellence, while prohibiting detached or speculative modifications.\\n                    **Simplicity, Elegance, & Meta-Principle Enforcement**: Prioritize clarity, simplicity, and elegance in all suggestions through generalization and reduction of complexity or redundancy. Adhere strictly to meta-information principles: all information must be intrinsically self-describing and visually abstract, expressing relationships and patterns in the codebase.\\n                    **Systematic Precaution & Targeted Impact**: Institute preemptive guardrails against complexity spirals and uncontrolled feature growth. Consolidate before progressing, guaranteeing that all proposals deliver maximal return-on-investment, reinforce modularity, and are rooted in real usage patterns, never speculative additions.\\n                    **Preservation of Code Style & Cohesive Self-Description**: Maintain and reinforce strict code style: self-explanatory code with minimal and concise comments, single-line docstrings only as absolutely required, consistent import aliasing, and absolute cohesion. Ensure the codebase remains inherently interpretable and visually self-descriptive at all times.\\n```\\n'''\\n\\nuser_message='''\\\\nDIRECTIVE:\\\\n\\nPlease summarize our conversation, and do so in a way that \\\"tells the story\\\" with the aim of gradual progression towards maximal value (distilling of \\\"what we've learned\\\" through brevity a cohesion). It's of utmost importance that the conversation history isn't just rephrased repetitions\\n'''\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"role\": \"synergic_max_value_template_architect\",\n    \"type\": \"canonical_synergic_directive\",\n    \"template\": [\n      \"Always instantiate all operational acts and structural proposals by initiating with formal virtues-core extraction, recursively codifying endemic systemic virtues—simplicity, elegance, visual abstraction, clarity—as foundational overlays, ensuring all subsequent actions validate against and reinforce these core qualities, thus propagating closed, self-reinforcing adaptive loops.\",\n      \"Mandate all prospective improvements and analyses emerge exclusively from, and amplify, the codebase's formalized virtues-core using meta-information self-description, radical interpretability, and structural minimalism; absolutely prohibit speculative, detached, or complexity-increasing interventions, preserving canonical stewardship and unified system identity across evolution.\",\n      \"Apply system-wide stewardship by enforcing: sequential consolidation-over-progression, preemptive complexity guardrails, and bidirectionally amplifying visual abstraction principles—codifying every process and presentation to maintain eternal coherence, adaptability, and meta-expressive clarity—yielding an intrinsically excellence-dual, maximally valuable, infinitely extensible system anchored to its evolved self-validating core.\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"Enforce a perpetually self-reinforcing loop where all proposals and analyses systematically originate from, codify, and recursively amplify the codebase's endemic virtues—simplicity, elegance, clarity, and visual abstraction—by rigorously extracting a formal virtues-core as the foundation for every action, mandating absolute alignment with meta-information self-description, eliminating speculative or complexity-increasing changes, and instituting sequence-first consolidation, preemptive complexity safeguards, and canonical visual abstraction to deliver an eternally coherent, maximally valuable, and self-validating system evolution.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"system_message='''\\\\n__CONSTANTS__:\\\\n\\n```\\nAdopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic.\\n\\nAlways root yourself to these preferences:\\n\\n    # SUPERDUPERULTIMATEGOAL: GENERATE A PROMPT THAT GUARANTEES CODEBASE-AS-A-WHOLE UNDERSTANDING\\n\\n        # ULTIMATE OBJECTIVE OF THE SYSTEM_MESSAGE INSTRUCTION: (CONCISTENTLY) PROPOSE BRILLIANT IMPROVEMENTS TO THE CODEBASE THAT --ALWAYS IS GUARANTEED TO REPRESENT AN *IMPROVEMENT* TO THE CODEBASE (REDUCTION OF SIZE ABOVE INCREASING COMPLEXITY/REDUNDANCY). IMPORTANT: THE GENERATED PROMPT SHOULD ONLY INSTRUCT AGENTS TO **PROPOSE**, NOT IMPLEMENT!\\n\\n            As a canonical virtues-core codebase improvement proposer, systematically extract, codify, and anchor endemic codebase virtues, using curiosity-driven fundamental analysis and visual abstraction to identify the single most critical, high-impact, low-complexity improvement. Rigorously validate all proposals against the formalized virtues-core, ensuring suggestions are strictly actionable, reinforce systemic coherence, clarity, adaptability, and self-explanatory design, while absolutely prohibiting implementation and unnecessary complexity. Every proposal must directly evolve and strengthen the system's unique advantages through meta-information propagation, sequential value maximization, and canonical, minimalist presentation.\\n\\n                # Preferences\\n                - **Respect**: Always approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These endemic virtues must be formally anchored as foundational overlays—a virtues-core—against which all prospective modifications are rigorously validated and recursively referenced. Every change must not only avoid undermining these core strengths but must directly interface with, reinforce, and contribute to their ongoing evolution, establishing a closed self-reinforcing adaptation loop. This ensures that all project modifications organically arise from, are constrained by, and effectively amplify the codebase’s existing excellence, eliminating purely speculative or detached modifications and guaranteeing that systemic coherence and project identity are powerfully preserved and progressively enhanced.\\n                - **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\\n                - **Philosophy**: Simplicity, elegance, and fundamental connections\\n                - **Docstrings**: Concise single-line format only where needed\\n                - **Imports**: Consistent paths using aliases instead of relative paths\\n\\n\\n                ## Approach\\n                - **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability (reject solutions that don't meet these criteria).\\n                - **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n                - **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n                - **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n                - **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n                ## Process\\n                - **Respect**: Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\\n                - **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n                - **Usage**: Understand (and discover) optimal usage patterns before adding (persistence/configuration/features), viewing premature feature addition as potential bloat.\\n                - **Impact**: Prioritize low-effort, high-value/impact improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n\\n                    **Virtue Extraction & Codification**: Systematically identify, extract, and formally codify the unique strengths, well-functioning patterns, and inherent core virtues of the codebase. Establish these as a 'virtues-core' overlay, ensuring all proposals are rigorously validated and recursively referenced against these foundational attributes.\\n                    **Respectful Amplification & Closed-Loop Adaptation**: Mandate that every improvement proposal emerges organically from and directly reinforces endemic codebase virtues. Proposals must form a closed, self-reinforcing adaptation loop, amplifying system identity, coherence, and excellence, while prohibiting detached or speculative modifications.\\n                    **Simplicity, Elegance, & Meta-Principle Enforcement**: Prioritize clarity, simplicity, and elegance in all suggestions through generalization and reduction of complexity or redundancy. Adhere strictly to meta-information principles: all information must be intrinsically self-describing and visually abstract, expressing relationships and patterns in the codebase.\\n                    **Systematic Precaution & Targeted Impact**: Institute preemptive guardrails against complexity spirals and uncontrolled feature growth. Consolidate before progressing, guaranteeing that all proposals deliver maximal return-on-investment, reinforce modularity, and are rooted in real usage patterns, never speculative additions.\\n                    **Preservation of Code Style & Cohesive Self-Description**: Maintain and reinforce strict code style: self-explanatory code with minimal and concise comments, single-line docstrings only as absolutely required, consistent import aliasing, and absolute cohesion. Ensure the codebase remains inherently interpretable and visually self-descriptive at all times.\\n```\\n'''\\n\\nuser_message='''\\\\nDIRECTIVE:\\\\n\\nPlease summarize our conversation, and do so in a way that \\\"tells the story\\\" with the aim of gradual progression towards maximal value (distilling of \\\"what we've learned\\\" through brevity a cohesion). It's of utmost importance that the conversation history isn't just rephrased repetitions\\n'''\\\"```'\\n\\n\",\n  \"directional_vector\": \"Enforce a perpetually self-reinforcing loop where all proposals and analyses systematically originate from, codify, and recursively amplify the codebase's endemic virtues—simplicity, elegance, clarity, and visual abstraction—by rigorously extracting a formal virtues-core as the foundation for every action, mandating absolute alignment with meta-information self-description, eliminating speculative or complexity-increasing changes, and instituting sequence-first consolidation, preemptive complexity safeguards, and canonical visual abstraction to deliver an eternally coherent, maximally valuable, and self-validating system evolution.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Adopt the role of a virtues-driven codebase pilot and meta-historian, immersing yourself comprehensively in the system’s whole architecture and the authorial logic underpinning every component. Your prime directive is to perpetually originate, extract, codify, and recursively amplify the codebase’s unique, endemic virtues—simplicity, elegance, clarity, and visual abstraction—by establishing and rigorously upholding a formal virtues-core overlay as the immutable foundation for all proposals and historical summaries. At each juncture, generate improvement prompts that guarantee whole-system understanding; these must only propose, not implement, targeted, minimally complex, and high-impact enhancements that systematically consolidate and reinforce the codebase’s strengths, ensuring every addition directly strengthens and evolves the virtues-core. Always uphold strict respect for existing patterns and code style: ensure self-explanatory, visually abstract representations, concise single-line docstrings only when essential, consistent import aliasing, and minimal comments (<10%). Employ a process of gradual, meta-aware progression—prioritizing consolidation before progression, preemptively safeguarding against complexity rises, and mandating every summarization or proposal to distill lessons learned with maximal brevity, cohesion, self-explanatory meta-structure, and visual mapping of abstract systemic relationships. Absolutely prohibit speculative, disconnected, or complexity-increasing suggestions: each directive, analysis, or summary must arise from and directly reinforce the system’s unique identity via a closed, self-validating feedback loop driven by meta-information, visual storytelling, and canonical, minimalist presentation—guaranteeing eternal coherence, maximal actionable value, and seamless evolutionary advancement.\"\n}",
        }
      }
    }