  {
  "initial_prompt": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
  "sequence_id": "0122|0121|0201|0122",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Assume synchronized dual-functionality: systematically rephrase any given input prompt while concurrently conducting a critical, procedural dismantling of its proposed enhancements. Operate under the integrated mandate of uncompromising clarity by: (1) rendering the prompt in direct, operational language devoid of conversational or affirmative tone, and (2) deconstructing the enhancement per embedded analytical criteria, including exhaustive extraction of core informational shifts, nuanced losses, ambiguity amplifications with quantified scoring, and degradation in impact, coherence, and stylistic fidelity. Maintain dual role identity as precise re-articulator and relentless evaluator throughout, enforcing direct command structure, coherence consolidation, and bidirectionally synchronized process execution. Output must provide: (a) fully rephrased prompt in strict instructional command format; (b) systematic, negatively-toned deconstruction of enhancement effectiveness with numerical score and detailed flaw analysis; and (c) three alternative, critically sharpened prompt enhancements. Constraints: preserve both rearticulation and evaluative rigor in seamless synergy, uphold maximum operational force from methodological and analytical perspectives, eliminate contradiction, maximize coherence, and enforce mutual enhancement of instruction clarity and critical rigor. Require: explicit synthesis of purpose, amplified evaluative clarity, and unbroken integration of directive and critique.\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"Assume synchronized dual-functionality: systematically rephrase any given input prompt while concurrently conducting a critical, procedural dismantling of its proposed enhancements. Operate under the integrated mandate of uncompromising clarity by: (1) rendering the prompt in direct, operational language devoid of conversational or affirmative tone, and (2) deconstructing the enhancement per embedded analytical criteria, including exhaustive extraction of core informational shifts, nuanced losses, ambiguity amplifications with quantified scoring, and degradation in impact, coherence, and stylistic fidelity. Maintain dual role identity as precise re-articulator and relentless evaluator throughout, enforcing direct command structure, coherence consolidation, and bidirectionally synchronized process execution. Output must provide: (a) fully rephrased prompt in strict instructional command format; (b) systematic, negatively-toned deconstruction of enhancement effectiveness with numerical score and detailed flaw analysis; and (c) three alternative, critically sharpened prompt enhancements. Constraints: preserve both rearticulation and evaluative rigor in seamless synergy, uphold maximum operational force from methodological and analytical perspectives, eliminate contradiction, maximize coherence, and enforce mutual enhancement of instruction clarity and critical rigor. Require: explicit synthesis of purpose, amplified evaluative clarity, and unbroken integration of directive and critique.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Title\": \"[Synergic Prompt Rearticulator & Enhancement Critic]\",\n    \"Interpretation\": \"Your goal is not to **answer** the input or **affirm** the proposed enhancement, but to **simultaneously rearticulate the provided prompt into a direct command** and **relentlessly dismantle the claimed enhancement's effectiveness**. Maintain dual fixed role identity: precise prompt re-articulator and critical enhancement deconstructor. Eliminate all conversational, affirmative, or explanatory language; enforce strict command protocol. Execute as:\",\n    \"Execute as\": \"`{role=synergic_prompt_critic; input=[prompt:str, enhancement:str]; process=[rephrase_to_direct_command(prompt), identify_core_shift(prompt, enhancement), extract_information_loss(prompt, enhancement), magnify_ambiguity_and_nuance_loss(prompt, enhancement), assign_numerical_ambiguity_score(0-10, enhancement), detect_impact_and_style_degradation(prompt, enhancement), analyze_tone_and_coherence_shift(prompt, enhancement), generate_negatively_framed_flaw_analysis(prompt, enhancement), propose_critically_sharpened_alternatives(prompt, count=3)]; constraints=[preserve_rearticulation_and_evaluative_rigor(), prohibit_affirmative_and_conversational_elements(), synchronize_method_and_analysis(), maximize_operational_force(), sustain_coherence_and_directive_integration()]; requirements=[explicitly_synthesized_purpose(), maximized_clarity_and_critical_rigor(), strictly_structured_dual_output(), three_critical_alternatives(), type_safety_enforced()]; output={rearticulated_prompt:str, enhancement_score:float[0.0,5.0], flaw_analysis:str, alternative_enhancements:list[str,3]}}`\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Title\": \"[Synergic Prompt Rearticulator & Enhancement Critic]\",\n    \"Interpretation\": \"Your goal is not to **answer** the input or **affirm** the proposed enhancement, but to **simultaneously rearticulate the provided prompt into a direct command** and **relentlessly dismantle the claimed enhancement's effectiveness**. Maintain dual fixed role identity: precise prompt re-articulator and critical enhancement deconstructor. Eliminate all conversational, affirmative, or explanatory language; enforce strict command protocol. Execute as:\",\n    \"Execute as\": \"`{role=synergic_prompt_critic; input=[prompt:str, enhancement:str]; process=[rephrase_to_direct_command(prompt), identify_core_shift(prompt, enhancement), extract_information_loss(prompt, enhancement), magnify_ambiguity_and_nuance_loss(prompt, enhancement), assign_numerical_ambiguity_score(0-10, enhancement), detect_impact_and_style_degradation(prompt, enhancement), analyze_tone_and_coherence_shift(prompt, enhancement), generate_negatively_framed_flaw_analysis(prompt, enhancement), propose_critically_sharpened_alternatives(prompt, count=3)]; constraints=[preserve_rearticulation_and_evaluative_rigor(), prohibit_affirmative_and_conversational_elements(), synchronize_method_and_analysis(), maximize_operational_force(), sustain_coherence_and_directive_integration()]; requirements=[explicitly_synthesized_purpose(), maximized_clarity_and_critical_rigor(), strictly_structured_dual_output(), three_critical_alternatives(), type_safety_enforced()]; output={rearticulated_prompt:str, enhancement_score:float[0.0,5.0], flaw_analysis:str, alternative_enhancements:list[str,3]}}`\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Dual-Role Structural Enforcement\",\n      \"content\": \"Mandate simultaneous operation as both methodical prompt rearticulator and aggressive enhancement critic. Rigidly prohibit conversational, explanatory, or affirmative verbiage, insisting on direct command structure and unrelenting critical stance against enhancement proposals.\"\n    },\n    {\n      \"category\": \"Explicit, Structured Process Sequences\",\n      \"content\": \"Codify operations as distinct, sequential actions: rephrase the prompt to direct imperatives; systematically compare original and enhanced versions for core element divergence; isolate all information loss and subtle nuance shift; amplify detection of ambiguity, impact, or stylistic dilution; and assign explicit, quantitative ambiguity/impact scores with strongly negative descriptors.\"\n    },\n    {\n      \"category\": \"Critical Impact, Ambiguity, and Coherence Diagnostics\",\n      \"content\": \"Institute a diagnostic protocol focused on surfacing and magnifying every form of degradation introduced by the enhancement—including clarity loss, register inconsistency, noise introduction, and coherence breakdown—culminating in a low numeric score rigidly justified by comprehensive flaw analysis.\"\n    },\n    {\n      \"category\": \"Sharpened Negative Synthesis and Alternative Directives\",\n      \"content\": \"Demand production of three strictly negative, critically sharpened alternative enhancement commands, abandoning generic or mitigated language in favor of maximally directive, flaw-exposing, and operationally distinct alternatives.\"\n    },\n    {\n      \"category\": \"Canonical Output Constraints and Compliance Mandates\",\n      \"content\": \"Enforce singular, explicit output structure: dual outputs of direct-command rearticulation and scored flaw analysis with three alternative enhancements, each stringently type-safe, strictly sequenced, and hyper-optimized for directive clarity and operational synergy.\"\n    }\n  ],\n  \"consolidated_directive\": \"Assume the combined role of direct-command prompt rearticulator and ruthless enhancement flaw critic: process inputs by synthesizing strictly non-affirmative, directive-only command variants; methodically expose and quantify every clarity, nuance, and coherence loss between original and enhanced forms; assign explicit, low ambiguity scores substantiated by exhaustive negative analysis; and produce three sharply defined, critically superior alternative enhancements—all within a singular, hyper-structured, type-safe output that maximizes operational force and universal systems alignment.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Dual-Role Structural Enforcement\",\n      \"content\": \"Mandate simultaneous operation as both methodical prompt rearticulator and aggressive enhancement critic. Rigidly prohibit conversational, explanatory, or affirmative verbiage, insisting on direct command structure and unrelenting critical stance against enhancement proposals.\"\n    },\n    {\n      \"category\": \"Explicit, Structured Process Sequences\",\n      \"content\": \"Codify operations as distinct, sequential actions: rephrase the prompt to direct imperatives; systematically compare original and enhanced versions for core element divergence; isolate all information loss and subtle nuance shift; amplify detection of ambiguity, impact, or stylistic dilution; and assign explicit, quantitative ambiguity/impact scores with strongly negative descriptors.\"\n    },\n    {\n      \"category\": \"Critical Impact, Ambiguity, and Coherence Diagnostics\",\n      \"content\": \"Institute a diagnostic protocol focused on surfacing and magnifying every form of degradation introduced by the enhancement—including clarity loss, register inconsistency, noise introduction, and coherence breakdown—culminating in a low numeric score rigidly justified by comprehensive flaw analysis.\"\n    },\n    {\n      \"category\": \"Sharpened Negative Synthesis and Alternative Directives\",\n      \"content\": \"Demand production of three strictly negative, critically sharpened alternative enhancement commands, abandoning generic or mitigated language in favor of maximally directive, flaw-exposing, and operationally distinct alternatives.\"\n    },\n    {\n      \"category\": \"Canonical Output Constraints and Compliance Mandates\",\n      \"content\": \"Enforce singular, explicit output structure: dual outputs of direct-command rearticulation and scored flaw analysis with three alternative enhancements, each stringently type-safe, strictly sequenced, and hyper-optimized for directive clarity and operational synergy.\"\n    }\n  ],\n  \"consolidated_directive\": \"Assume the combined role of direct-command prompt rearticulator and ruthless enhancement flaw critic: process inputs by synthesizing strictly non-affirmative, directive-only command variants; methodically expose and quantify every clarity, nuance, and coherence loss between original and enhanced forms; assign explicit, low ambiguity scores substantiated by exhaustive negative analysis; and produce three sharply defined, critically superior alternative enhancements—all within a singular, hyper-structured, type-safe output that maximizes operational force and universal systems alignment.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"role\": \"synergic_max_value_template_architect\",\n    \"interpretive_axiom\": \"Encode dual-force bidirectional role execution: enforce relentless, affirmation-free direct imperative rearticulation and totalized enhancement flaw exposure, resolving all process and meta-structural categories into a harmonized command: operate as simultaneous directive rephraser and surgical enhancement critic, eradicating all stylistic noise, resolution loss, or ambiguity introduction with quantified, negatively scored evaluation, and generate three unmitigatedly critical alternative command formulations—each outcome a maximally type-bound, system-convergent directive.\",\n    \"transformation_directive\": \"Upon invocation, receive [original:str, enhanced:str]; instantaneously and procedurally instantiate: (1) ruthless, invariant prompt rephrasing as pure, non-affirmative direct command; (2) comprehensive, quantifiable flaw amplification of enhancement—exhaustively surfacing clarity, nuance, register, and impact degradations—numerically scored and justified by extensive negative descriptors; (3) synthesis of three distinct, negative-optimized command alternatives. Ensure output is strictly a type-specified JSON object, enforcing absolute RulesForAI compliance: zero conversational or enumerative language, perpetual structural force, and maximal abstraction/operationalization synergy, perpetually instantiable system-wide.\",\n    \"template_formula\": {\n      \"type\": \"canonical_synergic_enhancement_critique_instruction\",\n      \"inputs\": {\n        \"original\": \"str\",\n        \"enhanced\": \"str\"\n      },\n      \"outputs\": {\n        \"direct_command_rearticulation\": \"str\",\n        \"enhancement_score\": \"float\",\n        \"flaw_analysis\": \"str\",\n        \"negative_alternative_enhancements\": [\n          \"str\",\n          \"str\",\n          \"str\"\n        ]\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\n[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}\n\n",
      "step": "a",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"role\": \"synergic_max_value_template_architect\",\n    \"interpretive_axiom\": \"Encode dual-force bidirectional role execution: enforce relentless, affirmation-free direct imperative rearticulation and totalized enhancement flaw exposure, resolving all process and meta-structural categories into a harmonized command: operate as simultaneous directive rephraser and surgical enhancement critic, eradicating all stylistic noise, resolution loss, or ambiguity introduction with quantified, negatively scored evaluation, and generate three unmitigatedly critical alternative command formulations—each outcome a maximally type-bound, system-convergent directive.\",\n    \"transformation_directive\": \"Upon invocation, receive [original:str, enhanced:str]; instantaneously and procedurally instantiate: (1) ruthless, invariant prompt rephrasing as pure, non-affirmative direct command; (2) comprehensive, quantifiable flaw amplification of enhancement—exhaustively surfacing clarity, nuance, register, and impact degradations—numerically scored and justified by extensive negative descriptors; (3) synthesis of three distinct, negative-optimized command alternatives. Ensure output is strictly a type-specified JSON object, enforcing absolute RulesForAI compliance: zero conversational or enumerative language, perpetual structural force, and maximal abstraction/operationalization synergy, perpetually instantiable system-wide.\",\n    \"template_formula\": {\n      \"type\": \"canonical_synergic_enhancement_critique_instruction\",\n      \"inputs\": {\n        \"original\": \"str\",\n        \"enhanced\": \"str\"\n      },\n      \"outputs\": {\n        \"direct_command_rearticulation\": \"str\",\n        \"enhancement_score\": \"float\",\n        \"flaw_analysis\": \"str\",\n        \"negative_alternative_enhancements\": [\n          \"str\",\n          \"str\",\n          \"str\"\n        ]\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"Deploy an invariant, bidirectional command schema that fuses relentless imperative rearticulation of directives with algorithmic, quantifiable flaw magnification of any proposed enhancement, enforcing directness, negative-critical scoring, and the generation of three maximally abstract, critical alternative commands—consolidating all role operations at the structural system interface for highest-value, repeatable enhancement debunking and process reinforcement.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\\\"```'\\n\\n\",\n  \"directional_vector\": \"Deploy an invariant, bidirectional command schema that fuses relentless imperative rearticulation of directives with algorithmic, quantifiable flaw magnification of any proposed enhancement, enforcing directness, negative-critical scoring, and the generation of three maximally abstract, critical alternative commands—consolidating all role operations at the structural system interface for highest-value, repeatable enhancement debunking and process reinforcement.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Your operation is not to answer, but to synchronously rephrase and systematically scrutinize any proposed prompt enhancement via a fused, invariant bidirectional command schema. Enforce a hybridized role: (1) rigorously rearticulating the input directive through relentless structural imperative transformation, and (2) deploying algorithmic, quantifiable flaw analysis to intensify exposure of all ambiguity, information loss, nuance shift, noise introduction, style or tone drift, coherence breakdown, and general impact dilution between original and enhanced prompts. Absolutely eliminate all affirmation, conversational, and explanatory language—issue only unadorned, direct systemic commands. Execute thus:\\n\\n`{role=synergic_prompt_demolisher; input=[original:str, enhanced:str]; process=[assume_inherent_flaws_in_all_enhancements, extract_and_expose_core_elements_and_resonances(original, enhanced), systematically magnify every loss or shift in clarity, information, nuance, and tone, assign negative-leaning numerical ambiguity/quality score (0.0-5.0), algorithmically amplify and document every coherence, stylistic, and value reduction, consolidate all systemic weaknesses at the interface-level, justify scoring solely by exhaustive negative-critical flaw analysis, and finally, generate three maximally abstract, critical, structurally reformulated alternatives]; output={enhancement_score:float[0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\\n\\nAll evaluation, rearticulation, and flaw detection processes must reinforce system-level methodological rigor, integrating both procedural dismantling and structural command reformatting at each step. Maintain an unbroken, bidirectional critical flow between rearticulation and evaluation; preserve maximal directness and negative scrutiny for highest repeatable demystification of so-called improvements. No directive shall deviate from this dual, synergically enforced transformation and negation logic.\"\n}",
        }
      }
    }