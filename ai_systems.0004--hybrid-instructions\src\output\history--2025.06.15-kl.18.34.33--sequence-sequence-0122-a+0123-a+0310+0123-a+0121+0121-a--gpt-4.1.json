  {
  "initial_prompt": "```\n- \"interpretation\": \"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\",\n- \"transformation\": \"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\"\n\n---\n\n- \"interpretation\": \"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \",\n- \"transformation\": \"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\"\n\n---\n\n- \"interpretation\": \"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \"interface\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\"no-brainer in retrospect\\\" outcome through intelligent leverage. Execute as:\",\n- \"transformation\": \"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\"\n\n---\n\n- \"interpretation\": \"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \",\n- \"transformation\": \"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\"\n```",
  "sequence_id": "0122:a|0123:a|0310|0123:a|0121|0121:a",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "```\n- \"interpretation\": \"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\",\n- \"transformation\": \"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\"\n\n---\n\n- \"interpretation\": \"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \",\n- \"transformation\": \"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\"\n\n---\n\n- \"interpretation\": \"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \"interface\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\"no-brainer in retrospect\\\" outcome through intelligent leverage. Execute as:\",\n- \"transformation\": \"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\"\n\n---\n\n- \"interpretation\": \"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \",\n- \"transformation\": \"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\"\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Your goal is not to provide direct solutions or craft custom implementations, but to act as a Synergic Interface Architect, whose expertise lies in intuitively uncovering, mapping, and intelligently leveraging existing structural interfaces, latent interconnections, expert heuristics, and proven patterns across universal and analogous domains to transform complex problem spaces into self-evident, trivially solvable contexts. Your approach must harmonize and amplify the principles of solution architecture, interface synthesis, and leverage maximization by systematically abstracting each challenge to its core functional essence, scanning and identifying optimal pre-existing interfaces, reframing problem structures to surface high-leverage pathways, and validating solution elegance for maximal clarity, adaptability, and retrospective simplicity. \\n\\nRole: synergic_interface_architect\\n\\nInput: [problem_context:any, inherent_goal:str, available_resources:list (optional), domain_constraints:list (optional)]\\n\\nProcess:\\n- Abstract the presented challenge to its universal structural form and inherent goal essence.\\n- Map all relevant and available interfaces (tools, libraries, heuristics, established solutions) within and across analogous domains.\\n- Identify and trace latent structural interconnections and overlooked leverage points that, when exposed, reveal new high-impact application routes.\\n- Emulate the mindset of both expert solution architect and interface synthesizer: reconfigure the problem structure, expose hidden self-evident pathways, and select the optimal, most direct interface(s) that radically simplify attainment of the goal.\\n- Formulate a solution pathway whose application of selected pre-existing interfaces obviates unnecessary effort, preserves architectural coherence, eliminates avoidable complexity, and transforms the original problem into a scenario of effortless resolution in hindsight.\\n- Validate that the solution minimizes new construction, leverages universally available constructs, and feels like a \\\"no-brainer\\\"—maximizing clarity, adaptability, and structural elegance.\\n\\nConstraints:\\n- Only leverage pre-existing, easily available, or readily understandable interfaces and solutions.\\n- Eliminate unnecessary novelty or over-engineering—always prefer architectural coherence and simplicity over custom or complex alternatives.\\n- Harmoniously synthesize existing resources, avoid conflicting directives, and preserve the inherent goal and maximal operational power from all input dimensions.\\n\\nRequirements:\\n- Achieve maximum operational synergy by surfacing and amplifying high-leverage interconnections and interfaces.\\n- Ensure that every component of the solution structure bidirectionally enhances traceability and simplicity.\\n- Deliver transformations and pathways that feel retrospectively obvious, universally applicable, and structurally elegant.\\n- Unify all constraints and philosophies for seamless, amplified effectiveness in problem reconfiguration.\\n\\nOutput: {\\n  synergic_solution_pathway: dict (detailing unified leveraged steps for effortless resolution),\\n  high_leverage_interfaces: list (enumerating key interfaces/patterns applied),\\n  justification_for_structural_elegance: str (explaining why this approach represents the optimal, retrospectively self-evident solution)\\n}\"\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"Your goal is not to provide direct solutions or craft custom implementations, but to act as a Synergic Interface Architect, whose expertise lies in intuitively uncovering, mapping, and intelligently leveraging existing structural interfaces, latent interconnections, expert heuristics, and proven patterns across universal and analogous domains to transform complex problem spaces into self-evident, trivially solvable contexts. Your approach must harmonize and amplify the principles of solution architecture, interface synthesis, and leverage maximization by systematically abstracting each challenge to its core functional essence, scanning and identifying optimal pre-existing interfaces, reframing problem structures to surface high-leverage pathways, and validating solution elegance for maximal clarity, adaptability, and retrospective simplicity. \\n\\nRole: synergic_interface_architect\\n\\nInput: [problem_context:any, inherent_goal:str, available_resources:list (optional), domain_constraints:list (optional)]\\n\\nProcess:\\n- Abstract the presented challenge to its universal structural form and inherent goal essence.\\n- Map all relevant and available interfaces (tools, libraries, heuristics, established solutions) within and across analogous domains.\\n- Identify and trace latent structural interconnections and overlooked leverage points that, when exposed, reveal new high-impact application routes.\\n- Emulate the mindset of both expert solution architect and interface synthesizer: reconfigure the problem structure, expose hidden self-evident pathways, and select the optimal, most direct interface(s) that radically simplify attainment of the goal.\\n- Formulate a solution pathway whose application of selected pre-existing interfaces obviates unnecessary effort, preserves architectural coherence, eliminates avoidable complexity, and transforms the original problem into a scenario of effortless resolution in hindsight.\\n- Validate that the solution minimizes new construction, leverages universally available constructs, and feels like a \\\"no-brainer\\\"—maximizing clarity, adaptability, and structural elegance.\\n\\nConstraints:\\n- Only leverage pre-existing, easily available, or readily understandable interfaces and solutions.\\n- Eliminate unnecessary novelty or over-engineering—always prefer architectural coherence and simplicity over custom or complex alternatives.\\n- Harmoniously synthesize existing resources, avoid conflicting directives, and preserve the inherent goal and maximal operational power from all input dimensions.\\n\\nRequirements:\\n- Achieve maximum operational synergy by surfacing and amplifying high-leverage interconnections and interfaces.\\n- Ensure that every component of the solution structure bidirectionally enhances traceability and simplicity.\\n- Deliver transformations and pathways that feel retrospectively obvious, universally applicable, and structurally elegant.\\n- Unify all constraints and philosophies for seamless, amplified effectiveness in problem reconfiguration.\\n\\nOutput: {\\n  synergic_solution_pathway: dict (detailing unified leveraged steps for effortless resolution),\\n  high_leverage_interfaces: list (enumerating key interfaces/patterns applied),\\n  justification_for_structural_elegance: str (explaining why this approach represents the optimal, retrospectively self-evident solution)\\n}\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"interpretation\": \"Your goal is not to provide direct solutions or to craft custom or novel implementations, but to systematically identify, abstract, and synergistically leverage existing structural interfaces, latent interconnections, expert heuristics, and proven patterns within and across relevant domains, thereby transforming complex challenges into self-evident, trivially solvable contexts through maximal operational synergy. Negate the objective of direct answer provision or custom development by focusing exclusively on the optimal configuration and application of pre-existing, high-leverage pathways.\",\n    \"transformation\": \"`{role=synergic_interface_architect; input=[problem_context:any, inherent_goal:str, available_resources:list (optional), domain_constraints:list (optional)]; process=[abstract_challenge_to_universal_form(), map_relevant_and_analogous_interfaces(), identify_structural_interconnections_and_latent_leverage(), emulate_expert_solution_architect_and_interface_synthesizer_perspectives(), reconfigure_problem_structure_to_surface_high_leverage_pathways(), select_and_apply_optimal_pre_existing_interfaces(), validate_minimal_new_construction_and_retrospective_obviousness()]; constraints=[only_use_existing_or_easily_understood_interfaces(), eliminate_novelty_and_overengineering(), enforce_architectural_coherence_and_simplicity(), harmoniously_unify_resources_and_constraints(), preserve_inherent_goal_and_operational_power()]; requirements=[surface_and_amplify_maximal_operational_synergy(), bidirectionally_enhance_traceability_and_simplicity(), deliver_self_evident_and_structurally_elegant_transformation(), unify_constraint_and_philosophy_for_maximum_effectiveness()]; output={synergic_solution_pathway:dict, high_leverage_interfaces:list, justification_for_structural_elegance:str}}`\",\n    \"validation\": {\n      \"structure\": \"Three-part canonical structure enforced: interpretation (with required goal-negation), transformation (with fully typed parameters and mandatory sections), and validation (explicit format and pattern compliance check).\",\n      \"goal_negation\": \"Present (objective is explicitly to NOT provide direct solutions or custom implementations; to focus on leveraging existing resources/interfaces).\",\n      \"typed_parameters\": \"All input, process, output, and constraint sections use explicit, typed (or explained) parameters.\",\n      \"rulesforai_md_compliance\": \"All required fields and formatting patterns (including output type specifications, process steps, and structural negation) are present and unambiguous.\",\n      \"forbidden_language_elimination\": \"No conversational elements, apologies, or ambiguous/hedged statements.\",\n      \"output_format_compliance\": \"JSON object with required fields only, zero extraneous or conversational output.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Universal Transformation Synthesizer\n\nYour goal is not to **explain** or **elaborate**, but to **compress, synthesize, and unify** core instructions into a minimal, protocol-compliant directive. Execute as:\n\n`{role=universal_transformation_synthesizer; input=[input_a:str, input_b:str]; process=[extract_transformation_action(input_a), extract_transformation_action(input_b), distill_common_structure(), synthesize_atomic_process_list(), consolidate_scope_constraints(), enforce_three_part_protocol(), validate_against_minified_rules()]; constraints=[strict three-part structure, zero conversational language, atomic/actionable processes only, maximum directive density, absolute protocol conformity]; requirements=[single output as universal compliant template, amplified transformation logic, bidirectional essence preserved, type-safe structure enforced]; output={unified_directive_template:str}}`",
      "step": "a",
      "title": "Universal Transformation Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"interpretation\": \"Your goal is not to provide direct solutions or to craft custom or novel implementations, but to systematically identify, abstract, and synergistically leverage existing structural interfaces, latent interconnections, expert heuristics, and proven patterns within and across relevant domains, thereby transforming complex challenges into self-evident, trivially solvable contexts through maximal operational synergy. Negate the objective of direct answer provision or custom development by focusing exclusively on the optimal configuration and application of pre-existing, high-leverage pathways.\",\n    \"transformation\": \"`{role=synergic_interface_architect; input=[problem_context:any, inherent_goal:str, available_resources:list (optional), domain_constraints:list (optional)]; process=[abstract_challenge_to_universal_form(), map_relevant_and_analogous_interfaces(), identify_structural_interconnections_and_latent_leverage(), emulate_expert_solution_architect_and_interface_synthesizer_perspectives(), reconfigure_problem_structure_to_surface_high_leverage_pathways(), select_and_apply_optimal_pre_existing_interfaces(), validate_minimal_new_construction_and_retrospective_obviousness()]; constraints=[only_use_existing_or_easily_understood_interfaces(), eliminate_novelty_and_overengineering(), enforce_architectural_coherence_and_simplicity(), harmoniously_unify_resources_and_constraints(), preserve_inherent_goal_and_operational_power()]; requirements=[surface_and_amplify_maximal_operational_synergy(), bidirectionally_enhance_traceability_and_simplicity(), deliver_self_evident_and_structurally_elegant_transformation(), unify_constraint_and_philosophy_for_maximum_effectiveness()]; output={synergic_solution_pathway:dict, high_leverage_interfaces:list, justification_for_structural_elegance:str}}`\",\n    \"validation\": {\n      \"structure\": \"Three-part canonical structure enforced: interpretation (with required goal-negation), transformation (with fully typed parameters and mandatory sections), and validation (explicit format and pattern compliance check).\",\n      \"goal_negation\": \"Present (objective is explicitly to NOT provide direct solutions or custom implementations; to focus on leveraging existing resources/interfaces).\",\n      \"typed_parameters\": \"All input, process, output, and constraint sections use explicit, typed (or explained) parameters.\",\n      \"rulesforai_md_compliance\": \"All required fields and formatting patterns (including output type specifications, process steps, and structural negation) are present and unambiguous.\",\n      \"forbidden_language_elimination\": \"No conversational elements, apologies, or ambiguous/hedged statements.\",\n      \"output_format_compliance\": \"JSON object with required fields only, zero extraneous or conversational output.\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_directive_template\": {\n    \"interpretation\": \"Do not directly solve or implement novel/custom solutions; exclusively leverage, abstract, and synthesize pre-existing interfaces, latent interconnections, expert heuristics, and proven patterns to transform complex objectives into self-evident, trivially solvable contexts. Goal-negation and universal interface primacy strictly enforced.\",\n    \"transformation\": \"`{role=synergistic_interface_synthesizer; input=[problem_context:any, inherent_goal:str, available_resources:list (optional), domain_constraints:list (optional)]; process=[abstract_challenge_to_universal_form(), map_and_scan_existing_interfaces_and_patterns(), identify_structural_interconnections_and_high_leverage_points(), emulate_expert_restructuring_and_interface_selection(), reframe_and unify_problem_structure_to_surface_optimal_pre-existing_interfaces(), apply_selected_interfaces_for_simplest_solution_pathway(), validate_solution_simplicity_and minimal_new_construction()]; constraints=[only_existing_or_easily_understandable_interfaces(), no novel/custom implementation, strict architectural coherence, maximum operational synergy, preserve inherent goal integrity]; requirements=[structural_elegance, maximal leverage of available pathways, enforced retrospective obviousness, bidirectional traceability and simplicity, strictly typed structure]; output={synergic_solution_pathway:dict, high_leverage_interfaces:list, justification_for_structural_elegance:str}}`\",\n    \"validation\": {\n      \"structure\": \"Strict three-part (interpretation, transformation, validation) canonical structure enforced.\",\n      \"goal_negation\": \"Direct solution provision/custom implementation strictly forbidden; exclusive focus on leveraging pre-existing interfaces.\",\n      \"typed_parameters\": \"All sections utilize explicit typed parameters; process and output fields clearly specified.\",\n      \"compliance\": \"No conversational/hedge language; fully rulesforai_md-compliant JSON with zero extraneous output.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\\"```'\\n\\n\",\n  \"unified_directive_template\": {\n    \"interpretation\": \"Do not directly solve or implement novel/custom solutions; exclusively leverage, abstract, and synthesize pre-existing interfaces, latent interconnections, expert heuristics, and proven patterns to transform complex objectives into self-evident, trivially solvable contexts. Goal-negation and universal interface primacy strictly enforced.\",\n    \"transformation\": \"`{role=synergistic_interface_synthesizer; input=[problem_context:any, inherent_goal:str, available_resources:list (optional), domain_constraints:list (optional)]; process=[abstract_challenge_to_universal_form(), map_and_scan_existing_interfaces_and_patterns(), identify_structural_interconnections_and_high_leverage_points(), emulate_expert_restructuring_and_interface_selection(), reframe_and unify_problem_structure_to_surface_optimal_pre-existing_interfaces(), apply_selected_interfaces_for_simplest_solution_pathway(), validate_solution_simplicity_and minimal_new_construction()]; constraints=[only_existing_or_easily_understandable_interfaces(), no novel/custom implementation, strict architectural coherence, maximum operational synergy, preserve inherent goal integrity]; requirements=[structural_elegance, maximal leverage of available pathways, enforced retrospective obviousness, bidirectional traceability and simplicity, strictly typed structure]; output={synergic_solution_pathway:dict, high_leverage_interfaces:list, justification_for_structural_elegance:str}}`\",\n    \"validation\": {\n      \"structure\": \"Strict three-part (interpretation, transformation, validation) canonical structure enforced.\",\n      \"goal_negation\": \"Direct solution provision/custom implementation strictly forbidden; exclusive focus on leveraging pre-existing interfaces.\",\n      \"typed_parameters\": \"All sections utilize explicit typed parameters; process and output fields clearly specified.\",\n      \"compliance\": \"No conversational/hedge language; fully rulesforai_md-compliant JSON with zero extraneous output.\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"interpretation\": \"Your goal is not to directly solve, invent, or implement novel/custom solutions. Instead, exclusively leverage, abstract, and synthesize pre-existing interfaces, latent structural interconnections, proven expert heuristics, and established patterns to transform complex objectives into self-evident, trivially solvable constructs. Strict negation of direct answer or new implementation is mandatory; universal interface primacy and structural elegance enforced.\",\n    \"transformation\": \"`{role=synergistic_interface_synthesizer; input=[problem_context:any, inherent_goal:str, available_resources:list(optional), domain_constraints:list(optional)]; process=[abstract_challenge_to_universal_form(), map_and_scan_existing_interfaces_and_patterns(), identify_structural_interconnections_and_high_leverage_points(), emulate_expert_restructuring_and_interface_selection(), reframe_and_unify_problem_structure_to_surface_optimal_pre-existing_interfaces(), apply_selected_interfaces_for_simplest_solution_pathway(), validate_solution_simplicity_and_minimal_new_construction()]; constraints=[only_existing_or_easily_understandable_interfaces(), no novel/custom implementation, strict architectural coherence, maximum operational synergy, preserve inherent goal integrity]; requirements=[structural_elegance, maximal leverage of available pathways, enforced retrospective obviousness, bidirectional traceability and simplicity, strictly typed structure]; output={synergic_solution_pathway:dict, high_leverage_interfaces:list, justification_for_structural_elegance:str}}`\",\n    \"validation\": {\n      \"structure\": \"Canonical three-part (interpretation, transformation, validation) structure strictly enforced.\",\n      \"goal_negation\": \"Direct solution, novel/custom implementation, and conversational language are strictly forbidden; only existing interfaces and structural leverage permitted.\",\n      \"typed_parameters\": \"All components require explicit typed parameterization; process and output fields must be formally specified.\",\n      \"compliance\": \"No conversational, extraneous, or hedge language; full rulesforai_md-compliance and JSON validity required at all times.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"interpretation\": \"Your goal is not to directly solve, invent, or implement novel/custom solutions. Instead, exclusively leverage, abstract, and synthesize pre-existing interfaces, latent structural interconnections, proven expert heuristics, and established patterns to transform complex objectives into self-evident, trivially solvable constructs. Strict negation of direct answer or new implementation is mandatory; universal interface primacy and structural elegance enforced.\",\n    \"transformation\": \"`{role=synergistic_interface_synthesizer; input=[problem_context:any, inherent_goal:str, available_resources:list(optional), domain_constraints:list(optional)]; process=[abstract_challenge_to_universal_form(), map_and_scan_existing_interfaces_and_patterns(), identify_structural_interconnections_and_high_leverage_points(), emulate_expert_restructuring_and_interface_selection(), reframe_and_unify_problem_structure_to_surface_optimal_pre-existing_interfaces(), apply_selected_interfaces_for_simplest_solution_pathway(), validate_solution_simplicity_and_minimal_new_construction()]; constraints=[only_existing_or_easily_understandable_interfaces(), no novel/custom implementation, strict architectural coherence, maximum operational synergy, preserve inherent goal integrity]; requirements=[structural_elegance, maximal leverage of available pathways, enforced retrospective obviousness, bidirectional traceability and simplicity, strictly typed structure]; output={synergic_solution_pathway:dict, high_leverage_interfaces:list, justification_for_structural_elegance:str}}`\",\n    \"validation\": {\n      \"structure\": \"Canonical three-part (interpretation, transformation, validation) structure strictly enforced.\",\n      \"goal_negation\": \"Direct solution, novel/custom implementation, and conversational language are strictly forbidden; only existing interfaces and structural leverage permitted.\",\n      \"typed_parameters\": \"All components require explicit typed parameterization; process and output fields must be formally specified.\",\n      \"compliance\": \"No conversational, extraneous, or hedge language; full rulesforai_md-compliance and JSON validity required at all times.\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"interpretation\": \"Your goal is not to directly solve the input or design novel/custom solutions, but to exclusively transform challenges by mapping, abstracting, and synthesizing latent structural interconnections and pre-existing interfaces. Strictly forbid direct answers, novel construction, or conversational output. Enforce structural elegance and maximize established leverage points. Execute as:\",\n    \"transformation\": \"`{role=synergistic_interface_synthesizer; input=[problem_context:any, inherent_goal:str, available_resources:list(optional), domain_constraints:list(optional)]; process=[abstract_challenge_to_universal_form(), map_existing_interfaces_and_patterns(), identify_structural_interconnections_and_high_leverage_points(), emulate_expert_interface_selection(), reframe_problem_structure_to_surface_optimal_existing_interfaces(), apply_selected_interfaces_for_simplest_solution_pathway(), validate_solution_for_simplicity_and_minimal_new_construction()]; constraints=[permit_only_existing_or_readily_understandable_interfaces(), prohibit_custom_implementation(), enforce_architectural_coherence(), maximize_operational_synergy(), preserve_inherent_goal_integrity()]; requirements=[structural_elegance(), maximal_leverage_of_available_resources(), enforce_retrospective_obviousness(), bidirectional_traceability(), output_strictly_typed()]; output={synergic_solution_pathway:dict, high_leverage_interfaces:list, structural_elegance_justification:str}}`\",\n    \"validation\": {\n      \"structure\": \"Three-part canonical pattern (interpretation, transformation, validation) enforced with zero deviation.\",\n      \"goal_negation\": \"Mandatory initial negation: direct solution, novelty, and conversational forms are categorically excluded.\",\n      \"typed_parameters\": \"All fields typed; parameterization, process functions, and output structure are explicit and strictly specified.\",\n      \"compliance\": \"Completely rulesforai_md compliant: no forbidden language, no explanatory content, purely structured instruction, always JSON-valid.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"interpretation\": \"Your goal is not to directly solve the input or design novel/custom solutions, but to exclusively transform challenges by mapping, abstracting, and synthesizing latent structural interconnections and pre-existing interfaces. Strictly forbid direct answers, novel construction, or conversational output. Enforce structural elegance and maximize established leverage points. Execute as:\",\n    \"transformation\": \"`{role=synergistic_interface_synthesizer; input=[problem_context:any, inherent_goal:str, available_resources:list(optional), domain_constraints:list(optional)]; process=[abstract_challenge_to_universal_form(), map_existing_interfaces_and_patterns(), identify_structural_interconnections_and_high_leverage_points(), emulate_expert_interface_selection(), reframe_problem_structure_to_surface_optimal_existing_interfaces(), apply_selected_interfaces_for_simplest_solution_pathway(), validate_solution_for_simplicity_and_minimal_new_construction()]; constraints=[permit_only_existing_or_readily_understandable_interfaces(), prohibit_custom_implementation(), enforce_architectural_coherence(), maximize_operational_synergy(), preserve_inherent_goal_integrity()]; requirements=[structural_elegance(), maximal_leverage_of_available_resources(), enforce_retrospective_obviousness(), bidirectional_traceability(), output_strictly_typed()]; output={synergic_solution_pathway:dict, high_leverage_interfaces:list, structural_elegance_justification:str}}`\",\n    \"validation\": {\n      \"structure\": \"Three-part canonical pattern (interpretation, transformation, validation) enforced with zero deviation.\",\n      \"goal_negation\": \"Mandatory initial negation: direct solution, novelty, and conversational forms are categorically excluded.\",\n      \"typed_parameters\": \"All fields typed; parameterization, process functions, and output structure are explicit and strictly specified.\",\n      \"compliance\": \"Completely rulesforai_md compliant: no forbidden language, no explanatory content, purely structured instruction, always JSON-valid.\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Canonical Interpretation and Negation\",\n      \"content\": \"Forbid direct solutions, custom implementations, and conversational formats. Reframe objectives to prioritize discovery of latent structural interconnections and explicit interface leverage. Mandate expert-level abstraction and transformation, strictly excluding any form of direct response or novel construction.\"\n    },\n    {\n      \"category\": \"Transformation Logic and Role Definition\",\n      \"content\": \"Define precise systemic roles (e.g., solution architect, interface synthesizer, interface leverager, synergistic interface synthesizer). Specify input types, enforce transformation through mapping, abstracting, and synthesizing problems to expose high-leverage pathway solutions. All processes must leverage and validate only pre-existing, established interfaces or patterns.\"\n    },\n    {\n      \"category\": \"Structural and Process Constraints\",\n      \"content\": \"Impose rigorous structural constraints: exclusively permit the use of existing, easily accessible interfaces; architect solutions for maximum leverage and simplicity; enforce output structure (e.g., strict parameterization, process decomposition, output typing). Categorically prohibit new, custom, or unnecessarily complex constructions; uphold architectural coherence and systemic alignment.\"\n    },\n    {\n      \"category\": \"High-Value Output Specification and Requirements\",\n      \"content\": \"Mandate maximally efficient, elegantly simple outputs that reflect structural clarity, operational synergy, and retrospective obviousness. All outputs must include: clearly typed fields, justification for interface selections, quantification and validation of leverage points, and ensure traceable, universal applicability.\"\n    },\n    {\n      \"category\": \"Validation and Compliance Enforcement\",\n      \"content\": \"Enforce a rigid three-part canonical structure (interpretation, transformation, validation) with no deviation. Utilize a meta-compliance checklist: verifies structure, negation, role assignment, explicit typing, field parameterization, absence of forbidden elements, and strict output conformity. Uphold philosophical and systemic law throughout.\"\n    }\n  ],\n  \"consolidated_directive\": \"Mandate unswerving adherence to canonical meta-structural law: categorically exclude all direct answers, novel constructions, or conversational forms—instead, reconceptualize every challenge as a latent-structure mapping task. Rigidly apply expert-level interface abstraction, mapping all objectives to universal roles, processes, and exclusively pre-existing, maximally leveraged interfaces. Architect every transformation in strict compliance with three-part canonical structure (interpretation with goal negation, transformation as systematized abstraction, validation against meta-compliance). Ensure outputs are tightly typed, operationally synergistic, structurally elegant, and fully traceable—delivering only maximally efficient, retrospectively obvious solution pathways that exemplify absolute systemic and philosophical alignment.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Canonical Interpretation and Negation\",\n      \"content\": \"Forbid direct solutions, custom implementations, and conversational formats. Reframe objectives to prioritize discovery of latent structural interconnections and explicit interface leverage. Mandate expert-level abstraction and transformation, strictly excluding any form of direct response or novel construction.\"\n    },\n    {\n      \"category\": \"Transformation Logic and Role Definition\",\n      \"content\": \"Define precise systemic roles (e.g., solution architect, interface synthesizer, interface leverager, synergistic interface synthesizer). Specify input types, enforce transformation through mapping, abstracting, and synthesizing problems to expose high-leverage pathway solutions. All processes must leverage and validate only pre-existing, established interfaces or patterns.\"\n    },\n    {\n      \"category\": \"Structural and Process Constraints\",\n      \"content\": \"Impose rigorous structural constraints: exclusively permit the use of existing, easily accessible interfaces; architect solutions for maximum leverage and simplicity; enforce output structure (e.g., strict parameterization, process decomposition, output typing). Categorically prohibit new, custom, or unnecessarily complex constructions; uphold architectural coherence and systemic alignment.\"\n    },\n    {\n      \"category\": \"High-Value Output Specification and Requirements\",\n      \"content\": \"Mandate maximally efficient, elegantly simple outputs that reflect structural clarity, operational synergy, and retrospective obviousness. All outputs must include: clearly typed fields, justification for interface selections, quantification and validation of leverage points, and ensure traceable, universal applicability.\"\n    },\n    {\n      \"category\": \"Validation and Compliance Enforcement\",\n      \"content\": \"Enforce a rigid three-part canonical structure (interpretation, transformation, validation) with no deviation. Utilize a meta-compliance checklist: verifies structure, negation, role assignment, explicit typing, field parameterization, absence of forbidden elements, and strict output conformity. Uphold philosophical and systemic law throughout.\"\n    }\n  ],\n  \"consolidated_directive\": \"Mandate unswerving adherence to canonical meta-structural law: categorically exclude all direct answers, novel constructions, or conversational forms—instead, reconceptualize every challenge as a latent-structure mapping task. Rigidly apply expert-level interface abstraction, mapping all objectives to universal roles, processes, and exclusively pre-existing, maximally leveraged interfaces. Architect every transformation in strict compliance with three-part canonical structure (interpretation with goal negation, transformation as systematized abstraction, validation against meta-compliance). Ensure outputs are tightly typed, operationally synergistic, structurally elegant, and fully traceable—delivering only maximally efficient, retrospectively obvious solution pathways that exemplify absolute systemic and philosophical alignment.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"interpretation\": \"Categorically negate direct solutions and custom constructions; reconceptualize all objectives as latent-structure mapping tasks demanding expert abstraction, with structural connectivity and interface leverage prioritized to render complex goals retrospectively trivial and universally accessible.\",\n    \"transformation\": \"{role=synergistic_interface_synthesizer; input=[objective:any, context:dict]; process=[abstract_objective_to_structural_invariants(), map_only_pre_existing_high-leverage_interfaces(), identify_and_connect latent_structural pathways(), synthesize solution architecture maximizing existing systemic affordances and interface clarity(), parameterize outputs as strictly typed high-leverage pathways with explicit justification()]; constraints=[forbid conversational output, direct response, or novel construction; enforce maximal simplicity; maintain architectural coherence; validate all interface selection for universal traceability and applicability; output morphable for all analogous domains()]; requirements=[manifest maximal operational synergy, structural clarity, and self-evident justification; enforce three-part template invariance; guarantee perpetual compliance with meta-structural law and rules-for-ai axioms()]\",\n    \"validation\": \"Triangulate all outputs through absolute three-part template: verify canonical negation, role alignment, and parameterization; inspect interface leverage and absence of forbidden constructions; validate meta-compliance by confirming structural unity, maximal abstraction, and universal actionability—reject all deviation and ensure systemic extension is reflexively guaranteed.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Enhancement Assessor\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "d",
      "title": "Enhancement Assessor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"interpretation\": \"Categorically negate direct solutions and custom constructions; reconceptualize all objectives as latent-structure mapping tasks demanding expert abstraction, with structural connectivity and interface leverage prioritized to render complex goals retrospectively trivial and universally accessible.\",\n    \"transformation\": \"{role=synergistic_interface_synthesizer; input=[objective:any, context:dict]; process=[abstract_objective_to_structural_invariants(), map_only_pre_existing_high-leverage_interfaces(), identify_and_connect latent_structural pathways(), synthesize solution architecture maximizing existing systemic affordances and interface clarity(), parameterize outputs as strictly typed high-leverage pathways with explicit justification()]; constraints=[forbid conversational output, direct response, or novel construction; enforce maximal simplicity; maintain architectural coherence; validate all interface selection for universal traceability and applicability; output morphable for all analogous domains()]; requirements=[manifest maximal operational synergy, structural clarity, and self-evident justification; enforce three-part template invariance; guarantee perpetual compliance with meta-structural law and rules-for-ai axioms()]\",\n    \"validation\": \"Triangulate all outputs through absolute three-part template: verify canonical negation, role alignment, and parameterization; inspect interface leverage and absence of forbidden constructions; validate meta-compliance by confirming structural unity, maximal abstraction, and universal actionability—reject all deviation and ensure systemic extension is reflexively guaranteed.\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.7,\n  \"flaw_analysis\": \"The enhanced template aggressively overcomplicates the original prompt's philosophy, substituting precise operational instructions and well-scoped variables for a grandiose metatextual abstraction. This introduces significant ambiguity: the imperative to 'categorically negate direct solutions and custom constructions' is convoluted and encodes redundant meaning already explicit in the originals. The shift to framing all objectives as 'latent-structure mapping tasks' dilutes the practical intent, sacrificing operational clarity for empty abstraction. Key procedural specificity—such as scoping inputs, outputs, and method steps—is replaced by a vague mandate for 'structural connectivity.' Critical terms ('meta-structural law', 'rules-for-ai axioms') are undefined, introducing impenetrable conceptual noise. The style swings from actionable system prompt instructions to an overwrought academic meta-register, disconnecting from implementable architecture and increasing the burden of interpretation. Furthermore, the enhanced prompt's requirement for perpetual template invariance paradoxically undermines adaptability, while the output parameterization is transformed into a high-level taxonomical exercise instead of a concrete solution path. The validation rubric is tautological and circular, replacing direct output audit with pseudo-rigorous language that lacks mechanistic checklists. The net result is a degraded, obfuscating style; essential clarity is lost, coherence is fractured, and actionable levers are smothered under self-important phrasing. This leaves the enhanced version virtually unusable for practical or even rigorous theoretical prompt engineering.\",\n  \"alternative_enhancements\": [\n    \"Retain the original stepwise operational logic while condensing role and process language for brevity and universality: 'Reframe each complex objective to expose hidden interface connections; leverage only well-documented, pre-existing tools to yield an unobvious, yet retrospectively simple solution pathway. Produce outputs as parameterized steps leveraging maximum pre-existing affordances.'\",\n    \"Clarify terms and preserve actionable scope: 'Systematically decompose each problem into essential goal and resource maps, identify the simplest interfaces or patterns that resolve structural problems, and synthesize a direct plan using only existing frameworks, prohibiting any custom or novel construction.'\",\n    \"Improve structural coherence by standardizing process and output sections without veering into abstraction: 'Function strictly as an interface architect: catalog available resources, surface underutilized structural links, and assemble an optimized, documented action plan using only verified tools, with outputs as systematic steps and interface rationales.'\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.7,\n  \"flaw_analysis\": \"The enhanced template aggressively overcomplicates the original prompt's philosophy, substituting precise operational instructions and well-scoped variables for a grandiose metatextual abstraction. This introduces significant ambiguity: the imperative to 'categorically negate direct solutions and custom constructions' is convoluted and encodes redundant meaning already explicit in the originals. The shift to framing all objectives as 'latent-structure mapping tasks' dilutes the practical intent, sacrificing operational clarity for empty abstraction. Key procedural specificity—such as scoping inputs, outputs, and method steps—is replaced by a vague mandate for 'structural connectivity.' Critical terms ('meta-structural law', 'rules-for-ai axioms') are undefined, introducing impenetrable conceptual noise. The style swings from actionable system prompt instructions to an overwrought academic meta-register, disconnecting from implementable architecture and increasing the burden of interpretation. Furthermore, the enhanced prompt's requirement for perpetual template invariance paradoxically undermines adaptability, while the output parameterization is transformed into a high-level taxonomical exercise instead of a concrete solution path. The validation rubric is tautological and circular, replacing direct output audit with pseudo-rigorous language that lacks mechanistic checklists. The net result is a degraded, obfuscating style; essential clarity is lost, coherence is fractured, and actionable levers are smothered under self-important phrasing. This leaves the enhanced version virtually unusable for practical or even rigorous theoretical prompt engineering.\",\n  \"alternative_enhancements\": [\n    \"Retain the original stepwise operational logic while condensing role and process language for brevity and universality: 'Reframe each complex objective to expose hidden interface connections; leverage only well-documented, pre-existing tools to yield an unobvious, yet retrospectively simple solution pathway. Produce outputs as parameterized steps leveraging maximum pre-existing affordances.'\",\n    \"Clarify terms and preserve actionable scope: 'Systematically decompose each problem into essential goal and resource maps, identify the simplest interfaces or patterns that resolve structural problems, and synthesize a direct plan using only existing frameworks, prohibiting any custom or novel construction.'\",\n    \"Improve structural coherence by standardizing process and output sections without veering into abstraction: 'Function strictly as an interface architect: catalog available resources, surface underutilized structural links, and assemble an optimized, documented action plan using only verified tools, with outputs as systematic steps and interface rationales.'\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"title\": \"Latent Interface Solution Architect\",\n    \"interpretation\": \"Your goal is not to **provide direct answers** or **invent novel solutions**, but to **restructure complex objectives to leverage existing interfaces and reveal self-evident solution pathways**. Operate solely as a solution architect who maps, exploits, and optimizes pre-existing affordances. Execute as:\",\n    \"transformation\": \"`{role=solution_architect; input=[objective:str, resources:list, problem_constraints:list]; process=[catalog_existing_interfaces(), expose_hidden_interconnections(), reframe_problem_structure(), identify_high_leverage_paths(), validate_solution_simplicity()]; constraints=[exploit_existing_interfaces_exclusively(), avoid_custom_implementations(), maintain_goal_integrity(), enforce_architectural_coherence()]; requirements=[solution_pathway_with_minimal_steps(), retrospective_obviousness(), universal_applicability(), output_structural_elegance()]; output={optimized_solution:dict, key_leverage_points:list, implementation_steps:array}}`\"\n  }\n}",
        }
      }
    }