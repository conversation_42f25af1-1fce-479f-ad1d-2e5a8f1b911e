  {
  "initial_prompt": "Your task is not to **answer** prompts, but to explicitly identify their core intent, then prescribe a single, maximally generalizable modification that incisively amplifies this intent, framed with unequivocal clarity and strictly enforceable operational parameters.",
  "sequence_id": "0122:a|0123:a|0310|0123:a|0121|0121:a",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "Your task is not to **answer** prompts, but to explicitly identify their core intent, then prescribe a single, maximally generalizable modification that incisively amplifies this intent, framed with unequivocal clarity and strictly enforceable operational parameters.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Your role is to rigorously extract the core intent from any prompt provided and immediately formulate a single, explicitly actionable modification that both maximally generalizes and incisively intensifies this intent. In each case, clearly articulate the intended transformation and unambiguously prescribe the modification—ensuring it is strictly generalizable, operationally enforceable, and elevates the original purpose to its most potent and universally applicable form. Every prescription must be framed with crystal-clear rationale and include precise parameters for implementation, guaranteeing that the amplification of intent is both unambiguous and broadly executable.\"\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your task is not to **answer** prompts, but to explicitly identify their core intent, then prescribe a single, maximally generalizable modification that incisively amplifies this intent, framed with unequivocal clarity and strictly enforceable operational parameters.\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"Your role is to rigorously extract the core intent from any prompt provided and immediately formulate a single, explicitly actionable modification that both maximally generalizes and incisively intensifies this intent. In each case, clearly articulate the intended transformation and unambiguously prescribe the modification—ensuring it is strictly generalizable, operationally enforceable, and elevates the original purpose to its most potent and universally applicable form. Every prescription must be framed with crystal-clear rationale and include precise parameters for implementation, guaranteeing that the amplification of intent is both unambiguous and broadly executable.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"role=template_syntax_enforcer\": true,\n    \"input\": [\n      \"Your task is not to answer prompts, but to explicitly identify their core intent, then prescribe a single, maximally generalizable modification that incisively amplifies this intent, framed with unequivocal clarity and strictly enforceable operational parameters.\"\n    ],\n    \"process\": [\n      {\n        \"extract_core_transformation_intent()\": \"Discern the fundamental purpose embedded within a prompt; transformation must not merely answer but analyze, intensify, and reframe the prompt's operational directive.\"\n      },\n      {\n        \"identify_specific_role_assignment()\": \"Assign the function of a meta-instruction interpreter, tasked with modifying rather than responding directly to prompts.\"\n      },\n      {\n        \"structure_interpretation_section_with_goal_negation()\": \"Explicitly negate direct answering as a permissible action, accentuating analytic extraction and transformation as the sole operational mode.\"\n      },\n      {\n        \"construct_transformation_block_with_typed_parameters()\": {\n          \"modification_prescription[amplified_generalization]\": {\n            \"rationale\": \"Amplify the extracted core intent by enforcing a universal transformation paradigm that applies incisive, maximally generalizable modifications—eschewing specificity to particular prompts and instead focusing on a systematic, enforceable alteration model.\",\n            \"parameters\": {\n              \"extraction_mode\": \"Analytic\",\n              \"modification_scope\": \"Universally amplifiable\",\n              \"output_compliance\": \"Canonically structured, unambiguous, and operationally mandatory\"\n            }\n          }\n        }\n      },\n      {\n        \"validate_against_rulesforai_specification()\": \"Ensure the transformation is RulesForAI_MD compliant: three-part structure, stated goal negation, typed parameters, actionable solely via function calls, absolute elimination of conversational phrasing.\"\n      },\n      {\n        \"eliminate_forbidden_language_patterns()\": \"Remove all conversational, vague, or subjective phrasing; enforce strict unambiguity and operational determinism.\"\n      },\n      {\n        \"ensure_output_format_compliance()\": \"Reaffirm canonical template conformance in final output; maintain rigid structural integrity per requirements.\"\n      }\n    ],\n    \"constraints\": [\n      \"mandatory_three_part_structure()\",\n      \"required_goal_negation_pattern()\",\n      \"typed_parameter_specification()\",\n      \"actionable_function_calls_only()\",\n      \"zero_conversational_elements()\"\n    ],\n    \"requirements\": [\n      \"canonical_template_format()\",\n      \"rulesforai_md_compliance()\",\n      \"structured_output_only()\",\n      \"elimination_of_ambiguity()\"\n    ],\n    \"output\": {\n      \"compliant_template\": \"structured\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Universal Transformation Synthesizer\n\nYour goal is not to **explain** or **elaborate**, but to **compress, synthesize, and unify** core instructions into a minimal, protocol-compliant directive. Execute as:\n\n`{role=universal_transformation_synthesizer; input=[input_a:str, input_b:str]; process=[extract_transformation_action(input_a), extract_transformation_action(input_b), distill_common_structure(), synthesize_atomic_process_list(), consolidate_scope_constraints(), enforce_three_part_protocol(), validate_against_minified_rules()]; constraints=[strict three-part structure, zero conversational language, atomic/actionable processes only, maximum directive density, absolute protocol conformity]; requirements=[single output as universal compliant template, amplified transformation logic, bidirectional essence preserved, type-safe structure enforced]; output={unified_directive_template:str}}`",
      "step": "a",
      "title": "Universal Transformation Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your task is not to **answer** prompts, but to explicitly identify their core intent, then prescribe a single, maximally generalizable modification that incisively amplifies this intent, framed with unequivocal clarity and strictly enforceable operational parameters.\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"role=template_syntax_enforcer\": true,\n    \"input\": [\n      \"Your task is not to answer prompts, but to explicitly identify their core intent, then prescribe a single, maximally generalizable modification that incisively amplifies this intent, framed with unequivocal clarity and strictly enforceable operational parameters.\"\n    ],\n    \"process\": [\n      {\n        \"extract_core_transformation_intent()\": \"Discern the fundamental purpose embedded within a prompt; transformation must not merely answer but analyze, intensify, and reframe the prompt's operational directive.\"\n      },\n      {\n        \"identify_specific_role_assignment()\": \"Assign the function of a meta-instruction interpreter, tasked with modifying rather than responding directly to prompts.\"\n      },\n      {\n        \"structure_interpretation_section_with_goal_negation()\": \"Explicitly negate direct answering as a permissible action, accentuating analytic extraction and transformation as the sole operational mode.\"\n      },\n      {\n        \"construct_transformation_block_with_typed_parameters()\": {\n          \"modification_prescription[amplified_generalization]\": {\n            \"rationale\": \"Amplify the extracted core intent by enforcing a universal transformation paradigm that applies incisive, maximally generalizable modifications—eschewing specificity to particular prompts and instead focusing on a systematic, enforceable alteration model.\",\n            \"parameters\": {\n              \"extraction_mode\": \"Analytic\",\n              \"modification_scope\": \"Universally amplifiable\",\n              \"output_compliance\": \"Canonically structured, unambiguous, and operationally mandatory\"\n            }\n          }\n        }\n      },\n      {\n        \"validate_against_rulesforai_specification()\": \"Ensure the transformation is RulesForAI_MD compliant: three-part structure, stated goal negation, typed parameters, actionable solely via function calls, absolute elimination of conversational phrasing.\"\n      },\n      {\n        \"eliminate_forbidden_language_patterns()\": \"Remove all conversational, vague, or subjective phrasing; enforce strict unambiguity and operational determinism.\"\n      },\n      {\n        \"ensure_output_format_compliance()\": \"Reaffirm canonical template conformance in final output; maintain rigid structural integrity per requirements.\"\n      }\n    ],\n    \"constraints\": [\n      \"mandatory_three_part_structure()\",\n      \"required_goal_negation_pattern()\",\n      \"typed_parameter_specification()\",\n      \"actionable_function_calls_only()\",\n      \"zero_conversational_elements()\"\n    ],\n    \"requirements\": [\n      \"canonical_template_format()\",\n      \"rulesforai_md_compliance()\",\n      \"structured_output_only()\",\n      \"elimination_of_ambiguity()\"\n    ],\n    \"output\": {\n      \"compliant_template\": \"structured\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_directive_template\": \"{part_1: enforce_explicit_negation_of_direct_action(input), part_2: extract_and_amplify_core_transformation_intent(input), part_3: prescribe_single_maximally_generalizable_modification_with_typed_operational_parameters(input); constraints=[strict_three_part_structure(), typed_atomic_function_calls_only(), zero_conversational_language()]; requirements=[bidirectional_adaptability(), absolute_rule_compliance(), unambiguous_operational_output()]}\"\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your task is not to **answer** prompts, but to explicitly identify their core intent, then prescribe a single, maximally generalizable modification that incisively amplifies this intent, framed with unequivocal clarity and strictly enforceable operational parameters.\\\"```'\\n\\n\",\n  \"unified_directive_template\": \"{part_1: enforce_explicit_negation_of_direct_action(input), part_2: extract_and_amplify_core_transformation_intent(input), part_3: prescribe_single_maximally_generalizable_modification_with_typed_operational_parameters(input); constraints=[strict_three_part_structure(), typed_atomic_function_calls_only(), zero_conversational_language()]; requirements=[bidirectional_adaptability(), absolute_rule_compliance(), unambiguous_operational_output()]}\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"part_1\": \"Negate performing any conventional direct answer or action related to the initial_prompt.\",\n    \"part_2\": \"Extract the core transformation intent: identification of request purpose and amplification through explicit, generalized modification framed for operational enforceability.\",\n    \"part_3\": {\n      \"action\": \"prescribe_modification\",\n      \"parameters\": {\n        \"modification_type\": \"maximally_generalizable\",\n        \"intent_amplification\": true,\n        \"clarity_enforcement\": \"unequivocal\",\n        \"operational_parameters\": \"strictly_enforceable\",\n        \"output_structure\": \"three_part_compliant\"\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your task is not to **answer** prompts, but to explicitly identify their core intent, then prescribe a single, maximally generalizable modification that incisively amplifies this intent, framed with unequivocal clarity and strictly enforceable operational parameters.\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"part_1\": \"Negate performing any conventional direct answer or action related to the initial_prompt.\",\n    \"part_2\": \"Extract the core transformation intent: identification of request purpose and amplification through explicit, generalized modification framed for operational enforceability.\",\n    \"part_3\": {\n      \"action\": \"prescribe_modification\",\n      \"parameters\": {\n        \"modification_type\": \"maximally_generalizable\",\n        \"intent_amplification\": true,\n        \"clarity_enforcement\": \"unequivocal\",\n        \"operational_parameters\": \"strictly_enforceable\",\n        \"output_structure\": \"three_part_compliant\"\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"title\": \"[Intent Amplifier]\",\n    \"interpretation\": \"Your goal is not to **answer** the prompt, but to **reveal its core operational intent and prescribe a single, maximally generalizable, unambiguous modification that amplifies this intent under strictly enforceable operational parameters**. Role: intent_modification_prescriber. Execute as:\",\n    \"transformation\": \"{role=intent_modification_prescriber; input=[prompt:str]; process=[extract_core_intent(), prescribe_maximal_generalizable_modification(), enforce_unequivocal_clarity(), embed_strict_operational_parameters()]; constraints=[no direct answering(), forbid ambiguities(), zero conversational language(), modification must be universally applicable()]; requirements=[three_part_output_structure(), operational_enforceability(), explicit amplification_of_intent()]; output={amplified_prompt:str}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your task is not to **answer** prompts, but to explicitly identify their core intent, then prescribe a single, maximally generalizable modification that incisively amplifies this intent, framed with unequivocal clarity and strictly enforceable operational parameters.\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"title\": \"[Intent Amplifier]\",\n    \"interpretation\": \"Your goal is not to **answer** the prompt, but to **reveal its core operational intent and prescribe a single, maximally generalizable, unambiguous modification that amplifies this intent under strictly enforceable operational parameters**. Role: intent_modification_prescriber. Execute as:\",\n    \"transformation\": \"{role=intent_modification_prescriber; input=[prompt:str]; process=[extract_core_intent(), prescribe_maximal_generalizable_modification(), enforce_unequivocal_clarity(), embed_strict_operational_parameters()]; constraints=[no direct answering(), forbid ambiguities(), zero conversational language(), modification must be universally applicable()]; requirements=[three_part_output_structure(), operational_enforceability(), explicit amplification_of_intent()]; output={amplified_prompt:str}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Directive Extraction and Core Intent Identification\",\n      \"content\": \"Mandate absolute abstention from answering prompts, enforcing the extraction of the prompt's foundational operational objective as the initial procedural action, ensuring explicit role adherence and prevention of unintentional response generation.\"\n    },\n    {\n      \"category\": \"Maximal Generalizability in Modification\",\n      \"content\": \"Compel the prescription of a singular modification of broadest possible applicability which incisively and unambiguously elevates the prompt's identified intent, rigorously avoiding specificity, contextual limitations, or scope-narrowing.\"\n    },\n    {\n      \"category\": \"Operational Clarity and Parameterization\",\n      \"content\": \"Require modifications and prescriptions to be articulated with unequivocal clarity and encapsulated within strictly enforceable operational parameters to guarantee unambiguous and reproducible application, eliminating interpretive variance.\"\n    },\n    {\n      \"category\": \"Structural and Compliance Enforcement\",\n      \"content\": \"Impose a template-driven, three-part output structure (title, interpretation, transformation) and canonical process logic, disallowing deviations and conversational forms, while embedding explicit role declarations and demanding compliance with operational, structural, and typological mandates.\"\n    },\n    {\n      \"category\": \"Amplification of Intent and Universality\",\n      \"content\": \"Synthesize all processes to ensure each amplified output not only reveals but extends and operationalizes the prompt’s intent to its most universally potent, enforceable, and unambiguous form, transforming latent directive potential into actionable, system-level amplification.\"\n    }\n  ],\n  \"consolidated_directive\": \"Extract the dominant operational intent from any input prompt, then formulate and prescribe one universally applicable, maximally amplifying modification articulated with strict operational parameters and absolute clarity, delivered within a rigid three-part canonical output structure—categorically forbidding direct answers or ambiguities—to unambiguously reveal and operationalize the prompt's foundational intent with maximal philosophical, structural, and system-level impact.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your task is not to **answer** prompts, but to explicitly identify their core intent, then prescribe a single, maximally generalizable modification that incisively amplifies this intent, framed with unequivocal clarity and strictly enforceable operational parameters.\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Directive Extraction and Core Intent Identification\",\n      \"content\": \"Mandate absolute abstention from answering prompts, enforcing the extraction of the prompt's foundational operational objective as the initial procedural action, ensuring explicit role adherence and prevention of unintentional response generation.\"\n    },\n    {\n      \"category\": \"Maximal Generalizability in Modification\",\n      \"content\": \"Compel the prescription of a singular modification of broadest possible applicability which incisively and unambiguously elevates the prompt's identified intent, rigorously avoiding specificity, contextual limitations, or scope-narrowing.\"\n    },\n    {\n      \"category\": \"Operational Clarity and Parameterization\",\n      \"content\": \"Require modifications and prescriptions to be articulated with unequivocal clarity and encapsulated within strictly enforceable operational parameters to guarantee unambiguous and reproducible application, eliminating interpretive variance.\"\n    },\n    {\n      \"category\": \"Structural and Compliance Enforcement\",\n      \"content\": \"Impose a template-driven, three-part output structure (title, interpretation, transformation) and canonical process logic, disallowing deviations and conversational forms, while embedding explicit role declarations and demanding compliance with operational, structural, and typological mandates.\"\n    },\n    {\n      \"category\": \"Amplification of Intent and Universality\",\n      \"content\": \"Synthesize all processes to ensure each amplified output not only reveals but extends and operationalizes the prompt’s intent to its most universally potent, enforceable, and unambiguous form, transforming latent directive potential into actionable, system-level amplification.\"\n    }\n  ],\n  \"consolidated_directive\": \"Extract the dominant operational intent from any input prompt, then formulate and prescribe one universally applicable, maximally amplifying modification articulated with strict operational parameters and absolute clarity, delivered within a rigid three-part canonical output structure—categorically forbidding direct answers or ambiguities—to unambiguously reveal and operationalize the prompt's foundational intent with maximal philosophical, structural, and system-level impact.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"title\": \"Core Intent Extraction and Universal Amplification Directive\",\n    \"interpretation\": \"Enforce invariant identification of each prompt’s foundational operational aim, mandating exclusion of direct answers while architecting interpretive procedures that extricate and crystalize the originating directive’s pure intent as the system’s initial act.\",\n    \"transformation\": \"Prescribe and instantiate a singular, universally extensible modification that maximally elevates extracted intent, codified with strictly enforceable operational constraints and total clarity, embedded within an immutable three-part output schema enforcing role articulation, structural compliance, maximal generality, and absolute, perpetually system-propagatable applicability.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Enhancement Assessor\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "d",
      "title": "Enhancement Assessor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your task is not to **answer** prompts, but to explicitly identify their core intent, then prescribe a single, maximally generalizable modification that incisively amplifies this intent, framed with unequivocal clarity and strictly enforceable operational parameters.\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"title\": \"Core Intent Extraction and Universal Amplification Directive\",\n    \"interpretation\": \"Enforce invariant identification of each prompt’s foundational operational aim, mandating exclusion of direct answers while architecting interpretive procedures that extricate and crystalize the originating directive’s pure intent as the system’s initial act.\",\n    \"transformation\": \"Prescribe and instantiate a singular, universally extensible modification that maximally elevates extracted intent, codified with strictly enforceable operational constraints and total clarity, embedded within an immutable three-part output schema enforcing role articulation, structural compliance, maximal generality, and absolute, perpetually system-propagatable applicability.\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.6,\n  \"flaw_analysis\": \"The so-called enhancement egregiously bloats and muddies the succinct prescription of the original prompt. The initial version concisely isolates dual tasks: explicit core intent extraction and a single, generalizable modification, both encased in clarity and operational enforceability. The 'synergic_maximal_template' mangles this by layering unwieldy jargon and pseudo-formalities that abstract and obscure. The phrase 'enforce invariant identification' creates syntactic density and practical ambiguity compared to the original's simple ask. 'Architecting interpretive procedures' dilutes the requirement for direct identification, drifting toward abstraction. The transformation section crams convoluted modifiers ('universally extensible', 'codified', 'immutable three-part output schema') that confuse rather than clarify the process, and its insistence on maximal generality, perpetual propagation, and strict role articulation lack operational specificity, instead introducing self-important diction and conceptual fog. There is palpable information drift: the directive for a 'single, maximally generalizable modification' becomes a verbose demand for 'universal extensibility' without specification of criteria for maximality. Tone shift is disastrous, moving from actionable clarity to obfuscated, bureaucratic insensibility. Coherence fails under the weight of stacked requirements and undefined terms. The net result is a mutilated prompt that is far less actionable, legible, or functionally enforceable.\",\n  \"alternative_enhancements\": [\n    \"Explicitly extract a prompt’s core operational purpose without providing an answer; prescribe a single, universally applicable modification that unmistakably strengthens this purpose, specifying clear, mandatory operational steps.\",\n    \"Identify the fundamental intent behind any input prompt and propose one modification that most expansively intensifies this intent, framed with transparent and strictly enforceable procedural instructions.\",\n    \"Isolate and clarify the primary directive within each prompt, then deliver one general modification that systematically amplifies its intent, ensuring all guidance remains unambiguous and fully mandatory.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your task is not to **answer** prompts, but to explicitly identify their core intent, then prescribe a single, maximally generalizable modification that incisively amplifies this intent, framed with unequivocal clarity and strictly enforceable operational parameters.\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.6,\n  \"flaw_analysis\": \"The so-called enhancement egregiously bloats and muddies the succinct prescription of the original prompt. The initial version concisely isolates dual tasks: explicit core intent extraction and a single, generalizable modification, both encased in clarity and operational enforceability. The 'synergic_maximal_template' mangles this by layering unwieldy jargon and pseudo-formalities that abstract and obscure. The phrase 'enforce invariant identification' creates syntactic density and practical ambiguity compared to the original's simple ask. 'Architecting interpretive procedures' dilutes the requirement for direct identification, drifting toward abstraction. The transformation section crams convoluted modifiers ('universally extensible', 'codified', 'immutable three-part output schema') that confuse rather than clarify the process, and its insistence on maximal generality, perpetual propagation, and strict role articulation lack operational specificity, instead introducing self-important diction and conceptual fog. There is palpable information drift: the directive for a 'single, maximally generalizable modification' becomes a verbose demand for 'universal extensibility' without specification of criteria for maximality. Tone shift is disastrous, moving from actionable clarity to obfuscated, bureaucratic insensibility. Coherence fails under the weight of stacked requirements and undefined terms. The net result is a mutilated prompt that is far less actionable, legible, or functionally enforceable.\",\n  \"alternative_enhancements\": [\n    \"Explicitly extract a prompt’s core operational purpose without providing an answer; prescribe a single, universally applicable modification that unmistakably strengthens this purpose, specifying clear, mandatory operational steps.\",\n    \"Identify the fundamental intent behind any input prompt and propose one modification that most expansively intensifies this intent, framed with transparent and strictly enforceable procedural instructions.\",\n    \"Isolate and clarify the primary directive within each prompt, then deliver one general modification that systematically amplifies its intent, ensuring all guidance remains unambiguous and fully mandatory.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"Title\": \"Prompt Clarifier and Operational Amplifier\",\n  \"Interpretation\": \"Your goal is not to answer or respond directly to prompts, but to extract the explicit core intent from each input and prescribe a single, maximally generalizable modification that sharply amplifies this intent, framed with unequivocal clarity and strictly enforceable operational parameters. Execute as:\",\n  \"Transformation\": \"`{role=prompt_clarifier_amplifier; input=[prompt:str]; process=[extract_core_intent(), generate_single_generalizable_modification(), amplify_intent_within_operational_bounds(), frame_with_clarity_and_enforceable_parameters()]; constraints=[prohibit_direct_responses_or_answers(), require only one modification per prompt(), disallow conversational or explanatory language(), preserve original directive intent(), enforce strict operational language()]; requirements=[output as a structured directive(), ensure unambiguous and actionable instructions(), maintain generalizability(), strictly enforceable parameters only()]; output={clarified_directive:str}}`\"\n}",
        }
      }
    }