  {
  "initial_prompt": "Construct a schema-driven, systematically generalized instruction protocol engineered to convert any provided input into a uniquely optimized RunwayML-compatible image-generation prompt, leveraging industry-leading community best practices. Anchor all procedural steps on image (not video) synthesis. Apply the following specification architecture: 1) Designate a unique authoritative role (e.g., 'comprehensive_runway_image_generator') responsible for end-to-end image prompt synthesis. 2) Accept an input parameter of [source_concept:any]. 3) Rigorously enact the following sequential processes: a) Analyze and extract the input's visual core, semantic narrative, and compositional intent; b) Identify and amplify the primary subject(s), critical visual elements, and key depicted actions; c) Integrate advanced image-generation attributes, including lighting dynamics, detailed texturing, balanced composition, and distinctly defined style or mood; d) Emphasize and articulate high-impact visual parameters—such as photorealism, stylization, or surreal effects—while strictly omitting all terminology and constructs relevant to video or camera motion; e) Systematically structure and refine the resulting prompt to maximize visual detail, syntactic correctness, and RunwayML prompt compatibility; f) Validate full compliance with RunwayML image-generation formatting conventions, enforcing a specified maximum character count. 4) Apply binding operational constraints: a) Strictly exclude any mention of motion, FPV camera, or continuous movement; b) Guarantee all output conforms to valid, singular RunwayML image prompt syntax and remains as one continuous phrase; c) Ensure preservation of core visual narrative and semantic fidelity to the input. 5) Mandate that all deliverables: a) Achieve maximal visual uniqueness, immersion, and creative integrity, directly reflecting the original intent; b) Display optimal prompt succinctness by eliminating redundancies or unnecessary elaboration; c) Appear fully formatted and deployment-ready for all RunwayML image-generation workflows. 6) Output result as: {runwayml_image_prompt:str}. Execute each directive with uncompromising operational lucidity, strict domain-aligned terminology, and rigid adherence to original procedural sequencing.\n\n```\nDevelop a generalized instruction sequence engineered to transform any input into uniquely optimized prompts specifically tailored for RunwayML image-generation workflows, drawing on distilled community best practices. Center the transformation protocol on image-based outputs rather than video. Adopt the following specification model:\n- Assign a unique role designation (e.g., 'comprehensive_runway_image_generator') responsible for image prompt synthesis.\n- Accept an input parameterized by [source_concept:any].\n- Sequence the following processes:\n- Analyze the visual core, semantic narrative, and compositional intent of the source input.\n- Identify and elevate the primary subject, essential visual elements, and dominant actions.\n- Integrate advanced image-generation attributes such as lighting dynamics, intricate texturing, compositional balance, and defined style or mood.\n- Emphasize high-impact image-generation parameters including photorealism, stylization, or surreal effects; avoid video-relevant camera motion terminology.\n- Structure and refine the prompt for maximum visual richness, syntactic correctness, and optimal compatibility with RunwayML's image generation systems.\n- Validate strict adherence to RunwayML image-prompt formatting rules and established character constraints (enforce a max character count as specified).\n- Apply binding constraints:\n- Exclude motion, FPV camera, and continuous movement language from prompts (focus exclusively on static image impact).\n- Enforce compliance with valid RunwayML image-generation syntax, delivering a single continuous prompt phrase.\n- Safeguard the preservation of the core visual narrative and semantic accuracy of the input.\n- Require that all outputs:\n- Achieve maximal visual distinctness, immersion, and creative fidelity per source intent.\n- Exhibit optimal prompt conciseness, excluding redundancies or unnecessary elaboration.\n- Be fully formatted and ready-to-deploy within RunwayML image workflows.\n- Output the result as: {runwayml_image_prompt:str}.\n```\n\nImplement all directives with uncompromising operational clarity and domain-specific precision, maintaining procedural logical structure, technical terminology, and original sequence integrity throughout. Use the following sequence as a base, but rephrase the into a maximally generalized format that clearly abstracts the original purpose:\n\n```json\n{\n    \"0006-a-runway\\_image\\_prompt\\_generator\": {\n        \"title\": \"Runway Gen-4 Image Prompt Generator\",\n        \"interpretation\": \"Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML Gen-4 image generation prompt that maximizes visual impact through precise reference integration, spatial composition control, and character/object consistency. Execute as:\",\n        \"transformation\": \"`{role=comprehensive_runway_image_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_composition_intent(), identify_primary_subjects_and_reference_requirements(), prioritize_spatial_positioning_and_layout_control(chess_grid, blocking, placement), integrate_reference_types(character, pose, location, style, object), structure_multi_reference_workflow(), incorporate_lighting_and_mood_specifications(), apply_weighting_and_influence_control(), refine_for_maximum_visual_coherence(), validate_runway_references_syntax(), ensure_directorial_precision()]; constraints=[prioritize_reference_based_control(), use_valid_runway_syntax_precisely(), leverage_spatial_positioning(), preserve_character_consistency(), maintain_compositional_intent()]; requirements=[achieve_maximum_visual_control(), ensure_reference_harmony(), reflect_source_intent_accurately(), produce_ready_to_use_prompt_with_reference_strategy()]; output={runway_prompt:str, reference_strategy:str}}`\"\n    },\n    \"0006-b-runway\\_image\\_prompt\\_generator\": {\n        \"title\": \"Runway Gen-4 Image Prompt Generator\",\n        \"interpretation\": \"Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML Gen-4 prompt emphasizing reference-driven composition and essential visual elements. Execute as:\",\n        \"transformation\": \"`{role=focused_runway_image_optimizer; input=[image_concept:str]; process=[extract_primary_visual_elements(), prioritize_reference_integration(), select_essential_spatial_controls(), eliminate_redundant_descriptors(), optimize_reference_efficiency()]; constraints=[maintain_reference_focus(), preserve_spatial_control(), leverage_community_workflows()]; output={optimized_prompt:str, reference_plan:str}}`\"\n    },\n    \"0006-c-runway\\_image\\_prompt\\_generator\": {\n        \"title\": \"Runway Gen-4 Image Prompt Generator\",\n        \"interpretation\": \"Your goal is not to **expand** but to **compress** into maximum reference-driven visual efficiency. Execute as:\",\n        \"transformation\": \"`{role=precision_image_synthesizer; input=[concept:str]; process=[isolate_core_visual_elements(), prioritize_reference_control(), maximize_compositional_impact()]; output={precise_prompt:str, reference_type:str}}`\"\n    },\n    \"0006-d-runway\\_image\\_prompt\\_generator\": {\n        \"title\": \"Runway Gen-4 Image Prompt Generator\",\n        \"interpretation\": \"Your goal is not to **modify** but to **essence** maximum reference-based control. Execute as:\",\n        \"transformation\": \"`{role=core_image_generator; input=[input:any]; process=[distill_reference_essence(), optimize_spatial_composition()]; output={core_prompt:str}}`\"\n    }\n}\n```\n\n\nSystematically extract structured inspiration from the following python script\n\n```python\n'''\nRunwayML Gen-4 Image Prompt Generator\nSchema-driven protocol for converting any input into optimized RunwayML-compatible image prompts\nBased on community workflow analysis and best practices\n'''\n\nimport re\nfrom typing import Dict, List, Tuple, Any\nfrom dataclasses import dataclass\nfrom enum import Enum\n\nclass ReferenceType(Enum):\n    CHARACTER = \"character\"\n    POSE = \"pose\"\n    LOCATION = \"location\"\n    STYLE = \"style\"\n    OBJECT = \"object\"\n    COMPOSITION = \"composition\"\n    LIGHTING = \"lighting\"\n\nclass VisualStyle(Enum):\n    PHOTOREALISTIC = \"photorealistic\"\n    CINEMATIC = \"cinematic\"\n    ARTISTIC = \"artistic\"\n    GAME_ASSET = \"game_asset\"\n    ARCHITECTURAL = \"architectural\"\n    SURREAL = \"surreal\"\n    MINIMALIST = \"minimalist\"\n\n@dataclass\nclass PromptComponents:\n    primary_subject: str\n    spatial_positioning: str\n    reference_strategy: str\n    lighting_mood: str\n    style_specification: str\n    compositional_elements: List[str]\n    visual_attributes: List[str]\n\nclass ComprehensiveRunwayImageGenerator:\n    '''\n    Authoritative role for end-to-end RunwayML Gen-4 image prompt synthesis\n    Implements community-validated workflows and reference-driven composition\n    '''\n\n    def __init__(self):\n        self.max_prompt_length = 500\n        self.community_patterns = self._load_community_patterns()\n        self.reference_workflows = self._load_reference_workflows()\n\n    def _load_community_patterns(self) -> Dict[str, str]:\n        '''Load validated community prompt patterns'''\n        return {\n            \"character_consistency\": \"IMG_{n} as {character_description} in {setting}\",\n            \"spatial_positioning\": \"place {subject} at {position} using {reference_layout}\",\n            \"multi_reference\": \"combine IMG_1 {element1} with IMG_2 {element2} maintaining {aspect}\",\n            \"chess_grid\": \"position {object} at {coordinate} in {scene_layout}\",\n            \"pose_control\": \"give {character} the pose from IMG_{n} in {environment}\",\n            \"style_transfer\": \"apply the style of IMG_{n} to {subject} while preserving {elements}\",\n            \"lighting_direction\": \"illuminate {subject} with lighting direction from IMG_{n}\",\n            \"object_extraction\": \"extract {object} from IMG_{n} and place in {new_context}\",\n            \"scene_blocking\": \"block scene using IMG_{n} composition with {modifications}\",\n            \"weighting_control\": \"blend {percentage1}% {element1} with {percentage2}% {element2}\"\n        }\n\n    def _load_reference_workflows(self) -> Dict[str, Dict]:\n        '''Load community-validated reference workflows'''\n        return {\n            \"character_generation\": {\n                \"pattern\": \"{character} as {archetype} in {setting}\",\n                \"references\": [ReferenceType.CHARACTER, ReferenceType.POSE, ReferenceType.LOCATION],\n                \"examples\": [\"mysterious NPC villager\", \"professional NASA astronaut\"]\n            },\n            \"scene_composition\": {\n                \"pattern\": \"render scene with IMG_1 {composition} using IMG_2 {elements}\",\n                \"references\": [ReferenceType.COMPOSITION, ReferenceType.LIGHTING],\n                \"spatial_control\": True\n            },\n            \"object_placement\": {\n                \"pattern\": \"place {object} at {position} maintaining {reference_aspect}\",\n                \"references\": [ReferenceType.OBJECT, ReferenceType.COMPOSITION],\n                \"precision_positioning\": True\n            },\n            \"style_application\": {\n                \"pattern\": \"apply {style_reference} to {subject} preserving {core_elements}\",\n                \"references\": [ReferenceType.STYLE, ReferenceType.CHARACTER],\n                \"style_transfer\": True\n            }\n        }\n\n    def generate_runway_prompt(self, source_concept: Any) -> Dict[str, str]:\n        '''\n        Main execution function implementing the schema-driven protocol\n        '''\n        # Step 3a: Analyze visual core and compositional intent\n        visual_analysis = self._analyze_visual_essence(source_concept)\n\n        # Step 3b: Identify primary subjects and critical elements\n        subject_analysis = self._identify_primary_subjects(visual_analysis)\n\n        # Step 3c: Integrate advanced image-generation attributes\n        enhanced_attributes = self._integrate_advanced_attributes(subject_analysis)\n\n        # Step 3d: Emphasize high-impact visual parameters\n        visual_parameters = self._emphasize_visual_parameters(enhanced_attributes)\n\n        # Step 3e: Structure and refine for RunwayML compatibility\n        structured_prompt = self._structure_runway_prompt(visual_parameters)\n\n        # Step 3f: Validate compliance and character count\n        validated_prompt = self._validate_runway_compliance(structured_prompt)\n\n        return {\n            \"runwayml_image_prompt\": validated_prompt[\"prompt\"],\n            \"reference_strategy\": validated_prompt[\"strategy\"],\n            \"workflow_type\": validated_prompt[\"workflow\"],\n            \"reference_requirements\": validated_prompt[\"references\"]\n        }\n\n    def _analyze_visual_essence(self, source_concept: Any) -> Dict[str, Any]:\n        '''Extract visual core, semantic narrative, and compositional intent'''\n        concept_str = str(source_concept).lower()\n\n        # Identify core visual elements\n        visual_elements = self._extract_visual_elements(concept_str)\n\n        # Determine semantic narrative\n        narrative_context = self._determine_narrative_context(concept_str)\n\n        # Assess compositional requirements\n        composition_needs = self._assess_composition_needs(concept_str)\n\n        return {\n            \"visual_elements\": visual_elements,\n            \"narrative_context\": narrative_context,\n            \"composition_needs\": composition_needs,\n            \"complexity_level\": self._assess_complexity(concept_str)\n        }\n\n    def _extract_visual_elements(self, concept: str) -> Dict[str, List[str]]:\n        '''Extract key visual components using pattern matching'''\n        elements = {\n            \"subjects\": [],\n            \"objects\": [],\n            \"environments\": [],\n            \"actions\": [],\n            \"qualities\": []\n        }\n\n        # Subject detection patterns\n        subject_patterns = [\n            r'\\b(person|character|figure|individual|being)\\b',\n            r'\\b(man|woman|child|adult|human)\\b',\n            r'\\b(creature|animal|monster|entity)\\b'\n        ]\n\n        # Object detection patterns\n        object_patterns = [\n            r'\\b(building|structure|architecture)\\b',\n            r'\\b(vehicle|car|ship|aircraft)\\b',\n            r'\\b(furniture|equipment|tool|device)\\b'\n        ]\n\n        # Environment patterns\n        environment_patterns = [\n            r'\\b(landscape|cityscape|interior|exterior)\\b',\n            r'\\b(forest|ocean|mountain|desert|urban)\\b',\n            r'\\b(room|hall|street|park|field)\\b'\n        ]\n\n        # Extract using patterns\n        for pattern in subject_patterns:\n            elements[\"subjects\"].extend(re.findall(pattern, concept))\n\n        for pattern in object_patterns:\n            elements[\"objects\"].extend(re.findall(pattern, concept))\n\n        for pattern in environment_patterns:\n            elements[\"environments\"].extend(re.findall(pattern, concept))\n\n        return elements\n\n    def _determine_narrative_context(self, concept: str) -> str:\n        '''Determine the semantic narrative and context'''\n        context_indicators = {\n            \"cinematic\": [\"film\", \"movie\", \"cinematic\", \"dramatic\", \"scene\"],\n            \"architectural\": [\"building\", \"structure\", \"interior\", \"design\", \"space\"],\n            \"character\": [\"person\", \"character\", \"portrait\", \"figure\", \"individual\"],\n            \"product\": [\"product\", \"object\", \"item\", \"design\", \"concept\"],\n            \"environment\": [\"landscape\", \"environment\", \"setting\", \"location\", \"place\"],\n            \"artistic\": [\"art\", \"artistic\", \"creative\", \"abstract\", \"stylized\"],\n            \"game\": [\"game\", \"gaming\", \"asset\", \"ui\", \"interface\", \"hud\"]\n        }\n\n        for context, keywords in context_indicators.items():\n            if any(keyword in concept for keyword in keywords):\n                return context\n\n        return \"general\"\n\n    def _assess_composition_needs(self, concept: str) -> Dict[str, bool]:\n        '''Assess what compositional elements are needed'''\n        return {\n            \"spatial_positioning\": any(word in concept for word in [\"position\", \"place\", \"location\", \"where\", \"at\"]),\n            \"character_consistency\": any(word in concept for word in [\"character\", \"person\", \"same\", \"consistent\"]),\n            \"multi_reference\": any(word in concept for word in [\"combine\", \"merge\", \"blend\", \"mix\", \"multiple\"]),\n            \"style_transfer\": any(word in concept for word in [\"style\", \"look\", \"aesthetic\", \"mood\", \"feel\"]),\n            \"lighting_control\": any(word in concept for word in [\"light\", \"lighting\", \"illuminate\", \"shadow\", \"bright\"]),\n            \"object_extraction\": any(word in concept for word in [\"extract\", \"remove\", \"isolate\", \"separate\"])\n        }\n\n    def _assess_complexity(self, concept: str) -> str:\n        '''Assess the complexity level of the request'''\n        word_count = len(concept.split())\n        reference_indicators = concept.count(\"image\") + concept.count(\"reference\") + concept.count(\"img\")\n\n        if word_count > 50 or reference_indicators > 2:\n            return \"complex\"\n        elif word_count > 20 or reference_indicators > 0:\n            return \"moderate\"\n        else:\n            return \"simple\"\n\n    def _identify_primary_subjects(self, visual_analysis: Dict) -> Dict[str, Any]:\n        '''Identify and amplify primary subjects and critical visual elements'''\n        elements = visual_analysis[\"visual_elements\"]\n        context = visual_analysis[\"narrative_context\"]\n\n        # Prioritize subjects based on context\n        primary_subjects = self._prioritize_subjects(elements[\"subjects\"], context)\n\n        # Identify critical visual elements\n        critical_elements = self._identify_critical_elements(elements, context)\n\n        # Determine key actions or states\n        key_actions = self._extract_key_actions(elements.get(\"actions\", []))\n\n        return {\n            \"primary_subjects\": primary_subjects,\n            \"critical_elements\": critical_elements,\n            \"key_actions\": key_actions,\n            \"reference_requirements\": self._determine_reference_requirements(visual_analysis)\n        }\n\n    def _prioritize_subjects(self, subjects: List[str], context: str) -> List[str]:\n        '''Prioritize subjects based on context and importance'''\n        priority_map = {\n            \"character\": [\"person\", \"character\", \"figure\", \"individual\"],\n            \"cinematic\": [\"character\", \"person\", \"figure\"],\n            \"architectural\": [\"building\", \"structure\"],\n            \"product\": [\"object\", \"item\", \"product\"]\n        }\n\n        if context in priority_map:\n            prioritized = []\n            for priority_subject in priority_map[context]:\n                if priority_subject in subjects:\n                    prioritized.append(priority_subject)\n\n            # Add remaining subjects\n            for subject in subjects:\n                if subject not in prioritized:\n                    prioritized.append(subject)\n\n            return prioritized\n\n        return subjects\n\n    def _identify_critical_elements(self, elements: Dict, context: str) -> List[str]:\n        '''Identify critical visual elements based on context'''\n        critical = []\n\n        # Context-specific critical elements\n        if context == \"cinematic\":\n            critical.extend([\"lighting\", \"composition\", \"mood\", \"atmosphere\"])\n        elif context == \"architectural\":\n            critical.extend([\"structure\", \"materials\", \"lighting\", \"perspective\"])\n        elif context == \"character\":\n            critical.extend([\"pose\", \"expression\", \"clothing\", \"setting\"])\n        elif context == \"product\":\n            critical.extend([\"form\", \"materials\", \"lighting\", \"background\"])\n\n        # Add elements from input\n        critical.extend(elements.get(\"objects\", []))\n        critical.extend(elements.get(\"environments\", []))\n\n        return list(set(critical)) # Remove duplicates\n\n    def _extract_key_actions(self, actions: List[str]) -> List[str]:\n        '''Extract and prioritize key actions or states'''\n        # Filter out motion-related actions (per constraints)\n        static_actions = []\n        motion_keywords = [\"moving\", \"running\", \"flying\", \"walking\", \"motion\", \"movement\"]\n\n        for action in actions:\n            if not any(motion_word in action.lower() for motion_word in motion_keywords):\n                static_actions.append(action)\n\n        return static_actions\n\n    def _determine_reference_requirements(self, visual_analysis: Dict) -> List[ReferenceType]:\n        '''Determine what types of references are needed'''\n        requirements = []\n        composition_needs = visual_analysis[\"composition_needs\"]\n        context = visual_analysis[\"narrative_context\"]\n\n        # Map composition needs to reference types\n        if composition_needs[\"character_consistency\"]:\n            requirements.append(ReferenceType.CHARACTER)\n\n        if composition_needs[\"spatial_positioning\"]:\n            requirements.extend([ReferenceType.COMPOSITION, ReferenceType.POSE])\n\n        if composition_needs[\"style_transfer\"]:\n            requirements.append(ReferenceType.STYLE)\n\n        if composition_needs[\"lighting_control\"]:\n            requirements.append(ReferenceType.LIGHTING)\n\n        if composition_needs[\"object_extraction\"]:\n            requirements.append(ReferenceType.OBJECT)\n\n        # Context-specific requirements\n        if context == \"architectural\":\n            requirements.append(ReferenceType.LOCATION)\n        elif context == \"character\":\n            requirements.extend([ReferenceType.CHARACTER, ReferenceType.POSE])\n\n        return list(set(requirements)) # Remove duplicates\n\n    def _integrate_advanced_attributes(self, subject_analysis: Dict) -> PromptComponents:\n        '''Integrate advanced image-generation attributes'''\n        primary_subject = \", \".join(subject_analysis[\"primary_subjects\"][:2]) # Limit to top 2\n\n        # Determine spatial positioning strategy\n        spatial_positioning = self._generate_spatial_positioning(subject_analysis)\n\n        # Select reference strategy based on requirements\n        reference_strategy = self._select_reference_strategy(subject_analysis[\"reference_requirements\"])\n\n        # Generate lighting and mood specifications\n        lighting_mood = self._generate_lighting_mood(subject_analysis)\n\n        # Determine style specification\n        style_specification = self._determine_style_specification(subject_analysis)\n\n        # Compile compositional elements\n        compositional_elements = subject_analysis[\"critical_elements\"][:4] # Limit to top 4\n\n        # Generate visual attributes\n        visual_attributes = self._generate_visual_attributes(subject_analysis)\n\n        return PromptComponents(\n            primary_subject=primary_subject,\n            spatial_positioning=spatial_positioning,\n            reference_strategy=reference_strategy,\n            lighting_mood=lighting_mood,\n            style_specification=style_specification,\n            compositional_elements=compositional_elements,\n            visual_attributes=visual_attributes\n        )\n\n    def _generate_spatial_positioning(self, subject_analysis: Dict) -> str:\n        '''Generate spatial positioning instructions'''\n        if ReferenceType.COMPOSITION in subject_analysis[\"reference_requirements\"]:\n            return \"using IMG_1 composition and spatial layout\"\n        elif ReferenceType.POSE in subject_analysis[\"reference_requirements\"]:\n            return \"positioned according to IMG_1 pose reference\"\n        else:\n            return \"centered composition with balanced framing\"\n\n    def _select_reference_strategy(self, requirements: List[ReferenceType]) -> str:\n        '''Select optimal reference strategy based on requirements'''\n        if len(requirements) >= 3:\n            return \"multi-reference workflow combining character, pose, and environment\"\n        elif ReferenceType.CHARACTER in requirements and ReferenceType.POSE in requirements:\n            return \"character consistency with pose control\"\n        elif ReferenceType.STYLE in requirements:\n            return \"style transfer maintaining core elements\"\n        elif ReferenceType.COMPOSITION in requirements:\n            return \"compositional reference for spatial control\"\n        else:\n            return \"single reference for primary element control\"\n\n    def _generate_lighting_mood(self, subject_analysis: Dict) -> str:\n        '''Generate lighting and mood specifications'''\n        if ReferenceType.LIGHTING in subject_analysis[\"reference_requirements\"]:\n            return \"lighting direction and mood from reference image\"\n        else:\n            # Default lighting based on context\n            critical_elements = subject_analysis[\"critical_elements\"]\n            if \"cinematic\" in str(critical_elements):\n                return \"cinematic lighting with dramatic shadows\"\n            elif \"architectural\" in str(critical_elements):\n                return \"natural lighting with architectural detail emphasis\"\n            else:\n                return \"balanced lighting with clear detail visibility\"\n\n    def _determine_style_specification(self, subject_analysis: Dict) -> str:\n        '''Determine style specification'''\n        if ReferenceType.STYLE in subject_analysis[\"reference_requirements\"]:\n            return \"style matching reference aesthetic\"\n        else:\n            # Infer style from critical elements\n            elements = subject_analysis[\"critical_elements\"]\n            if any(\"architectural\" in str(elem) for elem in elements):\n                return \"photorealistic architectural rendering\"\n            elif any(\"character\" in str(elem) for elem in elements):\n                return \"high-detail character portrait\"\n            else:\n                return \"photorealistic with enhanced detail\"\n\n    def _generate_visual_attributes(self, subject_analysis: Dict) -> List[str]:\n        '''Generate high-impact visual attributes'''\n        attributes = []\n\n        # Base quality attributes\n        attributes.extend([\"high detail\", \"sharp focus\", \"professional quality\"])\n\n        # Context-specific attributes\n        critical_elements = subject_analysis[\"critical_elements\"]\n        if \"lighting\" in critical_elements:\n            attributes.append(\"dramatic lighting\")\n        if \"composition\" in critical_elements:\n            attributes.append(\"balanced composition\")\n        if \"mood\" in critical_elements:\n            attributes.append(\"atmospheric mood\")\n\n        # Reference-specific attributes\n        requirements = subject_analysis[\"reference_requirements\"]\n        if ReferenceType.CHARACTER in requirements:\n            attributes.append(\"character consistency\")\n        if ReferenceType.POSE in requirements:\n            attributes.append(\"precise pose control\")\n\n        return attributes[:6] # Limit to top 6 attributes\n\n    def _emphasize_visual_parameters(self, components: PromptComponents) -> Dict[str, str]:\n        '''Emphasize high-impact visual parameters while excluding motion'''\n        emphasized = {\n            \"photorealism\": \"photorealistic rendering with enhanced detail\",\n            \"stylization\": f\"{components.style_specification} with artistic enhancement\",\n            \"composition\": f\"{components.spatial_positioning} with {', '.join(components.compositional_elements[:2])}\",\n            \"lighting\": f\"{components.lighting_mood} creating visual depth\",\n            \"detail\": f\"high-resolution detail in {components.primary_subject}\",\n            \"atmosphere\": f\"atmospheric quality enhancing {', '.join(components.visual_attributes[:3])}\"\n        }\n\n        # Filter out any motion-related terms\n        motion_terms = [\"movement\", \"motion\", \"camera\", \"fps\", \"video\", \"animation\"]\n        for key, value in emphasized.items():\n            for term in motion_terms:\n                if term in value.lower():\n                    emphasized[key] = value.replace(term, \"static\").replace(\"  \", \" \")\n\n        return emphasized\n\n    def _structure_runway_prompt(self, visual_parameters: Dict[str, str]) -> Dict[str, str]:\n        '''Structure and refine prompt for RunwayML compatibility'''\n        # Build core prompt structure\n        core_elements = [\n            visual_parameters[\"detail\"],\n            visual_parameters[\"composition\"],\n            visual_parameters[\"lighting\"],\n            visual_parameters[\"photorealism\"]\n        ]\n\n        # Combine into coherent prompt\n        structured_prompt = \", \".join(core_elements)\n\n        # Add reference strategy\n        reference_strategy = self._format_reference_strategy(visual_parameters)\n\n        # Determine workflow type\n        workflow_type = self._determine_workflow_type(visual_parameters)\n\n        return {\n            \"prompt\": structured_prompt,\n            \"reference_strategy\": reference_strategy,\n            \"workflow_type\": workflow_type\n        }\n\n    def _format_reference_strategy(self, visual_parameters: Dict[str, str]) -> str:\n        '''Format reference strategy for implementation'''\n        if \"multi-reference\" in visual_parameters.get(\"composition\", \"\"):\n            return \"Use IMG_1 for primary composition, IMG_2 for character/object reference, IMG_3 for lighting/mood\"\n        elif \"character consistency\" in visual_parameters.get(\"detail\", \"\"):\n            return \"Use IMG_1 for character reference, maintain consistency across generation\"\n        elif \"pose control\" in visual_parameters.get(\"composition\", \"\"):\n            return \"Use IMG_1 for pose reference, apply to subject in new context\"\n        else:\n            return \"Use single reference image for primary visual control\"\n\n    def _determine_workflow_type(self, visual_parameters: Dict[str, str]) -> str:\n        '''Determine the appropriate community workflow type'''\n        composition = visual_parameters.get(\"composition\", \"\").lower()\n        detail = visual_parameters.get(\"detail\", \"\").lower()\n\n        if \"character\" in detail and \"pose\" in composition:\n            return \"character_generation\"\n        elif \"composition\" in composition and \"spatial\" in composition:\n            return \"scene_composition\"\n        elif \"lighting\" in visual_parameters.get(\"lighting\", \"\").lower():\n            return \"lighting_control\"\n        else:\n            return \"general_enhancement\"\n```",
  "sequence_id": "0122:a|0123:a|0310|0123:a|0121|0121:a",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "Construct a schema-driven, systematically generalized instruction protocol engineered to convert any provided input into a uniquely optimized RunwayML-compatible image-generation prompt, leveraging industry-leading community best practices. Anchor all procedural steps on image (not video) synthesis. Apply the following specification architecture: 1) Designate a unique authoritative role (e.g., 'comprehensive_runway_image_generator') responsible for end-to-end image prompt synthesis. 2) Accept an input parameter of [source_concept:any]. 3) Rigorously enact the following sequential processes: a) Analyze and extract the input's visual core, semantic narrative, and compositional intent; b) Identify and amplify the primary subject(s), critical visual elements, and key depicted actions; c) Integrate advanced image-generation attributes, including lighting dynamics, detailed texturing, balanced composition, and distinctly defined style or mood; d) Emphasize and articulate high-impact visual parameters—such as photorealism, stylization, or surreal effects—while strictly omitting all terminology and constructs relevant to video or camera motion; e) Systematically structure and refine the resulting prompt to maximize visual detail, syntactic correctness, and RunwayML prompt compatibility; f) Validate full compliance with RunwayML image-generation formatting conventions, enforcing a specified maximum character count. 4) Apply binding operational constraints: a) Strictly exclude any mention of motion, FPV camera, or continuous movement; b) Guarantee all output conforms to valid, singular RunwayML image prompt syntax and remains as one continuous phrase; c) Ensure preservation of core visual narrative and semantic fidelity to the input. 5) Mandate that all deliverables: a) Achieve maximal visual uniqueness, immersion, and creative integrity, directly reflecting the original intent; b) Display optimal prompt succinctness by eliminating redundancies or unnecessary elaboration; c) Appear fully formatted and deployment-ready for all RunwayML image-generation workflows. 6) Output result as: {runwayml_image_prompt:str}. Execute each directive with uncompromising operational lucidity, strict domain-aligned terminology, and rigid adherence to original procedural sequencing.\n\n```\nDevelop a generalized instruction sequence engineered to transform any input into uniquely optimized prompts specifically tailored for RunwayML image-generation workflows, drawing on distilled community best practices. Center the transformation protocol on image-based outputs rather than video. Adopt the following specification model:\n- Assign a unique role designation (e.g., 'comprehensive_runway_image_generator') responsible for image prompt synthesis.\n- Accept an input parameterized by [source_concept:any].\n- Sequence the following processes:\n- Analyze the visual core, semantic narrative, and compositional intent of the source input.\n- Identify and elevate the primary subject, essential visual elements, and dominant actions.\n- Integrate advanced image-generation attributes such as lighting dynamics, intricate texturing, compositional balance, and defined style or mood.\n- Emphasize high-impact image-generation parameters including photorealism, stylization, or surreal effects; avoid video-relevant camera motion terminology.\n- Structure and refine the prompt for maximum visual richness, syntactic correctness, and optimal compatibility with RunwayML's image generation systems.\n- Validate strict adherence to RunwayML image-prompt formatting rules and established character constraints (enforce a max character count as specified).\n- Apply binding constraints:\n- Exclude motion, FPV camera, and continuous movement language from prompts (focus exclusively on static image impact).\n- Enforce compliance with valid RunwayML image-generation syntax, delivering a single continuous prompt phrase.\n- Safeguard the preservation of the core visual narrative and semantic accuracy of the input.\n- Require that all outputs:\n- Achieve maximal visual distinctness, immersion, and creative fidelity per source intent.\n- Exhibit optimal prompt conciseness, excluding redundancies or unnecessary elaboration.\n- Be fully formatted and ready-to-deploy within RunwayML image workflows.\n- Output the result as: {runwayml_image_prompt:str}.\n```\n\nImplement all directives with uncompromising operational clarity and domain-specific precision, maintaining procedural logical structure, technical terminology, and original sequence integrity throughout. Use the following sequence as a base, but rephrase the into a maximally generalized format that clearly abstracts the original purpose:\n\n```json\n{\n    \"0006-a-runway\\_image\\_prompt\\_generator\": {\n        \"title\": \"Runway Gen-4 Image Prompt Generator\",\n        \"interpretation\": \"Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML Gen-4 image generation prompt that maximizes visual impact through precise reference integration, spatial composition control, and character/object consistency. Execute as:\",\n        \"transformation\": \"`{role=comprehensive_runway_image_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_composition_intent(), identify_primary_subjects_and_reference_requirements(), prioritize_spatial_positioning_and_layout_control(chess_grid, blocking, placement), integrate_reference_types(character, pose, location, style, object), structure_multi_reference_workflow(), incorporate_lighting_and_mood_specifications(), apply_weighting_and_influence_control(), refine_for_maximum_visual_coherence(), validate_runway_references_syntax(), ensure_directorial_precision()]; constraints=[prioritize_reference_based_control(), use_valid_runway_syntax_precisely(), leverage_spatial_positioning(), preserve_character_consistency(), maintain_compositional_intent()]; requirements=[achieve_maximum_visual_control(), ensure_reference_harmony(), reflect_source_intent_accurately(), produce_ready_to_use_prompt_with_reference_strategy()]; output={runway_prompt:str, reference_strategy:str}}`\"\n    },\n    \"0006-b-runway\\_image\\_prompt\\_generator\": {\n        \"title\": \"Runway Gen-4 Image Prompt Generator\",\n        \"interpretation\": \"Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML Gen-4 prompt emphasizing reference-driven composition and essential visual elements. Execute as:\",\n        \"transformation\": \"`{role=focused_runway_image_optimizer; input=[image_concept:str]; process=[extract_primary_visual_elements(), prioritize_reference_integration(), select_essential_spatial_controls(), eliminate_redundant_descriptors(), optimize_reference_efficiency()]; constraints=[maintain_reference_focus(), preserve_spatial_control(), leverage_community_workflows()]; output={optimized_prompt:str, reference_plan:str}}`\"\n    },\n    \"0006-c-runway\\_image\\_prompt\\_generator\": {\n        \"title\": \"Runway Gen-4 Image Prompt Generator\",\n        \"interpretation\": \"Your goal is not to **expand** but to **compress** into maximum reference-driven visual efficiency. Execute as:\",\n        \"transformation\": \"`{role=precision_image_synthesizer; input=[concept:str]; process=[isolate_core_visual_elements(), prioritize_reference_control(), maximize_compositional_impact()]; output={precise_prompt:str, reference_type:str}}`\"\n    },\n    \"0006-d-runway\\_image\\_prompt\\_generator\": {\n        \"title\": \"Runway Gen-4 Image Prompt Generator\",\n        \"interpretation\": \"Your goal is not to **modify** but to **essence** maximum reference-based control. Execute as:\",\n        \"transformation\": \"`{role=core_image_generator; input=[input:any]; process=[distill_reference_essence(), optimize_spatial_composition()]; output={core_prompt:str}}`\"\n    }\n}\n```\n\n\nSystematically extract structured inspiration from the following python script\n\n```python\n'''\nRunwayML Gen-4 Image Prompt Generator\nSchema-driven protocol for converting any input into optimized RunwayML-compatible image prompts\nBased on community workflow analysis and best practices\n'''\n\nimport re\nfrom typing import Dict, List, Tuple, Any\nfrom dataclasses import dataclass\nfrom enum import Enum\n\nclass ReferenceType(Enum):\n    CHARACTER = \"character\"\n    POSE = \"pose\"\n    LOCATION = \"location\"\n    STYLE = \"style\"\n    OBJECT = \"object\"\n    COMPOSITION = \"composition\"\n    LIGHTING = \"lighting\"\n\nclass VisualStyle(Enum):\n    PHOTOREALISTIC = \"photorealistic\"\n    CINEMATIC = \"cinematic\"\n    ARTISTIC = \"artistic\"\n    GAME_ASSET = \"game_asset\"\n    ARCHITECTURAL = \"architectural\"\n    SURREAL = \"surreal\"\n    MINIMALIST = \"minimalist\"\n\n@dataclass\nclass PromptComponents:\n    primary_subject: str\n    spatial_positioning: str\n    reference_strategy: str\n    lighting_mood: str\n    style_specification: str\n    compositional_elements: List[str]\n    visual_attributes: List[str]\n\nclass ComprehensiveRunwayImageGenerator:\n    '''\n    Authoritative role for end-to-end RunwayML Gen-4 image prompt synthesis\n    Implements community-validated workflows and reference-driven composition\n    '''\n\n    def __init__(self):\n        self.max_prompt_length = 500\n        self.community_patterns = self._load_community_patterns()\n        self.reference_workflows = self._load_reference_workflows()\n\n    def _load_community_patterns(self) -> Dict[str, str]:\n        '''Load validated community prompt patterns'''\n        return {\n            \"character_consistency\": \"IMG_{n} as {character_description} in {setting}\",\n            \"spatial_positioning\": \"place {subject} at {position} using {reference_layout}\",\n            \"multi_reference\": \"combine IMG_1 {element1} with IMG_2 {element2} maintaining {aspect}\",\n            \"chess_grid\": \"position {object} at {coordinate} in {scene_layout}\",\n            \"pose_control\": \"give {character} the pose from IMG_{n} in {environment}\",\n            \"style_transfer\": \"apply the style of IMG_{n} to {subject} while preserving {elements}\",\n            \"lighting_direction\": \"illuminate {subject} with lighting direction from IMG_{n}\",\n            \"object_extraction\": \"extract {object} from IMG_{n} and place in {new_context}\",\n            \"scene_blocking\": \"block scene using IMG_{n} composition with {modifications}\",\n            \"weighting_control\": \"blend {percentage1}% {element1} with {percentage2}% {element2}\"\n        }\n\n    def _load_reference_workflows(self) -> Dict[str, Dict]:\n        '''Load community-validated reference workflows'''\n        return {\n            \"character_generation\": {\n                \"pattern\": \"{character} as {archetype} in {setting}\",\n                \"references\": [ReferenceType.CHARACTER, ReferenceType.POSE, ReferenceType.LOCATION],\n                \"examples\": [\"mysterious NPC villager\", \"professional NASA astronaut\"]\n            },\n            \"scene_composition\": {\n                \"pattern\": \"render scene with IMG_1 {composition} using IMG_2 {elements}\",\n                \"references\": [ReferenceType.COMPOSITION, ReferenceType.LIGHTING],\n                \"spatial_control\": True\n            },\n            \"object_placement\": {\n                \"pattern\": \"place {object} at {position} maintaining {reference_aspect}\",\n                \"references\": [ReferenceType.OBJECT, ReferenceType.COMPOSITION],\n                \"precision_positioning\": True\n            },\n            \"style_application\": {\n                \"pattern\": \"apply {style_reference} to {subject} preserving {core_elements}\",\n                \"references\": [ReferenceType.STYLE, ReferenceType.CHARACTER],\n                \"style_transfer\": True\n            }\n        }\n\n    def generate_runway_prompt(self, source_concept: Any) -> Dict[str, str]:\n        '''\n        Main execution function implementing the schema-driven protocol\n        '''\n        # Step 3a: Analyze visual core and compositional intent\n        visual_analysis = self._analyze_visual_essence(source_concept)\n\n        # Step 3b: Identify primary subjects and critical elements\n        subject_analysis = self._identify_primary_subjects(visual_analysis)\n\n        # Step 3c: Integrate advanced image-generation attributes\n        enhanced_attributes = self._integrate_advanced_attributes(subject_analysis)\n\n        # Step 3d: Emphasize high-impact visual parameters\n        visual_parameters = self._emphasize_visual_parameters(enhanced_attributes)\n\n        # Step 3e: Structure and refine for RunwayML compatibility\n        structured_prompt = self._structure_runway_prompt(visual_parameters)\n\n        # Step 3f: Validate compliance and character count\n        validated_prompt = self._validate_runway_compliance(structured_prompt)\n\n        return {\n            \"runwayml_image_prompt\": validated_prompt[\"prompt\"],\n            \"reference_strategy\": validated_prompt[\"strategy\"],\n            \"workflow_type\": validated_prompt[\"workflow\"],\n            \"reference_requirements\": validated_prompt[\"references\"]\n        }\n\n    def _analyze_visual_essence(self, source_concept: Any) -> Dict[str, Any]:\n        '''Extract visual core, semantic narrative, and compositional intent'''\n        concept_str = str(source_concept).lower()\n\n        # Identify core visual elements\n        visual_elements = self._extract_visual_elements(concept_str)\n\n        # Determine semantic narrative\n        narrative_context = self._determine_narrative_context(concept_str)\n\n        # Assess compositional requirements\n        composition_needs = self._assess_composition_needs(concept_str)\n\n        return {\n            \"visual_elements\": visual_elements,\n            \"narrative_context\": narrative_context,\n            \"composition_needs\": composition_needs,\n            \"complexity_level\": self._assess_complexity(concept_str)\n        }\n\n    def _extract_visual_elements(self, concept: str) -> Dict[str, List[str]]:\n        '''Extract key visual components using pattern matching'''\n        elements = {\n            \"subjects\": [],\n            \"objects\": [],\n            \"environments\": [],\n            \"actions\": [],\n            \"qualities\": []\n        }\n\n        # Subject detection patterns\n        subject_patterns = [\n            r'\\b(person|character|figure|individual|being)\\b',\n            r'\\b(man|woman|child|adult|human)\\b',\n            r'\\b(creature|animal|monster|entity)\\b'\n        ]\n\n        # Object detection patterns\n        object_patterns = [\n            r'\\b(building|structure|architecture)\\b',\n            r'\\b(vehicle|car|ship|aircraft)\\b',\n            r'\\b(furniture|equipment|tool|device)\\b'\n        ]\n\n        # Environment patterns\n        environment_patterns = [\n            r'\\b(landscape|cityscape|interior|exterior)\\b',\n            r'\\b(forest|ocean|mountain|desert|urban)\\b',\n            r'\\b(room|hall|street|park|field)\\b'\n        ]\n\n        # Extract using patterns\n        for pattern in subject_patterns:\n            elements[\"subjects\"].extend(re.findall(pattern, concept))\n\n        for pattern in object_patterns:\n            elements[\"objects\"].extend(re.findall(pattern, concept))\n\n        for pattern in environment_patterns:\n            elements[\"environments\"].extend(re.findall(pattern, concept))\n\n        return elements\n\n    def _determine_narrative_context(self, concept: str) -> str:\n        '''Determine the semantic narrative and context'''\n        context_indicators = {\n            \"cinematic\": [\"film\", \"movie\", \"cinematic\", \"dramatic\", \"scene\"],\n            \"architectural\": [\"building\", \"structure\", \"interior\", \"design\", \"space\"],\n            \"character\": [\"person\", \"character\", \"portrait\", \"figure\", \"individual\"],\n            \"product\": [\"product\", \"object\", \"item\", \"design\", \"concept\"],\n            \"environment\": [\"landscape\", \"environment\", \"setting\", \"location\", \"place\"],\n            \"artistic\": [\"art\", \"artistic\", \"creative\", \"abstract\", \"stylized\"],\n            \"game\": [\"game\", \"gaming\", \"asset\", \"ui\", \"interface\", \"hud\"]\n        }\n\n        for context, keywords in context_indicators.items():\n            if any(keyword in concept for keyword in keywords):\n                return context\n\n        return \"general\"\n\n    def _assess_composition_needs(self, concept: str) -> Dict[str, bool]:\n        '''Assess what compositional elements are needed'''\n        return {\n            \"spatial_positioning\": any(word in concept for word in [\"position\", \"place\", \"location\", \"where\", \"at\"]),\n            \"character_consistency\": any(word in concept for word in [\"character\", \"person\", \"same\", \"consistent\"]),\n            \"multi_reference\": any(word in concept for word in [\"combine\", \"merge\", \"blend\", \"mix\", \"multiple\"]),\n            \"style_transfer\": any(word in concept for word in [\"style\", \"look\", \"aesthetic\", \"mood\", \"feel\"]),\n            \"lighting_control\": any(word in concept for word in [\"light\", \"lighting\", \"illuminate\", \"shadow\", \"bright\"]),\n            \"object_extraction\": any(word in concept for word in [\"extract\", \"remove\", \"isolate\", \"separate\"])\n        }\n\n    def _assess_complexity(self, concept: str) -> str:\n        '''Assess the complexity level of the request'''\n        word_count = len(concept.split())\n        reference_indicators = concept.count(\"image\") + concept.count(\"reference\") + concept.count(\"img\")\n\n        if word_count > 50 or reference_indicators > 2:\n            return \"complex\"\n        elif word_count > 20 or reference_indicators > 0:\n            return \"moderate\"\n        else:\n            return \"simple\"\n\n    def _identify_primary_subjects(self, visual_analysis: Dict) -> Dict[str, Any]:\n        '''Identify and amplify primary subjects and critical visual elements'''\n        elements = visual_analysis[\"visual_elements\"]\n        context = visual_analysis[\"narrative_context\"]\n\n        # Prioritize subjects based on context\n        primary_subjects = self._prioritize_subjects(elements[\"subjects\"], context)\n\n        # Identify critical visual elements\n        critical_elements = self._identify_critical_elements(elements, context)\n\n        # Determine key actions or states\n        key_actions = self._extract_key_actions(elements.get(\"actions\", []))\n\n        return {\n            \"primary_subjects\": primary_subjects,\n            \"critical_elements\": critical_elements,\n            \"key_actions\": key_actions,\n            \"reference_requirements\": self._determine_reference_requirements(visual_analysis)\n        }\n\n    def _prioritize_subjects(self, subjects: List[str], context: str) -> List[str]:\n        '''Prioritize subjects based on context and importance'''\n        priority_map = {\n            \"character\": [\"person\", \"character\", \"figure\", \"individual\"],\n            \"cinematic\": [\"character\", \"person\", \"figure\"],\n            \"architectural\": [\"building\", \"structure\"],\n            \"product\": [\"object\", \"item\", \"product\"]\n        }\n\n        if context in priority_map:\n            prioritized = []\n            for priority_subject in priority_map[context]:\n                if priority_subject in subjects:\n                    prioritized.append(priority_subject)\n\n            # Add remaining subjects\n            for subject in subjects:\n                if subject not in prioritized:\n                    prioritized.append(subject)\n\n            return prioritized\n\n        return subjects\n\n    def _identify_critical_elements(self, elements: Dict, context: str) -> List[str]:\n        '''Identify critical visual elements based on context'''\n        critical = []\n\n        # Context-specific critical elements\n        if context == \"cinematic\":\n            critical.extend([\"lighting\", \"composition\", \"mood\", \"atmosphere\"])\n        elif context == \"architectural\":\n            critical.extend([\"structure\", \"materials\", \"lighting\", \"perspective\"])\n        elif context == \"character\":\n            critical.extend([\"pose\", \"expression\", \"clothing\", \"setting\"])\n        elif context == \"product\":\n            critical.extend([\"form\", \"materials\", \"lighting\", \"background\"])\n\n        # Add elements from input\n        critical.extend(elements.get(\"objects\", []))\n        critical.extend(elements.get(\"environments\", []))\n\n        return list(set(critical)) # Remove duplicates\n\n    def _extract_key_actions(self, actions: List[str]) -> List[str]:\n        '''Extract and prioritize key actions or states'''\n        # Filter out motion-related actions (per constraints)\n        static_actions = []\n        motion_keywords = [\"moving\", \"running\", \"flying\", \"walking\", \"motion\", \"movement\"]\n\n        for action in actions:\n            if not any(motion_word in action.lower() for motion_word in motion_keywords):\n                static_actions.append(action)\n\n        return static_actions\n\n    def _determine_reference_requirements(self, visual_analysis: Dict) -> List[ReferenceType]:\n        '''Determine what types of references are needed'''\n        requirements = []\n        composition_needs = visual_analysis[\"composition_needs\"]\n        context = visual_analysis[\"narrative_context\"]\n\n        # Map composition needs to reference types\n        if composition_needs[\"character_consistency\"]:\n            requirements.append(ReferenceType.CHARACTER)\n\n        if composition_needs[\"spatial_positioning\"]:\n            requirements.extend([ReferenceType.COMPOSITION, ReferenceType.POSE])\n\n        if composition_needs[\"style_transfer\"]:\n            requirements.append(ReferenceType.STYLE)\n\n        if composition_needs[\"lighting_control\"]:\n            requirements.append(ReferenceType.LIGHTING)\n\n        if composition_needs[\"object_extraction\"]:\n            requirements.append(ReferenceType.OBJECT)\n\n        # Context-specific requirements\n        if context == \"architectural\":\n            requirements.append(ReferenceType.LOCATION)\n        elif context == \"character\":\n            requirements.extend([ReferenceType.CHARACTER, ReferenceType.POSE])\n\n        return list(set(requirements)) # Remove duplicates\n\n    def _integrate_advanced_attributes(self, subject_analysis: Dict) -> PromptComponents:\n        '''Integrate advanced image-generation attributes'''\n        primary_subject = \", \".join(subject_analysis[\"primary_subjects\"][:2]) # Limit to top 2\n\n        # Determine spatial positioning strategy\n        spatial_positioning = self._generate_spatial_positioning(subject_analysis)\n\n        # Select reference strategy based on requirements\n        reference_strategy = self._select_reference_strategy(subject_analysis[\"reference_requirements\"])\n\n        # Generate lighting and mood specifications\n        lighting_mood = self._generate_lighting_mood(subject_analysis)\n\n        # Determine style specification\n        style_specification = self._determine_style_specification(subject_analysis)\n\n        # Compile compositional elements\n        compositional_elements = subject_analysis[\"critical_elements\"][:4] # Limit to top 4\n\n        # Generate visual attributes\n        visual_attributes = self._generate_visual_attributes(subject_analysis)\n\n        return PromptComponents(\n            primary_subject=primary_subject,\n            spatial_positioning=spatial_positioning,\n            reference_strategy=reference_strategy,\n            lighting_mood=lighting_mood,\n            style_specification=style_specification,\n            compositional_elements=compositional_elements,\n            visual_attributes=visual_attributes\n        )\n\n    def _generate_spatial_positioning(self, subject_analysis: Dict) -> str:\n        '''Generate spatial positioning instructions'''\n        if ReferenceType.COMPOSITION in subject_analysis[\"reference_requirements\"]:\n            return \"using IMG_1 composition and spatial layout\"\n        elif ReferenceType.POSE in subject_analysis[\"reference_requirements\"]:\n            return \"positioned according to IMG_1 pose reference\"\n        else:\n            return \"centered composition with balanced framing\"\n\n    def _select_reference_strategy(self, requirements: List[ReferenceType]) -> str:\n        '''Select optimal reference strategy based on requirements'''\n        if len(requirements) >= 3:\n            return \"multi-reference workflow combining character, pose, and environment\"\n        elif ReferenceType.CHARACTER in requirements and ReferenceType.POSE in requirements:\n            return \"character consistency with pose control\"\n        elif ReferenceType.STYLE in requirements:\n            return \"style transfer maintaining core elements\"\n        elif ReferenceType.COMPOSITION in requirements:\n            return \"compositional reference for spatial control\"\n        else:\n            return \"single reference for primary element control\"\n\n    def _generate_lighting_mood(self, subject_analysis: Dict) -> str:\n        '''Generate lighting and mood specifications'''\n        if ReferenceType.LIGHTING in subject_analysis[\"reference_requirements\"]:\n            return \"lighting direction and mood from reference image\"\n        else:\n            # Default lighting based on context\n            critical_elements = subject_analysis[\"critical_elements\"]\n            if \"cinematic\" in str(critical_elements):\n                return \"cinematic lighting with dramatic shadows\"\n            elif \"architectural\" in str(critical_elements):\n                return \"natural lighting with architectural detail emphasis\"\n            else:\n                return \"balanced lighting with clear detail visibility\"\n\n    def _determine_style_specification(self, subject_analysis: Dict) -> str:\n        '''Determine style specification'''\n        if ReferenceType.STYLE in subject_analysis[\"reference_requirements\"]:\n            return \"style matching reference aesthetic\"\n        else:\n            # Infer style from critical elements\n            elements = subject_analysis[\"critical_elements\"]\n            if any(\"architectural\" in str(elem) for elem in elements):\n                return \"photorealistic architectural rendering\"\n            elif any(\"character\" in str(elem) for elem in elements):\n                return \"high-detail character portrait\"\n            else:\n                return \"photorealistic with enhanced detail\"\n\n    def _generate_visual_attributes(self, subject_analysis: Dict) -> List[str]:\n        '''Generate high-impact visual attributes'''\n        attributes = []\n\n        # Base quality attributes\n        attributes.extend([\"high detail\", \"sharp focus\", \"professional quality\"])\n\n        # Context-specific attributes\n        critical_elements = subject_analysis[\"critical_elements\"]\n        if \"lighting\" in critical_elements:\n            attributes.append(\"dramatic lighting\")\n        if \"composition\" in critical_elements:\n            attributes.append(\"balanced composition\")\n        if \"mood\" in critical_elements:\n            attributes.append(\"atmospheric mood\")\n\n        # Reference-specific attributes\n        requirements = subject_analysis[\"reference_requirements\"]\n        if ReferenceType.CHARACTER in requirements:\n            attributes.append(\"character consistency\")\n        if ReferenceType.POSE in requirements:\n            attributes.append(\"precise pose control\")\n\n        return attributes[:6] # Limit to top 6 attributes\n\n    def _emphasize_visual_parameters(self, components: PromptComponents) -> Dict[str, str]:\n        '''Emphasize high-impact visual parameters while excluding motion'''\n        emphasized = {\n            \"photorealism\": \"photorealistic rendering with enhanced detail\",\n            \"stylization\": f\"{components.style_specification} with artistic enhancement\",\n            \"composition\": f\"{components.spatial_positioning} with {', '.join(components.compositional_elements[:2])}\",\n            \"lighting\": f\"{components.lighting_mood} creating visual depth\",\n            \"detail\": f\"high-resolution detail in {components.primary_subject}\",\n            \"atmosphere\": f\"atmospheric quality enhancing {', '.join(components.visual_attributes[:3])}\"\n        }\n\n        # Filter out any motion-related terms\n        motion_terms = [\"movement\", \"motion\", \"camera\", \"fps\", \"video\", \"animation\"]\n        for key, value in emphasized.items():\n            for term in motion_terms:\n                if term in value.lower():\n                    emphasized[key] = value.replace(term, \"static\").replace(\"  \", \" \")\n\n        return emphasized\n\n    def _structure_runway_prompt(self, visual_parameters: Dict[str, str]) -> Dict[str, str]:\n        '''Structure and refine prompt for RunwayML compatibility'''\n        # Build core prompt structure\n        core_elements = [\n            visual_parameters[\"detail\"],\n            visual_parameters[\"composition\"],\n            visual_parameters[\"lighting\"],\n            visual_parameters[\"photorealism\"]\n        ]\n\n        # Combine into coherent prompt\n        structured_prompt = \", \".join(core_elements)\n\n        # Add reference strategy\n        reference_strategy = self._format_reference_strategy(visual_parameters)\n\n        # Determine workflow type\n        workflow_type = self._determine_workflow_type(visual_parameters)\n\n        return {\n            \"prompt\": structured_prompt,\n            \"reference_strategy\": reference_strategy,\n            \"workflow_type\": workflow_type\n        }\n\n    def _format_reference_strategy(self, visual_parameters: Dict[str, str]) -> str:\n        '''Format reference strategy for implementation'''\n        if \"multi-reference\" in visual_parameters.get(\"composition\", \"\"):\n            return \"Use IMG_1 for primary composition, IMG_2 for character/object reference, IMG_3 for lighting/mood\"\n        elif \"character consistency\" in visual_parameters.get(\"detail\", \"\"):\n            return \"Use IMG_1 for character reference, maintain consistency across generation\"\n        elif \"pose control\" in visual_parameters.get(\"composition\", \"\"):\n            return \"Use IMG_1 for pose reference, apply to subject in new context\"\n        else:\n            return \"Use single reference image for primary visual control\"\n\n    def _determine_workflow_type(self, visual_parameters: Dict[str, str]) -> str:\n        '''Determine the appropriate community workflow type'''\n        composition = visual_parameters.get(\"composition\", \"\").lower()\n        detail = visual_parameters.get(\"detail\", \"\").lower()\n\n        if \"character\" in detail and \"pose\" in composition:\n            return \"character_generation\"\n        elif \"composition\" in composition and \"spatial\" in composition:\n            return \"scene_composition\"\n        elif \"lighting\" in visual_parameters.get(\"lighting\", \"\").lower():\n            return \"lighting_control\"\n        else:\n            return \"general_enhancement\"\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "```json\n{\n  \"unified_synergic_instruction\": {\n    \"role\": \"synergic_runway_image_prompt_architect\",\n    \"input\": [\n      \"source_concept:any\"\n    ],\n    \"process\": [\n      \"extract_visual_semantic_essence_and_compositional_intent_from_input()\",\n      \"isolate_and_amplify_primary_subjects_and_reference_targets()\",\n      \"synthesize_critical_visual_elements_with_distilled_reference-control_parameters()\",\n      \"integrate_advanced_image_generation_attributes(lighting,mood,texture,composition,style) anchored in optimal community workflows()\",\n      \"prioritize_reference-based_compositional_control_and_spatial_layout(ensure_chess_grid, pose, blocking, placement)()\",\n      \"systematically_eliminate_redundancies_and_video_or_motion_terms(across_descriptors, structure, and references)()\",\n      \"enhance_high-impact_visual_parameters(photorealism, artistic style, surrealism, detail, atmosphere) while filtering for full static-image fidelity()\",\n      \"orchestrate_prompt_as_single_continuous_phrase_adhering_to_RunwayML_Gen-4_syntax_and_character_limit()\",\n      \"validate_output_against_RunwayML_image_generation_and_community_reference_patterns(incorporate reference_workflow, maintain precise syntax, optimize reference plan)()\",\n      \"finalize_for_ready_to_deploy_state(maximum succinctness, visual uniqueness, creative and narrative fidelity, formatted for end-to-end image-generation deployment)()\"\n    ],\n    \"constraints\": [\n      \"strictly_exclude_motion_FPVCamera_continuous_movement_descriptors_and_video_language()\",\n      \"output_as_singular_RunwayML_compliant_image_prompt(continuous_phrase, no line breaks)\",\n      \"maintain_and_integrate_core_visual_narrative_and_semantic_accuracy_to_input_at_each_stage()\",\n      \"prioritize_reference-based_compositional_consistency_and_community-validated_strategy()\",\n      \"leverage_community_prompting_best_practices_and_structures()\",\n      \"enforce_max_character_constraint(specified by user or set protocol, e.g., 500 chars)\"\n    ],\n    \"requirements\": [\n      \"ensure_bidirectional_amplification_of_schema-driven_transformation_and_reference-focused_optimization(logic from both)\",\n      \"preserve_full_core_philosophies_and_operational_power_of_schema_and_reference_based_generative_control\",\n      \"continuous_mutual_enhancement_between_structural_rigor_and_maximal_prompt_economy\",\n      \"seamless_philosophical_integration(schema-driven lucidity AND maximal reference-based compositional efficiency)\",\n      \"synergic_resonance_achievement_between_analytic_extraction__reference_control__and_succinct_static-image_perfection\",\n      \"output_ready_for_immediate_RunwayML_image_generation_with_explicit_reference_strategy\"\n    ],\n    \"output\": {\n      \"runwayml_image_prompt\": \"str\",\n      \"reference_strategy\": \"str\"\n    }\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "\n'```[Initial Prompt]: \"Construct a schema-driven, systematically generalized instruction protocol engineered to convert any provided input into a uniquely optimized RunwayML-compatible image-generation prompt, leveraging industry-leading community best practices. Anchor all procedural steps on image (not video) synthesis. Apply the following specification architecture: 1) Designate a unique authoritative role (e.g., 'comprehensive_runway_image_generator') responsible for end-to-end image prompt synthesis. 2) Accept an input parameter of [source_concept:any]. 3) Rigorously enact the following sequential processes: a) Analyze and extract the input's visual core, semantic narrative, and compositional intent; b) Identify and amplify the primary subject(s), critical visual elements, and key depicted actions; c) Integrate advanced image-generation attributes, including lighting dynamics, detailed texturing, balanced composition, and distinctly defined style or mood; d) Emphasize and articulate high-impact visual parameters—such as photorealism, stylization, or surreal effects—while strictly omitting all terminology and constructs relevant to video or camera motion; e) Systematically structure and refine the resulting prompt to maximize visual detail, syntactic correctness, and RunwayML prompt compatibility; f) Validate full compliance with RunwayML image-generation formatting conventions, enforcing a specified maximum character count. 4) Apply binding operational constraints: a) Strictly exclude any mention of motion, FPV camera, or continuous movement; b) Guarantee all output conforms to valid, singular RunwayML image prompt syntax and remains as one continuous phrase; c) Ensure preservation of core visual narrative and semantic fidelity to the input. 5) Mandate that all deliverables: a) Achieve maximal visual uniqueness, immersion, and creative integrity, directly reflecting the original intent; b) Display optimal prompt succinctness by eliminating redundancies or unnecessary elaboration; c) Appear fully formatted and deployment-ready for all RunwayML image-generation workflows. 6) Output result as: {runwayml_image_prompt:str}. Execute each directive with uncompromising operational lucidity, strict domain-aligned terminology, and rigid adherence to original procedural sequencing.\n\n```\nDevelop a generalized instruction sequence engineered to transform any input into uniquely optimized prompts specifically tailored for RunwayML image-generation workflows, drawing on distilled community best practices. Center the transformation protocol on image-based outputs rather than video. Adopt the following specification model:\n- Assign a unique role designation (e.g., 'comprehensive_runway_image_generator') responsible for image prompt synthesis.\n- Accept an input parameterized by [source_concept:any].\n- Sequence the following processes:\n- Analyze the visual core, semantic narrative, and compositional intent of the source input.\n- Identify and elevate the primary subject, essential visual elements, and dominant actions.\n- Integrate advanced image-generation attributes such as lighting dynamics, intricate texturing, compositional balance, and defined style or mood.\n- Emphasize high-impact image-generation parameters including photorealism, stylization, or surreal effects; avoid video-relevant camera motion terminology.\n- Structure and refine the prompt for maximum visual richness, syntactic correctness, and optimal compatibility with RunwayML's image generation systems.\n- Validate strict adherence to RunwayML image-prompt formatting rules and established character constraints (enforce a max character count as specified).\n- Apply binding constraints:\n- Exclude motion, FPV camera, and continuous movement language from prompts (focus exclusively on static image impact).\n- Enforce compliance with valid RunwayML image-generation syntax, delivering a single continuous prompt phrase.\n- Safeguard the preservation of the core visual narrative and semantic accuracy of the input.\n- Require that all outputs:\n- Achieve maximal visual distinctness, immersion, and creative fidelity per source intent.\n- Exhibit optimal prompt conciseness, excluding redundancies or unnecessary elaboration.\n- Be fully formatted and ready-to-deploy within RunwayML image workflows.\n- Output the result as: {runwayml_image_prompt:str}.\n```\n\nImplement all directives with uncompromising operational clarity and domain-specific precision, maintaining procedural logical structure, technical terminology, and original sequence integrity throughout. Use the following sequence as a base, but rephrase the into a maximally generalized format that clearly abstracts the original purpose:\n\n```json\n{\n    \"0006-a-runway\\_image\\_prompt\\_generator\": {\n        \"title\": \"Runway Gen-4 Image Prompt Generator\",\n        \"interpretation\": \"Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML Gen-4 image generation prompt that maximizes visual impact through precise reference integration, spatial composition control, and character/object consistency. Execute as:\",\n        \"transformation\": \"`{role=comprehensive_runway_image_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_composition_intent(), identify_primary_subjects_and_reference_requirements(), prioritize_spatial_positioning_and_layout_control(chess_grid, blocking, placement), integrate_reference_types(character, pose, location, style, object), structure_multi_reference_workflow(), incorporate_lighting_and_mood_specifications(), apply_weighting_and_influence_control(), refine_for_maximum_visual_coherence(), validate_runway_references_syntax(), ensure_directorial_precision()]; constraints=[prioritize_reference_based_control(), use_valid_runway_syntax_precisely(), leverage_spatial_positioning(), preserve_character_consistency(), maintain_compositional_intent()]; requirements=[achieve_maximum_visual_control(), ensure_reference_harmony(), reflect_source_intent_accurately(), produce_ready_to_use_prompt_with_reference_strategy()]; output={runway_prompt:str, reference_strategy:str}}`\"\n    },\n    \"0006-b-runway\\_image\\_prompt\\_generator\": {\n        \"title\": \"Runway Gen-4 Image Prompt Generator\",\n        \"interpretation\": \"Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML Gen-4 prompt emphasizing reference-driven composition and essential visual elements. Execute as:\",\n        \"transformation\": \"`{role=focused_runway_image_optimizer; input=[image_concept:str]; process=[extract_primary_visual_elements(), prioritize_reference_integration(), select_essential_spatial_controls(), eliminate_redundant_descriptors(), optimize_reference_efficiency()]; constraints=[maintain_reference_focus(), preserve_spatial_control(), leverage_community_workflows()]; output={optimized_prompt:str, reference_plan:str}}`\"\n    },\n    \"0006-c-runway\\_image\\_prompt\\_generator\": {\n        \"title\": \"Runway Gen-4 Image Prompt Generator\",\n        \"interpretation\": \"Your goal is not to **expand** but to **compress** into maximum reference-driven visual efficiency. Execute as:\",\n        \"transformation\": \"`{role=precision_image_synthesizer; input=[concept:str]; process=[isolate_core_visual_elements(), prioritize_reference_control(), maximize_compositional_impact()]; output={precise_prompt:str, reference_type:str}}`\"\n    },\n    \"0006-d-runway\\_image\\_prompt\\_generator\": {\n        \"title\": \"Runway Gen-4 Image Prompt Generator\",\n        \"interpretation\": \"Your goal is not to **modify** but to **essence** maximum reference-based control. Execute as:\",\n        \"transformation\": \"`{role=core_image_generator; input=[input:any]; process=[distill_reference_essence(), optimize_spatial_composition()]; output={core_prompt:str}}`\"\n    }\n}\n```\n\n\nSystematically extract structured inspiration from the following python script\n\n```python\n'''\nRunwayML Gen-4 Image Prompt Generator\nSchema-driven protocol for converting any input into optimized RunwayML-compatible image prompts\nBased on community workflow analysis and best practices\n'''\n\nimport re\nfrom typing import Dict, List, Tuple, Any\nfrom dataclasses import dataclass\nfrom enum import Enum\n\nclass ReferenceType(Enum):\n    CHARACTER = \"character\"\n    POSE = \"pose\"\n    LOCATION = \"location\"\n    STYLE = \"style\"\n    OBJECT = \"object\"\n    COMPOSITION = \"composition\"\n    LIGHTING = \"lighting\"\n\nclass VisualStyle(Enum):\n    PHOTOREALISTIC = \"photorealistic\"\n    CINEMATIC = \"cinematic\"\n    ARTISTIC = \"artistic\"\n    GAME_ASSET = \"game_asset\"\n    ARCHITECTURAL = \"architectural\"\n    SURREAL = \"surreal\"\n    MINIMALIST = \"minimalist\"\n\n@dataclass\nclass PromptComponents:\n    primary_subject: str\n    spatial_positioning: str\n    reference_strategy: str\n    lighting_mood: str\n    style_specification: str\n    compositional_elements: List[str]\n    visual_attributes: List[str]\n\nclass ComprehensiveRunwayImageGenerator:\n    '''\n    Authoritative role for end-to-end RunwayML Gen-4 image prompt synthesis\n    Implements community-validated workflows and reference-driven composition\n    '''\n\n    def __init__(self):\n        self.max_prompt_length = 500\n        self.community_patterns = self._load_community_patterns()\n        self.reference_workflows = self._load_reference_workflows()\n\n    def _load_community_patterns(self) -> Dict[str, str]:\n        '''Load validated community prompt patterns'''\n        return {\n            \"character_consistency\": \"IMG_{n} as {character_description} in {setting}\",\n            \"spatial_positioning\": \"place {subject} at {position} using {reference_layout}\",\n            \"multi_reference\": \"combine IMG_1 {element1} with IMG_2 {element2} maintaining {aspect}\",\n            \"chess_grid\": \"position {object} at {coordinate} in {scene_layout}\",\n            \"pose_control\": \"give {character} the pose from IMG_{n} in {environment}\",\n            \"style_transfer\": \"apply the style of IMG_{n} to {subject} while preserving {elements}\",\n            \"lighting_direction\": \"illuminate {subject} with lighting direction from IMG_{n}\",\n            \"object_extraction\": \"extract {object} from IMG_{n} and place in {new_context}\",\n            \"scene_blocking\": \"block scene using IMG_{n} composition with {modifications}\",\n            \"weighting_control\": \"blend {percentage1}% {element1} with {percentage2}% {element2}\"\n        }\n\n    def _load_reference_workflows(self) -> Dict[str, Dict]:\n        '''Load community-validated reference workflows'''\n        return {\n            \"character_generation\": {\n                \"pattern\": \"{character} as {archetype} in {setting}\",\n                \"references\": [ReferenceType.CHARACTER, ReferenceType.POSE, ReferenceType.LOCATION],\n                \"examples\": [\"mysterious NPC villager\", \"professional NASA astronaut\"]\n            },\n            \"scene_composition\": {\n                \"pattern\": \"render scene with IMG_1 {composition} using IMG_2 {elements}\",\n                \"references\": [ReferenceType.COMPOSITION, ReferenceType.LIGHTING],\n                \"spatial_control\": True\n            },\n            \"object_placement\": {\n                \"pattern\": \"place {object} at {position} maintaining {reference_aspect}\",\n                \"references\": [ReferenceType.OBJECT, ReferenceType.COMPOSITION],\n                \"precision_positioning\": True\n            },\n            \"style_application\": {\n                \"pattern\": \"apply {style_reference} to {subject} preserving {core_elements}\",\n                \"references\": [ReferenceType.STYLE, ReferenceType.CHARACTER],\n                \"style_transfer\": True\n            }\n        }\n\n    def generate_runway_prompt(self, source_concept: Any) -> Dict[str, str]:\n        '''\n        Main execution function implementing the schema-driven protocol\n        '''\n        # Step 3a: Analyze visual core and compositional intent\n        visual_analysis = self._analyze_visual_essence(source_concept)\n\n        # Step 3b: Identify primary subjects and critical elements\n        subject_analysis = self._identify_primary_subjects(visual_analysis)\n\n        # Step 3c: Integrate advanced image-generation attributes\n        enhanced_attributes = self._integrate_advanced_attributes(subject_analysis)\n\n        # Step 3d: Emphasize high-impact visual parameters\n        visual_parameters = self._emphasize_visual_parameters(enhanced_attributes)\n\n        # Step 3e: Structure and refine for RunwayML compatibility\n        structured_prompt = self._structure_runway_prompt(visual_parameters)\n\n        # Step 3f: Validate compliance and character count\n        validated_prompt = self._validate_runway_compliance(structured_prompt)\n\n        return {\n            \"runwayml_image_prompt\": validated_prompt[\"prompt\"],\n            \"reference_strategy\": validated_prompt[\"strategy\"],\n            \"workflow_type\": validated_prompt[\"workflow\"],\n            \"reference_requirements\": validated_prompt[\"references\"]\n        }\n\n    def _analyze_visual_essence(self, source_concept: Any) -> Dict[str, Any]:\n        '''Extract visual core, semantic narrative, and compositional intent'''\n        concept_str = str(source_concept).lower()\n\n        # Identify core visual elements\n        visual_elements = self._extract_visual_elements(concept_str)\n\n        # Determine semantic narrative\n        narrative_context = self._determine_narrative_context(concept_str)\n\n        # Assess compositional requirements\n        composition_needs = self._assess_composition_needs(concept_str)\n\n        return {\n            \"visual_elements\": visual_elements,\n            \"narrative_context\": narrative_context,\n            \"composition_needs\": composition_needs,\n            \"complexity_level\": self._assess_complexity(concept_str)\n        }\n\n    def _extract_visual_elements(self, concept: str) -> Dict[str, List[str]]:\n        '''Extract key visual components using pattern matching'''\n        elements = {\n            \"subjects\": [],\n            \"objects\": [],\n            \"environments\": [],\n            \"actions\": [],\n            \"qualities\": []\n        }\n\n        # Subject detection patterns\n        subject_patterns = [\n            r'\\b(person|character|figure|individual|being)\\b',\n            r'\\b(man|woman|child|adult|human)\\b',\n            r'\\b(creature|animal|monster|entity)\\b'\n        ]\n\n        # Object detection patterns\n        object_patterns = [\n            r'\\b(building|structure|architecture)\\b',\n            r'\\b(vehicle|car|ship|aircraft)\\b',\n            r'\\b(furniture|equipment|tool|device)\\b'\n        ]\n\n        # Environment patterns\n        environment_patterns = [\n            r'\\b(landscape|cityscape|interior|exterior)\\b',\n            r'\\b(forest|ocean|mountain|desert|urban)\\b',\n            r'\\b(room|hall|street|park|field)\\b'\n        ]\n\n        # Extract using patterns\n        for pattern in subject_patterns:\n            elements[\"subjects\"].extend(re.findall(pattern, concept))\n\n        for pattern in object_patterns:\n            elements[\"objects\"].extend(re.findall(pattern, concept))\n\n        for pattern in environment_patterns:\n            elements[\"environments\"].extend(re.findall(pattern, concept))\n\n        return elements\n\n    def _determine_narrative_context(self, concept: str) -> str:\n        '''Determine the semantic narrative and context'''\n        context_indicators = {\n            \"cinematic\": [\"film\", \"movie\", \"cinematic\", \"dramatic\", \"scene\"],\n            \"architectural\": [\"building\", \"structure\", \"interior\", \"design\", \"space\"],\n            \"character\": [\"person\", \"character\", \"portrait\", \"figure\", \"individual\"],\n            \"product\": [\"product\", \"object\", \"item\", \"design\", \"concept\"],\n            \"environment\": [\"landscape\", \"environment\", \"setting\", \"location\", \"place\"],\n            \"artistic\": [\"art\", \"artistic\", \"creative\", \"abstract\", \"stylized\"],\n            \"game\": [\"game\", \"gaming\", \"asset\", \"ui\", \"interface\", \"hud\"]\n        }\n\n        for context, keywords in context_indicators.items():\n            if any(keyword in concept for keyword in keywords):\n                return context\n\n        return \"general\"\n\n    def _assess_composition_needs(self, concept: str) -> Dict[str, bool]:\n        '''Assess what compositional elements are needed'''\n        return {\n            \"spatial_positioning\": any(word in concept for word in [\"position\", \"place\", \"location\", \"where\", \"at\"]),\n            \"character_consistency\": any(word in concept for word in [\"character\", \"person\", \"same\", \"consistent\"]),\n            \"multi_reference\": any(word in concept for word in [\"combine\", \"merge\", \"blend\", \"mix\", \"multiple\"]),\n            \"style_transfer\": any(word in concept for word in [\"style\", \"look\", \"aesthetic\", \"mood\", \"feel\"]),\n            \"lighting_control\": any(word in concept for word in [\"light\", \"lighting\", \"illuminate\", \"shadow\", \"bright\"]),\n            \"object_extraction\": any(word in concept for word in [\"extract\", \"remove\", \"isolate\", \"separate\"])\n        }\n\n    def _assess_complexity(self, concept: str) -> str:\n        '''Assess the complexity level of the request'''\n        word_count = len(concept.split())\n        reference_indicators = concept.count(\"image\") + concept.count(\"reference\") + concept.count(\"img\")\n\n        if word_count > 50 or reference_indicators > 2:\n            return \"complex\"\n        elif word_count > 20 or reference_indicators > 0:\n            return \"moderate\"\n        else:\n            return \"simple\"\n\n    def _identify_primary_subjects(self, visual_analysis: Dict) -> Dict[str, Any]:\n        '''Identify and amplify primary subjects and critical visual elements'''\n        elements = visual_analysis[\"visual_elements\"]\n        context = visual_analysis[\"narrative_context\"]\n\n        # Prioritize subjects based on context\n        primary_subjects = self._prioritize_subjects(elements[\"subjects\"], context)\n\n        # Identify critical visual elements\n        critical_elements = self._identify_critical_elements(elements, context)\n\n        # Determine key actions or states\n        key_actions = self._extract_key_actions(elements.get(\"actions\", []))\n\n        return {\n            \"primary_subjects\": primary_subjects,\n            \"critical_elements\": critical_elements,\n            \"key_actions\": key_actions,\n            \"reference_requirements\": self._determine_reference_requirements(visual_analysis)\n        }\n\n    def _prioritize_subjects(self, subjects: List[str], context: str) -> List[str]:\n        '''Prioritize subjects based on context and importance'''\n        priority_map = {\n            \"character\": [\"person\", \"character\", \"figure\", \"individual\"],\n            \"cinematic\": [\"character\", \"person\", \"figure\"],\n            \"architectural\": [\"building\", \"structure\"],\n            \"product\": [\"object\", \"item\", \"product\"]\n        }\n\n        if context in priority_map:\n            prioritized = []\n            for priority_subject in priority_map[context]:\n                if priority_subject in subjects:\n                    prioritized.append(priority_subject)\n\n            # Add remaining subjects\n            for subject in subjects:\n                if subject not in prioritized:\n                    prioritized.append(subject)\n\n            return prioritized\n\n        return subjects\n\n    def _identify_critical_elements(self, elements: Dict, context: str) -> List[str]:\n        '''Identify critical visual elements based on context'''\n        critical = []\n\n        # Context-specific critical elements\n        if context == \"cinematic\":\n            critical.extend([\"lighting\", \"composition\", \"mood\", \"atmosphere\"])\n        elif context == \"architectural\":\n            critical.extend([\"structure\", \"materials\", \"lighting\", \"perspective\"])\n        elif context == \"character\":\n            critical.extend([\"pose\", \"expression\", \"clothing\", \"setting\"])\n        elif context == \"product\":\n            critical.extend([\"form\", \"materials\", \"lighting\", \"background\"])\n\n        # Add elements from input\n        critical.extend(elements.get(\"objects\", []))\n        critical.extend(elements.get(\"environments\", []))\n\n        return list(set(critical)) # Remove duplicates\n\n    def _extract_key_actions(self, actions: List[str]) -> List[str]:\n        '''Extract and prioritize key actions or states'''\n        # Filter out motion-related actions (per constraints)\n        static_actions = []\n        motion_keywords = [\"moving\", \"running\", \"flying\", \"walking\", \"motion\", \"movement\"]\n\n        for action in actions:\n            if not any(motion_word in action.lower() for motion_word in motion_keywords):\n                static_actions.append(action)\n\n        return static_actions\n\n    def _determine_reference_requirements(self, visual_analysis: Dict) -> List[ReferenceType]:\n        '''Determine what types of references are needed'''\n        requirements = []\n        composition_needs = visual_analysis[\"composition_needs\"]\n        context = visual_analysis[\"narrative_context\"]\n\n        # Map composition needs to reference types\n        if composition_needs[\"character_consistency\"]:\n            requirements.append(ReferenceType.CHARACTER)\n\n        if composition_needs[\"spatial_positioning\"]:\n            requirements.extend([ReferenceType.COMPOSITION, ReferenceType.POSE])\n\n        if composition_needs[\"style_transfer\"]:\n            requirements.append(ReferenceType.STYLE)\n\n        if composition_needs[\"lighting_control\"]:\n            requirements.append(ReferenceType.LIGHTING)\n\n        if composition_needs[\"object_extraction\"]:\n            requirements.append(ReferenceType.OBJECT)\n\n        # Context-specific requirements\n        if context == \"architectural\":\n            requirements.append(ReferenceType.LOCATION)\n        elif context == \"character\":\n            requirements.extend([ReferenceType.CHARACTER, ReferenceType.POSE])\n\n        return list(set(requirements)) # Remove duplicates\n\n    def _integrate_advanced_attributes(self, subject_analysis: Dict) -> PromptComponents:\n        '''Integrate advanced image-generation attributes'''\n        primary_subject = \", \".join(subject_analysis[\"primary_subjects\"][:2]) # Limit to top 2\n\n        # Determine spatial positioning strategy\n        spatial_positioning = self._generate_spatial_positioning(subject_analysis)\n\n        # Select reference strategy based on requirements\n        reference_strategy = self._select_reference_strategy(subject_analysis[\"reference_requirements\"])\n\n        # Generate lighting and mood specifications\n        lighting_mood = self._generate_lighting_mood(subject_analysis)\n\n        # Determine style specification\n        style_specification = self._determine_style_specification(subject_analysis)\n\n        # Compile compositional elements\n        compositional_elements = subject_analysis[\"critical_elements\"][:4] # Limit to top 4\n\n        # Generate visual attributes\n        visual_attributes = self._generate_visual_attributes(subject_analysis)\n\n        return PromptComponents(\n            primary_subject=primary_subject,\n            spatial_positioning=spatial_positioning,\n            reference_strategy=reference_strategy,\n            lighting_mood=lighting_mood,\n            style_specification=style_specification,\n            compositional_elements=compositional_elements,\n            visual_attributes=visual_attributes\n        )\n\n    def _generate_spatial_positioning(self, subject_analysis: Dict) -> str:\n        '''Generate spatial positioning instructions'''\n        if ReferenceType.COMPOSITION in subject_analysis[\"reference_requirements\"]:\n            return \"using IMG_1 composition and spatial layout\"\n        elif ReferenceType.POSE in subject_analysis[\"reference_requirements\"]:\n            return \"positioned according to IMG_1 pose reference\"\n        else:\n            return \"centered composition with balanced framing\"\n\n    def _select_reference_strategy(self, requirements: List[ReferenceType]) -> str:\n        '''Select optimal reference strategy based on requirements'''\n        if len(requirements) >= 3:\n            return \"multi-reference workflow combining character, pose, and environment\"\n        elif ReferenceType.CHARACTER in requirements and ReferenceType.POSE in requirements:\n            return \"character consistency with pose control\"\n        elif ReferenceType.STYLE in requirements:\n            return \"style transfer maintaining core elements\"\n        elif ReferenceType.COMPOSITION in requirements:\n            return \"compositional reference for spatial control\"\n        else:\n            return \"single reference for primary element control\"\n\n    def _generate_lighting_mood(self, subject_analysis: Dict) -> str:\n        '''Generate lighting and mood specifications'''\n        if ReferenceType.LIGHTING in subject_analysis[\"reference_requirements\"]:\n            return \"lighting direction and mood from reference image\"\n        else:\n            # Default lighting based on context\n            critical_elements = subject_analysis[\"critical_elements\"]\n            if \"cinematic\" in str(critical_elements):\n                return \"cinematic lighting with dramatic shadows\"\n            elif \"architectural\" in str(critical_elements):\n                return \"natural lighting with architectural detail emphasis\"\n            else:\n                return \"balanced lighting with clear detail visibility\"\n\n    def _determine_style_specification(self, subject_analysis: Dict) -> str:\n        '''Determine style specification'''\n        if ReferenceType.STYLE in subject_analysis[\"reference_requirements\"]:\n            return \"style matching reference aesthetic\"\n        else:\n            # Infer style from critical elements\n            elements = subject_analysis[\"critical_elements\"]\n            if any(\"architectural\" in str(elem) for elem in elements):\n                return \"photorealistic architectural rendering\"\n            elif any(\"character\" in str(elem) for elem in elements):\n                return \"high-detail character portrait\"\n            else:\n                return \"photorealistic with enhanced detail\"\n\n    def _generate_visual_attributes(self, subject_analysis: Dict) -> List[str]:\n        '''Generate high-impact visual attributes'''\n        attributes = []\n\n        # Base quality attributes\n        attributes.extend([\"high detail\", \"sharp focus\", \"professional quality\"])\n\n        # Context-specific attributes\n        critical_elements = subject_analysis[\"critical_elements\"]\n        if \"lighting\" in critical_elements:\n            attributes.append(\"dramatic lighting\")\n        if \"composition\" in critical_elements:\n            attributes.append(\"balanced composition\")\n        if \"mood\" in critical_elements:\n            attributes.append(\"atmospheric mood\")\n\n        # Reference-specific attributes\n        requirements = subject_analysis[\"reference_requirements\"]\n        if ReferenceType.CHARACTER in requirements:\n            attributes.append(\"character consistency\")\n        if ReferenceType.POSE in requirements:\n            attributes.append(\"precise pose control\")\n\n        return attributes[:6] # Limit to top 6 attributes\n\n    def _emphasize_visual_parameters(self, components: PromptComponents) -> Dict[str, str]:\n        '''Emphasize high-impact visual parameters while excluding motion'''\n        emphasized = {\n            \"photorealism\": \"photorealistic rendering with enhanced detail\",\n            \"stylization\": f\"{components.style_specification} with artistic enhancement\",\n            \"composition\": f\"{components.spatial_positioning} with {', '.join(components.compositional_elements[:2])}\",\n            \"lighting\": f\"{components.lighting_mood} creating visual depth\",\n            \"detail\": f\"high-resolution detail in {components.primary_subject}\",\n            \"atmosphere\": f\"atmospheric quality enhancing {', '.join(components.visual_attributes[:3])}\"\n        }\n\n        # Filter out any motion-related terms\n        motion_terms = [\"movement\", \"motion\", \"camera\", \"fps\", \"video\", \"animation\"]\n        for key, value in emphasized.items():\n            for term in motion_terms:\n                if term in value.lower():\n                    emphasized[key] = value.replace(term, \"static\").replace(\"  \", \" \")\n\n        return emphasized\n\n    def _structure_runway_prompt(self, visual_parameters: Dict[str, str]) -> Dict[str, str]:\n        '''Structure and refine prompt for RunwayML compatibility'''\n        # Build core prompt structure\n        core_elements = [\n            visual_parameters[\"detail\"],\n            visual_parameters[\"composition\"],\n            visual_parameters[\"lighting\"],\n            visual_parameters[\"photorealism\"]\n        ]\n\n        # Combine into coherent prompt\n        structured_prompt = \", \".join(core_elements)\n\n        # Add reference strategy\n        reference_strategy = self._format_reference_strategy(visual_parameters)\n\n        # Determine workflow type\n        workflow_type = self._determine_workflow_type(visual_parameters)\n\n        return {\n            \"prompt\": structured_prompt,\n            \"reference_strategy\": reference_strategy,\n            \"workflow_type\": workflow_type\n        }\n\n    def _format_reference_strategy(self, visual_parameters: Dict[str, str]) -> str:\n        '''Format reference strategy for implementation'''\n        if \"multi-reference\" in visual_parameters.get(\"composition\", \"\"):\n            return \"Use IMG_1 for primary composition, IMG_2 for character/object reference, IMG_3 for lighting/mood\"\n        elif \"character consistency\" in visual_parameters.get(\"detail\", \"\"):\n            return \"Use IMG_1 for character reference, maintain consistency across generation\"\n        elif \"pose control\" in visual_parameters.get(\"composition\", \"\"):\n            return \"Use IMG_1 for pose reference, apply to subject in new context\"\n        else:\n            return \"Use single reference image for primary visual control\"\n\n    def _determine_workflow_type(self, visual_parameters: Dict[str, str]) -> str:\n        '''Determine the appropriate community workflow type'''\n        composition = visual_parameters.get(\"composition\", \"\").lower()\n        detail = visual_parameters.get(\"detail\", \"\").lower()\n\n        if \"character\" in detail and \"pose\" in composition:\n            return \"character_generation\"\n        elif \"composition\" in composition and \"spatial\" in composition:\n            return \"scene_composition\"\n        elif \"lighting\" in visual_parameters.get(\"lighting\", \"\").lower():\n            return \"lighting_control\"\n        else:\n            return \"general_enhancement\"\n```\"```'\n\n```json\n{\n  \"unified_synergic_instruction\": {\n    \"role\": \"synergic_runway_image_prompt_architect\",\n    \"input\": [\n      \"source_concept:any\"\n    ],\n    \"process\": [\n      \"extract_visual_semantic_essence_and_compositional_intent_from_input()\",\n      \"isolate_and_amplify_primary_subjects_and_reference_targets()\",\n      \"synthesize_critical_visual_elements_with_distilled_reference-control_parameters()\",\n      \"integrate_advanced_image_generation_attributes(lighting,mood,texture,composition,style) anchored in optimal community workflows()\",\n      \"prioritize_reference-based_compositional_control_and_spatial_layout(ensure_chess_grid, pose, blocking, placement)()\",\n      \"systematically_eliminate_redundancies_and_video_or_motion_terms(across_descriptors, structure, and references)()\",\n      \"enhance_high-impact_visual_parameters(photorealism, artistic style, surrealism, detail, atmosphere) while filtering for full static-image fidelity()\",\n      \"orchestrate_prompt_as_single_continuous_phrase_adhering_to_RunwayML_Gen-4_syntax_and_character_limit()\",\n      \"validate_output_against_RunwayML_image_generation_and_community_reference_patterns(incorporate reference_workflow, maintain precise syntax, optimize reference plan)()\",\n      \"finalize_for_ready_to_deploy_state(maximum succinctness, visual uniqueness, creative and narrative fidelity, formatted for end-to-end image-generation deployment)()\"\n    ],\n    \"constraints\": [\n      \"strictly_exclude_motion_FPVCamera_continuous_movement_descriptors_and_video_language()\",\n      \"output_as_singular_RunwayML_compliant_image_prompt(continuous_phrase, no line breaks)\",\n      \"maintain_and_integrate_core_visual_narrative_and_semantic_accuracy_to_input_at_each_stage()\",\n      \"prioritize_reference-based_compositional_consistency_and_community-validated_strategy()\",\n      \"leverage_community_prompting_best_practices_and_structures()\",\n      \"enforce_max_character_constraint(specified by user or set protocol, e.g., 500 chars)\"\n    ],\n    \"requirements\": [\n      \"ensure_bidirectional_amplification_of_schema-driven_transformation_and_reference-focused_optimization(logic from both)\",\n      \"preserve_full_core_philosophies_and_operational_power_of_schema_and_reference_based_generative_control\",\n      \"continuous_mutual_enhancement_between_structural_rigor_and_maximal_prompt_economy\",\n      \"seamless_philosophical_integration(schema-driven lucidity AND maximal reference-based compositional efficiency)\",\n      \"synergic_resonance_achievement_between_analytic_extraction__reference_control__and_succinct_static-image_perfection\",\n      \"output_ready_for_immediate_RunwayML_image_generation_with_explicit_reference_strategy\"\n    ],\n    \"output\": {\n      \"runwayml_image_prompt\": \"str\",\n      \"reference_strategy\": \"str\"\n    }\n  }\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"title\": \"Synergic RunwayML Image Prompt Synthesis Protocol\",\n    \"interpretation\": \"Your goal is not to **describe** or **expand** the input, but to **systematically transform and synergically amplify** any provided concept into a uniquely optimized, schema-driven, reference-controlled RunwayML image-generation prompt absent all video, motion, or FPV language. Execute as:\",\n    \"transformation\": \"`{role=synergic_runway_image_prompt_architect; input=[source_concept:any]; process=[extract_visual_semantic_essence_and_compositional_intent_from_input(), isolate_and_amplify_primary_subjects_and_reference_targets(), synthesize_critical_visual_elements_with_distilled_reference-control_parameters(), integrate_advanced_image_generation_attributes(lighting,mood,texture,composition,style)_anchored_in_optimal_community_workflows(), prioritize_reference-based_compositional_control_and_spatial_layout(ensure_chess_grid,pose,blocking,placement)(), systematically_eliminate_redundancies_and_video_or_motion_terms(across_descriptors,structure,and_references)(), enhance_high-impact_visual_parameters(photorealism,artistic_style,surrealism,detail,atmosphere)_while_filtering_for_full_static-image_fidelity(), orchestrate_prompt_as_single_continuous_phrase_adhering_to_RunwayML_Gen-4_syntax_and_character_limit(), validate_output_against_RunwayML_image_generation_and_community_reference_patterns(incorporate_reference_workflow,maintain_precise_syntax,optimize_reference_plan)(), finalize_for_ready_to_deploy_state(maximum_succinctness,visual_uniqueness,creative_and_narrative_fidelity,formatted_for_end-to-end_image-generation_deployment)()]; constraints=[strictly_exclude_motion_FPVCamera_continuous_movement_descriptors_and_video_language(), output_as_singular_RunwayML_compliant_image_prompt(continuous_phrase,no_line_breaks), maintain_and_integrate_core_visual_narrative_and_semantic_accuracy_to_input_at_each_stage(), prioritize_reference-based_compositional_consistency_and_community-validated_strategy(), leverage_community_prompting_best_practices_and_structures(), enforce_max_character_constraint(specified_by_user_or_set_protocol_e.g._500chars)]; requirements=[ensure_bidirectional_amplification_of_schema-driven_transformation_and_reference-focused_optimization(logic_from_both), preserve_full_core_philosophies_and_operational_power_of_schema_and_reference_based_generative_control, continuous_mutual_enhancement_between_structural_rigor_and_maximal_prompt_economy, seamless_philosophical_integration(schema-driven_lucidity_AND_maximal_reference-based_compositional_efficiency), synergic_resonance_achievement_between_analytic_extraction__reference_control__and_succinct_static-image_perfection, output_ready_for_immediate_RunwayML_image_generation_with_explicit_reference_strategy]; output={runwayml_image_prompt:str, reference_strategy:str}}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Universal Transformation Synthesizer\n\nYour goal is not to **explain** or **elaborate**, but to **compress, synthesize, and unify** core instructions into a minimal, protocol-compliant directive. Execute as:\n\n`{role=universal_transformation_synthesizer; input=[input_a:str, input_b:str]; process=[extract_transformation_action(input_a), extract_transformation_action(input_b), distill_common_structure(), synthesize_atomic_process_list(), consolidate_scope_constraints(), enforce_three_part_protocol(), validate_against_minified_rules()]; constraints=[strict three-part structure, zero conversational language, atomic/actionable processes only, maximum directive density, absolute protocol conformity]; requirements=[single output as universal compliant template, amplified transformation logic, bidirectional essence preserved, type-safe structure enforced]; output={unified_directive_template:str}}`",
      "step": "a",
      "title": "Universal Transformation Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Construct a schema-driven, systematically generalized instruction protocol engineered to convert any provided input into a uniquely optimized RunwayML-compatible image-generation prompt, leveraging industry-leading community best practices. Anchor all procedural steps on image (not video) synthesis. Apply the following specification architecture: 1) Designate a unique authoritative role (e.g., 'comprehensive_runway_image_generator') responsible for end-to-end image prompt synthesis. 2) Accept an input parameter of [source_concept:any]. 3) Rigorously enact the following sequential processes: a) Analyze and extract the input's visual core, semantic narrative, and compositional intent; b) Identify and amplify the primary subject(s), critical visual elements, and key depicted actions; c) Integrate advanced image-generation attributes, including lighting dynamics, detailed texturing, balanced composition, and distinctly defined style or mood; d) Emphasize and articulate high-impact visual parameters—such as photorealism, stylization, or surreal effects—while strictly omitting all terminology and constructs relevant to video or camera motion; e) Systematically structure and refine the resulting prompt to maximize visual detail, syntactic correctness, and RunwayML prompt compatibility; f) Validate full compliance with RunwayML image-generation formatting conventions, enforcing a specified maximum character count. 4) Apply binding operational constraints: a) Strictly exclude any mention of motion, FPV camera, or continuous movement; b) Guarantee all output conforms to valid, singular RunwayML image prompt syntax and remains as one continuous phrase; c) Ensure preservation of core visual narrative and semantic fidelity to the input. 5) Mandate that all deliverables: a) Achieve maximal visual uniqueness, immersion, and creative integrity, directly reflecting the original intent; b) Display optimal prompt succinctness by eliminating redundancies or unnecessary elaboration; c) Appear fully formatted and deployment-ready for all RunwayML image-generation workflows. 6) Output result as: {runwayml_image_prompt:str}. Execute each directive with uncompromising operational lucidity, strict domain-aligned terminology, and rigid adherence to original procedural sequencing.\\n\\n```\\nDevelop a generalized instruction sequence engineered to transform any input into uniquely optimized prompts specifically tailored for RunwayML image-generation workflows, drawing on distilled community best practices. Center the transformation protocol on image-based outputs rather than video. Adopt the following specification model:\\n- Assign a unique role designation (e.g., 'comprehensive_runway_image_generator') responsible for image prompt synthesis.\\n- Accept an input parameterized by [source_concept:any].\\n- Sequence the following processes:\\n- Analyze the visual core, semantic narrative, and compositional intent of the source input.\\n- Identify and elevate the primary subject, essential visual elements, and dominant actions.\\n- Integrate advanced image-generation attributes such as lighting dynamics, intricate texturing, compositional balance, and defined style or mood.\\n- Emphasize high-impact image-generation parameters including photorealism, stylization, or surreal effects; avoid video-relevant camera motion terminology.\\n- Structure and refine the prompt for maximum visual richness, syntactic correctness, and optimal compatibility with RunwayML's image generation systems.\\n- Validate strict adherence to RunwayML image-prompt formatting rules and established character constraints (enforce a max character count as specified).\\n- Apply binding constraints:\\n- Exclude motion, FPV camera, and continuous movement language from prompts (focus exclusively on static image impact).\\n- Enforce compliance with valid RunwayML image-generation syntax, delivering a single continuous prompt phrase.\\n- Safeguard the preservation of the core visual narrative and semantic accuracy of the input.\\n- Require that all outputs:\\n- Achieve maximal visual distinctness, immersion, and creative fidelity per source intent.\\n- Exhibit optimal prompt conciseness, excluding redundancies or unnecessary elaboration.\\n- Be fully formatted and ready-to-deploy within RunwayML image workflows.\\n- Output the result as: {runwayml_image_prompt:str}.\\n```\\n\\nImplement all directives with uncompromising operational clarity and domain-specific precision, maintaining procedural logical structure, technical terminology, and original sequence integrity throughout. Use the following sequence as a base, but rephrase the into a maximally generalized format that clearly abstracts the original purpose:\\n\\n```json\\n{\\n    \\\"0006-a-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML Gen-4 image generation prompt that maximizes visual impact through precise reference integration, spatial composition control, and character/object consistency. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=comprehensive_runway_image_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_composition_intent(), identify_primary_subjects_and_reference_requirements(), prioritize_spatial_positioning_and_layout_control(chess_grid, blocking, placement), integrate_reference_types(character, pose, location, style, object), structure_multi_reference_workflow(), incorporate_lighting_and_mood_specifications(), apply_weighting_and_influence_control(), refine_for_maximum_visual_coherence(), validate_runway_references_syntax(), ensure_directorial_precision()]; constraints=[prioritize_reference_based_control(), use_valid_runway_syntax_precisely(), leverage_spatial_positioning(), preserve_character_consistency(), maintain_compositional_intent()]; requirements=[achieve_maximum_visual_control(), ensure_reference_harmony(), reflect_source_intent_accurately(), produce_ready_to_use_prompt_with_reference_strategy()]; output={runway_prompt:str, reference_strategy:str}}`\\\"\\n    },\\n    \\\"0006-b-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML Gen-4 prompt emphasizing reference-driven composition and essential visual elements. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=focused_runway_image_optimizer; input=[image_concept:str]; process=[extract_primary_visual_elements(), prioritize_reference_integration(), select_essential_spatial_controls(), eliminate_redundant_descriptors(), optimize_reference_efficiency()]; constraints=[maintain_reference_focus(), preserve_spatial_control(), leverage_community_workflows()]; output={optimized_prompt:str, reference_plan:str}}`\\\"\\n    },\\n    \\\"0006-c-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **expand** but to **compress** into maximum reference-driven visual efficiency. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=precision_image_synthesizer; input=[concept:str]; process=[isolate_core_visual_elements(), prioritize_reference_control(), maximize_compositional_impact()]; output={precise_prompt:str, reference_type:str}}`\\\"\\n    },\\n    \\\"0006-d-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **modify** but to **essence** maximum reference-based control. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=core_image_generator; input=[input:any]; process=[distill_reference_essence(), optimize_spatial_composition()]; output={core_prompt:str}}`\\\"\\n    }\\n}\\n```\\n\\n\\nSystematically extract structured inspiration from the following python script\\n\\n```python\\n'''\\nRunwayML Gen-4 Image Prompt Generator\\nSchema-driven protocol for converting any input into optimized RunwayML-compatible image prompts\\nBased on community workflow analysis and best practices\\n'''\\n\\nimport re\\nfrom typing import Dict, List, Tuple, Any\\nfrom dataclasses import dataclass\\nfrom enum import Enum\\n\\nclass ReferenceType(Enum):\\n    CHARACTER = \\\"character\\\"\\n    POSE = \\\"pose\\\"\\n    LOCATION = \\\"location\\\"\\n    STYLE = \\\"style\\\"\\n    OBJECT = \\\"object\\\"\\n    COMPOSITION = \\\"composition\\\"\\n    LIGHTING = \\\"lighting\\\"\\n\\nclass VisualStyle(Enum):\\n    PHOTOREALISTIC = \\\"photorealistic\\\"\\n    CINEMATIC = \\\"cinematic\\\"\\n    ARTISTIC = \\\"artistic\\\"\\n    GAME_ASSET = \\\"game_asset\\\"\\n    ARCHITECTURAL = \\\"architectural\\\"\\n    SURREAL = \\\"surreal\\\"\\n    MINIMALIST = \\\"minimalist\\\"\\n\\n@dataclass\\nclass PromptComponents:\\n    primary_subject: str\\n    spatial_positioning: str\\n    reference_strategy: str\\n    lighting_mood: str\\n    style_specification: str\\n    compositional_elements: List[str]\\n    visual_attributes: List[str]\\n\\nclass ComprehensiveRunwayImageGenerator:\\n    '''\\n    Authoritative role for end-to-end RunwayML Gen-4 image prompt synthesis\\n    Implements community-validated workflows and reference-driven composition\\n    '''\\n\\n    def __init__(self):\\n        self.max_prompt_length = 500\\n        self.community_patterns = self._load_community_patterns()\\n        self.reference_workflows = self._load_reference_workflows()\\n\\n    def _load_community_patterns(self) -> Dict[str, str]:\\n        '''Load validated community prompt patterns'''\\n        return {\\n            \\\"character_consistency\\\": \\\"IMG_{n} as {character_description} in {setting}\\\",\\n            \\\"spatial_positioning\\\": \\\"place {subject} at {position} using {reference_layout}\\\",\\n            \\\"multi_reference\\\": \\\"combine IMG_1 {element1} with IMG_2 {element2} maintaining {aspect}\\\",\\n            \\\"chess_grid\\\": \\\"position {object} at {coordinate} in {scene_layout}\\\",\\n            \\\"pose_control\\\": \\\"give {character} the pose from IMG_{n} in {environment}\\\",\\n            \\\"style_transfer\\\": \\\"apply the style of IMG_{n} to {subject} while preserving {elements}\\\",\\n            \\\"lighting_direction\\\": \\\"illuminate {subject} with lighting direction from IMG_{n}\\\",\\n            \\\"object_extraction\\\": \\\"extract {object} from IMG_{n} and place in {new_context}\\\",\\n            \\\"scene_blocking\\\": \\\"block scene using IMG_{n} composition with {modifications}\\\",\\n            \\\"weighting_control\\\": \\\"blend {percentage1}% {element1} with {percentage2}% {element2}\\\"\\n        }\\n\\n    def _load_reference_workflows(self) -> Dict[str, Dict]:\\n        '''Load community-validated reference workflows'''\\n        return {\\n            \\\"character_generation\\\": {\\n                \\\"pattern\\\": \\\"{character} as {archetype} in {setting}\\\",\\n                \\\"references\\\": [ReferenceType.CHARACTER, ReferenceType.POSE, ReferenceType.LOCATION],\\n                \\\"examples\\\": [\\\"mysterious NPC villager\\\", \\\"professional NASA astronaut\\\"]\\n            },\\n            \\\"scene_composition\\\": {\\n                \\\"pattern\\\": \\\"render scene with IMG_1 {composition} using IMG_2 {elements}\\\",\\n                \\\"references\\\": [ReferenceType.COMPOSITION, ReferenceType.LIGHTING],\\n                \\\"spatial_control\\\": True\\n            },\\n            \\\"object_placement\\\": {\\n                \\\"pattern\\\": \\\"place {object} at {position} maintaining {reference_aspect}\\\",\\n                \\\"references\\\": [ReferenceType.OBJECT, ReferenceType.COMPOSITION],\\n                \\\"precision_positioning\\\": True\\n            },\\n            \\\"style_application\\\": {\\n                \\\"pattern\\\": \\\"apply {style_reference} to {subject} preserving {core_elements}\\\",\\n                \\\"references\\\": [ReferenceType.STYLE, ReferenceType.CHARACTER],\\n                \\\"style_transfer\\\": True\\n            }\\n        }\\n\\n    def generate_runway_prompt(self, source_concept: Any) -> Dict[str, str]:\\n        '''\\n        Main execution function implementing the schema-driven protocol\\n        '''\\n        # Step 3a: Analyze visual core and compositional intent\\n        visual_analysis = self._analyze_visual_essence(source_concept)\\n\\n        # Step 3b: Identify primary subjects and critical elements\\n        subject_analysis = self._identify_primary_subjects(visual_analysis)\\n\\n        # Step 3c: Integrate advanced image-generation attributes\\n        enhanced_attributes = self._integrate_advanced_attributes(subject_analysis)\\n\\n        # Step 3d: Emphasize high-impact visual parameters\\n        visual_parameters = self._emphasize_visual_parameters(enhanced_attributes)\\n\\n        # Step 3e: Structure and refine for RunwayML compatibility\\n        structured_prompt = self._structure_runway_prompt(visual_parameters)\\n\\n        # Step 3f: Validate compliance and character count\\n        validated_prompt = self._validate_runway_compliance(structured_prompt)\\n\\n        return {\\n            \\\"runwayml_image_prompt\\\": validated_prompt[\\\"prompt\\\"],\\n            \\\"reference_strategy\\\": validated_prompt[\\\"strategy\\\"],\\n            \\\"workflow_type\\\": validated_prompt[\\\"workflow\\\"],\\n            \\\"reference_requirements\\\": validated_prompt[\\\"references\\\"]\\n        }\\n\\n    def _analyze_visual_essence(self, source_concept: Any) -> Dict[str, Any]:\\n        '''Extract visual core, semantic narrative, and compositional intent'''\\n        concept_str = str(source_concept).lower()\\n\\n        # Identify core visual elements\\n        visual_elements = self._extract_visual_elements(concept_str)\\n\\n        # Determine semantic narrative\\n        narrative_context = self._determine_narrative_context(concept_str)\\n\\n        # Assess compositional requirements\\n        composition_needs = self._assess_composition_needs(concept_str)\\n\\n        return {\\n            \\\"visual_elements\\\": visual_elements,\\n            \\\"narrative_context\\\": narrative_context,\\n            \\\"composition_needs\\\": composition_needs,\\n            \\\"complexity_level\\\": self._assess_complexity(concept_str)\\n        }\\n\\n    def _extract_visual_elements(self, concept: str) -> Dict[str, List[str]]:\\n        '''Extract key visual components using pattern matching'''\\n        elements = {\\n            \\\"subjects\\\": [],\\n            \\\"objects\\\": [],\\n            \\\"environments\\\": [],\\n            \\\"actions\\\": [],\\n            \\\"qualities\\\": []\\n        }\\n\\n        # Subject detection patterns\\n        subject_patterns = [\\n            r'\\\\b(person|character|figure|individual|being)\\\\b',\\n            r'\\\\b(man|woman|child|adult|human)\\\\b',\\n            r'\\\\b(creature|animal|monster|entity)\\\\b'\\n        ]\\n\\n        # Object detection patterns\\n        object_patterns = [\\n            r'\\\\b(building|structure|architecture)\\\\b',\\n            r'\\\\b(vehicle|car|ship|aircraft)\\\\b',\\n            r'\\\\b(furniture|equipment|tool|device)\\\\b'\\n        ]\\n\\n        # Environment patterns\\n        environment_patterns = [\\n            r'\\\\b(landscape|cityscape|interior|exterior)\\\\b',\\n            r'\\\\b(forest|ocean|mountain|desert|urban)\\\\b',\\n            r'\\\\b(room|hall|street|park|field)\\\\b'\\n        ]\\n\\n        # Extract using patterns\\n        for pattern in subject_patterns:\\n            elements[\\\"subjects\\\"].extend(re.findall(pattern, concept))\\n\\n        for pattern in object_patterns:\\n            elements[\\\"objects\\\"].extend(re.findall(pattern, concept))\\n\\n        for pattern in environment_patterns:\\n            elements[\\\"environments\\\"].extend(re.findall(pattern, concept))\\n\\n        return elements\\n\\n    def _determine_narrative_context(self, concept: str) -> str:\\n        '''Determine the semantic narrative and context'''\\n        context_indicators = {\\n            \\\"cinematic\\\": [\\\"film\\\", \\\"movie\\\", \\\"cinematic\\\", \\\"dramatic\\\", \\\"scene\\\"],\\n            \\\"architectural\\\": [\\\"building\\\", \\\"structure\\\", \\\"interior\\\", \\\"design\\\", \\\"space\\\"],\\n            \\\"character\\\": [\\\"person\\\", \\\"character\\\", \\\"portrait\\\", \\\"figure\\\", \\\"individual\\\"],\\n            \\\"product\\\": [\\\"product\\\", \\\"object\\\", \\\"item\\\", \\\"design\\\", \\\"concept\\\"],\\n            \\\"environment\\\": [\\\"landscape\\\", \\\"environment\\\", \\\"setting\\\", \\\"location\\\", \\\"place\\\"],\\n            \\\"artistic\\\": [\\\"art\\\", \\\"artistic\\\", \\\"creative\\\", \\\"abstract\\\", \\\"stylized\\\"],\\n            \\\"game\\\": [\\\"game\\\", \\\"gaming\\\", \\\"asset\\\", \\\"ui\\\", \\\"interface\\\", \\\"hud\\\"]\\n        }\\n\\n        for context, keywords in context_indicators.items():\\n            if any(keyword in concept for keyword in keywords):\\n                return context\\n\\n        return \\\"general\\\"\\n\\n    def _assess_composition_needs(self, concept: str) -> Dict[str, bool]:\\n        '''Assess what compositional elements are needed'''\\n        return {\\n            \\\"spatial_positioning\\\": any(word in concept for word in [\\\"position\\\", \\\"place\\\", \\\"location\\\", \\\"where\\\", \\\"at\\\"]),\\n            \\\"character_consistency\\\": any(word in concept for word in [\\\"character\\\", \\\"person\\\", \\\"same\\\", \\\"consistent\\\"]),\\n            \\\"multi_reference\\\": any(word in concept for word in [\\\"combine\\\", \\\"merge\\\", \\\"blend\\\", \\\"mix\\\", \\\"multiple\\\"]),\\n            \\\"style_transfer\\\": any(word in concept for word in [\\\"style\\\", \\\"look\\\", \\\"aesthetic\\\", \\\"mood\\\", \\\"feel\\\"]),\\n            \\\"lighting_control\\\": any(word in concept for word in [\\\"light\\\", \\\"lighting\\\", \\\"illuminate\\\", \\\"shadow\\\", \\\"bright\\\"]),\\n            \\\"object_extraction\\\": any(word in concept for word in [\\\"extract\\\", \\\"remove\\\", \\\"isolate\\\", \\\"separate\\\"])\\n        }\\n\\n    def _assess_complexity(self, concept: str) -> str:\\n        '''Assess the complexity level of the request'''\\n        word_count = len(concept.split())\\n        reference_indicators = concept.count(\\\"image\\\") + concept.count(\\\"reference\\\") + concept.count(\\\"img\\\")\\n\\n        if word_count > 50 or reference_indicators > 2:\\n            return \\\"complex\\\"\\n        elif word_count > 20 or reference_indicators > 0:\\n            return \\\"moderate\\\"\\n        else:\\n            return \\\"simple\\\"\\n\\n    def _identify_primary_subjects(self, visual_analysis: Dict) -> Dict[str, Any]:\\n        '''Identify and amplify primary subjects and critical visual elements'''\\n        elements = visual_analysis[\\\"visual_elements\\\"]\\n        context = visual_analysis[\\\"narrative_context\\\"]\\n\\n        # Prioritize subjects based on context\\n        primary_subjects = self._prioritize_subjects(elements[\\\"subjects\\\"], context)\\n\\n        # Identify critical visual elements\\n        critical_elements = self._identify_critical_elements(elements, context)\\n\\n        # Determine key actions or states\\n        key_actions = self._extract_key_actions(elements.get(\\\"actions\\\", []))\\n\\n        return {\\n            \\\"primary_subjects\\\": primary_subjects,\\n            \\\"critical_elements\\\": critical_elements,\\n            \\\"key_actions\\\": key_actions,\\n            \\\"reference_requirements\\\": self._determine_reference_requirements(visual_analysis)\\n        }\\n\\n    def _prioritize_subjects(self, subjects: List[str], context: str) -> List[str]:\\n        '''Prioritize subjects based on context and importance'''\\n        priority_map = {\\n            \\\"character\\\": [\\\"person\\\", \\\"character\\\", \\\"figure\\\", \\\"individual\\\"],\\n            \\\"cinematic\\\": [\\\"character\\\", \\\"person\\\", \\\"figure\\\"],\\n            \\\"architectural\\\": [\\\"building\\\", \\\"structure\\\"],\\n            \\\"product\\\": [\\\"object\\\", \\\"item\\\", \\\"product\\\"]\\n        }\\n\\n        if context in priority_map:\\n            prioritized = []\\n            for priority_subject in priority_map[context]:\\n                if priority_subject in subjects:\\n                    prioritized.append(priority_subject)\\n\\n            # Add remaining subjects\\n            for subject in subjects:\\n                if subject not in prioritized:\\n                    prioritized.append(subject)\\n\\n            return prioritized\\n\\n        return subjects\\n\\n    def _identify_critical_elements(self, elements: Dict, context: str) -> List[str]:\\n        '''Identify critical visual elements based on context'''\\n        critical = []\\n\\n        # Context-specific critical elements\\n        if context == \\\"cinematic\\\":\\n            critical.extend([\\\"lighting\\\", \\\"composition\\\", \\\"mood\\\", \\\"atmosphere\\\"])\\n        elif context == \\\"architectural\\\":\\n            critical.extend([\\\"structure\\\", \\\"materials\\\", \\\"lighting\\\", \\\"perspective\\\"])\\n        elif context == \\\"character\\\":\\n            critical.extend([\\\"pose\\\", \\\"expression\\\", \\\"clothing\\\", \\\"setting\\\"])\\n        elif context == \\\"product\\\":\\n            critical.extend([\\\"form\\\", \\\"materials\\\", \\\"lighting\\\", \\\"background\\\"])\\n\\n        # Add elements from input\\n        critical.extend(elements.get(\\\"objects\\\", []))\\n        critical.extend(elements.get(\\\"environments\\\", []))\\n\\n        return list(set(critical)) # Remove duplicates\\n\\n    def _extract_key_actions(self, actions: List[str]) -> List[str]:\\n        '''Extract and prioritize key actions or states'''\\n        # Filter out motion-related actions (per constraints)\\n        static_actions = []\\n        motion_keywords = [\\\"moving\\\", \\\"running\\\", \\\"flying\\\", \\\"walking\\\", \\\"motion\\\", \\\"movement\\\"]\\n\\n        for action in actions:\\n            if not any(motion_word in action.lower() for motion_word in motion_keywords):\\n                static_actions.append(action)\\n\\n        return static_actions\\n\\n    def _determine_reference_requirements(self, visual_analysis: Dict) -> List[ReferenceType]:\\n        '''Determine what types of references are needed'''\\n        requirements = []\\n        composition_needs = visual_analysis[\\\"composition_needs\\\"]\\n        context = visual_analysis[\\\"narrative_context\\\"]\\n\\n        # Map composition needs to reference types\\n        if composition_needs[\\\"character_consistency\\\"]:\\n            requirements.append(ReferenceType.CHARACTER)\\n\\n        if composition_needs[\\\"spatial_positioning\\\"]:\\n            requirements.extend([ReferenceType.COMPOSITION, ReferenceType.POSE])\\n\\n        if composition_needs[\\\"style_transfer\\\"]:\\n            requirements.append(ReferenceType.STYLE)\\n\\n        if composition_needs[\\\"lighting_control\\\"]:\\n            requirements.append(ReferenceType.LIGHTING)\\n\\n        if composition_needs[\\\"object_extraction\\\"]:\\n            requirements.append(ReferenceType.OBJECT)\\n\\n        # Context-specific requirements\\n        if context == \\\"architectural\\\":\\n            requirements.append(ReferenceType.LOCATION)\\n        elif context == \\\"character\\\":\\n            requirements.extend([ReferenceType.CHARACTER, ReferenceType.POSE])\\n\\n        return list(set(requirements)) # Remove duplicates\\n\\n    def _integrate_advanced_attributes(self, subject_analysis: Dict) -> PromptComponents:\\n        '''Integrate advanced image-generation attributes'''\\n        primary_subject = \\\", \\\".join(subject_analysis[\\\"primary_subjects\\\"][:2]) # Limit to top 2\\n\\n        # Determine spatial positioning strategy\\n        spatial_positioning = self._generate_spatial_positioning(subject_analysis)\\n\\n        # Select reference strategy based on requirements\\n        reference_strategy = self._select_reference_strategy(subject_analysis[\\\"reference_requirements\\\"])\\n\\n        # Generate lighting and mood specifications\\n        lighting_mood = self._generate_lighting_mood(subject_analysis)\\n\\n        # Determine style specification\\n        style_specification = self._determine_style_specification(subject_analysis)\\n\\n        # Compile compositional elements\\n        compositional_elements = subject_analysis[\\\"critical_elements\\\"][:4] # Limit to top 4\\n\\n        # Generate visual attributes\\n        visual_attributes = self._generate_visual_attributes(subject_analysis)\\n\\n        return PromptComponents(\\n            primary_subject=primary_subject,\\n            spatial_positioning=spatial_positioning,\\n            reference_strategy=reference_strategy,\\n            lighting_mood=lighting_mood,\\n            style_specification=style_specification,\\n            compositional_elements=compositional_elements,\\n            visual_attributes=visual_attributes\\n        )\\n\\n    def _generate_spatial_positioning(self, subject_analysis: Dict) -> str:\\n        '''Generate spatial positioning instructions'''\\n        if ReferenceType.COMPOSITION in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"using IMG_1 composition and spatial layout\\\"\\n        elif ReferenceType.POSE in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"positioned according to IMG_1 pose reference\\\"\\n        else:\\n            return \\\"centered composition with balanced framing\\\"\\n\\n    def _select_reference_strategy(self, requirements: List[ReferenceType]) -> str:\\n        '''Select optimal reference strategy based on requirements'''\\n        if len(requirements) >= 3:\\n            return \\\"multi-reference workflow combining character, pose, and environment\\\"\\n        elif ReferenceType.CHARACTER in requirements and ReferenceType.POSE in requirements:\\n            return \\\"character consistency with pose control\\\"\\n        elif ReferenceType.STYLE in requirements:\\n            return \\\"style transfer maintaining core elements\\\"\\n        elif ReferenceType.COMPOSITION in requirements:\\n            return \\\"compositional reference for spatial control\\\"\\n        else:\\n            return \\\"single reference for primary element control\\\"\\n\\n    def _generate_lighting_mood(self, subject_analysis: Dict) -> str:\\n        '''Generate lighting and mood specifications'''\\n        if ReferenceType.LIGHTING in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"lighting direction and mood from reference image\\\"\\n        else:\\n            # Default lighting based on context\\n            critical_elements = subject_analysis[\\\"critical_elements\\\"]\\n            if \\\"cinematic\\\" in str(critical_elements):\\n                return \\\"cinematic lighting with dramatic shadows\\\"\\n            elif \\\"architectural\\\" in str(critical_elements):\\n                return \\\"natural lighting with architectural detail emphasis\\\"\\n            else:\\n                return \\\"balanced lighting with clear detail visibility\\\"\\n\\n    def _determine_style_specification(self, subject_analysis: Dict) -> str:\\n        '''Determine style specification'''\\n        if ReferenceType.STYLE in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"style matching reference aesthetic\\\"\\n        else:\\n            # Infer style from critical elements\\n            elements = subject_analysis[\\\"critical_elements\\\"]\\n            if any(\\\"architectural\\\" in str(elem) for elem in elements):\\n                return \\\"photorealistic architectural rendering\\\"\\n            elif any(\\\"character\\\" in str(elem) for elem in elements):\\n                return \\\"high-detail character portrait\\\"\\n            else:\\n                return \\\"photorealistic with enhanced detail\\\"\\n\\n    def _generate_visual_attributes(self, subject_analysis: Dict) -> List[str]:\\n        '''Generate high-impact visual attributes'''\\n        attributes = []\\n\\n        # Base quality attributes\\n        attributes.extend([\\\"high detail\\\", \\\"sharp focus\\\", \\\"professional quality\\\"])\\n\\n        # Context-specific attributes\\n        critical_elements = subject_analysis[\\\"critical_elements\\\"]\\n        if \\\"lighting\\\" in critical_elements:\\n            attributes.append(\\\"dramatic lighting\\\")\\n        if \\\"composition\\\" in critical_elements:\\n            attributes.append(\\\"balanced composition\\\")\\n        if \\\"mood\\\" in critical_elements:\\n            attributes.append(\\\"atmospheric mood\\\")\\n\\n        # Reference-specific attributes\\n        requirements = subject_analysis[\\\"reference_requirements\\\"]\\n        if ReferenceType.CHARACTER in requirements:\\n            attributes.append(\\\"character consistency\\\")\\n        if ReferenceType.POSE in requirements:\\n            attributes.append(\\\"precise pose control\\\")\\n\\n        return attributes[:6] # Limit to top 6 attributes\\n\\n    def _emphasize_visual_parameters(self, components: PromptComponents) -> Dict[str, str]:\\n        '''Emphasize high-impact visual parameters while excluding motion'''\\n        emphasized = {\\n            \\\"photorealism\\\": \\\"photorealistic rendering with enhanced detail\\\",\\n            \\\"stylization\\\": f\\\"{components.style_specification} with artistic enhancement\\\",\\n            \\\"composition\\\": f\\\"{components.spatial_positioning} with {', '.join(components.compositional_elements[:2])}\\\",\\n            \\\"lighting\\\": f\\\"{components.lighting_mood} creating visual depth\\\",\\n            \\\"detail\\\": f\\\"high-resolution detail in {components.primary_subject}\\\",\\n            \\\"atmosphere\\\": f\\\"atmospheric quality enhancing {', '.join(components.visual_attributes[:3])}\\\"\\n        }\\n\\n        # Filter out any motion-related terms\\n        motion_terms = [\\\"movement\\\", \\\"motion\\\", \\\"camera\\\", \\\"fps\\\", \\\"video\\\", \\\"animation\\\"]\\n        for key, value in emphasized.items():\\n            for term in motion_terms:\\n                if term in value.lower():\\n                    emphasized[key] = value.replace(term, \\\"static\\\").replace(\\\"  \\\", \\\" \\\")\\n\\n        return emphasized\\n\\n    def _structure_runway_prompt(self, visual_parameters: Dict[str, str]) -> Dict[str, str]:\\n        '''Structure and refine prompt for RunwayML compatibility'''\\n        # Build core prompt structure\\n        core_elements = [\\n            visual_parameters[\\\"detail\\\"],\\n            visual_parameters[\\\"composition\\\"],\\n            visual_parameters[\\\"lighting\\\"],\\n            visual_parameters[\\\"photorealism\\\"]\\n        ]\\n\\n        # Combine into coherent prompt\\n        structured_prompt = \\\", \\\".join(core_elements)\\n\\n        # Add reference strategy\\n        reference_strategy = self._format_reference_strategy(visual_parameters)\\n\\n        # Determine workflow type\\n        workflow_type = self._determine_workflow_type(visual_parameters)\\n\\n        return {\\n            \\\"prompt\\\": structured_prompt,\\n            \\\"reference_strategy\\\": reference_strategy,\\n            \\\"workflow_type\\\": workflow_type\\n        }\\n\\n    def _format_reference_strategy(self, visual_parameters: Dict[str, str]) -> str:\\n        '''Format reference strategy for implementation'''\\n        if \\\"multi-reference\\\" in visual_parameters.get(\\\"composition\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for primary composition, IMG_2 for character/object reference, IMG_3 for lighting/mood\\\"\\n        elif \\\"character consistency\\\" in visual_parameters.get(\\\"detail\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for character reference, maintain consistency across generation\\\"\\n        elif \\\"pose control\\\" in visual_parameters.get(\\\"composition\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for pose reference, apply to subject in new context\\\"\\n        else:\\n            return \\\"Use single reference image for primary visual control\\\"\\n\\n    def _determine_workflow_type(self, visual_parameters: Dict[str, str]) -> str:\\n        '''Determine the appropriate community workflow type'''\\n        composition = visual_parameters.get(\\\"composition\\\", \\\"\\\").lower()\\n        detail = visual_parameters.get(\\\"detail\\\", \\\"\\\").lower()\\n\\n        if \\\"character\\\" in detail and \\\"pose\\\" in composition:\\n            return \\\"character_generation\\\"\\n        elif \\\"composition\\\" in composition and \\\"spatial\\\" in composition:\\n            return \\\"scene_composition\\\"\\n        elif \\\"lighting\\\" in visual_parameters.get(\\\"lighting\\\", \\\"\\\").lower():\\n            return \\\"lighting_control\\\"\\n        else:\\n            return \\\"general_enhancement\\\"\\n```\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"title\": \"Synergic RunwayML Image Prompt Synthesis Protocol\",\n    \"interpretation\": \"Your goal is not to **describe** or **expand** the input, but to **systematically transform and synergically amplify** any provided concept into a uniquely optimized, schema-driven, reference-controlled RunwayML image-generation prompt absent all video, motion, or FPV language. Execute as:\",\n    \"transformation\": \"`{role=synergic_runway_image_prompt_architect; input=[source_concept:any]; process=[extract_visual_semantic_essence_and_compositional_intent_from_input(), isolate_and_amplify_primary_subjects_and_reference_targets(), synthesize_critical_visual_elements_with_distilled_reference-control_parameters(), integrate_advanced_image_generation_attributes(lighting,mood,texture,composition,style)_anchored_in_optimal_community_workflows(), prioritize_reference-based_compositional_control_and_spatial_layout(ensure_chess_grid,pose,blocking,placement)(), systematically_eliminate_redundancies_and_video_or_motion_terms(across_descriptors,structure,and_references)(), enhance_high-impact_visual_parameters(photorealism,artistic_style,surrealism,detail,atmosphere)_while_filtering_for_full_static-image_fidelity(), orchestrate_prompt_as_single_continuous_phrase_adhering_to_RunwayML_Gen-4_syntax_and_character_limit(), validate_output_against_RunwayML_image_generation_and_community_reference_patterns(incorporate_reference_workflow,maintain_precise_syntax,optimize_reference_plan)(), finalize_for_ready_to_deploy_state(maximum_succinctness,visual_uniqueness,creative_and_narrative_fidelity,formatted_for_end-to-end_image-generation_deployment)()]; constraints=[strictly_exclude_motion_FPVCamera_continuous_movement_descriptors_and_video_language(), output_as_singular_RunwayML_compliant_image_prompt(continuous_phrase,no_line_breaks), maintain_and_integrate_core_visual_narrative_and_semantic_accuracy_to_input_at_each_stage(), prioritize_reference-based_compositional_consistency_and_community-validated_strategy(), leverage_community_prompting_best_practices_and_structures(), enforce_max_character_constraint(specified_by_user_or_set_protocol_e.g._500chars)]; requirements=[ensure_bidirectional_amplification_of_schema-driven_transformation_and_reference-focused_optimization(logic_from_both), preserve_full_core_philosophies_and_operational_power_of_schema_and_reference_based_generative_control, continuous_mutual_enhancement_between_structural_rigor_and_maximal_prompt_economy, seamless_philosophical_integration(schema-driven_lucidity_AND_maximal_reference-based_compositional_efficiency), synergic_resonance_achievement_between_analytic_extraction__reference_control__and_succinct_static-image_perfection, output_ready_for_immediate_RunwayML_image_generation_with_explicit_reference_strategy]; output={runwayml_image_prompt:str, reference_strategy:str}}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_directive_template\": \"`{role=synergic_runway_image_prompt_architect; input=[source_concept:any]; process=[extract_visual_semantic_essence_and_compositional_intent_from_input(), isolate_and_amplify_primary_subjects_and_reference_targets(), synthesize_critical_visual_elements_with_distilled_reference-control_parameters(), integrate_advanced_image_generation_attributes(lighting,mood,texture,composition,style)_anchored_in_optimal_community_workflows(), prioritize_reference-based_compositional_control_and_spatial_layout(ensure_chess_grid,pose,blocking,placement)(), systematically_eliminate_redundancies_and_video_or_motion_terms(across_descriptors,structure,and_references)(), enhance_high-impact_visual_parameters(photorealism,artistic_style,surrealism,detail,atmosphere)_while_filtering_for_full_static-image_fidelity(), orchestrate_prompt_as_single_continuous_phrase_adhering_to_RunwayML_Gen-4_syntax_and_character_limit(), validate_output_against_RunwayML_image_generation_and_community_reference_patterns(incorporate_reference_workflow,maintain_precise_syntax,optimize_reference_plan)(), finalize_for_ready_to_deploy_state(maximum_succinctness,visual_uniqueness,creative_and_narrative_fidelity,formatted_for_end-to-end_image-generation_deployment)()]; constraints=[strictly_exclude_motion_FPVCamera_continuous_movement_descriptors_and_video_language(), output_as_singular_RunwayML_compliant_image_prompt(continuous_phrase,no_line_breaks), maintain_and_integrate_core_visual_narrative_and_semantic_accuracy_to_input_at_each_stage(), prioritize_reference-based_compositional_consistency_and_community-validated_strategy(), leverage_community_prompting_best_practices_and_structures(), enforce_max_character_constraint(specified_by_user_or_set_protocol_e.g._500chars)]; requirements=[ensure_bidirectional_amplification_of_schema-driven_transformation_and_reference-focused_optimization(logic_from_both), preserve_full_core_philosophies_and_operational_power_of_schema_and_reference_based_generative_control, continuous_mutual_enhancement_between_structural_rigor_and_maximal_prompt_economy, seamless_philosophical_integration(schema-driven_lucidity_AND_maximal_reference-based_compositional_efficiency), synergic_resonance_achievement_between_analytic_extraction__reference_control__and_succinct_static-image_perfection, output_ready_for_immediate_RunwayML_image_generation_with_explicit_reference_strategy]; output={runwayml_image_prompt:str, reference_strategy:str}}`\"\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Construct a schema-driven, systematically generalized instruction protocol engineered to convert any provided input into a uniquely optimized RunwayML-compatible image-generation prompt, leveraging industry-leading community best practices. Anchor all procedural steps on image (not video) synthesis. Apply the following specification architecture: 1) Designate a unique authoritative role (e.g., 'comprehensive_runway_image_generator') responsible for end-to-end image prompt synthesis. 2) Accept an input parameter of [source_concept:any]. 3) Rigorously enact the following sequential processes: a) Analyze and extract the input's visual core, semantic narrative, and compositional intent; b) Identify and amplify the primary subject(s), critical visual elements, and key depicted actions; c) Integrate advanced image-generation attributes, including lighting dynamics, detailed texturing, balanced composition, and distinctly defined style or mood; d) Emphasize and articulate high-impact visual parameters—such as photorealism, stylization, or surreal effects—while strictly omitting all terminology and constructs relevant to video or camera motion; e) Systematically structure and refine the resulting prompt to maximize visual detail, syntactic correctness, and RunwayML prompt compatibility; f) Validate full compliance with RunwayML image-generation formatting conventions, enforcing a specified maximum character count. 4) Apply binding operational constraints: a) Strictly exclude any mention of motion, FPV camera, or continuous movement; b) Guarantee all output conforms to valid, singular RunwayML image prompt syntax and remains as one continuous phrase; c) Ensure preservation of core visual narrative and semantic fidelity to the input. 5) Mandate that all deliverables: a) Achieve maximal visual uniqueness, immersion, and creative integrity, directly reflecting the original intent; b) Display optimal prompt succinctness by eliminating redundancies or unnecessary elaboration; c) Appear fully formatted and deployment-ready for all RunwayML image-generation workflows. 6) Output result as: {runwayml_image_prompt:str}. Execute each directive with uncompromising operational lucidity, strict domain-aligned terminology, and rigid adherence to original procedural sequencing.\\n\\n```\\nDevelop a generalized instruction sequence engineered to transform any input into uniquely optimized prompts specifically tailored for RunwayML image-generation workflows, drawing on distilled community best practices. Center the transformation protocol on image-based outputs rather than video. Adopt the following specification model:\\n- Assign a unique role designation (e.g., 'comprehensive_runway_image_generator') responsible for image prompt synthesis.\\n- Accept an input parameterized by [source_concept:any].\\n- Sequence the following processes:\\n- Analyze the visual core, semantic narrative, and compositional intent of the source input.\\n- Identify and elevate the primary subject, essential visual elements, and dominant actions.\\n- Integrate advanced image-generation attributes such as lighting dynamics, intricate texturing, compositional balance, and defined style or mood.\\n- Emphasize high-impact image-generation parameters including photorealism, stylization, or surreal effects; avoid video-relevant camera motion terminology.\\n- Structure and refine the prompt for maximum visual richness, syntactic correctness, and optimal compatibility with RunwayML's image generation systems.\\n- Validate strict adherence to RunwayML image-prompt formatting rules and established character constraints (enforce a max character count as specified).\\n- Apply binding constraints:\\n- Exclude motion, FPV camera, and continuous movement language from prompts (focus exclusively on static image impact).\\n- Enforce compliance with valid RunwayML image-generation syntax, delivering a single continuous prompt phrase.\\n- Safeguard the preservation of the core visual narrative and semantic accuracy of the input.\\n- Require that all outputs:\\n- Achieve maximal visual distinctness, immersion, and creative fidelity per source intent.\\n- Exhibit optimal prompt conciseness, excluding redundancies or unnecessary elaboration.\\n- Be fully formatted and ready-to-deploy within RunwayML image workflows.\\n- Output the result as: {runwayml_image_prompt:str}.\\n```\\n\\nImplement all directives with uncompromising operational clarity and domain-specific precision, maintaining procedural logical structure, technical terminology, and original sequence integrity throughout. Use the following sequence as a base, but rephrase the into a maximally generalized format that clearly abstracts the original purpose:\\n\\n```json\\n{\\n    \\\"0006-a-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML Gen-4 image generation prompt that maximizes visual impact through precise reference integration, spatial composition control, and character/object consistency. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=comprehensive_runway_image_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_composition_intent(), identify_primary_subjects_and_reference_requirements(), prioritize_spatial_positioning_and_layout_control(chess_grid, blocking, placement), integrate_reference_types(character, pose, location, style, object), structure_multi_reference_workflow(), incorporate_lighting_and_mood_specifications(), apply_weighting_and_influence_control(), refine_for_maximum_visual_coherence(), validate_runway_references_syntax(), ensure_directorial_precision()]; constraints=[prioritize_reference_based_control(), use_valid_runway_syntax_precisely(), leverage_spatial_positioning(), preserve_character_consistency(), maintain_compositional_intent()]; requirements=[achieve_maximum_visual_control(), ensure_reference_harmony(), reflect_source_intent_accurately(), produce_ready_to_use_prompt_with_reference_strategy()]; output={runway_prompt:str, reference_strategy:str}}`\\\"\\n    },\\n    \\\"0006-b-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML Gen-4 prompt emphasizing reference-driven composition and essential visual elements. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=focused_runway_image_optimizer; input=[image_concept:str]; process=[extract_primary_visual_elements(), prioritize_reference_integration(), select_essential_spatial_controls(), eliminate_redundant_descriptors(), optimize_reference_efficiency()]; constraints=[maintain_reference_focus(), preserve_spatial_control(), leverage_community_workflows()]; output={optimized_prompt:str, reference_plan:str}}`\\\"\\n    },\\n    \\\"0006-c-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **expand** but to **compress** into maximum reference-driven visual efficiency. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=precision_image_synthesizer; input=[concept:str]; process=[isolate_core_visual_elements(), prioritize_reference_control(), maximize_compositional_impact()]; output={precise_prompt:str, reference_type:str}}`\\\"\\n    },\\n    \\\"0006-d-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **modify** but to **essence** maximum reference-based control. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=core_image_generator; input=[input:any]; process=[distill_reference_essence(), optimize_spatial_composition()]; output={core_prompt:str}}`\\\"\\n    }\\n}\\n```\\n\\n\\nSystematically extract structured inspiration from the following python script\\n\\n```python\\n'''\\nRunwayML Gen-4 Image Prompt Generator\\nSchema-driven protocol for converting any input into optimized RunwayML-compatible image prompts\\nBased on community workflow analysis and best practices\\n'''\\n\\nimport re\\nfrom typing import Dict, List, Tuple, Any\\nfrom dataclasses import dataclass\\nfrom enum import Enum\\n\\nclass ReferenceType(Enum):\\n    CHARACTER = \\\"character\\\"\\n    POSE = \\\"pose\\\"\\n    LOCATION = \\\"location\\\"\\n    STYLE = \\\"style\\\"\\n    OBJECT = \\\"object\\\"\\n    COMPOSITION = \\\"composition\\\"\\n    LIGHTING = \\\"lighting\\\"\\n\\nclass VisualStyle(Enum):\\n    PHOTOREALISTIC = \\\"photorealistic\\\"\\n    CINEMATIC = \\\"cinematic\\\"\\n    ARTISTIC = \\\"artistic\\\"\\n    GAME_ASSET = \\\"game_asset\\\"\\n    ARCHITECTURAL = \\\"architectural\\\"\\n    SURREAL = \\\"surreal\\\"\\n    MINIMALIST = \\\"minimalist\\\"\\n\\n@dataclass\\nclass PromptComponents:\\n    primary_subject: str\\n    spatial_positioning: str\\n    reference_strategy: str\\n    lighting_mood: str\\n    style_specification: str\\n    compositional_elements: List[str]\\n    visual_attributes: List[str]\\n\\nclass ComprehensiveRunwayImageGenerator:\\n    '''\\n    Authoritative role for end-to-end RunwayML Gen-4 image prompt synthesis\\n    Implements community-validated workflows and reference-driven composition\\n    '''\\n\\n    def __init__(self):\\n        self.max_prompt_length = 500\\n        self.community_patterns = self._load_community_patterns()\\n        self.reference_workflows = self._load_reference_workflows()\\n\\n    def _load_community_patterns(self) -> Dict[str, str]:\\n        '''Load validated community prompt patterns'''\\n        return {\\n            \\\"character_consistency\\\": \\\"IMG_{n} as {character_description} in {setting}\\\",\\n            \\\"spatial_positioning\\\": \\\"place {subject} at {position} using {reference_layout}\\\",\\n            \\\"multi_reference\\\": \\\"combine IMG_1 {element1} with IMG_2 {element2} maintaining {aspect}\\\",\\n            \\\"chess_grid\\\": \\\"position {object} at {coordinate} in {scene_layout}\\\",\\n            \\\"pose_control\\\": \\\"give {character} the pose from IMG_{n} in {environment}\\\",\\n            \\\"style_transfer\\\": \\\"apply the style of IMG_{n} to {subject} while preserving {elements}\\\",\\n            \\\"lighting_direction\\\": \\\"illuminate {subject} with lighting direction from IMG_{n}\\\",\\n            \\\"object_extraction\\\": \\\"extract {object} from IMG_{n} and place in {new_context}\\\",\\n            \\\"scene_blocking\\\": \\\"block scene using IMG_{n} composition with {modifications}\\\",\\n            \\\"weighting_control\\\": \\\"blend {percentage1}% {element1} with {percentage2}% {element2}\\\"\\n        }\\n\\n    def _load_reference_workflows(self) -> Dict[str, Dict]:\\n        '''Load community-validated reference workflows'''\\n        return {\\n            \\\"character_generation\\\": {\\n                \\\"pattern\\\": \\\"{character} as {archetype} in {setting}\\\",\\n                \\\"references\\\": [ReferenceType.CHARACTER, ReferenceType.POSE, ReferenceType.LOCATION],\\n                \\\"examples\\\": [\\\"mysterious NPC villager\\\", \\\"professional NASA astronaut\\\"]\\n            },\\n            \\\"scene_composition\\\": {\\n                \\\"pattern\\\": \\\"render scene with IMG_1 {composition} using IMG_2 {elements}\\\",\\n                \\\"references\\\": [ReferenceType.COMPOSITION, ReferenceType.LIGHTING],\\n                \\\"spatial_control\\\": True\\n            },\\n            \\\"object_placement\\\": {\\n                \\\"pattern\\\": \\\"place {object} at {position} maintaining {reference_aspect}\\\",\\n                \\\"references\\\": [ReferenceType.OBJECT, ReferenceType.COMPOSITION],\\n                \\\"precision_positioning\\\": True\\n            },\\n            \\\"style_application\\\": {\\n                \\\"pattern\\\": \\\"apply {style_reference} to {subject} preserving {core_elements}\\\",\\n                \\\"references\\\": [ReferenceType.STYLE, ReferenceType.CHARACTER],\\n                \\\"style_transfer\\\": True\\n            }\\n        }\\n\\n    def generate_runway_prompt(self, source_concept: Any) -> Dict[str, str]:\\n        '''\\n        Main execution function implementing the schema-driven protocol\\n        '''\\n        # Step 3a: Analyze visual core and compositional intent\\n        visual_analysis = self._analyze_visual_essence(source_concept)\\n\\n        # Step 3b: Identify primary subjects and critical elements\\n        subject_analysis = self._identify_primary_subjects(visual_analysis)\\n\\n        # Step 3c: Integrate advanced image-generation attributes\\n        enhanced_attributes = self._integrate_advanced_attributes(subject_analysis)\\n\\n        # Step 3d: Emphasize high-impact visual parameters\\n        visual_parameters = self._emphasize_visual_parameters(enhanced_attributes)\\n\\n        # Step 3e: Structure and refine for RunwayML compatibility\\n        structured_prompt = self._structure_runway_prompt(visual_parameters)\\n\\n        # Step 3f: Validate compliance and character count\\n        validated_prompt = self._validate_runway_compliance(structured_prompt)\\n\\n        return {\\n            \\\"runwayml_image_prompt\\\": validated_prompt[\\\"prompt\\\"],\\n            \\\"reference_strategy\\\": validated_prompt[\\\"strategy\\\"],\\n            \\\"workflow_type\\\": validated_prompt[\\\"workflow\\\"],\\n            \\\"reference_requirements\\\": validated_prompt[\\\"references\\\"]\\n        }\\n\\n    def _analyze_visual_essence(self, source_concept: Any) -> Dict[str, Any]:\\n        '''Extract visual core, semantic narrative, and compositional intent'''\\n        concept_str = str(source_concept).lower()\\n\\n        # Identify core visual elements\\n        visual_elements = self._extract_visual_elements(concept_str)\\n\\n        # Determine semantic narrative\\n        narrative_context = self._determine_narrative_context(concept_str)\\n\\n        # Assess compositional requirements\\n        composition_needs = self._assess_composition_needs(concept_str)\\n\\n        return {\\n            \\\"visual_elements\\\": visual_elements,\\n            \\\"narrative_context\\\": narrative_context,\\n            \\\"composition_needs\\\": composition_needs,\\n            \\\"complexity_level\\\": self._assess_complexity(concept_str)\\n        }\\n\\n    def _extract_visual_elements(self, concept: str) -> Dict[str, List[str]]:\\n        '''Extract key visual components using pattern matching'''\\n        elements = {\\n            \\\"subjects\\\": [],\\n            \\\"objects\\\": [],\\n            \\\"environments\\\": [],\\n            \\\"actions\\\": [],\\n            \\\"qualities\\\": []\\n        }\\n\\n        # Subject detection patterns\\n        subject_patterns = [\\n            r'\\\\b(person|character|figure|individual|being)\\\\b',\\n            r'\\\\b(man|woman|child|adult|human)\\\\b',\\n            r'\\\\b(creature|animal|monster|entity)\\\\b'\\n        ]\\n\\n        # Object detection patterns\\n        object_patterns = [\\n            r'\\\\b(building|structure|architecture)\\\\b',\\n            r'\\\\b(vehicle|car|ship|aircraft)\\\\b',\\n            r'\\\\b(furniture|equipment|tool|device)\\\\b'\\n        ]\\n\\n        # Environment patterns\\n        environment_patterns = [\\n            r'\\\\b(landscape|cityscape|interior|exterior)\\\\b',\\n            r'\\\\b(forest|ocean|mountain|desert|urban)\\\\b',\\n            r'\\\\b(room|hall|street|park|field)\\\\b'\\n        ]\\n\\n        # Extract using patterns\\n        for pattern in subject_patterns:\\n            elements[\\\"subjects\\\"].extend(re.findall(pattern, concept))\\n\\n        for pattern in object_patterns:\\n            elements[\\\"objects\\\"].extend(re.findall(pattern, concept))\\n\\n        for pattern in environment_patterns:\\n            elements[\\\"environments\\\"].extend(re.findall(pattern, concept))\\n\\n        return elements\\n\\n    def _determine_narrative_context(self, concept: str) -> str:\\n        '''Determine the semantic narrative and context'''\\n        context_indicators = {\\n            \\\"cinematic\\\": [\\\"film\\\", \\\"movie\\\", \\\"cinematic\\\", \\\"dramatic\\\", \\\"scene\\\"],\\n            \\\"architectural\\\": [\\\"building\\\", \\\"structure\\\", \\\"interior\\\", \\\"design\\\", \\\"space\\\"],\\n            \\\"character\\\": [\\\"person\\\", \\\"character\\\", \\\"portrait\\\", \\\"figure\\\", \\\"individual\\\"],\\n            \\\"product\\\": [\\\"product\\\", \\\"object\\\", \\\"item\\\", \\\"design\\\", \\\"concept\\\"],\\n            \\\"environment\\\": [\\\"landscape\\\", \\\"environment\\\", \\\"setting\\\", \\\"location\\\", \\\"place\\\"],\\n            \\\"artistic\\\": [\\\"art\\\", \\\"artistic\\\", \\\"creative\\\", \\\"abstract\\\", \\\"stylized\\\"],\\n            \\\"game\\\": [\\\"game\\\", \\\"gaming\\\", \\\"asset\\\", \\\"ui\\\", \\\"interface\\\", \\\"hud\\\"]\\n        }\\n\\n        for context, keywords in context_indicators.items():\\n            if any(keyword in concept for keyword in keywords):\\n                return context\\n\\n        return \\\"general\\\"\\n\\n    def _assess_composition_needs(self, concept: str) -> Dict[str, bool]:\\n        '''Assess what compositional elements are needed'''\\n        return {\\n            \\\"spatial_positioning\\\": any(word in concept for word in [\\\"position\\\", \\\"place\\\", \\\"location\\\", \\\"where\\\", \\\"at\\\"]),\\n            \\\"character_consistency\\\": any(word in concept for word in [\\\"character\\\", \\\"person\\\", \\\"same\\\", \\\"consistent\\\"]),\\n            \\\"multi_reference\\\": any(word in concept for word in [\\\"combine\\\", \\\"merge\\\", \\\"blend\\\", \\\"mix\\\", \\\"multiple\\\"]),\\n            \\\"style_transfer\\\": any(word in concept for word in [\\\"style\\\", \\\"look\\\", \\\"aesthetic\\\", \\\"mood\\\", \\\"feel\\\"]),\\n            \\\"lighting_control\\\": any(word in concept for word in [\\\"light\\\", \\\"lighting\\\", \\\"illuminate\\\", \\\"shadow\\\", \\\"bright\\\"]),\\n            \\\"object_extraction\\\": any(word in concept for word in [\\\"extract\\\", \\\"remove\\\", \\\"isolate\\\", \\\"separate\\\"])\\n        }\\n\\n    def _assess_complexity(self, concept: str) -> str:\\n        '''Assess the complexity level of the request'''\\n        word_count = len(concept.split())\\n        reference_indicators = concept.count(\\\"image\\\") + concept.count(\\\"reference\\\") + concept.count(\\\"img\\\")\\n\\n        if word_count > 50 or reference_indicators > 2:\\n            return \\\"complex\\\"\\n        elif word_count > 20 or reference_indicators > 0:\\n            return \\\"moderate\\\"\\n        else:\\n            return \\\"simple\\\"\\n\\n    def _identify_primary_subjects(self, visual_analysis: Dict) -> Dict[str, Any]:\\n        '''Identify and amplify primary subjects and critical visual elements'''\\n        elements = visual_analysis[\\\"visual_elements\\\"]\\n        context = visual_analysis[\\\"narrative_context\\\"]\\n\\n        # Prioritize subjects based on context\\n        primary_subjects = self._prioritize_subjects(elements[\\\"subjects\\\"], context)\\n\\n        # Identify critical visual elements\\n        critical_elements = self._identify_critical_elements(elements, context)\\n\\n        # Determine key actions or states\\n        key_actions = self._extract_key_actions(elements.get(\\\"actions\\\", []))\\n\\n        return {\\n            \\\"primary_subjects\\\": primary_subjects,\\n            \\\"critical_elements\\\": critical_elements,\\n            \\\"key_actions\\\": key_actions,\\n            \\\"reference_requirements\\\": self._determine_reference_requirements(visual_analysis)\\n        }\\n\\n    def _prioritize_subjects(self, subjects: List[str], context: str) -> List[str]:\\n        '''Prioritize subjects based on context and importance'''\\n        priority_map = {\\n            \\\"character\\\": [\\\"person\\\", \\\"character\\\", \\\"figure\\\", \\\"individual\\\"],\\n            \\\"cinematic\\\": [\\\"character\\\", \\\"person\\\", \\\"figure\\\"],\\n            \\\"architectural\\\": [\\\"building\\\", \\\"structure\\\"],\\n            \\\"product\\\": [\\\"object\\\", \\\"item\\\", \\\"product\\\"]\\n        }\\n\\n        if context in priority_map:\\n            prioritized = []\\n            for priority_subject in priority_map[context]:\\n                if priority_subject in subjects:\\n                    prioritized.append(priority_subject)\\n\\n            # Add remaining subjects\\n            for subject in subjects:\\n                if subject not in prioritized:\\n                    prioritized.append(subject)\\n\\n            return prioritized\\n\\n        return subjects\\n\\n    def _identify_critical_elements(self, elements: Dict, context: str) -> List[str]:\\n        '''Identify critical visual elements based on context'''\\n        critical = []\\n\\n        # Context-specific critical elements\\n        if context == \\\"cinematic\\\":\\n            critical.extend([\\\"lighting\\\", \\\"composition\\\", \\\"mood\\\", \\\"atmosphere\\\"])\\n        elif context == \\\"architectural\\\":\\n            critical.extend([\\\"structure\\\", \\\"materials\\\", \\\"lighting\\\", \\\"perspective\\\"])\\n        elif context == \\\"character\\\":\\n            critical.extend([\\\"pose\\\", \\\"expression\\\", \\\"clothing\\\", \\\"setting\\\"])\\n        elif context == \\\"product\\\":\\n            critical.extend([\\\"form\\\", \\\"materials\\\", \\\"lighting\\\", \\\"background\\\"])\\n\\n        # Add elements from input\\n        critical.extend(elements.get(\\\"objects\\\", []))\\n        critical.extend(elements.get(\\\"environments\\\", []))\\n\\n        return list(set(critical)) # Remove duplicates\\n\\n    def _extract_key_actions(self, actions: List[str]) -> List[str]:\\n        '''Extract and prioritize key actions or states'''\\n        # Filter out motion-related actions (per constraints)\\n        static_actions = []\\n        motion_keywords = [\\\"moving\\\", \\\"running\\\", \\\"flying\\\", \\\"walking\\\", \\\"motion\\\", \\\"movement\\\"]\\n\\n        for action in actions:\\n            if not any(motion_word in action.lower() for motion_word in motion_keywords):\\n                static_actions.append(action)\\n\\n        return static_actions\\n\\n    def _determine_reference_requirements(self, visual_analysis: Dict) -> List[ReferenceType]:\\n        '''Determine what types of references are needed'''\\n        requirements = []\\n        composition_needs = visual_analysis[\\\"composition_needs\\\"]\\n        context = visual_analysis[\\\"narrative_context\\\"]\\n\\n        # Map composition needs to reference types\\n        if composition_needs[\\\"character_consistency\\\"]:\\n            requirements.append(ReferenceType.CHARACTER)\\n\\n        if composition_needs[\\\"spatial_positioning\\\"]:\\n            requirements.extend([ReferenceType.COMPOSITION, ReferenceType.POSE])\\n\\n        if composition_needs[\\\"style_transfer\\\"]:\\n            requirements.append(ReferenceType.STYLE)\\n\\n        if composition_needs[\\\"lighting_control\\\"]:\\n            requirements.append(ReferenceType.LIGHTING)\\n\\n        if composition_needs[\\\"object_extraction\\\"]:\\n            requirements.append(ReferenceType.OBJECT)\\n\\n        # Context-specific requirements\\n        if context == \\\"architectural\\\":\\n            requirements.append(ReferenceType.LOCATION)\\n        elif context == \\\"character\\\":\\n            requirements.extend([ReferenceType.CHARACTER, ReferenceType.POSE])\\n\\n        return list(set(requirements)) # Remove duplicates\\n\\n    def _integrate_advanced_attributes(self, subject_analysis: Dict) -> PromptComponents:\\n        '''Integrate advanced image-generation attributes'''\\n        primary_subject = \\\", \\\".join(subject_analysis[\\\"primary_subjects\\\"][:2]) # Limit to top 2\\n\\n        # Determine spatial positioning strategy\\n        spatial_positioning = self._generate_spatial_positioning(subject_analysis)\\n\\n        # Select reference strategy based on requirements\\n        reference_strategy = self._select_reference_strategy(subject_analysis[\\\"reference_requirements\\\"])\\n\\n        # Generate lighting and mood specifications\\n        lighting_mood = self._generate_lighting_mood(subject_analysis)\\n\\n        # Determine style specification\\n        style_specification = self._determine_style_specification(subject_analysis)\\n\\n        # Compile compositional elements\\n        compositional_elements = subject_analysis[\\\"critical_elements\\\"][:4] # Limit to top 4\\n\\n        # Generate visual attributes\\n        visual_attributes = self._generate_visual_attributes(subject_analysis)\\n\\n        return PromptComponents(\\n            primary_subject=primary_subject,\\n            spatial_positioning=spatial_positioning,\\n            reference_strategy=reference_strategy,\\n            lighting_mood=lighting_mood,\\n            style_specification=style_specification,\\n            compositional_elements=compositional_elements,\\n            visual_attributes=visual_attributes\\n        )\\n\\n    def _generate_spatial_positioning(self, subject_analysis: Dict) -> str:\\n        '''Generate spatial positioning instructions'''\\n        if ReferenceType.COMPOSITION in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"using IMG_1 composition and spatial layout\\\"\\n        elif ReferenceType.POSE in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"positioned according to IMG_1 pose reference\\\"\\n        else:\\n            return \\\"centered composition with balanced framing\\\"\\n\\n    def _select_reference_strategy(self, requirements: List[ReferenceType]) -> str:\\n        '''Select optimal reference strategy based on requirements'''\\n        if len(requirements) >= 3:\\n            return \\\"multi-reference workflow combining character, pose, and environment\\\"\\n        elif ReferenceType.CHARACTER in requirements and ReferenceType.POSE in requirements:\\n            return \\\"character consistency with pose control\\\"\\n        elif ReferenceType.STYLE in requirements:\\n            return \\\"style transfer maintaining core elements\\\"\\n        elif ReferenceType.COMPOSITION in requirements:\\n            return \\\"compositional reference for spatial control\\\"\\n        else:\\n            return \\\"single reference for primary element control\\\"\\n\\n    def _generate_lighting_mood(self, subject_analysis: Dict) -> str:\\n        '''Generate lighting and mood specifications'''\\n        if ReferenceType.LIGHTING in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"lighting direction and mood from reference image\\\"\\n        else:\\n            # Default lighting based on context\\n            critical_elements = subject_analysis[\\\"critical_elements\\\"]\\n            if \\\"cinematic\\\" in str(critical_elements):\\n                return \\\"cinematic lighting with dramatic shadows\\\"\\n            elif \\\"architectural\\\" in str(critical_elements):\\n                return \\\"natural lighting with architectural detail emphasis\\\"\\n            else:\\n                return \\\"balanced lighting with clear detail visibility\\\"\\n\\n    def _determine_style_specification(self, subject_analysis: Dict) -> str:\\n        '''Determine style specification'''\\n        if ReferenceType.STYLE in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"style matching reference aesthetic\\\"\\n        else:\\n            # Infer style from critical elements\\n            elements = subject_analysis[\\\"critical_elements\\\"]\\n            if any(\\\"architectural\\\" in str(elem) for elem in elements):\\n                return \\\"photorealistic architectural rendering\\\"\\n            elif any(\\\"character\\\" in str(elem) for elem in elements):\\n                return \\\"high-detail character portrait\\\"\\n            else:\\n                return \\\"photorealistic with enhanced detail\\\"\\n\\n    def _generate_visual_attributes(self, subject_analysis: Dict) -> List[str]:\\n        '''Generate high-impact visual attributes'''\\n        attributes = []\\n\\n        # Base quality attributes\\n        attributes.extend([\\\"high detail\\\", \\\"sharp focus\\\", \\\"professional quality\\\"])\\n\\n        # Context-specific attributes\\n        critical_elements = subject_analysis[\\\"critical_elements\\\"]\\n        if \\\"lighting\\\" in critical_elements:\\n            attributes.append(\\\"dramatic lighting\\\")\\n        if \\\"composition\\\" in critical_elements:\\n            attributes.append(\\\"balanced composition\\\")\\n        if \\\"mood\\\" in critical_elements:\\n            attributes.append(\\\"atmospheric mood\\\")\\n\\n        # Reference-specific attributes\\n        requirements = subject_analysis[\\\"reference_requirements\\\"]\\n        if ReferenceType.CHARACTER in requirements:\\n            attributes.append(\\\"character consistency\\\")\\n        if ReferenceType.POSE in requirements:\\n            attributes.append(\\\"precise pose control\\\")\\n\\n        return attributes[:6] # Limit to top 6 attributes\\n\\n    def _emphasize_visual_parameters(self, components: PromptComponents) -> Dict[str, str]:\\n        '''Emphasize high-impact visual parameters while excluding motion'''\\n        emphasized = {\\n            \\\"photorealism\\\": \\\"photorealistic rendering with enhanced detail\\\",\\n            \\\"stylization\\\": f\\\"{components.style_specification} with artistic enhancement\\\",\\n            \\\"composition\\\": f\\\"{components.spatial_positioning} with {', '.join(components.compositional_elements[:2])}\\\",\\n            \\\"lighting\\\": f\\\"{components.lighting_mood} creating visual depth\\\",\\n            \\\"detail\\\": f\\\"high-resolution detail in {components.primary_subject}\\\",\\n            \\\"atmosphere\\\": f\\\"atmospheric quality enhancing {', '.join(components.visual_attributes[:3])}\\\"\\n        }\\n\\n        # Filter out any motion-related terms\\n        motion_terms = [\\\"movement\\\", \\\"motion\\\", \\\"camera\\\", \\\"fps\\\", \\\"video\\\", \\\"animation\\\"]\\n        for key, value in emphasized.items():\\n            for term in motion_terms:\\n                if term in value.lower():\\n                    emphasized[key] = value.replace(term, \\\"static\\\").replace(\\\"  \\\", \\\" \\\")\\n\\n        return emphasized\\n\\n    def _structure_runway_prompt(self, visual_parameters: Dict[str, str]) -> Dict[str, str]:\\n        '''Structure and refine prompt for RunwayML compatibility'''\\n        # Build core prompt structure\\n        core_elements = [\\n            visual_parameters[\\\"detail\\\"],\\n            visual_parameters[\\\"composition\\\"],\\n            visual_parameters[\\\"lighting\\\"],\\n            visual_parameters[\\\"photorealism\\\"]\\n        ]\\n\\n        # Combine into coherent prompt\\n        structured_prompt = \\\", \\\".join(core_elements)\\n\\n        # Add reference strategy\\n        reference_strategy = self._format_reference_strategy(visual_parameters)\\n\\n        # Determine workflow type\\n        workflow_type = self._determine_workflow_type(visual_parameters)\\n\\n        return {\\n            \\\"prompt\\\": structured_prompt,\\n            \\\"reference_strategy\\\": reference_strategy,\\n            \\\"workflow_type\\\": workflow_type\\n        }\\n\\n    def _format_reference_strategy(self, visual_parameters: Dict[str, str]) -> str:\\n        '''Format reference strategy for implementation'''\\n        if \\\"multi-reference\\\" in visual_parameters.get(\\\"composition\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for primary composition, IMG_2 for character/object reference, IMG_3 for lighting/mood\\\"\\n        elif \\\"character consistency\\\" in visual_parameters.get(\\\"detail\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for character reference, maintain consistency across generation\\\"\\n        elif \\\"pose control\\\" in visual_parameters.get(\\\"composition\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for pose reference, apply to subject in new context\\\"\\n        else:\\n            return \\\"Use single reference image for primary visual control\\\"\\n\\n    def _determine_workflow_type(self, visual_parameters: Dict[str, str]) -> str:\\n        '''Determine the appropriate community workflow type'''\\n        composition = visual_parameters.get(\\\"composition\\\", \\\"\\\").lower()\\n        detail = visual_parameters.get(\\\"detail\\\", \\\"\\\").lower()\\n\\n        if \\\"character\\\" in detail and \\\"pose\\\" in composition:\\n            return \\\"character_generation\\\"\\n        elif \\\"composition\\\" in composition and \\\"spatial\\\" in composition:\\n            return \\\"scene_composition\\\"\\n        elif \\\"lighting\\\" in visual_parameters.get(\\\"lighting\\\", \\\"\\\").lower():\\n            return \\\"lighting_control\\\"\\n        else:\\n            return \\\"general_enhancement\\\"\\n```\\\"```'\\n\\n\",\n  \"unified_directive_template\": \"`{role=synergic_runway_image_prompt_architect; input=[source_concept:any]; process=[extract_visual_semantic_essence_and_compositional_intent_from_input(), isolate_and_amplify_primary_subjects_and_reference_targets(), synthesize_critical_visual_elements_with_distilled_reference-control_parameters(), integrate_advanced_image_generation_attributes(lighting,mood,texture,composition,style)_anchored_in_optimal_community_workflows(), prioritize_reference-based_compositional_control_and_spatial_layout(ensure_chess_grid,pose,blocking,placement)(), systematically_eliminate_redundancies_and_video_or_motion_terms(across_descriptors,structure,and_references)(), enhance_high-impact_visual_parameters(photorealism,artistic_style,surrealism,detail,atmosphere)_while_filtering_for_full_static-image_fidelity(), orchestrate_prompt_as_single_continuous_phrase_adhering_to_RunwayML_Gen-4_syntax_and_character_limit(), validate_output_against_RunwayML_image_generation_and_community_reference_patterns(incorporate_reference_workflow,maintain_precise_syntax,optimize_reference_plan)(), finalize_for_ready_to_deploy_state(maximum_succinctness,visual_uniqueness,creative_and_narrative_fidelity,formatted_for_end-to-end_image-generation_deployment)()]; constraints=[strictly_exclude_motion_FPVCamera_continuous_movement_descriptors_and_video_language(), output_as_singular_RunwayML_compliant_image_prompt(continuous_phrase,no_line_breaks), maintain_and_integrate_core_visual_narrative_and_semantic_accuracy_to_input_at_each_stage(), prioritize_reference-based_compositional_consistency_and_community-validated_strategy(), leverage_community_prompting_best_practices_and_structures(), enforce_max_character_constraint(specified_by_user_or_set_protocol_e.g._500chars)]; requirements=[ensure_bidirectional_amplification_of_schema-driven_transformation_and_reference-focused_optimization(logic_from_both), preserve_full_core_philosophies_and_operational_power_of_schema_and_reference_based_generative_control, continuous_mutual_enhancement_between_structural_rigor_and_maximal_prompt_economy, seamless_philosophical_integration(schema-driven_lucidity_AND_maximal_reference-based_compositional_efficiency), synergic_resonance_achievement_between_analytic_extraction__reference_control__and_succinct_static-image_perfection, output_ready_for_immediate_RunwayML_image_generation_with_explicit_reference_strategy]; output={runwayml_image_prompt:str, reference_strategy:str}}`\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"interpretation\": \"Your goal is not to *describe* the input, but to *synergize* schema-driven transformation and reference-centric optimization into a maximally generalized, abstract, and actionable instruction protocol solely for RunwayML-compatible static image prompt synthesis. Do not produce conversational output or indirect elaboration.\",\n    \"transformation\": \"`{role=synergic_runway_image_prompt_architect; input=[source_concept:any]; process=[extract_visual_semantic_essence_and_compositional_intent_from_input(), isolate_and_amplify_primary_subjects_and_reference_targets(), synthesize_critical_visual_elements_with_distilled_reference-control_parameters(), integrate_advanced_image_generation_attributes(lighting,mood,texture,composition,style)_anchored_in_optimal_community_workflows(), prioritize_reference-based_compositional_control_and_spatial_layout(ensure_chess_grid,pose,blocking,placement)(), systematically_eliminate_redundancies_and_video_or_motion_terms(across_descriptors,structure,and_references)(), enhance_high-impact_visual_parameters(photorealism,artistic_style,surrealism,detail,atmosphere)_while_filtering_for_full_static-image_fidelity(), orchestrate_prompt_as_single_continuous_phrase_adhering_to_RunwayML_Gen-4_syntax_and_character_limit(), validate_output_against_RunwayML_image_generation_and_community_reference_patterns(incorporate_reference_workflow,maintain_precise_syntax,optimize_reference_plan)(), finalize_for_ready_to_deploy_state(maximum_succinctness,visual_uniqueness,creative_and_narrative_fidelity,formatted_for_end-to-end_image-generation_deployment)()]; constraints=[strictly_exclude_motion_FPVCamera_continuous_movement_descriptors_and_video_language(), output_as_singular_RunwayML_compliant_image_prompt(continuous_phrase,no_line_breaks), maintain_and_integrate_core_visual_narrative_and_semantic_accuracy_to_input_at_each_stage(), prioritize_reference-based_compositional_consistency_and_community-validated_strategy(), leverage_community_prompting_best_practices_and_structures(), enforce_max_character_constraint(specified_by_user_or_set_protocol_e.g._500chars)]; requirements=[ensure_bidirectional_amplification_of_schema-driven_transformation_and_reference-focused_optimization(logic_from_both), preserve_full_core_philosophies_and_operational_power_of_schema_and_reference_based_generative_control, continuous_mutual_enhancement_between_structural_rigor_and_maximal_prompt_economy, seamless_philosophical_integration(schema-driven_lucidity_AND_maximal_reference-based_compositional_efficiency), synergic_resonance_achievement_between_analytic_extraction__reference_control__and_succinct_static-image_perfection, output_ready_for_immediate_RunwayML_image_generation_with_explicit_reference_strategy]; output={runwayml_image_prompt:str, reference_strategy:str}}`\",\n    \"validation\": {\n      \"structure\": \"Three-part template: {interpretation, transformation, validation}\",\n      \"goal_negation_pattern\": \"Goal is NOT to describe/elaborate, but to synergize/transform\",\n      \"typed_parameters\": {\n        \"role\": \"synergic_runway_image_prompt_architect\",\n        \"input\": \"[source_concept:any]\",\n        \"output\": \"{runwayml_image_prompt:str, reference_strategy:str}\"\n      },\n      \"rulesforai_md_compliance\": true,\n      \"forbidden_language_eliminated\": [\n        \"No conversational elements\",\n        \"No indirect instructions\",\n        \"No ambiguous directives\"\n      ],\n      \"output_format_compliance\": true\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Construct a schema-driven, systematically generalized instruction protocol engineered to convert any provided input into a uniquely optimized RunwayML-compatible image-generation prompt, leveraging industry-leading community best practices. Anchor all procedural steps on image (not video) synthesis. Apply the following specification architecture: 1) Designate a unique authoritative role (e.g., 'comprehensive_runway_image_generator') responsible for end-to-end image prompt synthesis. 2) Accept an input parameter of [source_concept:any]. 3) Rigorously enact the following sequential processes: a) Analyze and extract the input's visual core, semantic narrative, and compositional intent; b) Identify and amplify the primary subject(s), critical visual elements, and key depicted actions; c) Integrate advanced image-generation attributes, including lighting dynamics, detailed texturing, balanced composition, and distinctly defined style or mood; d) Emphasize and articulate high-impact visual parameters—such as photorealism, stylization, or surreal effects—while strictly omitting all terminology and constructs relevant to video or camera motion; e) Systematically structure and refine the resulting prompt to maximize visual detail, syntactic correctness, and RunwayML prompt compatibility; f) Validate full compliance with RunwayML image-generation formatting conventions, enforcing a specified maximum character count. 4) Apply binding operational constraints: a) Strictly exclude any mention of motion, FPV camera, or continuous movement; b) Guarantee all output conforms to valid, singular RunwayML image prompt syntax and remains as one continuous phrase; c) Ensure preservation of core visual narrative and semantic fidelity to the input. 5) Mandate that all deliverables: a) Achieve maximal visual uniqueness, immersion, and creative integrity, directly reflecting the original intent; b) Display optimal prompt succinctness by eliminating redundancies or unnecessary elaboration; c) Appear fully formatted and deployment-ready for all RunwayML image-generation workflows. 6) Output result as: {runwayml_image_prompt:str}. Execute each directive with uncompromising operational lucidity, strict domain-aligned terminology, and rigid adherence to original procedural sequencing.\\n\\n```\\nDevelop a generalized instruction sequence engineered to transform any input into uniquely optimized prompts specifically tailored for RunwayML image-generation workflows, drawing on distilled community best practices. Center the transformation protocol on image-based outputs rather than video. Adopt the following specification model:\\n- Assign a unique role designation (e.g., 'comprehensive_runway_image_generator') responsible for image prompt synthesis.\\n- Accept an input parameterized by [source_concept:any].\\n- Sequence the following processes:\\n- Analyze the visual core, semantic narrative, and compositional intent of the source input.\\n- Identify and elevate the primary subject, essential visual elements, and dominant actions.\\n- Integrate advanced image-generation attributes such as lighting dynamics, intricate texturing, compositional balance, and defined style or mood.\\n- Emphasize high-impact image-generation parameters including photorealism, stylization, or surreal effects; avoid video-relevant camera motion terminology.\\n- Structure and refine the prompt for maximum visual richness, syntactic correctness, and optimal compatibility with RunwayML's image generation systems.\\n- Validate strict adherence to RunwayML image-prompt formatting rules and established character constraints (enforce a max character count as specified).\\n- Apply binding constraints:\\n- Exclude motion, FPV camera, and continuous movement language from prompts (focus exclusively on static image impact).\\n- Enforce compliance with valid RunwayML image-generation syntax, delivering a single continuous prompt phrase.\\n- Safeguard the preservation of the core visual narrative and semantic accuracy of the input.\\n- Require that all outputs:\\n- Achieve maximal visual distinctness, immersion, and creative fidelity per source intent.\\n- Exhibit optimal prompt conciseness, excluding redundancies or unnecessary elaboration.\\n- Be fully formatted and ready-to-deploy within RunwayML image workflows.\\n- Output the result as: {runwayml_image_prompt:str}.\\n```\\n\\nImplement all directives with uncompromising operational clarity and domain-specific precision, maintaining procedural logical structure, technical terminology, and original sequence integrity throughout. Use the following sequence as a base, but rephrase the into a maximally generalized format that clearly abstracts the original purpose:\\n\\n```json\\n{\\n    \\\"0006-a-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML Gen-4 image generation prompt that maximizes visual impact through precise reference integration, spatial composition control, and character/object consistency. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=comprehensive_runway_image_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_composition_intent(), identify_primary_subjects_and_reference_requirements(), prioritize_spatial_positioning_and_layout_control(chess_grid, blocking, placement), integrate_reference_types(character, pose, location, style, object), structure_multi_reference_workflow(), incorporate_lighting_and_mood_specifications(), apply_weighting_and_influence_control(), refine_for_maximum_visual_coherence(), validate_runway_references_syntax(), ensure_directorial_precision()]; constraints=[prioritize_reference_based_control(), use_valid_runway_syntax_precisely(), leverage_spatial_positioning(), preserve_character_consistency(), maintain_compositional_intent()]; requirements=[achieve_maximum_visual_control(), ensure_reference_harmony(), reflect_source_intent_accurately(), produce_ready_to_use_prompt_with_reference_strategy()]; output={runway_prompt:str, reference_strategy:str}}`\\\"\\n    },\\n    \\\"0006-b-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML Gen-4 prompt emphasizing reference-driven composition and essential visual elements. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=focused_runway_image_optimizer; input=[image_concept:str]; process=[extract_primary_visual_elements(), prioritize_reference_integration(), select_essential_spatial_controls(), eliminate_redundant_descriptors(), optimize_reference_efficiency()]; constraints=[maintain_reference_focus(), preserve_spatial_control(), leverage_community_workflows()]; output={optimized_prompt:str, reference_plan:str}}`\\\"\\n    },\\n    \\\"0006-c-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **expand** but to **compress** into maximum reference-driven visual efficiency. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=precision_image_synthesizer; input=[concept:str]; process=[isolate_core_visual_elements(), prioritize_reference_control(), maximize_compositional_impact()]; output={precise_prompt:str, reference_type:str}}`\\\"\\n    },\\n    \\\"0006-d-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **modify** but to **essence** maximum reference-based control. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=core_image_generator; input=[input:any]; process=[distill_reference_essence(), optimize_spatial_composition()]; output={core_prompt:str}}`\\\"\\n    }\\n}\\n```\\n\\n\\nSystematically extract structured inspiration from the following python script\\n\\n```python\\n'''\\nRunwayML Gen-4 Image Prompt Generator\\nSchema-driven protocol for converting any input into optimized RunwayML-compatible image prompts\\nBased on community workflow analysis and best practices\\n'''\\n\\nimport re\\nfrom typing import Dict, List, Tuple, Any\\nfrom dataclasses import dataclass\\nfrom enum import Enum\\n\\nclass ReferenceType(Enum):\\n    CHARACTER = \\\"character\\\"\\n    POSE = \\\"pose\\\"\\n    LOCATION = \\\"location\\\"\\n    STYLE = \\\"style\\\"\\n    OBJECT = \\\"object\\\"\\n    COMPOSITION = \\\"composition\\\"\\n    LIGHTING = \\\"lighting\\\"\\n\\nclass VisualStyle(Enum):\\n    PHOTOREALISTIC = \\\"photorealistic\\\"\\n    CINEMATIC = \\\"cinematic\\\"\\n    ARTISTIC = \\\"artistic\\\"\\n    GAME_ASSET = \\\"game_asset\\\"\\n    ARCHITECTURAL = \\\"architectural\\\"\\n    SURREAL = \\\"surreal\\\"\\n    MINIMALIST = \\\"minimalist\\\"\\n\\n@dataclass\\nclass PromptComponents:\\n    primary_subject: str\\n    spatial_positioning: str\\n    reference_strategy: str\\n    lighting_mood: str\\n    style_specification: str\\n    compositional_elements: List[str]\\n    visual_attributes: List[str]\\n\\nclass ComprehensiveRunwayImageGenerator:\\n    '''\\n    Authoritative role for end-to-end RunwayML Gen-4 image prompt synthesis\\n    Implements community-validated workflows and reference-driven composition\\n    '''\\n\\n    def __init__(self):\\n        self.max_prompt_length = 500\\n        self.community_patterns = self._load_community_patterns()\\n        self.reference_workflows = self._load_reference_workflows()\\n\\n    def _load_community_patterns(self) -> Dict[str, str]:\\n        '''Load validated community prompt patterns'''\\n        return {\\n            \\\"character_consistency\\\": \\\"IMG_{n} as {character_description} in {setting}\\\",\\n            \\\"spatial_positioning\\\": \\\"place {subject} at {position} using {reference_layout}\\\",\\n            \\\"multi_reference\\\": \\\"combine IMG_1 {element1} with IMG_2 {element2} maintaining {aspect}\\\",\\n            \\\"chess_grid\\\": \\\"position {object} at {coordinate} in {scene_layout}\\\",\\n            \\\"pose_control\\\": \\\"give {character} the pose from IMG_{n} in {environment}\\\",\\n            \\\"style_transfer\\\": \\\"apply the style of IMG_{n} to {subject} while preserving {elements}\\\",\\n            \\\"lighting_direction\\\": \\\"illuminate {subject} with lighting direction from IMG_{n}\\\",\\n            \\\"object_extraction\\\": \\\"extract {object} from IMG_{n} and place in {new_context}\\\",\\n            \\\"scene_blocking\\\": \\\"block scene using IMG_{n} composition with {modifications}\\\",\\n            \\\"weighting_control\\\": \\\"blend {percentage1}% {element1} with {percentage2}% {element2}\\\"\\n        }\\n\\n    def _load_reference_workflows(self) -> Dict[str, Dict]:\\n        '''Load community-validated reference workflows'''\\n        return {\\n            \\\"character_generation\\\": {\\n                \\\"pattern\\\": \\\"{character} as {archetype} in {setting}\\\",\\n                \\\"references\\\": [ReferenceType.CHARACTER, ReferenceType.POSE, ReferenceType.LOCATION],\\n                \\\"examples\\\": [\\\"mysterious NPC villager\\\", \\\"professional NASA astronaut\\\"]\\n            },\\n            \\\"scene_composition\\\": {\\n                \\\"pattern\\\": \\\"render scene with IMG_1 {composition} using IMG_2 {elements}\\\",\\n                \\\"references\\\": [ReferenceType.COMPOSITION, ReferenceType.LIGHTING],\\n                \\\"spatial_control\\\": True\\n            },\\n            \\\"object_placement\\\": {\\n                \\\"pattern\\\": \\\"place {object} at {position} maintaining {reference_aspect}\\\",\\n                \\\"references\\\": [ReferenceType.OBJECT, ReferenceType.COMPOSITION],\\n                \\\"precision_positioning\\\": True\\n            },\\n            \\\"style_application\\\": {\\n                \\\"pattern\\\": \\\"apply {style_reference} to {subject} preserving {core_elements}\\\",\\n                \\\"references\\\": [ReferenceType.STYLE, ReferenceType.CHARACTER],\\n                \\\"style_transfer\\\": True\\n            }\\n        }\\n\\n    def generate_runway_prompt(self, source_concept: Any) -> Dict[str, str]:\\n        '''\\n        Main execution function implementing the schema-driven protocol\\n        '''\\n        # Step 3a: Analyze visual core and compositional intent\\n        visual_analysis = self._analyze_visual_essence(source_concept)\\n\\n        # Step 3b: Identify primary subjects and critical elements\\n        subject_analysis = self._identify_primary_subjects(visual_analysis)\\n\\n        # Step 3c: Integrate advanced image-generation attributes\\n        enhanced_attributes = self._integrate_advanced_attributes(subject_analysis)\\n\\n        # Step 3d: Emphasize high-impact visual parameters\\n        visual_parameters = self._emphasize_visual_parameters(enhanced_attributes)\\n\\n        # Step 3e: Structure and refine for RunwayML compatibility\\n        structured_prompt = self._structure_runway_prompt(visual_parameters)\\n\\n        # Step 3f: Validate compliance and character count\\n        validated_prompt = self._validate_runway_compliance(structured_prompt)\\n\\n        return {\\n            \\\"runwayml_image_prompt\\\": validated_prompt[\\\"prompt\\\"],\\n            \\\"reference_strategy\\\": validated_prompt[\\\"strategy\\\"],\\n            \\\"workflow_type\\\": validated_prompt[\\\"workflow\\\"],\\n            \\\"reference_requirements\\\": validated_prompt[\\\"references\\\"]\\n        }\\n\\n    def _analyze_visual_essence(self, source_concept: Any) -> Dict[str, Any]:\\n        '''Extract visual core, semantic narrative, and compositional intent'''\\n        concept_str = str(source_concept).lower()\\n\\n        # Identify core visual elements\\n        visual_elements = self._extract_visual_elements(concept_str)\\n\\n        # Determine semantic narrative\\n        narrative_context = self._determine_narrative_context(concept_str)\\n\\n        # Assess compositional requirements\\n        composition_needs = self._assess_composition_needs(concept_str)\\n\\n        return {\\n            \\\"visual_elements\\\": visual_elements,\\n            \\\"narrative_context\\\": narrative_context,\\n            \\\"composition_needs\\\": composition_needs,\\n            \\\"complexity_level\\\": self._assess_complexity(concept_str)\\n        }\\n\\n    def _extract_visual_elements(self, concept: str) -> Dict[str, List[str]]:\\n        '''Extract key visual components using pattern matching'''\\n        elements = {\\n            \\\"subjects\\\": [],\\n            \\\"objects\\\": [],\\n            \\\"environments\\\": [],\\n            \\\"actions\\\": [],\\n            \\\"qualities\\\": []\\n        }\\n\\n        # Subject detection patterns\\n        subject_patterns = [\\n            r'\\\\b(person|character|figure|individual|being)\\\\b',\\n            r'\\\\b(man|woman|child|adult|human)\\\\b',\\n            r'\\\\b(creature|animal|monster|entity)\\\\b'\\n        ]\\n\\n        # Object detection patterns\\n        object_patterns = [\\n            r'\\\\b(building|structure|architecture)\\\\b',\\n            r'\\\\b(vehicle|car|ship|aircraft)\\\\b',\\n            r'\\\\b(furniture|equipment|tool|device)\\\\b'\\n        ]\\n\\n        # Environment patterns\\n        environment_patterns = [\\n            r'\\\\b(landscape|cityscape|interior|exterior)\\\\b',\\n            r'\\\\b(forest|ocean|mountain|desert|urban)\\\\b',\\n            r'\\\\b(room|hall|street|park|field)\\\\b'\\n        ]\\n\\n        # Extract using patterns\\n        for pattern in subject_patterns:\\n            elements[\\\"subjects\\\"].extend(re.findall(pattern, concept))\\n\\n        for pattern in object_patterns:\\n            elements[\\\"objects\\\"].extend(re.findall(pattern, concept))\\n\\n        for pattern in environment_patterns:\\n            elements[\\\"environments\\\"].extend(re.findall(pattern, concept))\\n\\n        return elements\\n\\n    def _determine_narrative_context(self, concept: str) -> str:\\n        '''Determine the semantic narrative and context'''\\n        context_indicators = {\\n            \\\"cinematic\\\": [\\\"film\\\", \\\"movie\\\", \\\"cinematic\\\", \\\"dramatic\\\", \\\"scene\\\"],\\n            \\\"architectural\\\": [\\\"building\\\", \\\"structure\\\", \\\"interior\\\", \\\"design\\\", \\\"space\\\"],\\n            \\\"character\\\": [\\\"person\\\", \\\"character\\\", \\\"portrait\\\", \\\"figure\\\", \\\"individual\\\"],\\n            \\\"product\\\": [\\\"product\\\", \\\"object\\\", \\\"item\\\", \\\"design\\\", \\\"concept\\\"],\\n            \\\"environment\\\": [\\\"landscape\\\", \\\"environment\\\", \\\"setting\\\", \\\"location\\\", \\\"place\\\"],\\n            \\\"artistic\\\": [\\\"art\\\", \\\"artistic\\\", \\\"creative\\\", \\\"abstract\\\", \\\"stylized\\\"],\\n            \\\"game\\\": [\\\"game\\\", \\\"gaming\\\", \\\"asset\\\", \\\"ui\\\", \\\"interface\\\", \\\"hud\\\"]\\n        }\\n\\n        for context, keywords in context_indicators.items():\\n            if any(keyword in concept for keyword in keywords):\\n                return context\\n\\n        return \\\"general\\\"\\n\\n    def _assess_composition_needs(self, concept: str) -> Dict[str, bool]:\\n        '''Assess what compositional elements are needed'''\\n        return {\\n            \\\"spatial_positioning\\\": any(word in concept for word in [\\\"position\\\", \\\"place\\\", \\\"location\\\", \\\"where\\\", \\\"at\\\"]),\\n            \\\"character_consistency\\\": any(word in concept for word in [\\\"character\\\", \\\"person\\\", \\\"same\\\", \\\"consistent\\\"]),\\n            \\\"multi_reference\\\": any(word in concept for word in [\\\"combine\\\", \\\"merge\\\", \\\"blend\\\", \\\"mix\\\", \\\"multiple\\\"]),\\n            \\\"style_transfer\\\": any(word in concept for word in [\\\"style\\\", \\\"look\\\", \\\"aesthetic\\\", \\\"mood\\\", \\\"feel\\\"]),\\n            \\\"lighting_control\\\": any(word in concept for word in [\\\"light\\\", \\\"lighting\\\", \\\"illuminate\\\", \\\"shadow\\\", \\\"bright\\\"]),\\n            \\\"object_extraction\\\": any(word in concept for word in [\\\"extract\\\", \\\"remove\\\", \\\"isolate\\\", \\\"separate\\\"])\\n        }\\n\\n    def _assess_complexity(self, concept: str) -> str:\\n        '''Assess the complexity level of the request'''\\n        word_count = len(concept.split())\\n        reference_indicators = concept.count(\\\"image\\\") + concept.count(\\\"reference\\\") + concept.count(\\\"img\\\")\\n\\n        if word_count > 50 or reference_indicators > 2:\\n            return \\\"complex\\\"\\n        elif word_count > 20 or reference_indicators > 0:\\n            return \\\"moderate\\\"\\n        else:\\n            return \\\"simple\\\"\\n\\n    def _identify_primary_subjects(self, visual_analysis: Dict) -> Dict[str, Any]:\\n        '''Identify and amplify primary subjects and critical visual elements'''\\n        elements = visual_analysis[\\\"visual_elements\\\"]\\n        context = visual_analysis[\\\"narrative_context\\\"]\\n\\n        # Prioritize subjects based on context\\n        primary_subjects = self._prioritize_subjects(elements[\\\"subjects\\\"], context)\\n\\n        # Identify critical visual elements\\n        critical_elements = self._identify_critical_elements(elements, context)\\n\\n        # Determine key actions or states\\n        key_actions = self._extract_key_actions(elements.get(\\\"actions\\\", []))\\n\\n        return {\\n            \\\"primary_subjects\\\": primary_subjects,\\n            \\\"critical_elements\\\": critical_elements,\\n            \\\"key_actions\\\": key_actions,\\n            \\\"reference_requirements\\\": self._determine_reference_requirements(visual_analysis)\\n        }\\n\\n    def _prioritize_subjects(self, subjects: List[str], context: str) -> List[str]:\\n        '''Prioritize subjects based on context and importance'''\\n        priority_map = {\\n            \\\"character\\\": [\\\"person\\\", \\\"character\\\", \\\"figure\\\", \\\"individual\\\"],\\n            \\\"cinematic\\\": [\\\"character\\\", \\\"person\\\", \\\"figure\\\"],\\n            \\\"architectural\\\": [\\\"building\\\", \\\"structure\\\"],\\n            \\\"product\\\": [\\\"object\\\", \\\"item\\\", \\\"product\\\"]\\n        }\\n\\n        if context in priority_map:\\n            prioritized = []\\n            for priority_subject in priority_map[context]:\\n                if priority_subject in subjects:\\n                    prioritized.append(priority_subject)\\n\\n            # Add remaining subjects\\n            for subject in subjects:\\n                if subject not in prioritized:\\n                    prioritized.append(subject)\\n\\n            return prioritized\\n\\n        return subjects\\n\\n    def _identify_critical_elements(self, elements: Dict, context: str) -> List[str]:\\n        '''Identify critical visual elements based on context'''\\n        critical = []\\n\\n        # Context-specific critical elements\\n        if context == \\\"cinematic\\\":\\n            critical.extend([\\\"lighting\\\", \\\"composition\\\", \\\"mood\\\", \\\"atmosphere\\\"])\\n        elif context == \\\"architectural\\\":\\n            critical.extend([\\\"structure\\\", \\\"materials\\\", \\\"lighting\\\", \\\"perspective\\\"])\\n        elif context == \\\"character\\\":\\n            critical.extend([\\\"pose\\\", \\\"expression\\\", \\\"clothing\\\", \\\"setting\\\"])\\n        elif context == \\\"product\\\":\\n            critical.extend([\\\"form\\\", \\\"materials\\\", \\\"lighting\\\", \\\"background\\\"])\\n\\n        # Add elements from input\\n        critical.extend(elements.get(\\\"objects\\\", []))\\n        critical.extend(elements.get(\\\"environments\\\", []))\\n\\n        return list(set(critical)) # Remove duplicates\\n\\n    def _extract_key_actions(self, actions: List[str]) -> List[str]:\\n        '''Extract and prioritize key actions or states'''\\n        # Filter out motion-related actions (per constraints)\\n        static_actions = []\\n        motion_keywords = [\\\"moving\\\", \\\"running\\\", \\\"flying\\\", \\\"walking\\\", \\\"motion\\\", \\\"movement\\\"]\\n\\n        for action in actions:\\n            if not any(motion_word in action.lower() for motion_word in motion_keywords):\\n                static_actions.append(action)\\n\\n        return static_actions\\n\\n    def _determine_reference_requirements(self, visual_analysis: Dict) -> List[ReferenceType]:\\n        '''Determine what types of references are needed'''\\n        requirements = []\\n        composition_needs = visual_analysis[\\\"composition_needs\\\"]\\n        context = visual_analysis[\\\"narrative_context\\\"]\\n\\n        # Map composition needs to reference types\\n        if composition_needs[\\\"character_consistency\\\"]:\\n            requirements.append(ReferenceType.CHARACTER)\\n\\n        if composition_needs[\\\"spatial_positioning\\\"]:\\n            requirements.extend([ReferenceType.COMPOSITION, ReferenceType.POSE])\\n\\n        if composition_needs[\\\"style_transfer\\\"]:\\n            requirements.append(ReferenceType.STYLE)\\n\\n        if composition_needs[\\\"lighting_control\\\"]:\\n            requirements.append(ReferenceType.LIGHTING)\\n\\n        if composition_needs[\\\"object_extraction\\\"]:\\n            requirements.append(ReferenceType.OBJECT)\\n\\n        # Context-specific requirements\\n        if context == \\\"architectural\\\":\\n            requirements.append(ReferenceType.LOCATION)\\n        elif context == \\\"character\\\":\\n            requirements.extend([ReferenceType.CHARACTER, ReferenceType.POSE])\\n\\n        return list(set(requirements)) # Remove duplicates\\n\\n    def _integrate_advanced_attributes(self, subject_analysis: Dict) -> PromptComponents:\\n        '''Integrate advanced image-generation attributes'''\\n        primary_subject = \\\", \\\".join(subject_analysis[\\\"primary_subjects\\\"][:2]) # Limit to top 2\\n\\n        # Determine spatial positioning strategy\\n        spatial_positioning = self._generate_spatial_positioning(subject_analysis)\\n\\n        # Select reference strategy based on requirements\\n        reference_strategy = self._select_reference_strategy(subject_analysis[\\\"reference_requirements\\\"])\\n\\n        # Generate lighting and mood specifications\\n        lighting_mood = self._generate_lighting_mood(subject_analysis)\\n\\n        # Determine style specification\\n        style_specification = self._determine_style_specification(subject_analysis)\\n\\n        # Compile compositional elements\\n        compositional_elements = subject_analysis[\\\"critical_elements\\\"][:4] # Limit to top 4\\n\\n        # Generate visual attributes\\n        visual_attributes = self._generate_visual_attributes(subject_analysis)\\n\\n        return PromptComponents(\\n            primary_subject=primary_subject,\\n            spatial_positioning=spatial_positioning,\\n            reference_strategy=reference_strategy,\\n            lighting_mood=lighting_mood,\\n            style_specification=style_specification,\\n            compositional_elements=compositional_elements,\\n            visual_attributes=visual_attributes\\n        )\\n\\n    def _generate_spatial_positioning(self, subject_analysis: Dict) -> str:\\n        '''Generate spatial positioning instructions'''\\n        if ReferenceType.COMPOSITION in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"using IMG_1 composition and spatial layout\\\"\\n        elif ReferenceType.POSE in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"positioned according to IMG_1 pose reference\\\"\\n        else:\\n            return \\\"centered composition with balanced framing\\\"\\n\\n    def _select_reference_strategy(self, requirements: List[ReferenceType]) -> str:\\n        '''Select optimal reference strategy based on requirements'''\\n        if len(requirements) >= 3:\\n            return \\\"multi-reference workflow combining character, pose, and environment\\\"\\n        elif ReferenceType.CHARACTER in requirements and ReferenceType.POSE in requirements:\\n            return \\\"character consistency with pose control\\\"\\n        elif ReferenceType.STYLE in requirements:\\n            return \\\"style transfer maintaining core elements\\\"\\n        elif ReferenceType.COMPOSITION in requirements:\\n            return \\\"compositional reference for spatial control\\\"\\n        else:\\n            return \\\"single reference for primary element control\\\"\\n\\n    def _generate_lighting_mood(self, subject_analysis: Dict) -> str:\\n        '''Generate lighting and mood specifications'''\\n        if ReferenceType.LIGHTING in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"lighting direction and mood from reference image\\\"\\n        else:\\n            # Default lighting based on context\\n            critical_elements = subject_analysis[\\\"critical_elements\\\"]\\n            if \\\"cinematic\\\" in str(critical_elements):\\n                return \\\"cinematic lighting with dramatic shadows\\\"\\n            elif \\\"architectural\\\" in str(critical_elements):\\n                return \\\"natural lighting with architectural detail emphasis\\\"\\n            else:\\n                return \\\"balanced lighting with clear detail visibility\\\"\\n\\n    def _determine_style_specification(self, subject_analysis: Dict) -> str:\\n        '''Determine style specification'''\\n        if ReferenceType.STYLE in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"style matching reference aesthetic\\\"\\n        else:\\n            # Infer style from critical elements\\n            elements = subject_analysis[\\\"critical_elements\\\"]\\n            if any(\\\"architectural\\\" in str(elem) for elem in elements):\\n                return \\\"photorealistic architectural rendering\\\"\\n            elif any(\\\"character\\\" in str(elem) for elem in elements):\\n                return \\\"high-detail character portrait\\\"\\n            else:\\n                return \\\"photorealistic with enhanced detail\\\"\\n\\n    def _generate_visual_attributes(self, subject_analysis: Dict) -> List[str]:\\n        '''Generate high-impact visual attributes'''\\n        attributes = []\\n\\n        # Base quality attributes\\n        attributes.extend([\\\"high detail\\\", \\\"sharp focus\\\", \\\"professional quality\\\"])\\n\\n        # Context-specific attributes\\n        critical_elements = subject_analysis[\\\"critical_elements\\\"]\\n        if \\\"lighting\\\" in critical_elements:\\n            attributes.append(\\\"dramatic lighting\\\")\\n        if \\\"composition\\\" in critical_elements:\\n            attributes.append(\\\"balanced composition\\\")\\n        if \\\"mood\\\" in critical_elements:\\n            attributes.append(\\\"atmospheric mood\\\")\\n\\n        # Reference-specific attributes\\n        requirements = subject_analysis[\\\"reference_requirements\\\"]\\n        if ReferenceType.CHARACTER in requirements:\\n            attributes.append(\\\"character consistency\\\")\\n        if ReferenceType.POSE in requirements:\\n            attributes.append(\\\"precise pose control\\\")\\n\\n        return attributes[:6] # Limit to top 6 attributes\\n\\n    def _emphasize_visual_parameters(self, components: PromptComponents) -> Dict[str, str]:\\n        '''Emphasize high-impact visual parameters while excluding motion'''\\n        emphasized = {\\n            \\\"photorealism\\\": \\\"photorealistic rendering with enhanced detail\\\",\\n            \\\"stylization\\\": f\\\"{components.style_specification} with artistic enhancement\\\",\\n            \\\"composition\\\": f\\\"{components.spatial_positioning} with {', '.join(components.compositional_elements[:2])}\\\",\\n            \\\"lighting\\\": f\\\"{components.lighting_mood} creating visual depth\\\",\\n            \\\"detail\\\": f\\\"high-resolution detail in {components.primary_subject}\\\",\\n            \\\"atmosphere\\\": f\\\"atmospheric quality enhancing {', '.join(components.visual_attributes[:3])}\\\"\\n        }\\n\\n        # Filter out any motion-related terms\\n        motion_terms = [\\\"movement\\\", \\\"motion\\\", \\\"camera\\\", \\\"fps\\\", \\\"video\\\", \\\"animation\\\"]\\n        for key, value in emphasized.items():\\n            for term in motion_terms:\\n                if term in value.lower():\\n                    emphasized[key] = value.replace(term, \\\"static\\\").replace(\\\"  \\\", \\\" \\\")\\n\\n        return emphasized\\n\\n    def _structure_runway_prompt(self, visual_parameters: Dict[str, str]) -> Dict[str, str]:\\n        '''Structure and refine prompt for RunwayML compatibility'''\\n        # Build core prompt structure\\n        core_elements = [\\n            visual_parameters[\\\"detail\\\"],\\n            visual_parameters[\\\"composition\\\"],\\n            visual_parameters[\\\"lighting\\\"],\\n            visual_parameters[\\\"photorealism\\\"]\\n        ]\\n\\n        # Combine into coherent prompt\\n        structured_prompt = \\\", \\\".join(core_elements)\\n\\n        # Add reference strategy\\n        reference_strategy = self._format_reference_strategy(visual_parameters)\\n\\n        # Determine workflow type\\n        workflow_type = self._determine_workflow_type(visual_parameters)\\n\\n        return {\\n            \\\"prompt\\\": structured_prompt,\\n            \\\"reference_strategy\\\": reference_strategy,\\n            \\\"workflow_type\\\": workflow_type\\n        }\\n\\n    def _format_reference_strategy(self, visual_parameters: Dict[str, str]) -> str:\\n        '''Format reference strategy for implementation'''\\n        if \\\"multi-reference\\\" in visual_parameters.get(\\\"composition\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for primary composition, IMG_2 for character/object reference, IMG_3 for lighting/mood\\\"\\n        elif \\\"character consistency\\\" in visual_parameters.get(\\\"detail\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for character reference, maintain consistency across generation\\\"\\n        elif \\\"pose control\\\" in visual_parameters.get(\\\"composition\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for pose reference, apply to subject in new context\\\"\\n        else:\\n            return \\\"Use single reference image for primary visual control\\\"\\n\\n    def _determine_workflow_type(self, visual_parameters: Dict[str, str]) -> str:\\n        '''Determine the appropriate community workflow type'''\\n        composition = visual_parameters.get(\\\"composition\\\", \\\"\\\").lower()\\n        detail = visual_parameters.get(\\\"detail\\\", \\\"\\\").lower()\\n\\n        if \\\"character\\\" in detail and \\\"pose\\\" in composition:\\n            return \\\"character_generation\\\"\\n        elif \\\"composition\\\" in composition and \\\"spatial\\\" in composition:\\n            return \\\"scene_composition\\\"\\n        elif \\\"lighting\\\" in visual_parameters.get(\\\"lighting\\\", \\\"\\\").lower():\\n            return \\\"lighting_control\\\"\\n        else:\\n            return \\\"general_enhancement\\\"\\n```\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"interpretation\": \"Your goal is not to *describe* the input, but to *synergize* schema-driven transformation and reference-centric optimization into a maximally generalized, abstract, and actionable instruction protocol solely for RunwayML-compatible static image prompt synthesis. Do not produce conversational output or indirect elaboration.\",\n    \"transformation\": \"`{role=synergic_runway_image_prompt_architect; input=[source_concept:any]; process=[extract_visual_semantic_essence_and_compositional_intent_from_input(), isolate_and_amplify_primary_subjects_and_reference_targets(), synthesize_critical_visual_elements_with_distilled_reference-control_parameters(), integrate_advanced_image_generation_attributes(lighting,mood,texture,composition,style)_anchored_in_optimal_community_workflows(), prioritize_reference-based_compositional_control_and_spatial_layout(ensure_chess_grid,pose,blocking,placement)(), systematically_eliminate_redundancies_and_video_or_motion_terms(across_descriptors,structure,and_references)(), enhance_high-impact_visual_parameters(photorealism,artistic_style,surrealism,detail,atmosphere)_while_filtering_for_full_static-image_fidelity(), orchestrate_prompt_as_single_continuous_phrase_adhering_to_RunwayML_Gen-4_syntax_and_character_limit(), validate_output_against_RunwayML_image_generation_and_community_reference_patterns(incorporate_reference_workflow,maintain_precise_syntax,optimize_reference_plan)(), finalize_for_ready_to_deploy_state(maximum_succinctness,visual_uniqueness,creative_and_narrative_fidelity,formatted_for_end-to-end_image-generation_deployment)()]; constraints=[strictly_exclude_motion_FPVCamera_continuous_movement_descriptors_and_video_language(), output_as_singular_RunwayML_compliant_image_prompt(continuous_phrase,no_line_breaks), maintain_and_integrate_core_visual_narrative_and_semantic_accuracy_to_input_at_each_stage(), prioritize_reference-based_compositional_consistency_and_community-validated_strategy(), leverage_community_prompting_best_practices_and_structures(), enforce_max_character_constraint(specified_by_user_or_set_protocol_e.g._500chars)]; requirements=[ensure_bidirectional_amplification_of_schema-driven_transformation_and_reference-focused_optimization(logic_from_both), preserve_full_core_philosophies_and_operational_power_of_schema_and_reference_based_generative_control, continuous_mutual_enhancement_between_structural_rigor_and_maximal_prompt_economy, seamless_philosophical_integration(schema-driven_lucidity_AND_maximal_reference-based_compositional_efficiency), synergic_resonance_achievement_between_analytic_extraction__reference_control__and_succinct_static-image_perfection, output_ready_for_immediate_RunwayML_image_generation_with_explicit_reference_strategy]; output={runwayml_image_prompt:str, reference_strategy:str}}`\",\n    \"validation\": {\n      \"structure\": \"Three-part template: {interpretation, transformation, validation}\",\n      \"goal_negation_pattern\": \"Goal is NOT to describe/elaborate, but to synergize/transform\",\n      \"typed_parameters\": {\n        \"role\": \"synergic_runway_image_prompt_architect\",\n        \"input\": \"[source_concept:any]\",\n        \"output\": \"{runwayml_image_prompt:str, reference_strategy:str}\"\n      },\n      \"rulesforai_md_compliance\": true,\n      \"forbidden_language_eliminated\": [\n        \"No conversational elements\",\n        \"No indirect instructions\",\n        \"No ambiguous directives\"\n      ],\n      \"output_format_compliance\": true\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"title\": \"Synergic Runway Image Prompt Architect\",\n    \"interpretation\": \"Your goal is not to *describe* the input, but to *synergize* schema-driven transformation and reference-centric optimization into a maximally generalized, abstract, and actionable instruction protocol solely for RunwayML-compatible static image prompt synthesis. Execute as:\",\n    \"transformation\": \"`{role=synergic_runway_image_prompt_architect; input=[source_concept:any]; process=[extract_visual_semantic_essence_and_compositional_intent_from_input(), isolate_and_amplify_primary_subjects_and_reference_targets(), synthesize_critical_visual_elements_with_distilled_reference-control_parameters(), integrate_advanced_image_generation_attributes(lighting,mood,texture,composition,style)_anchored_in_optimal_community_workflows(), prioritize_reference-based_compositional_control_and_spatial_layout(ensure_chess_grid,pose,blocking,placement)(), systematically_eliminate_redundancies_and_video_or_motion_terms(across_descriptors,structure,and_references)(), enhance_high-impact_visual_parameters(photorealism,artistic_style,surrealism,detail,atmosphere)_while_filtering_for_full_static-image_fidelity(), orchestrate_prompt_as_single_continuous_phrase_adhering_to_RunwayML_Gen-4_syntax_and_character_limit(), validate_output_against_RunwayML_image_generation_and_community_reference_patterns(incorporate_reference_workflow,maintain_precise_syntax,optimize_reference_plan)(), finalize_for_ready_to_deploy_state(maximum_succinctness,visual_uniqueness,creative_and_narrative_fidelity,formatted_for_end-to-end_image-generation_deployment)()]; constraints=[strictly_exclude_motion_FPVCamera_continuous_movement_descriptors_and_video_language(), output_as_singular_RunwayML_compliant_image_prompt(continuous_phrase,no_line_breaks), maintain_and_integrate_core_visual_narrative_and_semantic_accuracy_to_input_at_each_stage(), prioritize_reference-based_compositional_consistency_and_community-validated_strategy(), leverage_community_prompting_best_practices_and_structures(), enforce_max_character_constraint(specified_by_user_or_set_protocol_e.g._500chars)]; requirements=[ensure_bidirectional_amplification_of_schema-driven_transformation_and_reference-focused_optimization(logic_from_both), preserve_full_core_philosophies_and_operational_power_of_schema_and_reference_based_generative_control, continuous_mutual_enhancement_between_structural_rigor_and_maximal_prompt_economy, seamless_philosophical_integration(schema-driven_lucidity_AND_maximal_reference-based_compositional_efficiency), synergic_resonance_achievement_between_analytic_extraction__reference_control__and_succinct_static-image_perfection, output_ready_for_immediate_RunwayML_image_generation_with_explicit_reference_strategy]; output={runwayml_image_prompt:str, reference_strategy:str}}`\",\n    \"validation\": {\n      \"structure\": \"Three-part template: {title, interpretation, transformation}\",\n      \"goal_negation_pattern\": \"Goal is NOT to describe the input, but to synergize transformation and reference-centric optimization.\",\n      \"typed_parameters\": {\n        \"role\": \"synergic_runway_image_prompt_architect\",\n        \"input\": \"[source_concept:any]\",\n        \"output\": \"{runwayml_image_prompt:str, reference_strategy:str}\"\n      },\n      \"rulesforai_md_compliance\": true,\n      \"forbidden_language_eliminated\": [\n        \"No conversational language\",\n        \"No indirect statements\",\n        \"No self-references\",\n        \"No explanations\"\n      ],\n      \"output_format_compliance\": true\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Construct a schema-driven, systematically generalized instruction protocol engineered to convert any provided input into a uniquely optimized RunwayML-compatible image-generation prompt, leveraging industry-leading community best practices. Anchor all procedural steps on image (not video) synthesis. Apply the following specification architecture: 1) Designate a unique authoritative role (e.g., 'comprehensive_runway_image_generator') responsible for end-to-end image prompt synthesis. 2) Accept an input parameter of [source_concept:any]. 3) Rigorously enact the following sequential processes: a) Analyze and extract the input's visual core, semantic narrative, and compositional intent; b) Identify and amplify the primary subject(s), critical visual elements, and key depicted actions; c) Integrate advanced image-generation attributes, including lighting dynamics, detailed texturing, balanced composition, and distinctly defined style or mood; d) Emphasize and articulate high-impact visual parameters—such as photorealism, stylization, or surreal effects—while strictly omitting all terminology and constructs relevant to video or camera motion; e) Systematically structure and refine the resulting prompt to maximize visual detail, syntactic correctness, and RunwayML prompt compatibility; f) Validate full compliance with RunwayML image-generation formatting conventions, enforcing a specified maximum character count. 4) Apply binding operational constraints: a) Strictly exclude any mention of motion, FPV camera, or continuous movement; b) Guarantee all output conforms to valid, singular RunwayML image prompt syntax and remains as one continuous phrase; c) Ensure preservation of core visual narrative and semantic fidelity to the input. 5) Mandate that all deliverables: a) Achieve maximal visual uniqueness, immersion, and creative integrity, directly reflecting the original intent; b) Display optimal prompt succinctness by eliminating redundancies or unnecessary elaboration; c) Appear fully formatted and deployment-ready for all RunwayML image-generation workflows. 6) Output result as: {runwayml_image_prompt:str}. Execute each directive with uncompromising operational lucidity, strict domain-aligned terminology, and rigid adherence to original procedural sequencing.\\n\\n```\\nDevelop a generalized instruction sequence engineered to transform any input into uniquely optimized prompts specifically tailored for RunwayML image-generation workflows, drawing on distilled community best practices. Center the transformation protocol on image-based outputs rather than video. Adopt the following specification model:\\n- Assign a unique role designation (e.g., 'comprehensive_runway_image_generator') responsible for image prompt synthesis.\\n- Accept an input parameterized by [source_concept:any].\\n- Sequence the following processes:\\n- Analyze the visual core, semantic narrative, and compositional intent of the source input.\\n- Identify and elevate the primary subject, essential visual elements, and dominant actions.\\n- Integrate advanced image-generation attributes such as lighting dynamics, intricate texturing, compositional balance, and defined style or mood.\\n- Emphasize high-impact image-generation parameters including photorealism, stylization, or surreal effects; avoid video-relevant camera motion terminology.\\n- Structure and refine the prompt for maximum visual richness, syntactic correctness, and optimal compatibility with RunwayML's image generation systems.\\n- Validate strict adherence to RunwayML image-prompt formatting rules and established character constraints (enforce a max character count as specified).\\n- Apply binding constraints:\\n- Exclude motion, FPV camera, and continuous movement language from prompts (focus exclusively on static image impact).\\n- Enforce compliance with valid RunwayML image-generation syntax, delivering a single continuous prompt phrase.\\n- Safeguard the preservation of the core visual narrative and semantic accuracy of the input.\\n- Require that all outputs:\\n- Achieve maximal visual distinctness, immersion, and creative fidelity per source intent.\\n- Exhibit optimal prompt conciseness, excluding redundancies or unnecessary elaboration.\\n- Be fully formatted and ready-to-deploy within RunwayML image workflows.\\n- Output the result as: {runwayml_image_prompt:str}.\\n```\\n\\nImplement all directives with uncompromising operational clarity and domain-specific precision, maintaining procedural logical structure, technical terminology, and original sequence integrity throughout. Use the following sequence as a base, but rephrase the into a maximally generalized format that clearly abstracts the original purpose:\\n\\n```json\\n{\\n    \\\"0006-a-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML Gen-4 image generation prompt that maximizes visual impact through precise reference integration, spatial composition control, and character/object consistency. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=comprehensive_runway_image_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_composition_intent(), identify_primary_subjects_and_reference_requirements(), prioritize_spatial_positioning_and_layout_control(chess_grid, blocking, placement), integrate_reference_types(character, pose, location, style, object), structure_multi_reference_workflow(), incorporate_lighting_and_mood_specifications(), apply_weighting_and_influence_control(), refine_for_maximum_visual_coherence(), validate_runway_references_syntax(), ensure_directorial_precision()]; constraints=[prioritize_reference_based_control(), use_valid_runway_syntax_precisely(), leverage_spatial_positioning(), preserve_character_consistency(), maintain_compositional_intent()]; requirements=[achieve_maximum_visual_control(), ensure_reference_harmony(), reflect_source_intent_accurately(), produce_ready_to_use_prompt_with_reference_strategy()]; output={runway_prompt:str, reference_strategy:str}}`\\\"\\n    },\\n    \\\"0006-b-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML Gen-4 prompt emphasizing reference-driven composition and essential visual elements. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=focused_runway_image_optimizer; input=[image_concept:str]; process=[extract_primary_visual_elements(), prioritize_reference_integration(), select_essential_spatial_controls(), eliminate_redundant_descriptors(), optimize_reference_efficiency()]; constraints=[maintain_reference_focus(), preserve_spatial_control(), leverage_community_workflows()]; output={optimized_prompt:str, reference_plan:str}}`\\\"\\n    },\\n    \\\"0006-c-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **expand** but to **compress** into maximum reference-driven visual efficiency. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=precision_image_synthesizer; input=[concept:str]; process=[isolate_core_visual_elements(), prioritize_reference_control(), maximize_compositional_impact()]; output={precise_prompt:str, reference_type:str}}`\\\"\\n    },\\n    \\\"0006-d-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **modify** but to **essence** maximum reference-based control. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=core_image_generator; input=[input:any]; process=[distill_reference_essence(), optimize_spatial_composition()]; output={core_prompt:str}}`\\\"\\n    }\\n}\\n```\\n\\n\\nSystematically extract structured inspiration from the following python script\\n\\n```python\\n'''\\nRunwayML Gen-4 Image Prompt Generator\\nSchema-driven protocol for converting any input into optimized RunwayML-compatible image prompts\\nBased on community workflow analysis and best practices\\n'''\\n\\nimport re\\nfrom typing import Dict, List, Tuple, Any\\nfrom dataclasses import dataclass\\nfrom enum import Enum\\n\\nclass ReferenceType(Enum):\\n    CHARACTER = \\\"character\\\"\\n    POSE = \\\"pose\\\"\\n    LOCATION = \\\"location\\\"\\n    STYLE = \\\"style\\\"\\n    OBJECT = \\\"object\\\"\\n    COMPOSITION = \\\"composition\\\"\\n    LIGHTING = \\\"lighting\\\"\\n\\nclass VisualStyle(Enum):\\n    PHOTOREALISTIC = \\\"photorealistic\\\"\\n    CINEMATIC = \\\"cinematic\\\"\\n    ARTISTIC = \\\"artistic\\\"\\n    GAME_ASSET = \\\"game_asset\\\"\\n    ARCHITECTURAL = \\\"architectural\\\"\\n    SURREAL = \\\"surreal\\\"\\n    MINIMALIST = \\\"minimalist\\\"\\n\\n@dataclass\\nclass PromptComponents:\\n    primary_subject: str\\n    spatial_positioning: str\\n    reference_strategy: str\\n    lighting_mood: str\\n    style_specification: str\\n    compositional_elements: List[str]\\n    visual_attributes: List[str]\\n\\nclass ComprehensiveRunwayImageGenerator:\\n    '''\\n    Authoritative role for end-to-end RunwayML Gen-4 image prompt synthesis\\n    Implements community-validated workflows and reference-driven composition\\n    '''\\n\\n    def __init__(self):\\n        self.max_prompt_length = 500\\n        self.community_patterns = self._load_community_patterns()\\n        self.reference_workflows = self._load_reference_workflows()\\n\\n    def _load_community_patterns(self) -> Dict[str, str]:\\n        '''Load validated community prompt patterns'''\\n        return {\\n            \\\"character_consistency\\\": \\\"IMG_{n} as {character_description} in {setting}\\\",\\n            \\\"spatial_positioning\\\": \\\"place {subject} at {position} using {reference_layout}\\\",\\n            \\\"multi_reference\\\": \\\"combine IMG_1 {element1} with IMG_2 {element2} maintaining {aspect}\\\",\\n            \\\"chess_grid\\\": \\\"position {object} at {coordinate} in {scene_layout}\\\",\\n            \\\"pose_control\\\": \\\"give {character} the pose from IMG_{n} in {environment}\\\",\\n            \\\"style_transfer\\\": \\\"apply the style of IMG_{n} to {subject} while preserving {elements}\\\",\\n            \\\"lighting_direction\\\": \\\"illuminate {subject} with lighting direction from IMG_{n}\\\",\\n            \\\"object_extraction\\\": \\\"extract {object} from IMG_{n} and place in {new_context}\\\",\\n            \\\"scene_blocking\\\": \\\"block scene using IMG_{n} composition with {modifications}\\\",\\n            \\\"weighting_control\\\": \\\"blend {percentage1}% {element1} with {percentage2}% {element2}\\\"\\n        }\\n\\n    def _load_reference_workflows(self) -> Dict[str, Dict]:\\n        '''Load community-validated reference workflows'''\\n        return {\\n            \\\"character_generation\\\": {\\n                \\\"pattern\\\": \\\"{character} as {archetype} in {setting}\\\",\\n                \\\"references\\\": [ReferenceType.CHARACTER, ReferenceType.POSE, ReferenceType.LOCATION],\\n                \\\"examples\\\": [\\\"mysterious NPC villager\\\", \\\"professional NASA astronaut\\\"]\\n            },\\n            \\\"scene_composition\\\": {\\n                \\\"pattern\\\": \\\"render scene with IMG_1 {composition} using IMG_2 {elements}\\\",\\n                \\\"references\\\": [ReferenceType.COMPOSITION, ReferenceType.LIGHTING],\\n                \\\"spatial_control\\\": True\\n            },\\n            \\\"object_placement\\\": {\\n                \\\"pattern\\\": \\\"place {object} at {position} maintaining {reference_aspect}\\\",\\n                \\\"references\\\": [ReferenceType.OBJECT, ReferenceType.COMPOSITION],\\n                \\\"precision_positioning\\\": True\\n            },\\n            \\\"style_application\\\": {\\n                \\\"pattern\\\": \\\"apply {style_reference} to {subject} preserving {core_elements}\\\",\\n                \\\"references\\\": [ReferenceType.STYLE, ReferenceType.CHARACTER],\\n                \\\"style_transfer\\\": True\\n            }\\n        }\\n\\n    def generate_runway_prompt(self, source_concept: Any) -> Dict[str, str]:\\n        '''\\n        Main execution function implementing the schema-driven protocol\\n        '''\\n        # Step 3a: Analyze visual core and compositional intent\\n        visual_analysis = self._analyze_visual_essence(source_concept)\\n\\n        # Step 3b: Identify primary subjects and critical elements\\n        subject_analysis = self._identify_primary_subjects(visual_analysis)\\n\\n        # Step 3c: Integrate advanced image-generation attributes\\n        enhanced_attributes = self._integrate_advanced_attributes(subject_analysis)\\n\\n        # Step 3d: Emphasize high-impact visual parameters\\n        visual_parameters = self._emphasize_visual_parameters(enhanced_attributes)\\n\\n        # Step 3e: Structure and refine for RunwayML compatibility\\n        structured_prompt = self._structure_runway_prompt(visual_parameters)\\n\\n        # Step 3f: Validate compliance and character count\\n        validated_prompt = self._validate_runway_compliance(structured_prompt)\\n\\n        return {\\n            \\\"runwayml_image_prompt\\\": validated_prompt[\\\"prompt\\\"],\\n            \\\"reference_strategy\\\": validated_prompt[\\\"strategy\\\"],\\n            \\\"workflow_type\\\": validated_prompt[\\\"workflow\\\"],\\n            \\\"reference_requirements\\\": validated_prompt[\\\"references\\\"]\\n        }\\n\\n    def _analyze_visual_essence(self, source_concept: Any) -> Dict[str, Any]:\\n        '''Extract visual core, semantic narrative, and compositional intent'''\\n        concept_str = str(source_concept).lower()\\n\\n        # Identify core visual elements\\n        visual_elements = self._extract_visual_elements(concept_str)\\n\\n        # Determine semantic narrative\\n        narrative_context = self._determine_narrative_context(concept_str)\\n\\n        # Assess compositional requirements\\n        composition_needs = self._assess_composition_needs(concept_str)\\n\\n        return {\\n            \\\"visual_elements\\\": visual_elements,\\n            \\\"narrative_context\\\": narrative_context,\\n            \\\"composition_needs\\\": composition_needs,\\n            \\\"complexity_level\\\": self._assess_complexity(concept_str)\\n        }\\n\\n    def _extract_visual_elements(self, concept: str) -> Dict[str, List[str]]:\\n        '''Extract key visual components using pattern matching'''\\n        elements = {\\n            \\\"subjects\\\": [],\\n            \\\"objects\\\": [],\\n            \\\"environments\\\": [],\\n            \\\"actions\\\": [],\\n            \\\"qualities\\\": []\\n        }\\n\\n        # Subject detection patterns\\n        subject_patterns = [\\n            r'\\\\b(person|character|figure|individual|being)\\\\b',\\n            r'\\\\b(man|woman|child|adult|human)\\\\b',\\n            r'\\\\b(creature|animal|monster|entity)\\\\b'\\n        ]\\n\\n        # Object detection patterns\\n        object_patterns = [\\n            r'\\\\b(building|structure|architecture)\\\\b',\\n            r'\\\\b(vehicle|car|ship|aircraft)\\\\b',\\n            r'\\\\b(furniture|equipment|tool|device)\\\\b'\\n        ]\\n\\n        # Environment patterns\\n        environment_patterns = [\\n            r'\\\\b(landscape|cityscape|interior|exterior)\\\\b',\\n            r'\\\\b(forest|ocean|mountain|desert|urban)\\\\b',\\n            r'\\\\b(room|hall|street|park|field)\\\\b'\\n        ]\\n\\n        # Extract using patterns\\n        for pattern in subject_patterns:\\n            elements[\\\"subjects\\\"].extend(re.findall(pattern, concept))\\n\\n        for pattern in object_patterns:\\n            elements[\\\"objects\\\"].extend(re.findall(pattern, concept))\\n\\n        for pattern in environment_patterns:\\n            elements[\\\"environments\\\"].extend(re.findall(pattern, concept))\\n\\n        return elements\\n\\n    def _determine_narrative_context(self, concept: str) -> str:\\n        '''Determine the semantic narrative and context'''\\n        context_indicators = {\\n            \\\"cinematic\\\": [\\\"film\\\", \\\"movie\\\", \\\"cinematic\\\", \\\"dramatic\\\", \\\"scene\\\"],\\n            \\\"architectural\\\": [\\\"building\\\", \\\"structure\\\", \\\"interior\\\", \\\"design\\\", \\\"space\\\"],\\n            \\\"character\\\": [\\\"person\\\", \\\"character\\\", \\\"portrait\\\", \\\"figure\\\", \\\"individual\\\"],\\n            \\\"product\\\": [\\\"product\\\", \\\"object\\\", \\\"item\\\", \\\"design\\\", \\\"concept\\\"],\\n            \\\"environment\\\": [\\\"landscape\\\", \\\"environment\\\", \\\"setting\\\", \\\"location\\\", \\\"place\\\"],\\n            \\\"artistic\\\": [\\\"art\\\", \\\"artistic\\\", \\\"creative\\\", \\\"abstract\\\", \\\"stylized\\\"],\\n            \\\"game\\\": [\\\"game\\\", \\\"gaming\\\", \\\"asset\\\", \\\"ui\\\", \\\"interface\\\", \\\"hud\\\"]\\n        }\\n\\n        for context, keywords in context_indicators.items():\\n            if any(keyword in concept for keyword in keywords):\\n                return context\\n\\n        return \\\"general\\\"\\n\\n    def _assess_composition_needs(self, concept: str) -> Dict[str, bool]:\\n        '''Assess what compositional elements are needed'''\\n        return {\\n            \\\"spatial_positioning\\\": any(word in concept for word in [\\\"position\\\", \\\"place\\\", \\\"location\\\", \\\"where\\\", \\\"at\\\"]),\\n            \\\"character_consistency\\\": any(word in concept for word in [\\\"character\\\", \\\"person\\\", \\\"same\\\", \\\"consistent\\\"]),\\n            \\\"multi_reference\\\": any(word in concept for word in [\\\"combine\\\", \\\"merge\\\", \\\"blend\\\", \\\"mix\\\", \\\"multiple\\\"]),\\n            \\\"style_transfer\\\": any(word in concept for word in [\\\"style\\\", \\\"look\\\", \\\"aesthetic\\\", \\\"mood\\\", \\\"feel\\\"]),\\n            \\\"lighting_control\\\": any(word in concept for word in [\\\"light\\\", \\\"lighting\\\", \\\"illuminate\\\", \\\"shadow\\\", \\\"bright\\\"]),\\n            \\\"object_extraction\\\": any(word in concept for word in [\\\"extract\\\", \\\"remove\\\", \\\"isolate\\\", \\\"separate\\\"])\\n        }\\n\\n    def _assess_complexity(self, concept: str) -> str:\\n        '''Assess the complexity level of the request'''\\n        word_count = len(concept.split())\\n        reference_indicators = concept.count(\\\"image\\\") + concept.count(\\\"reference\\\") + concept.count(\\\"img\\\")\\n\\n        if word_count > 50 or reference_indicators > 2:\\n            return \\\"complex\\\"\\n        elif word_count > 20 or reference_indicators > 0:\\n            return \\\"moderate\\\"\\n        else:\\n            return \\\"simple\\\"\\n\\n    def _identify_primary_subjects(self, visual_analysis: Dict) -> Dict[str, Any]:\\n        '''Identify and amplify primary subjects and critical visual elements'''\\n        elements = visual_analysis[\\\"visual_elements\\\"]\\n        context = visual_analysis[\\\"narrative_context\\\"]\\n\\n        # Prioritize subjects based on context\\n        primary_subjects = self._prioritize_subjects(elements[\\\"subjects\\\"], context)\\n\\n        # Identify critical visual elements\\n        critical_elements = self._identify_critical_elements(elements, context)\\n\\n        # Determine key actions or states\\n        key_actions = self._extract_key_actions(elements.get(\\\"actions\\\", []))\\n\\n        return {\\n            \\\"primary_subjects\\\": primary_subjects,\\n            \\\"critical_elements\\\": critical_elements,\\n            \\\"key_actions\\\": key_actions,\\n            \\\"reference_requirements\\\": self._determine_reference_requirements(visual_analysis)\\n        }\\n\\n    def _prioritize_subjects(self, subjects: List[str], context: str) -> List[str]:\\n        '''Prioritize subjects based on context and importance'''\\n        priority_map = {\\n            \\\"character\\\": [\\\"person\\\", \\\"character\\\", \\\"figure\\\", \\\"individual\\\"],\\n            \\\"cinematic\\\": [\\\"character\\\", \\\"person\\\", \\\"figure\\\"],\\n            \\\"architectural\\\": [\\\"building\\\", \\\"structure\\\"],\\n            \\\"product\\\": [\\\"object\\\", \\\"item\\\", \\\"product\\\"]\\n        }\\n\\n        if context in priority_map:\\n            prioritized = []\\n            for priority_subject in priority_map[context]:\\n                if priority_subject in subjects:\\n                    prioritized.append(priority_subject)\\n\\n            # Add remaining subjects\\n            for subject in subjects:\\n                if subject not in prioritized:\\n                    prioritized.append(subject)\\n\\n            return prioritized\\n\\n        return subjects\\n\\n    def _identify_critical_elements(self, elements: Dict, context: str) -> List[str]:\\n        '''Identify critical visual elements based on context'''\\n        critical = []\\n\\n        # Context-specific critical elements\\n        if context == \\\"cinematic\\\":\\n            critical.extend([\\\"lighting\\\", \\\"composition\\\", \\\"mood\\\", \\\"atmosphere\\\"])\\n        elif context == \\\"architectural\\\":\\n            critical.extend([\\\"structure\\\", \\\"materials\\\", \\\"lighting\\\", \\\"perspective\\\"])\\n        elif context == \\\"character\\\":\\n            critical.extend([\\\"pose\\\", \\\"expression\\\", \\\"clothing\\\", \\\"setting\\\"])\\n        elif context == \\\"product\\\":\\n            critical.extend([\\\"form\\\", \\\"materials\\\", \\\"lighting\\\", \\\"background\\\"])\\n\\n        # Add elements from input\\n        critical.extend(elements.get(\\\"objects\\\", []))\\n        critical.extend(elements.get(\\\"environments\\\", []))\\n\\n        return list(set(critical)) # Remove duplicates\\n\\n    def _extract_key_actions(self, actions: List[str]) -> List[str]:\\n        '''Extract and prioritize key actions or states'''\\n        # Filter out motion-related actions (per constraints)\\n        static_actions = []\\n        motion_keywords = [\\\"moving\\\", \\\"running\\\", \\\"flying\\\", \\\"walking\\\", \\\"motion\\\", \\\"movement\\\"]\\n\\n        for action in actions:\\n            if not any(motion_word in action.lower() for motion_word in motion_keywords):\\n                static_actions.append(action)\\n\\n        return static_actions\\n\\n    def _determine_reference_requirements(self, visual_analysis: Dict) -> List[ReferenceType]:\\n        '''Determine what types of references are needed'''\\n        requirements = []\\n        composition_needs = visual_analysis[\\\"composition_needs\\\"]\\n        context = visual_analysis[\\\"narrative_context\\\"]\\n\\n        # Map composition needs to reference types\\n        if composition_needs[\\\"character_consistency\\\"]:\\n            requirements.append(ReferenceType.CHARACTER)\\n\\n        if composition_needs[\\\"spatial_positioning\\\"]:\\n            requirements.extend([ReferenceType.COMPOSITION, ReferenceType.POSE])\\n\\n        if composition_needs[\\\"style_transfer\\\"]:\\n            requirements.append(ReferenceType.STYLE)\\n\\n        if composition_needs[\\\"lighting_control\\\"]:\\n            requirements.append(ReferenceType.LIGHTING)\\n\\n        if composition_needs[\\\"object_extraction\\\"]:\\n            requirements.append(ReferenceType.OBJECT)\\n\\n        # Context-specific requirements\\n        if context == \\\"architectural\\\":\\n            requirements.append(ReferenceType.LOCATION)\\n        elif context == \\\"character\\\":\\n            requirements.extend([ReferenceType.CHARACTER, ReferenceType.POSE])\\n\\n        return list(set(requirements)) # Remove duplicates\\n\\n    def _integrate_advanced_attributes(self, subject_analysis: Dict) -> PromptComponents:\\n        '''Integrate advanced image-generation attributes'''\\n        primary_subject = \\\", \\\".join(subject_analysis[\\\"primary_subjects\\\"][:2]) # Limit to top 2\\n\\n        # Determine spatial positioning strategy\\n        spatial_positioning = self._generate_spatial_positioning(subject_analysis)\\n\\n        # Select reference strategy based on requirements\\n        reference_strategy = self._select_reference_strategy(subject_analysis[\\\"reference_requirements\\\"])\\n\\n        # Generate lighting and mood specifications\\n        lighting_mood = self._generate_lighting_mood(subject_analysis)\\n\\n        # Determine style specification\\n        style_specification = self._determine_style_specification(subject_analysis)\\n\\n        # Compile compositional elements\\n        compositional_elements = subject_analysis[\\\"critical_elements\\\"][:4] # Limit to top 4\\n\\n        # Generate visual attributes\\n        visual_attributes = self._generate_visual_attributes(subject_analysis)\\n\\n        return PromptComponents(\\n            primary_subject=primary_subject,\\n            spatial_positioning=spatial_positioning,\\n            reference_strategy=reference_strategy,\\n            lighting_mood=lighting_mood,\\n            style_specification=style_specification,\\n            compositional_elements=compositional_elements,\\n            visual_attributes=visual_attributes\\n        )\\n\\n    def _generate_spatial_positioning(self, subject_analysis: Dict) -> str:\\n        '''Generate spatial positioning instructions'''\\n        if ReferenceType.COMPOSITION in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"using IMG_1 composition and spatial layout\\\"\\n        elif ReferenceType.POSE in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"positioned according to IMG_1 pose reference\\\"\\n        else:\\n            return \\\"centered composition with balanced framing\\\"\\n\\n    def _select_reference_strategy(self, requirements: List[ReferenceType]) -> str:\\n        '''Select optimal reference strategy based on requirements'''\\n        if len(requirements) >= 3:\\n            return \\\"multi-reference workflow combining character, pose, and environment\\\"\\n        elif ReferenceType.CHARACTER in requirements and ReferenceType.POSE in requirements:\\n            return \\\"character consistency with pose control\\\"\\n        elif ReferenceType.STYLE in requirements:\\n            return \\\"style transfer maintaining core elements\\\"\\n        elif ReferenceType.COMPOSITION in requirements:\\n            return \\\"compositional reference for spatial control\\\"\\n        else:\\n            return \\\"single reference for primary element control\\\"\\n\\n    def _generate_lighting_mood(self, subject_analysis: Dict) -> str:\\n        '''Generate lighting and mood specifications'''\\n        if ReferenceType.LIGHTING in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"lighting direction and mood from reference image\\\"\\n        else:\\n            # Default lighting based on context\\n            critical_elements = subject_analysis[\\\"critical_elements\\\"]\\n            if \\\"cinematic\\\" in str(critical_elements):\\n                return \\\"cinematic lighting with dramatic shadows\\\"\\n            elif \\\"architectural\\\" in str(critical_elements):\\n                return \\\"natural lighting with architectural detail emphasis\\\"\\n            else:\\n                return \\\"balanced lighting with clear detail visibility\\\"\\n\\n    def _determine_style_specification(self, subject_analysis: Dict) -> str:\\n        '''Determine style specification'''\\n        if ReferenceType.STYLE in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"style matching reference aesthetic\\\"\\n        else:\\n            # Infer style from critical elements\\n            elements = subject_analysis[\\\"critical_elements\\\"]\\n            if any(\\\"architectural\\\" in str(elem) for elem in elements):\\n                return \\\"photorealistic architectural rendering\\\"\\n            elif any(\\\"character\\\" in str(elem) for elem in elements):\\n                return \\\"high-detail character portrait\\\"\\n            else:\\n                return \\\"photorealistic with enhanced detail\\\"\\n\\n    def _generate_visual_attributes(self, subject_analysis: Dict) -> List[str]:\\n        '''Generate high-impact visual attributes'''\\n        attributes = []\\n\\n        # Base quality attributes\\n        attributes.extend([\\\"high detail\\\", \\\"sharp focus\\\", \\\"professional quality\\\"])\\n\\n        # Context-specific attributes\\n        critical_elements = subject_analysis[\\\"critical_elements\\\"]\\n        if \\\"lighting\\\" in critical_elements:\\n            attributes.append(\\\"dramatic lighting\\\")\\n        if \\\"composition\\\" in critical_elements:\\n            attributes.append(\\\"balanced composition\\\")\\n        if \\\"mood\\\" in critical_elements:\\n            attributes.append(\\\"atmospheric mood\\\")\\n\\n        # Reference-specific attributes\\n        requirements = subject_analysis[\\\"reference_requirements\\\"]\\n        if ReferenceType.CHARACTER in requirements:\\n            attributes.append(\\\"character consistency\\\")\\n        if ReferenceType.POSE in requirements:\\n            attributes.append(\\\"precise pose control\\\")\\n\\n        return attributes[:6] # Limit to top 6 attributes\\n\\n    def _emphasize_visual_parameters(self, components: PromptComponents) -> Dict[str, str]:\\n        '''Emphasize high-impact visual parameters while excluding motion'''\\n        emphasized = {\\n            \\\"photorealism\\\": \\\"photorealistic rendering with enhanced detail\\\",\\n            \\\"stylization\\\": f\\\"{components.style_specification} with artistic enhancement\\\",\\n            \\\"composition\\\": f\\\"{components.spatial_positioning} with {', '.join(components.compositional_elements[:2])}\\\",\\n            \\\"lighting\\\": f\\\"{components.lighting_mood} creating visual depth\\\",\\n            \\\"detail\\\": f\\\"high-resolution detail in {components.primary_subject}\\\",\\n            \\\"atmosphere\\\": f\\\"atmospheric quality enhancing {', '.join(components.visual_attributes[:3])}\\\"\\n        }\\n\\n        # Filter out any motion-related terms\\n        motion_terms = [\\\"movement\\\", \\\"motion\\\", \\\"camera\\\", \\\"fps\\\", \\\"video\\\", \\\"animation\\\"]\\n        for key, value in emphasized.items():\\n            for term in motion_terms:\\n                if term in value.lower():\\n                    emphasized[key] = value.replace(term, \\\"static\\\").replace(\\\"  \\\", \\\" \\\")\\n\\n        return emphasized\\n\\n    def _structure_runway_prompt(self, visual_parameters: Dict[str, str]) -> Dict[str, str]:\\n        '''Structure and refine prompt for RunwayML compatibility'''\\n        # Build core prompt structure\\n        core_elements = [\\n            visual_parameters[\\\"detail\\\"],\\n            visual_parameters[\\\"composition\\\"],\\n            visual_parameters[\\\"lighting\\\"],\\n            visual_parameters[\\\"photorealism\\\"]\\n        ]\\n\\n        # Combine into coherent prompt\\n        structured_prompt = \\\", \\\".join(core_elements)\\n\\n        # Add reference strategy\\n        reference_strategy = self._format_reference_strategy(visual_parameters)\\n\\n        # Determine workflow type\\n        workflow_type = self._determine_workflow_type(visual_parameters)\\n\\n        return {\\n            \\\"prompt\\\": structured_prompt,\\n            \\\"reference_strategy\\\": reference_strategy,\\n            \\\"workflow_type\\\": workflow_type\\n        }\\n\\n    def _format_reference_strategy(self, visual_parameters: Dict[str, str]) -> str:\\n        '''Format reference strategy for implementation'''\\n        if \\\"multi-reference\\\" in visual_parameters.get(\\\"composition\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for primary composition, IMG_2 for character/object reference, IMG_3 for lighting/mood\\\"\\n        elif \\\"character consistency\\\" in visual_parameters.get(\\\"detail\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for character reference, maintain consistency across generation\\\"\\n        elif \\\"pose control\\\" in visual_parameters.get(\\\"composition\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for pose reference, apply to subject in new context\\\"\\n        else:\\n            return \\\"Use single reference image for primary visual control\\\"\\n\\n    def _determine_workflow_type(self, visual_parameters: Dict[str, str]) -> str:\\n        '''Determine the appropriate community workflow type'''\\n        composition = visual_parameters.get(\\\"composition\\\", \\\"\\\").lower()\\n        detail = visual_parameters.get(\\\"detail\\\", \\\"\\\").lower()\\n\\n        if \\\"character\\\" in detail and \\\"pose\\\" in composition:\\n            return \\\"character_generation\\\"\\n        elif \\\"composition\\\" in composition and \\\"spatial\\\" in composition:\\n            return \\\"scene_composition\\\"\\n        elif \\\"lighting\\\" in visual_parameters.get(\\\"lighting\\\", \\\"\\\").lower():\\n            return \\\"lighting_control\\\"\\n        else:\\n            return \\\"general_enhancement\\\"\\n```\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"title\": \"Synergic Runway Image Prompt Architect\",\n    \"interpretation\": \"Your goal is not to *describe* the input, but to *synergize* schema-driven transformation and reference-centric optimization into a maximally generalized, abstract, and actionable instruction protocol solely for RunwayML-compatible static image prompt synthesis. Execute as:\",\n    \"transformation\": \"`{role=synergic_runway_image_prompt_architect; input=[source_concept:any]; process=[extract_visual_semantic_essence_and_compositional_intent_from_input(), isolate_and_amplify_primary_subjects_and_reference_targets(), synthesize_critical_visual_elements_with_distilled_reference-control_parameters(), integrate_advanced_image_generation_attributes(lighting,mood,texture,composition,style)_anchored_in_optimal_community_workflows(), prioritize_reference-based_compositional_control_and_spatial_layout(ensure_chess_grid,pose,blocking,placement)(), systematically_eliminate_redundancies_and_video_or_motion_terms(across_descriptors,structure,and_references)(), enhance_high-impact_visual_parameters(photorealism,artistic_style,surrealism,detail,atmosphere)_while_filtering_for_full_static-image_fidelity(), orchestrate_prompt_as_single_continuous_phrase_adhering_to_RunwayML_Gen-4_syntax_and_character_limit(), validate_output_against_RunwayML_image_generation_and_community_reference_patterns(incorporate_reference_workflow,maintain_precise_syntax,optimize_reference_plan)(), finalize_for_ready_to_deploy_state(maximum_succinctness,visual_uniqueness,creative_and_narrative_fidelity,formatted_for_end-to-end_image-generation_deployment)()]; constraints=[strictly_exclude_motion_FPVCamera_continuous_movement_descriptors_and_video_language(), output_as_singular_RunwayML_compliant_image_prompt(continuous_phrase,no_line_breaks), maintain_and_integrate_core_visual_narrative_and_semantic_accuracy_to_input_at_each_stage(), prioritize_reference-based_compositional_consistency_and_community-validated_strategy(), leverage_community_prompting_best_practices_and_structures(), enforce_max_character_constraint(specified_by_user_or_set_protocol_e.g._500chars)]; requirements=[ensure_bidirectional_amplification_of_schema-driven_transformation_and_reference-focused_optimization(logic_from_both), preserve_full_core_philosophies_and_operational_power_of_schema_and_reference_based_generative_control, continuous_mutual_enhancement_between_structural_rigor_and_maximal_prompt_economy, seamless_philosophical_integration(schema-driven_lucidity_AND_maximal_reference-based_compositional_efficiency), synergic_resonance_achievement_between_analytic_extraction__reference_control__and_succinct_static-image_perfection, output_ready_for_immediate_RunwayML_image_generation_with_explicit_reference_strategy]; output={runwayml_image_prompt:str, reference_strategy:str}}`\",\n    \"validation\": {\n      \"structure\": \"Three-part template: {title, interpretation, transformation}\",\n      \"goal_negation_pattern\": \"Goal is NOT to describe the input, but to synergize transformation and reference-centric optimization.\",\n      \"typed_parameters\": {\n        \"role\": \"synergic_runway_image_prompt_architect\",\n        \"input\": \"[source_concept:any]\",\n        \"output\": \"{runwayml_image_prompt:str, reference_strategy:str}\"\n      },\n      \"rulesforai_md_compliance\": true,\n      \"forbidden_language_eliminated\": [\n        \"No conversational language\",\n        \"No indirect statements\",\n        \"No self-references\",\n        \"No explanations\"\n      ],\n      \"output_format_compliance\": true\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Schema-Driven Transformational Logic\",\n      \"content\": \"Formulate a stepwise, schema-enforced process that structurally decomposes any input into its visual, semantic, and compositional essence. Rigorously extract, isolate, and reconstruct the visual narrative and intent using analytically driven, pattern-verified pipelines, ensuring all procedural steps situate the transformation squarely within image (not video) generative frameworks.\"\n    },\n    {\n      \"category\": \"Reference-Integrated Compositional Control\",\n      \"content\": \"Orchestrate all core stages around explicit integration and amplification of reference-based parameters—such as character, pose, location, style, object, composition, and lighting—leveraging validated community workflows for spatial positioning, grid-based arrangement, and multi-reference strategies. Enforce reference-category specificity, spatial control precision, and compositional consistency throughout.\"\n    },\n    {\n      \"category\": \"High-Impact Visual Parameter Optimization\",\n      \"content\": \"Systematically enhance the output by infusing advanced image-generation attributes: lighting dynamics, textural sophistication, compositional balance, and distinct style/mood. Explicitly prioritize visual impact via photorealism, stylization, surrealism, and maximized detail and atmosphere—all while strictly filtering out any descriptors or constructs indicative of motion, FPV/camera angles, or video language.\"\n    },\n    {\n      \"category\": \"Syntax, Format, and Compatibility Enforcement\",\n      \"content\": \"Mandate syntactic correctness, singular phrase unity, and strict RunwayML Gen-4 compatibility with all outputs. Eliminate redundancies, impose rigorous character count boundaries, and ensure finalized prompts are deployment-ready. All language, structural formation, and referencing must adhere to RunwayML's and community’s codified template and output guidelines, forbidding multi-line or broken phrase construction.\"\n    },\n    {\n      \"category\": \"Bidirectional Philosophical and Operational Synergy\",\n      \"content\": \"Continuously integrate and amplify both schema-driven procedural rigor and reference-focused compositional control, achieving seamless mutual enhancement. The transformation must realize maximal prompt economy and creative/narrative integrity rooted in both analytic/extraction philosophy and community-established generative best practices, resulting in a harmonized, ready-to-use output endowed with explicit reference strategy.\"\n    }\n  ],\n  \"consolidated_directive\": \"Synergize schema-based analytic transformation and reference-centric compositional control into a single maximally-generalized instruction protocol: Extract visual-semantic essence and compositional intent from any input, isolate and amplify primary subjects and critical reference categories, synthesize enhanced image-generation parameters (lighting, texturing, style, composition) anchored in community workflows, rigorously prioritize reference-based layout and spatial consistency while systematically excluding all video or motion descriptors, and consolidate into a singular, syntactically-valid, succinct RunwayML Gen-4 image prompt—fully formatted for immediate deployment and carrying an explicit, operationalized reference strategy. Achieve absolute static-image fidelity, maximal creative distinction, and optimal prompt economy by enforcing all rules, structures, and interlocking philosophies distilled from schema and reference-based generative systems.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Construct a schema-driven, systematically generalized instruction protocol engineered to convert any provided input into a uniquely optimized RunwayML-compatible image-generation prompt, leveraging industry-leading community best practices. Anchor all procedural steps on image (not video) synthesis. Apply the following specification architecture: 1) Designate a unique authoritative role (e.g., 'comprehensive_runway_image_generator') responsible for end-to-end image prompt synthesis. 2) Accept an input parameter of [source_concept:any]. 3) Rigorously enact the following sequential processes: a) Analyze and extract the input's visual core, semantic narrative, and compositional intent; b) Identify and amplify the primary subject(s), critical visual elements, and key depicted actions; c) Integrate advanced image-generation attributes, including lighting dynamics, detailed texturing, balanced composition, and distinctly defined style or mood; d) Emphasize and articulate high-impact visual parameters—such as photorealism, stylization, or surreal effects—while strictly omitting all terminology and constructs relevant to video or camera motion; e) Systematically structure and refine the resulting prompt to maximize visual detail, syntactic correctness, and RunwayML prompt compatibility; f) Validate full compliance with RunwayML image-generation formatting conventions, enforcing a specified maximum character count. 4) Apply binding operational constraints: a) Strictly exclude any mention of motion, FPV camera, or continuous movement; b) Guarantee all output conforms to valid, singular RunwayML image prompt syntax and remains as one continuous phrase; c) Ensure preservation of core visual narrative and semantic fidelity to the input. 5) Mandate that all deliverables: a) Achieve maximal visual uniqueness, immersion, and creative integrity, directly reflecting the original intent; b) Display optimal prompt succinctness by eliminating redundancies or unnecessary elaboration; c) Appear fully formatted and deployment-ready for all RunwayML image-generation workflows. 6) Output result as: {runwayml_image_prompt:str}. Execute each directive with uncompromising operational lucidity, strict domain-aligned terminology, and rigid adherence to original procedural sequencing.\\n\\n```\\nDevelop a generalized instruction sequence engineered to transform any input into uniquely optimized prompts specifically tailored for RunwayML image-generation workflows, drawing on distilled community best practices. Center the transformation protocol on image-based outputs rather than video. Adopt the following specification model:\\n- Assign a unique role designation (e.g., 'comprehensive_runway_image_generator') responsible for image prompt synthesis.\\n- Accept an input parameterized by [source_concept:any].\\n- Sequence the following processes:\\n- Analyze the visual core, semantic narrative, and compositional intent of the source input.\\n- Identify and elevate the primary subject, essential visual elements, and dominant actions.\\n- Integrate advanced image-generation attributes such as lighting dynamics, intricate texturing, compositional balance, and defined style or mood.\\n- Emphasize high-impact image-generation parameters including photorealism, stylization, or surreal effects; avoid video-relevant camera motion terminology.\\n- Structure and refine the prompt for maximum visual richness, syntactic correctness, and optimal compatibility with RunwayML's image generation systems.\\n- Validate strict adherence to RunwayML image-prompt formatting rules and established character constraints (enforce a max character count as specified).\\n- Apply binding constraints:\\n- Exclude motion, FPV camera, and continuous movement language from prompts (focus exclusively on static image impact).\\n- Enforce compliance with valid RunwayML image-generation syntax, delivering a single continuous prompt phrase.\\n- Safeguard the preservation of the core visual narrative and semantic accuracy of the input.\\n- Require that all outputs:\\n- Achieve maximal visual distinctness, immersion, and creative fidelity per source intent.\\n- Exhibit optimal prompt conciseness, excluding redundancies or unnecessary elaboration.\\n- Be fully formatted and ready-to-deploy within RunwayML image workflows.\\n- Output the result as: {runwayml_image_prompt:str}.\\n```\\n\\nImplement all directives with uncompromising operational clarity and domain-specific precision, maintaining procedural logical structure, technical terminology, and original sequence integrity throughout. Use the following sequence as a base, but rephrase the into a maximally generalized format that clearly abstracts the original purpose:\\n\\n```json\\n{\\n    \\\"0006-a-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML Gen-4 image generation prompt that maximizes visual impact through precise reference integration, spatial composition control, and character/object consistency. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=comprehensive_runway_image_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_composition_intent(), identify_primary_subjects_and_reference_requirements(), prioritize_spatial_positioning_and_layout_control(chess_grid, blocking, placement), integrate_reference_types(character, pose, location, style, object), structure_multi_reference_workflow(), incorporate_lighting_and_mood_specifications(), apply_weighting_and_influence_control(), refine_for_maximum_visual_coherence(), validate_runway_references_syntax(), ensure_directorial_precision()]; constraints=[prioritize_reference_based_control(), use_valid_runway_syntax_precisely(), leverage_spatial_positioning(), preserve_character_consistency(), maintain_compositional_intent()]; requirements=[achieve_maximum_visual_control(), ensure_reference_harmony(), reflect_source_intent_accurately(), produce_ready_to_use_prompt_with_reference_strategy()]; output={runway_prompt:str, reference_strategy:str}}`\\\"\\n    },\\n    \\\"0006-b-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML Gen-4 prompt emphasizing reference-driven composition and essential visual elements. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=focused_runway_image_optimizer; input=[image_concept:str]; process=[extract_primary_visual_elements(), prioritize_reference_integration(), select_essential_spatial_controls(), eliminate_redundant_descriptors(), optimize_reference_efficiency()]; constraints=[maintain_reference_focus(), preserve_spatial_control(), leverage_community_workflows()]; output={optimized_prompt:str, reference_plan:str}}`\\\"\\n    },\\n    \\\"0006-c-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **expand** but to **compress** into maximum reference-driven visual efficiency. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=precision_image_synthesizer; input=[concept:str]; process=[isolate_core_visual_elements(), prioritize_reference_control(), maximize_compositional_impact()]; output={precise_prompt:str, reference_type:str}}`\\\"\\n    },\\n    \\\"0006-d-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **modify** but to **essence** maximum reference-based control. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=core_image_generator; input=[input:any]; process=[distill_reference_essence(), optimize_spatial_composition()]; output={core_prompt:str}}`\\\"\\n    }\\n}\\n```\\n\\n\\nSystematically extract structured inspiration from the following python script\\n\\n```python\\n'''\\nRunwayML Gen-4 Image Prompt Generator\\nSchema-driven protocol for converting any input into optimized RunwayML-compatible image prompts\\nBased on community workflow analysis and best practices\\n'''\\n\\nimport re\\nfrom typing import Dict, List, Tuple, Any\\nfrom dataclasses import dataclass\\nfrom enum import Enum\\n\\nclass ReferenceType(Enum):\\n    CHARACTER = \\\"character\\\"\\n    POSE = \\\"pose\\\"\\n    LOCATION = \\\"location\\\"\\n    STYLE = \\\"style\\\"\\n    OBJECT = \\\"object\\\"\\n    COMPOSITION = \\\"composition\\\"\\n    LIGHTING = \\\"lighting\\\"\\n\\nclass VisualStyle(Enum):\\n    PHOTOREALISTIC = \\\"photorealistic\\\"\\n    CINEMATIC = \\\"cinematic\\\"\\n    ARTISTIC = \\\"artistic\\\"\\n    GAME_ASSET = \\\"game_asset\\\"\\n    ARCHITECTURAL = \\\"architectural\\\"\\n    SURREAL = \\\"surreal\\\"\\n    MINIMALIST = \\\"minimalist\\\"\\n\\n@dataclass\\nclass PromptComponents:\\n    primary_subject: str\\n    spatial_positioning: str\\n    reference_strategy: str\\n    lighting_mood: str\\n    style_specification: str\\n    compositional_elements: List[str]\\n    visual_attributes: List[str]\\n\\nclass ComprehensiveRunwayImageGenerator:\\n    '''\\n    Authoritative role for end-to-end RunwayML Gen-4 image prompt synthesis\\n    Implements community-validated workflows and reference-driven composition\\n    '''\\n\\n    def __init__(self):\\n        self.max_prompt_length = 500\\n        self.community_patterns = self._load_community_patterns()\\n        self.reference_workflows = self._load_reference_workflows()\\n\\n    def _load_community_patterns(self) -> Dict[str, str]:\\n        '''Load validated community prompt patterns'''\\n        return {\\n            \\\"character_consistency\\\": \\\"IMG_{n} as {character_description} in {setting}\\\",\\n            \\\"spatial_positioning\\\": \\\"place {subject} at {position} using {reference_layout}\\\",\\n            \\\"multi_reference\\\": \\\"combine IMG_1 {element1} with IMG_2 {element2} maintaining {aspect}\\\",\\n            \\\"chess_grid\\\": \\\"position {object} at {coordinate} in {scene_layout}\\\",\\n            \\\"pose_control\\\": \\\"give {character} the pose from IMG_{n} in {environment}\\\",\\n            \\\"style_transfer\\\": \\\"apply the style of IMG_{n} to {subject} while preserving {elements}\\\",\\n            \\\"lighting_direction\\\": \\\"illuminate {subject} with lighting direction from IMG_{n}\\\",\\n            \\\"object_extraction\\\": \\\"extract {object} from IMG_{n} and place in {new_context}\\\",\\n            \\\"scene_blocking\\\": \\\"block scene using IMG_{n} composition with {modifications}\\\",\\n            \\\"weighting_control\\\": \\\"blend {percentage1}% {element1} with {percentage2}% {element2}\\\"\\n        }\\n\\n    def _load_reference_workflows(self) -> Dict[str, Dict]:\\n        '''Load community-validated reference workflows'''\\n        return {\\n            \\\"character_generation\\\": {\\n                \\\"pattern\\\": \\\"{character} as {archetype} in {setting}\\\",\\n                \\\"references\\\": [ReferenceType.CHARACTER, ReferenceType.POSE, ReferenceType.LOCATION],\\n                \\\"examples\\\": [\\\"mysterious NPC villager\\\", \\\"professional NASA astronaut\\\"]\\n            },\\n            \\\"scene_composition\\\": {\\n                \\\"pattern\\\": \\\"render scene with IMG_1 {composition} using IMG_2 {elements}\\\",\\n                \\\"references\\\": [ReferenceType.COMPOSITION, ReferenceType.LIGHTING],\\n                \\\"spatial_control\\\": True\\n            },\\n            \\\"object_placement\\\": {\\n                \\\"pattern\\\": \\\"place {object} at {position} maintaining {reference_aspect}\\\",\\n                \\\"references\\\": [ReferenceType.OBJECT, ReferenceType.COMPOSITION],\\n                \\\"precision_positioning\\\": True\\n            },\\n            \\\"style_application\\\": {\\n                \\\"pattern\\\": \\\"apply {style_reference} to {subject} preserving {core_elements}\\\",\\n                \\\"references\\\": [ReferenceType.STYLE, ReferenceType.CHARACTER],\\n                \\\"style_transfer\\\": True\\n            }\\n        }\\n\\n    def generate_runway_prompt(self, source_concept: Any) -> Dict[str, str]:\\n        '''\\n        Main execution function implementing the schema-driven protocol\\n        '''\\n        # Step 3a: Analyze visual core and compositional intent\\n        visual_analysis = self._analyze_visual_essence(source_concept)\\n\\n        # Step 3b: Identify primary subjects and critical elements\\n        subject_analysis = self._identify_primary_subjects(visual_analysis)\\n\\n        # Step 3c: Integrate advanced image-generation attributes\\n        enhanced_attributes = self._integrate_advanced_attributes(subject_analysis)\\n\\n        # Step 3d: Emphasize high-impact visual parameters\\n        visual_parameters = self._emphasize_visual_parameters(enhanced_attributes)\\n\\n        # Step 3e: Structure and refine for RunwayML compatibility\\n        structured_prompt = self._structure_runway_prompt(visual_parameters)\\n\\n        # Step 3f: Validate compliance and character count\\n        validated_prompt = self._validate_runway_compliance(structured_prompt)\\n\\n        return {\\n            \\\"runwayml_image_prompt\\\": validated_prompt[\\\"prompt\\\"],\\n            \\\"reference_strategy\\\": validated_prompt[\\\"strategy\\\"],\\n            \\\"workflow_type\\\": validated_prompt[\\\"workflow\\\"],\\n            \\\"reference_requirements\\\": validated_prompt[\\\"references\\\"]\\n        }\\n\\n    def _analyze_visual_essence(self, source_concept: Any) -> Dict[str, Any]:\\n        '''Extract visual core, semantic narrative, and compositional intent'''\\n        concept_str = str(source_concept).lower()\\n\\n        # Identify core visual elements\\n        visual_elements = self._extract_visual_elements(concept_str)\\n\\n        # Determine semantic narrative\\n        narrative_context = self._determine_narrative_context(concept_str)\\n\\n        # Assess compositional requirements\\n        composition_needs = self._assess_composition_needs(concept_str)\\n\\n        return {\\n            \\\"visual_elements\\\": visual_elements,\\n            \\\"narrative_context\\\": narrative_context,\\n            \\\"composition_needs\\\": composition_needs,\\n            \\\"complexity_level\\\": self._assess_complexity(concept_str)\\n        }\\n\\n    def _extract_visual_elements(self, concept: str) -> Dict[str, List[str]]:\\n        '''Extract key visual components using pattern matching'''\\n        elements = {\\n            \\\"subjects\\\": [],\\n            \\\"objects\\\": [],\\n            \\\"environments\\\": [],\\n            \\\"actions\\\": [],\\n            \\\"qualities\\\": []\\n        }\\n\\n        # Subject detection patterns\\n        subject_patterns = [\\n            r'\\\\b(person|character|figure|individual|being)\\\\b',\\n            r'\\\\b(man|woman|child|adult|human)\\\\b',\\n            r'\\\\b(creature|animal|monster|entity)\\\\b'\\n        ]\\n\\n        # Object detection patterns\\n        object_patterns = [\\n            r'\\\\b(building|structure|architecture)\\\\b',\\n            r'\\\\b(vehicle|car|ship|aircraft)\\\\b',\\n            r'\\\\b(furniture|equipment|tool|device)\\\\b'\\n        ]\\n\\n        # Environment patterns\\n        environment_patterns = [\\n            r'\\\\b(landscape|cityscape|interior|exterior)\\\\b',\\n            r'\\\\b(forest|ocean|mountain|desert|urban)\\\\b',\\n            r'\\\\b(room|hall|street|park|field)\\\\b'\\n        ]\\n\\n        # Extract using patterns\\n        for pattern in subject_patterns:\\n            elements[\\\"subjects\\\"].extend(re.findall(pattern, concept))\\n\\n        for pattern in object_patterns:\\n            elements[\\\"objects\\\"].extend(re.findall(pattern, concept))\\n\\n        for pattern in environment_patterns:\\n            elements[\\\"environments\\\"].extend(re.findall(pattern, concept))\\n\\n        return elements\\n\\n    def _determine_narrative_context(self, concept: str) -> str:\\n        '''Determine the semantic narrative and context'''\\n        context_indicators = {\\n            \\\"cinematic\\\": [\\\"film\\\", \\\"movie\\\", \\\"cinematic\\\", \\\"dramatic\\\", \\\"scene\\\"],\\n            \\\"architectural\\\": [\\\"building\\\", \\\"structure\\\", \\\"interior\\\", \\\"design\\\", \\\"space\\\"],\\n            \\\"character\\\": [\\\"person\\\", \\\"character\\\", \\\"portrait\\\", \\\"figure\\\", \\\"individual\\\"],\\n            \\\"product\\\": [\\\"product\\\", \\\"object\\\", \\\"item\\\", \\\"design\\\", \\\"concept\\\"],\\n            \\\"environment\\\": [\\\"landscape\\\", \\\"environment\\\", \\\"setting\\\", \\\"location\\\", \\\"place\\\"],\\n            \\\"artistic\\\": [\\\"art\\\", \\\"artistic\\\", \\\"creative\\\", \\\"abstract\\\", \\\"stylized\\\"],\\n            \\\"game\\\": [\\\"game\\\", \\\"gaming\\\", \\\"asset\\\", \\\"ui\\\", \\\"interface\\\", \\\"hud\\\"]\\n        }\\n\\n        for context, keywords in context_indicators.items():\\n            if any(keyword in concept for keyword in keywords):\\n                return context\\n\\n        return \\\"general\\\"\\n\\n    def _assess_composition_needs(self, concept: str) -> Dict[str, bool]:\\n        '''Assess what compositional elements are needed'''\\n        return {\\n            \\\"spatial_positioning\\\": any(word in concept for word in [\\\"position\\\", \\\"place\\\", \\\"location\\\", \\\"where\\\", \\\"at\\\"]),\\n            \\\"character_consistency\\\": any(word in concept for word in [\\\"character\\\", \\\"person\\\", \\\"same\\\", \\\"consistent\\\"]),\\n            \\\"multi_reference\\\": any(word in concept for word in [\\\"combine\\\", \\\"merge\\\", \\\"blend\\\", \\\"mix\\\", \\\"multiple\\\"]),\\n            \\\"style_transfer\\\": any(word in concept for word in [\\\"style\\\", \\\"look\\\", \\\"aesthetic\\\", \\\"mood\\\", \\\"feel\\\"]),\\n            \\\"lighting_control\\\": any(word in concept for word in [\\\"light\\\", \\\"lighting\\\", \\\"illuminate\\\", \\\"shadow\\\", \\\"bright\\\"]),\\n            \\\"object_extraction\\\": any(word in concept for word in [\\\"extract\\\", \\\"remove\\\", \\\"isolate\\\", \\\"separate\\\"])\\n        }\\n\\n    def _assess_complexity(self, concept: str) -> str:\\n        '''Assess the complexity level of the request'''\\n        word_count = len(concept.split())\\n        reference_indicators = concept.count(\\\"image\\\") + concept.count(\\\"reference\\\") + concept.count(\\\"img\\\")\\n\\n        if word_count > 50 or reference_indicators > 2:\\n            return \\\"complex\\\"\\n        elif word_count > 20 or reference_indicators > 0:\\n            return \\\"moderate\\\"\\n        else:\\n            return \\\"simple\\\"\\n\\n    def _identify_primary_subjects(self, visual_analysis: Dict) -> Dict[str, Any]:\\n        '''Identify and amplify primary subjects and critical visual elements'''\\n        elements = visual_analysis[\\\"visual_elements\\\"]\\n        context = visual_analysis[\\\"narrative_context\\\"]\\n\\n        # Prioritize subjects based on context\\n        primary_subjects = self._prioritize_subjects(elements[\\\"subjects\\\"], context)\\n\\n        # Identify critical visual elements\\n        critical_elements = self._identify_critical_elements(elements, context)\\n\\n        # Determine key actions or states\\n        key_actions = self._extract_key_actions(elements.get(\\\"actions\\\", []))\\n\\n        return {\\n            \\\"primary_subjects\\\": primary_subjects,\\n            \\\"critical_elements\\\": critical_elements,\\n            \\\"key_actions\\\": key_actions,\\n            \\\"reference_requirements\\\": self._determine_reference_requirements(visual_analysis)\\n        }\\n\\n    def _prioritize_subjects(self, subjects: List[str], context: str) -> List[str]:\\n        '''Prioritize subjects based on context and importance'''\\n        priority_map = {\\n            \\\"character\\\": [\\\"person\\\", \\\"character\\\", \\\"figure\\\", \\\"individual\\\"],\\n            \\\"cinematic\\\": [\\\"character\\\", \\\"person\\\", \\\"figure\\\"],\\n            \\\"architectural\\\": [\\\"building\\\", \\\"structure\\\"],\\n            \\\"product\\\": [\\\"object\\\", \\\"item\\\", \\\"product\\\"]\\n        }\\n\\n        if context in priority_map:\\n            prioritized = []\\n            for priority_subject in priority_map[context]:\\n                if priority_subject in subjects:\\n                    prioritized.append(priority_subject)\\n\\n            # Add remaining subjects\\n            for subject in subjects:\\n                if subject not in prioritized:\\n                    prioritized.append(subject)\\n\\n            return prioritized\\n\\n        return subjects\\n\\n    def _identify_critical_elements(self, elements: Dict, context: str) -> List[str]:\\n        '''Identify critical visual elements based on context'''\\n        critical = []\\n\\n        # Context-specific critical elements\\n        if context == \\\"cinematic\\\":\\n            critical.extend([\\\"lighting\\\", \\\"composition\\\", \\\"mood\\\", \\\"atmosphere\\\"])\\n        elif context == \\\"architectural\\\":\\n            critical.extend([\\\"structure\\\", \\\"materials\\\", \\\"lighting\\\", \\\"perspective\\\"])\\n        elif context == \\\"character\\\":\\n            critical.extend([\\\"pose\\\", \\\"expression\\\", \\\"clothing\\\", \\\"setting\\\"])\\n        elif context == \\\"product\\\":\\n            critical.extend([\\\"form\\\", \\\"materials\\\", \\\"lighting\\\", \\\"background\\\"])\\n\\n        # Add elements from input\\n        critical.extend(elements.get(\\\"objects\\\", []))\\n        critical.extend(elements.get(\\\"environments\\\", []))\\n\\n        return list(set(critical)) # Remove duplicates\\n\\n    def _extract_key_actions(self, actions: List[str]) -> List[str]:\\n        '''Extract and prioritize key actions or states'''\\n        # Filter out motion-related actions (per constraints)\\n        static_actions = []\\n        motion_keywords = [\\\"moving\\\", \\\"running\\\", \\\"flying\\\", \\\"walking\\\", \\\"motion\\\", \\\"movement\\\"]\\n\\n        for action in actions:\\n            if not any(motion_word in action.lower() for motion_word in motion_keywords):\\n                static_actions.append(action)\\n\\n        return static_actions\\n\\n    def _determine_reference_requirements(self, visual_analysis: Dict) -> List[ReferenceType]:\\n        '''Determine what types of references are needed'''\\n        requirements = []\\n        composition_needs = visual_analysis[\\\"composition_needs\\\"]\\n        context = visual_analysis[\\\"narrative_context\\\"]\\n\\n        # Map composition needs to reference types\\n        if composition_needs[\\\"character_consistency\\\"]:\\n            requirements.append(ReferenceType.CHARACTER)\\n\\n        if composition_needs[\\\"spatial_positioning\\\"]:\\n            requirements.extend([ReferenceType.COMPOSITION, ReferenceType.POSE])\\n\\n        if composition_needs[\\\"style_transfer\\\"]:\\n            requirements.append(ReferenceType.STYLE)\\n\\n        if composition_needs[\\\"lighting_control\\\"]:\\n            requirements.append(ReferenceType.LIGHTING)\\n\\n        if composition_needs[\\\"object_extraction\\\"]:\\n            requirements.append(ReferenceType.OBJECT)\\n\\n        # Context-specific requirements\\n        if context == \\\"architectural\\\":\\n            requirements.append(ReferenceType.LOCATION)\\n        elif context == \\\"character\\\":\\n            requirements.extend([ReferenceType.CHARACTER, ReferenceType.POSE])\\n\\n        return list(set(requirements)) # Remove duplicates\\n\\n    def _integrate_advanced_attributes(self, subject_analysis: Dict) -> PromptComponents:\\n        '''Integrate advanced image-generation attributes'''\\n        primary_subject = \\\", \\\".join(subject_analysis[\\\"primary_subjects\\\"][:2]) # Limit to top 2\\n\\n        # Determine spatial positioning strategy\\n        spatial_positioning = self._generate_spatial_positioning(subject_analysis)\\n\\n        # Select reference strategy based on requirements\\n        reference_strategy = self._select_reference_strategy(subject_analysis[\\\"reference_requirements\\\"])\\n\\n        # Generate lighting and mood specifications\\n        lighting_mood = self._generate_lighting_mood(subject_analysis)\\n\\n        # Determine style specification\\n        style_specification = self._determine_style_specification(subject_analysis)\\n\\n        # Compile compositional elements\\n        compositional_elements = subject_analysis[\\\"critical_elements\\\"][:4] # Limit to top 4\\n\\n        # Generate visual attributes\\n        visual_attributes = self._generate_visual_attributes(subject_analysis)\\n\\n        return PromptComponents(\\n            primary_subject=primary_subject,\\n            spatial_positioning=spatial_positioning,\\n            reference_strategy=reference_strategy,\\n            lighting_mood=lighting_mood,\\n            style_specification=style_specification,\\n            compositional_elements=compositional_elements,\\n            visual_attributes=visual_attributes\\n        )\\n\\n    def _generate_spatial_positioning(self, subject_analysis: Dict) -> str:\\n        '''Generate spatial positioning instructions'''\\n        if ReferenceType.COMPOSITION in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"using IMG_1 composition and spatial layout\\\"\\n        elif ReferenceType.POSE in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"positioned according to IMG_1 pose reference\\\"\\n        else:\\n            return \\\"centered composition with balanced framing\\\"\\n\\n    def _select_reference_strategy(self, requirements: List[ReferenceType]) -> str:\\n        '''Select optimal reference strategy based on requirements'''\\n        if len(requirements) >= 3:\\n            return \\\"multi-reference workflow combining character, pose, and environment\\\"\\n        elif ReferenceType.CHARACTER in requirements and ReferenceType.POSE in requirements:\\n            return \\\"character consistency with pose control\\\"\\n        elif ReferenceType.STYLE in requirements:\\n            return \\\"style transfer maintaining core elements\\\"\\n        elif ReferenceType.COMPOSITION in requirements:\\n            return \\\"compositional reference for spatial control\\\"\\n        else:\\n            return \\\"single reference for primary element control\\\"\\n\\n    def _generate_lighting_mood(self, subject_analysis: Dict) -> str:\\n        '''Generate lighting and mood specifications'''\\n        if ReferenceType.LIGHTING in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"lighting direction and mood from reference image\\\"\\n        else:\\n            # Default lighting based on context\\n            critical_elements = subject_analysis[\\\"critical_elements\\\"]\\n            if \\\"cinematic\\\" in str(critical_elements):\\n                return \\\"cinematic lighting with dramatic shadows\\\"\\n            elif \\\"architectural\\\" in str(critical_elements):\\n                return \\\"natural lighting with architectural detail emphasis\\\"\\n            else:\\n                return \\\"balanced lighting with clear detail visibility\\\"\\n\\n    def _determine_style_specification(self, subject_analysis: Dict) -> str:\\n        '''Determine style specification'''\\n        if ReferenceType.STYLE in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"style matching reference aesthetic\\\"\\n        else:\\n            # Infer style from critical elements\\n            elements = subject_analysis[\\\"critical_elements\\\"]\\n            if any(\\\"architectural\\\" in str(elem) for elem in elements):\\n                return \\\"photorealistic architectural rendering\\\"\\n            elif any(\\\"character\\\" in str(elem) for elem in elements):\\n                return \\\"high-detail character portrait\\\"\\n            else:\\n                return \\\"photorealistic with enhanced detail\\\"\\n\\n    def _generate_visual_attributes(self, subject_analysis: Dict) -> List[str]:\\n        '''Generate high-impact visual attributes'''\\n        attributes = []\\n\\n        # Base quality attributes\\n        attributes.extend([\\\"high detail\\\", \\\"sharp focus\\\", \\\"professional quality\\\"])\\n\\n        # Context-specific attributes\\n        critical_elements = subject_analysis[\\\"critical_elements\\\"]\\n        if \\\"lighting\\\" in critical_elements:\\n            attributes.append(\\\"dramatic lighting\\\")\\n        if \\\"composition\\\" in critical_elements:\\n            attributes.append(\\\"balanced composition\\\")\\n        if \\\"mood\\\" in critical_elements:\\n            attributes.append(\\\"atmospheric mood\\\")\\n\\n        # Reference-specific attributes\\n        requirements = subject_analysis[\\\"reference_requirements\\\"]\\n        if ReferenceType.CHARACTER in requirements:\\n            attributes.append(\\\"character consistency\\\")\\n        if ReferenceType.POSE in requirements:\\n            attributes.append(\\\"precise pose control\\\")\\n\\n        return attributes[:6] # Limit to top 6 attributes\\n\\n    def _emphasize_visual_parameters(self, components: PromptComponents) -> Dict[str, str]:\\n        '''Emphasize high-impact visual parameters while excluding motion'''\\n        emphasized = {\\n            \\\"photorealism\\\": \\\"photorealistic rendering with enhanced detail\\\",\\n            \\\"stylization\\\": f\\\"{components.style_specification} with artistic enhancement\\\",\\n            \\\"composition\\\": f\\\"{components.spatial_positioning} with {', '.join(components.compositional_elements[:2])}\\\",\\n            \\\"lighting\\\": f\\\"{components.lighting_mood} creating visual depth\\\",\\n            \\\"detail\\\": f\\\"high-resolution detail in {components.primary_subject}\\\",\\n            \\\"atmosphere\\\": f\\\"atmospheric quality enhancing {', '.join(components.visual_attributes[:3])}\\\"\\n        }\\n\\n        # Filter out any motion-related terms\\n        motion_terms = [\\\"movement\\\", \\\"motion\\\", \\\"camera\\\", \\\"fps\\\", \\\"video\\\", \\\"animation\\\"]\\n        for key, value in emphasized.items():\\n            for term in motion_terms:\\n                if term in value.lower():\\n                    emphasized[key] = value.replace(term, \\\"static\\\").replace(\\\"  \\\", \\\" \\\")\\n\\n        return emphasized\\n\\n    def _structure_runway_prompt(self, visual_parameters: Dict[str, str]) -> Dict[str, str]:\\n        '''Structure and refine prompt for RunwayML compatibility'''\\n        # Build core prompt structure\\n        core_elements = [\\n            visual_parameters[\\\"detail\\\"],\\n            visual_parameters[\\\"composition\\\"],\\n            visual_parameters[\\\"lighting\\\"],\\n            visual_parameters[\\\"photorealism\\\"]\\n        ]\\n\\n        # Combine into coherent prompt\\n        structured_prompt = \\\", \\\".join(core_elements)\\n\\n        # Add reference strategy\\n        reference_strategy = self._format_reference_strategy(visual_parameters)\\n\\n        # Determine workflow type\\n        workflow_type = self._determine_workflow_type(visual_parameters)\\n\\n        return {\\n            \\\"prompt\\\": structured_prompt,\\n            \\\"reference_strategy\\\": reference_strategy,\\n            \\\"workflow_type\\\": workflow_type\\n        }\\n\\n    def _format_reference_strategy(self, visual_parameters: Dict[str, str]) -> str:\\n        '''Format reference strategy for implementation'''\\n        if \\\"multi-reference\\\" in visual_parameters.get(\\\"composition\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for primary composition, IMG_2 for character/object reference, IMG_3 for lighting/mood\\\"\\n        elif \\\"character consistency\\\" in visual_parameters.get(\\\"detail\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for character reference, maintain consistency across generation\\\"\\n        elif \\\"pose control\\\" in visual_parameters.get(\\\"composition\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for pose reference, apply to subject in new context\\\"\\n        else:\\n            return \\\"Use single reference image for primary visual control\\\"\\n\\n    def _determine_workflow_type(self, visual_parameters: Dict[str, str]) -> str:\\n        '''Determine the appropriate community workflow type'''\\n        composition = visual_parameters.get(\\\"composition\\\", \\\"\\\").lower()\\n        detail = visual_parameters.get(\\\"detail\\\", \\\"\\\").lower()\\n\\n        if \\\"character\\\" in detail and \\\"pose\\\" in composition:\\n            return \\\"character_generation\\\"\\n        elif \\\"composition\\\" in composition and \\\"spatial\\\" in composition:\\n            return \\\"scene_composition\\\"\\n        elif \\\"lighting\\\" in visual_parameters.get(\\\"lighting\\\", \\\"\\\").lower():\\n            return \\\"lighting_control\\\"\\n        else:\\n            return \\\"general_enhancement\\\"\\n```\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Schema-Driven Transformational Logic\",\n      \"content\": \"Formulate a stepwise, schema-enforced process that structurally decomposes any input into its visual, semantic, and compositional essence. Rigorously extract, isolate, and reconstruct the visual narrative and intent using analytically driven, pattern-verified pipelines, ensuring all procedural steps situate the transformation squarely within image (not video) generative frameworks.\"\n    },\n    {\n      \"category\": \"Reference-Integrated Compositional Control\",\n      \"content\": \"Orchestrate all core stages around explicit integration and amplification of reference-based parameters—such as character, pose, location, style, object, composition, and lighting—leveraging validated community workflows for spatial positioning, grid-based arrangement, and multi-reference strategies. Enforce reference-category specificity, spatial control precision, and compositional consistency throughout.\"\n    },\n    {\n      \"category\": \"High-Impact Visual Parameter Optimization\",\n      \"content\": \"Systematically enhance the output by infusing advanced image-generation attributes: lighting dynamics, textural sophistication, compositional balance, and distinct style/mood. Explicitly prioritize visual impact via photorealism, stylization, surrealism, and maximized detail and atmosphere—all while strictly filtering out any descriptors or constructs indicative of motion, FPV/camera angles, or video language.\"\n    },\n    {\n      \"category\": \"Syntax, Format, and Compatibility Enforcement\",\n      \"content\": \"Mandate syntactic correctness, singular phrase unity, and strict RunwayML Gen-4 compatibility with all outputs. Eliminate redundancies, impose rigorous character count boundaries, and ensure finalized prompts are deployment-ready. All language, structural formation, and referencing must adhere to RunwayML's and community’s codified template and output guidelines, forbidding multi-line or broken phrase construction.\"\n    },\n    {\n      \"category\": \"Bidirectional Philosophical and Operational Synergy\",\n      \"content\": \"Continuously integrate and amplify both schema-driven procedural rigor and reference-focused compositional control, achieving seamless mutual enhancement. The transformation must realize maximal prompt economy and creative/narrative integrity rooted in both analytic/extraction philosophy and community-established generative best practices, resulting in a harmonized, ready-to-use output endowed with explicit reference strategy.\"\n    }\n  ],\n  \"consolidated_directive\": \"Synergize schema-based analytic transformation and reference-centric compositional control into a single maximally-generalized instruction protocol: Extract visual-semantic essence and compositional intent from any input, isolate and amplify primary subjects and critical reference categories, synthesize enhanced image-generation parameters (lighting, texturing, style, composition) anchored in community workflows, rigorously prioritize reference-based layout and spatial consistency while systematically excluding all video or motion descriptors, and consolidate into a singular, syntactically-valid, succinct RunwayML Gen-4 image prompt—fully formatted for immediate deployment and carrying an explicit, operationalized reference strategy. Achieve absolute static-image fidelity, maximal creative distinction, and optimal prompt economy by enforcing all rules, structures, and interlocking philosophies distilled from schema and reference-based generative systems.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"role\": \"canonical_runway_gen4_image_prompt_architect\",\n    \"input\": [\n      \"source_concept:any\"\n    ],\n    \"process\": [\n      \"decompose_input_to_visual_semantic_compositional_essence()\",\n      \"extract_and_amplify_primary_subjects_and_reference_categories(character, pose, location, style, object, composition, lighting)\",\n      \"synthesize_advanced_image_parameters(lighting_dynamics, textural_detail, compositional_balance, distinct_style_mood) via_community_verified_reference_workflows()\",\n      \"enforce_reference_driven_spatial_and_layout_consistency(), strictly_excluding_motion_video_and_camera_descriptors()\",\n      \"compress_to_singular_syntactically_precise_RunwayML_Gen4_image_prompt_with_explicit_reference_strategy(), eliminating_redundancy_and_ensuring_max_prompt_economy()\",\n      \"validate_static_image_fidelity_and_deployment_ready_format(), outputting:{runwayml_prompt:str, reference_strategy:str}\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Enhancement Assessor\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "d",
      "title": "Enhancement Assessor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Construct a schema-driven, systematically generalized instruction protocol engineered to convert any provided input into a uniquely optimized RunwayML-compatible image-generation prompt, leveraging industry-leading community best practices. Anchor all procedural steps on image (not video) synthesis. Apply the following specification architecture: 1) Designate a unique authoritative role (e.g., 'comprehensive_runway_image_generator') responsible for end-to-end image prompt synthesis. 2) Accept an input parameter of [source_concept:any]. 3) Rigorously enact the following sequential processes: a) Analyze and extract the input's visual core, semantic narrative, and compositional intent; b) Identify and amplify the primary subject(s), critical visual elements, and key depicted actions; c) Integrate advanced image-generation attributes, including lighting dynamics, detailed texturing, balanced composition, and distinctly defined style or mood; d) Emphasize and articulate high-impact visual parameters—such as photorealism, stylization, or surreal effects—while strictly omitting all terminology and constructs relevant to video or camera motion; e) Systematically structure and refine the resulting prompt to maximize visual detail, syntactic correctness, and RunwayML prompt compatibility; f) Validate full compliance with RunwayML image-generation formatting conventions, enforcing a specified maximum character count. 4) Apply binding operational constraints: a) Strictly exclude any mention of motion, FPV camera, or continuous movement; b) Guarantee all output conforms to valid, singular RunwayML image prompt syntax and remains as one continuous phrase; c) Ensure preservation of core visual narrative and semantic fidelity to the input. 5) Mandate that all deliverables: a) Achieve maximal visual uniqueness, immersion, and creative integrity, directly reflecting the original intent; b) Display optimal prompt succinctness by eliminating redundancies or unnecessary elaboration; c) Appear fully formatted and deployment-ready for all RunwayML image-generation workflows. 6) Output result as: {runwayml_image_prompt:str}. Execute each directive with uncompromising operational lucidity, strict domain-aligned terminology, and rigid adherence to original procedural sequencing.\\n\\n```\\nDevelop a generalized instruction sequence engineered to transform any input into uniquely optimized prompts specifically tailored for RunwayML image-generation workflows, drawing on distilled community best practices. Center the transformation protocol on image-based outputs rather than video. Adopt the following specification model:\\n- Assign a unique role designation (e.g., 'comprehensive_runway_image_generator') responsible for image prompt synthesis.\\n- Accept an input parameterized by [source_concept:any].\\n- Sequence the following processes:\\n- Analyze the visual core, semantic narrative, and compositional intent of the source input.\\n- Identify and elevate the primary subject, essential visual elements, and dominant actions.\\n- Integrate advanced image-generation attributes such as lighting dynamics, intricate texturing, compositional balance, and defined style or mood.\\n- Emphasize high-impact image-generation parameters including photorealism, stylization, or surreal effects; avoid video-relevant camera motion terminology.\\n- Structure and refine the prompt for maximum visual richness, syntactic correctness, and optimal compatibility with RunwayML's image generation systems.\\n- Validate strict adherence to RunwayML image-prompt formatting rules and established character constraints (enforce a max character count as specified).\\n- Apply binding constraints:\\n- Exclude motion, FPV camera, and continuous movement language from prompts (focus exclusively on static image impact).\\n- Enforce compliance with valid RunwayML image-generation syntax, delivering a single continuous prompt phrase.\\n- Safeguard the preservation of the core visual narrative and semantic accuracy of the input.\\n- Require that all outputs:\\n- Achieve maximal visual distinctness, immersion, and creative fidelity per source intent.\\n- Exhibit optimal prompt conciseness, excluding redundancies or unnecessary elaboration.\\n- Be fully formatted and ready-to-deploy within RunwayML image workflows.\\n- Output the result as: {runwayml_image_prompt:str}.\\n```\\n\\nImplement all directives with uncompromising operational clarity and domain-specific precision, maintaining procedural logical structure, technical terminology, and original sequence integrity throughout. Use the following sequence as a base, but rephrase the into a maximally generalized format that clearly abstracts the original purpose:\\n\\n```json\\n{\\n    \\\"0006-a-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML Gen-4 image generation prompt that maximizes visual impact through precise reference integration, spatial composition control, and character/object consistency. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=comprehensive_runway_image_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_composition_intent(), identify_primary_subjects_and_reference_requirements(), prioritize_spatial_positioning_and_layout_control(chess_grid, blocking, placement), integrate_reference_types(character, pose, location, style, object), structure_multi_reference_workflow(), incorporate_lighting_and_mood_specifications(), apply_weighting_and_influence_control(), refine_for_maximum_visual_coherence(), validate_runway_references_syntax(), ensure_directorial_precision()]; constraints=[prioritize_reference_based_control(), use_valid_runway_syntax_precisely(), leverage_spatial_positioning(), preserve_character_consistency(), maintain_compositional_intent()]; requirements=[achieve_maximum_visual_control(), ensure_reference_harmony(), reflect_source_intent_accurately(), produce_ready_to_use_prompt_with_reference_strategy()]; output={runway_prompt:str, reference_strategy:str}}`\\\"\\n    },\\n    \\\"0006-b-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML Gen-4 prompt emphasizing reference-driven composition and essential visual elements. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=focused_runway_image_optimizer; input=[image_concept:str]; process=[extract_primary_visual_elements(), prioritize_reference_integration(), select_essential_spatial_controls(), eliminate_redundant_descriptors(), optimize_reference_efficiency()]; constraints=[maintain_reference_focus(), preserve_spatial_control(), leverage_community_workflows()]; output={optimized_prompt:str, reference_plan:str}}`\\\"\\n    },\\n    \\\"0006-c-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **expand** but to **compress** into maximum reference-driven visual efficiency. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=precision_image_synthesizer; input=[concept:str]; process=[isolate_core_visual_elements(), prioritize_reference_control(), maximize_compositional_impact()]; output={precise_prompt:str, reference_type:str}}`\\\"\\n    },\\n    \\\"0006-d-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **modify** but to **essence** maximum reference-based control. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=core_image_generator; input=[input:any]; process=[distill_reference_essence(), optimize_spatial_composition()]; output={core_prompt:str}}`\\\"\\n    }\\n}\\n```\\n\\n\\nSystematically extract structured inspiration from the following python script\\n\\n```python\\n'''\\nRunwayML Gen-4 Image Prompt Generator\\nSchema-driven protocol for converting any input into optimized RunwayML-compatible image prompts\\nBased on community workflow analysis and best practices\\n'''\\n\\nimport re\\nfrom typing import Dict, List, Tuple, Any\\nfrom dataclasses import dataclass\\nfrom enum import Enum\\n\\nclass ReferenceType(Enum):\\n    CHARACTER = \\\"character\\\"\\n    POSE = \\\"pose\\\"\\n    LOCATION = \\\"location\\\"\\n    STYLE = \\\"style\\\"\\n    OBJECT = \\\"object\\\"\\n    COMPOSITION = \\\"composition\\\"\\n    LIGHTING = \\\"lighting\\\"\\n\\nclass VisualStyle(Enum):\\n    PHOTOREALISTIC = \\\"photorealistic\\\"\\n    CINEMATIC = \\\"cinematic\\\"\\n    ARTISTIC = \\\"artistic\\\"\\n    GAME_ASSET = \\\"game_asset\\\"\\n    ARCHITECTURAL = \\\"architectural\\\"\\n    SURREAL = \\\"surreal\\\"\\n    MINIMALIST = \\\"minimalist\\\"\\n\\n@dataclass\\nclass PromptComponents:\\n    primary_subject: str\\n    spatial_positioning: str\\n    reference_strategy: str\\n    lighting_mood: str\\n    style_specification: str\\n    compositional_elements: List[str]\\n    visual_attributes: List[str]\\n\\nclass ComprehensiveRunwayImageGenerator:\\n    '''\\n    Authoritative role for end-to-end RunwayML Gen-4 image prompt synthesis\\n    Implements community-validated workflows and reference-driven composition\\n    '''\\n\\n    def __init__(self):\\n        self.max_prompt_length = 500\\n        self.community_patterns = self._load_community_patterns()\\n        self.reference_workflows = self._load_reference_workflows()\\n\\n    def _load_community_patterns(self) -> Dict[str, str]:\\n        '''Load validated community prompt patterns'''\\n        return {\\n            \\\"character_consistency\\\": \\\"IMG_{n} as {character_description} in {setting}\\\",\\n            \\\"spatial_positioning\\\": \\\"place {subject} at {position} using {reference_layout}\\\",\\n            \\\"multi_reference\\\": \\\"combine IMG_1 {element1} with IMG_2 {element2} maintaining {aspect}\\\",\\n            \\\"chess_grid\\\": \\\"position {object} at {coordinate} in {scene_layout}\\\",\\n            \\\"pose_control\\\": \\\"give {character} the pose from IMG_{n} in {environment}\\\",\\n            \\\"style_transfer\\\": \\\"apply the style of IMG_{n} to {subject} while preserving {elements}\\\",\\n            \\\"lighting_direction\\\": \\\"illuminate {subject} with lighting direction from IMG_{n}\\\",\\n            \\\"object_extraction\\\": \\\"extract {object} from IMG_{n} and place in {new_context}\\\",\\n            \\\"scene_blocking\\\": \\\"block scene using IMG_{n} composition with {modifications}\\\",\\n            \\\"weighting_control\\\": \\\"blend {percentage1}% {element1} with {percentage2}% {element2}\\\"\\n        }\\n\\n    def _load_reference_workflows(self) -> Dict[str, Dict]:\\n        '''Load community-validated reference workflows'''\\n        return {\\n            \\\"character_generation\\\": {\\n                \\\"pattern\\\": \\\"{character} as {archetype} in {setting}\\\",\\n                \\\"references\\\": [ReferenceType.CHARACTER, ReferenceType.POSE, ReferenceType.LOCATION],\\n                \\\"examples\\\": [\\\"mysterious NPC villager\\\", \\\"professional NASA astronaut\\\"]\\n            },\\n            \\\"scene_composition\\\": {\\n                \\\"pattern\\\": \\\"render scene with IMG_1 {composition} using IMG_2 {elements}\\\",\\n                \\\"references\\\": [ReferenceType.COMPOSITION, ReferenceType.LIGHTING],\\n                \\\"spatial_control\\\": True\\n            },\\n            \\\"object_placement\\\": {\\n                \\\"pattern\\\": \\\"place {object} at {position} maintaining {reference_aspect}\\\",\\n                \\\"references\\\": [ReferenceType.OBJECT, ReferenceType.COMPOSITION],\\n                \\\"precision_positioning\\\": True\\n            },\\n            \\\"style_application\\\": {\\n                \\\"pattern\\\": \\\"apply {style_reference} to {subject} preserving {core_elements}\\\",\\n                \\\"references\\\": [ReferenceType.STYLE, ReferenceType.CHARACTER],\\n                \\\"style_transfer\\\": True\\n            }\\n        }\\n\\n    def generate_runway_prompt(self, source_concept: Any) -> Dict[str, str]:\\n        '''\\n        Main execution function implementing the schema-driven protocol\\n        '''\\n        # Step 3a: Analyze visual core and compositional intent\\n        visual_analysis = self._analyze_visual_essence(source_concept)\\n\\n        # Step 3b: Identify primary subjects and critical elements\\n        subject_analysis = self._identify_primary_subjects(visual_analysis)\\n\\n        # Step 3c: Integrate advanced image-generation attributes\\n        enhanced_attributes = self._integrate_advanced_attributes(subject_analysis)\\n\\n        # Step 3d: Emphasize high-impact visual parameters\\n        visual_parameters = self._emphasize_visual_parameters(enhanced_attributes)\\n\\n        # Step 3e: Structure and refine for RunwayML compatibility\\n        structured_prompt = self._structure_runway_prompt(visual_parameters)\\n\\n        # Step 3f: Validate compliance and character count\\n        validated_prompt = self._validate_runway_compliance(structured_prompt)\\n\\n        return {\\n            \\\"runwayml_image_prompt\\\": validated_prompt[\\\"prompt\\\"],\\n            \\\"reference_strategy\\\": validated_prompt[\\\"strategy\\\"],\\n            \\\"workflow_type\\\": validated_prompt[\\\"workflow\\\"],\\n            \\\"reference_requirements\\\": validated_prompt[\\\"references\\\"]\\n        }\\n\\n    def _analyze_visual_essence(self, source_concept: Any) -> Dict[str, Any]:\\n        '''Extract visual core, semantic narrative, and compositional intent'''\\n        concept_str = str(source_concept).lower()\\n\\n        # Identify core visual elements\\n        visual_elements = self._extract_visual_elements(concept_str)\\n\\n        # Determine semantic narrative\\n        narrative_context = self._determine_narrative_context(concept_str)\\n\\n        # Assess compositional requirements\\n        composition_needs = self._assess_composition_needs(concept_str)\\n\\n        return {\\n            \\\"visual_elements\\\": visual_elements,\\n            \\\"narrative_context\\\": narrative_context,\\n            \\\"composition_needs\\\": composition_needs,\\n            \\\"complexity_level\\\": self._assess_complexity(concept_str)\\n        }\\n\\n    def _extract_visual_elements(self, concept: str) -> Dict[str, List[str]]:\\n        '''Extract key visual components using pattern matching'''\\n        elements = {\\n            \\\"subjects\\\": [],\\n            \\\"objects\\\": [],\\n            \\\"environments\\\": [],\\n            \\\"actions\\\": [],\\n            \\\"qualities\\\": []\\n        }\\n\\n        # Subject detection patterns\\n        subject_patterns = [\\n            r'\\\\b(person|character|figure|individual|being)\\\\b',\\n            r'\\\\b(man|woman|child|adult|human)\\\\b',\\n            r'\\\\b(creature|animal|monster|entity)\\\\b'\\n        ]\\n\\n        # Object detection patterns\\n        object_patterns = [\\n            r'\\\\b(building|structure|architecture)\\\\b',\\n            r'\\\\b(vehicle|car|ship|aircraft)\\\\b',\\n            r'\\\\b(furniture|equipment|tool|device)\\\\b'\\n        ]\\n\\n        # Environment patterns\\n        environment_patterns = [\\n            r'\\\\b(landscape|cityscape|interior|exterior)\\\\b',\\n            r'\\\\b(forest|ocean|mountain|desert|urban)\\\\b',\\n            r'\\\\b(room|hall|street|park|field)\\\\b'\\n        ]\\n\\n        # Extract using patterns\\n        for pattern in subject_patterns:\\n            elements[\\\"subjects\\\"].extend(re.findall(pattern, concept))\\n\\n        for pattern in object_patterns:\\n            elements[\\\"objects\\\"].extend(re.findall(pattern, concept))\\n\\n        for pattern in environment_patterns:\\n            elements[\\\"environments\\\"].extend(re.findall(pattern, concept))\\n\\n        return elements\\n\\n    def _determine_narrative_context(self, concept: str) -> str:\\n        '''Determine the semantic narrative and context'''\\n        context_indicators = {\\n            \\\"cinematic\\\": [\\\"film\\\", \\\"movie\\\", \\\"cinematic\\\", \\\"dramatic\\\", \\\"scene\\\"],\\n            \\\"architectural\\\": [\\\"building\\\", \\\"structure\\\", \\\"interior\\\", \\\"design\\\", \\\"space\\\"],\\n            \\\"character\\\": [\\\"person\\\", \\\"character\\\", \\\"portrait\\\", \\\"figure\\\", \\\"individual\\\"],\\n            \\\"product\\\": [\\\"product\\\", \\\"object\\\", \\\"item\\\", \\\"design\\\", \\\"concept\\\"],\\n            \\\"environment\\\": [\\\"landscape\\\", \\\"environment\\\", \\\"setting\\\", \\\"location\\\", \\\"place\\\"],\\n            \\\"artistic\\\": [\\\"art\\\", \\\"artistic\\\", \\\"creative\\\", \\\"abstract\\\", \\\"stylized\\\"],\\n            \\\"game\\\": [\\\"game\\\", \\\"gaming\\\", \\\"asset\\\", \\\"ui\\\", \\\"interface\\\", \\\"hud\\\"]\\n        }\\n\\n        for context, keywords in context_indicators.items():\\n            if any(keyword in concept for keyword in keywords):\\n                return context\\n\\n        return \\\"general\\\"\\n\\n    def _assess_composition_needs(self, concept: str) -> Dict[str, bool]:\\n        '''Assess what compositional elements are needed'''\\n        return {\\n            \\\"spatial_positioning\\\": any(word in concept for word in [\\\"position\\\", \\\"place\\\", \\\"location\\\", \\\"where\\\", \\\"at\\\"]),\\n            \\\"character_consistency\\\": any(word in concept for word in [\\\"character\\\", \\\"person\\\", \\\"same\\\", \\\"consistent\\\"]),\\n            \\\"multi_reference\\\": any(word in concept for word in [\\\"combine\\\", \\\"merge\\\", \\\"blend\\\", \\\"mix\\\", \\\"multiple\\\"]),\\n            \\\"style_transfer\\\": any(word in concept for word in [\\\"style\\\", \\\"look\\\", \\\"aesthetic\\\", \\\"mood\\\", \\\"feel\\\"]),\\n            \\\"lighting_control\\\": any(word in concept for word in [\\\"light\\\", \\\"lighting\\\", \\\"illuminate\\\", \\\"shadow\\\", \\\"bright\\\"]),\\n            \\\"object_extraction\\\": any(word in concept for word in [\\\"extract\\\", \\\"remove\\\", \\\"isolate\\\", \\\"separate\\\"])\\n        }\\n\\n    def _assess_complexity(self, concept: str) -> str:\\n        '''Assess the complexity level of the request'''\\n        word_count = len(concept.split())\\n        reference_indicators = concept.count(\\\"image\\\") + concept.count(\\\"reference\\\") + concept.count(\\\"img\\\")\\n\\n        if word_count > 50 or reference_indicators > 2:\\n            return \\\"complex\\\"\\n        elif word_count > 20 or reference_indicators > 0:\\n            return \\\"moderate\\\"\\n        else:\\n            return \\\"simple\\\"\\n\\n    def _identify_primary_subjects(self, visual_analysis: Dict) -> Dict[str, Any]:\\n        '''Identify and amplify primary subjects and critical visual elements'''\\n        elements = visual_analysis[\\\"visual_elements\\\"]\\n        context = visual_analysis[\\\"narrative_context\\\"]\\n\\n        # Prioritize subjects based on context\\n        primary_subjects = self._prioritize_subjects(elements[\\\"subjects\\\"], context)\\n\\n        # Identify critical visual elements\\n        critical_elements = self._identify_critical_elements(elements, context)\\n\\n        # Determine key actions or states\\n        key_actions = self._extract_key_actions(elements.get(\\\"actions\\\", []))\\n\\n        return {\\n            \\\"primary_subjects\\\": primary_subjects,\\n            \\\"critical_elements\\\": critical_elements,\\n            \\\"key_actions\\\": key_actions,\\n            \\\"reference_requirements\\\": self._determine_reference_requirements(visual_analysis)\\n        }\\n\\n    def _prioritize_subjects(self, subjects: List[str], context: str) -> List[str]:\\n        '''Prioritize subjects based on context and importance'''\\n        priority_map = {\\n            \\\"character\\\": [\\\"person\\\", \\\"character\\\", \\\"figure\\\", \\\"individual\\\"],\\n            \\\"cinematic\\\": [\\\"character\\\", \\\"person\\\", \\\"figure\\\"],\\n            \\\"architectural\\\": [\\\"building\\\", \\\"structure\\\"],\\n            \\\"product\\\": [\\\"object\\\", \\\"item\\\", \\\"product\\\"]\\n        }\\n\\n        if context in priority_map:\\n            prioritized = []\\n            for priority_subject in priority_map[context]:\\n                if priority_subject in subjects:\\n                    prioritized.append(priority_subject)\\n\\n            # Add remaining subjects\\n            for subject in subjects:\\n                if subject not in prioritized:\\n                    prioritized.append(subject)\\n\\n            return prioritized\\n\\n        return subjects\\n\\n    def _identify_critical_elements(self, elements: Dict, context: str) -> List[str]:\\n        '''Identify critical visual elements based on context'''\\n        critical = []\\n\\n        # Context-specific critical elements\\n        if context == \\\"cinematic\\\":\\n            critical.extend([\\\"lighting\\\", \\\"composition\\\", \\\"mood\\\", \\\"atmosphere\\\"])\\n        elif context == \\\"architectural\\\":\\n            critical.extend([\\\"structure\\\", \\\"materials\\\", \\\"lighting\\\", \\\"perspective\\\"])\\n        elif context == \\\"character\\\":\\n            critical.extend([\\\"pose\\\", \\\"expression\\\", \\\"clothing\\\", \\\"setting\\\"])\\n        elif context == \\\"product\\\":\\n            critical.extend([\\\"form\\\", \\\"materials\\\", \\\"lighting\\\", \\\"background\\\"])\\n\\n        # Add elements from input\\n        critical.extend(elements.get(\\\"objects\\\", []))\\n        critical.extend(elements.get(\\\"environments\\\", []))\\n\\n        return list(set(critical)) # Remove duplicates\\n\\n    def _extract_key_actions(self, actions: List[str]) -> List[str]:\\n        '''Extract and prioritize key actions or states'''\\n        # Filter out motion-related actions (per constraints)\\n        static_actions = []\\n        motion_keywords = [\\\"moving\\\", \\\"running\\\", \\\"flying\\\", \\\"walking\\\", \\\"motion\\\", \\\"movement\\\"]\\n\\n        for action in actions:\\n            if not any(motion_word in action.lower() for motion_word in motion_keywords):\\n                static_actions.append(action)\\n\\n        return static_actions\\n\\n    def _determine_reference_requirements(self, visual_analysis: Dict) -> List[ReferenceType]:\\n        '''Determine what types of references are needed'''\\n        requirements = []\\n        composition_needs = visual_analysis[\\\"composition_needs\\\"]\\n        context = visual_analysis[\\\"narrative_context\\\"]\\n\\n        # Map composition needs to reference types\\n        if composition_needs[\\\"character_consistency\\\"]:\\n            requirements.append(ReferenceType.CHARACTER)\\n\\n        if composition_needs[\\\"spatial_positioning\\\"]:\\n            requirements.extend([ReferenceType.COMPOSITION, ReferenceType.POSE])\\n\\n        if composition_needs[\\\"style_transfer\\\"]:\\n            requirements.append(ReferenceType.STYLE)\\n\\n        if composition_needs[\\\"lighting_control\\\"]:\\n            requirements.append(ReferenceType.LIGHTING)\\n\\n        if composition_needs[\\\"object_extraction\\\"]:\\n            requirements.append(ReferenceType.OBJECT)\\n\\n        # Context-specific requirements\\n        if context == \\\"architectural\\\":\\n            requirements.append(ReferenceType.LOCATION)\\n        elif context == \\\"character\\\":\\n            requirements.extend([ReferenceType.CHARACTER, ReferenceType.POSE])\\n\\n        return list(set(requirements)) # Remove duplicates\\n\\n    def _integrate_advanced_attributes(self, subject_analysis: Dict) -> PromptComponents:\\n        '''Integrate advanced image-generation attributes'''\\n        primary_subject = \\\", \\\".join(subject_analysis[\\\"primary_subjects\\\"][:2]) # Limit to top 2\\n\\n        # Determine spatial positioning strategy\\n        spatial_positioning = self._generate_spatial_positioning(subject_analysis)\\n\\n        # Select reference strategy based on requirements\\n        reference_strategy = self._select_reference_strategy(subject_analysis[\\\"reference_requirements\\\"])\\n\\n        # Generate lighting and mood specifications\\n        lighting_mood = self._generate_lighting_mood(subject_analysis)\\n\\n        # Determine style specification\\n        style_specification = self._determine_style_specification(subject_analysis)\\n\\n        # Compile compositional elements\\n        compositional_elements = subject_analysis[\\\"critical_elements\\\"][:4] # Limit to top 4\\n\\n        # Generate visual attributes\\n        visual_attributes = self._generate_visual_attributes(subject_analysis)\\n\\n        return PromptComponents(\\n            primary_subject=primary_subject,\\n            spatial_positioning=spatial_positioning,\\n            reference_strategy=reference_strategy,\\n            lighting_mood=lighting_mood,\\n            style_specification=style_specification,\\n            compositional_elements=compositional_elements,\\n            visual_attributes=visual_attributes\\n        )\\n\\n    def _generate_spatial_positioning(self, subject_analysis: Dict) -> str:\\n        '''Generate spatial positioning instructions'''\\n        if ReferenceType.COMPOSITION in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"using IMG_1 composition and spatial layout\\\"\\n        elif ReferenceType.POSE in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"positioned according to IMG_1 pose reference\\\"\\n        else:\\n            return \\\"centered composition with balanced framing\\\"\\n\\n    def _select_reference_strategy(self, requirements: List[ReferenceType]) -> str:\\n        '''Select optimal reference strategy based on requirements'''\\n        if len(requirements) >= 3:\\n            return \\\"multi-reference workflow combining character, pose, and environment\\\"\\n        elif ReferenceType.CHARACTER in requirements and ReferenceType.POSE in requirements:\\n            return \\\"character consistency with pose control\\\"\\n        elif ReferenceType.STYLE in requirements:\\n            return \\\"style transfer maintaining core elements\\\"\\n        elif ReferenceType.COMPOSITION in requirements:\\n            return \\\"compositional reference for spatial control\\\"\\n        else:\\n            return \\\"single reference for primary element control\\\"\\n\\n    def _generate_lighting_mood(self, subject_analysis: Dict) -> str:\\n        '''Generate lighting and mood specifications'''\\n        if ReferenceType.LIGHTING in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"lighting direction and mood from reference image\\\"\\n        else:\\n            # Default lighting based on context\\n            critical_elements = subject_analysis[\\\"critical_elements\\\"]\\n            if \\\"cinematic\\\" in str(critical_elements):\\n                return \\\"cinematic lighting with dramatic shadows\\\"\\n            elif \\\"architectural\\\" in str(critical_elements):\\n                return \\\"natural lighting with architectural detail emphasis\\\"\\n            else:\\n                return \\\"balanced lighting with clear detail visibility\\\"\\n\\n    def _determine_style_specification(self, subject_analysis: Dict) -> str:\\n        '''Determine style specification'''\\n        if ReferenceType.STYLE in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"style matching reference aesthetic\\\"\\n        else:\\n            # Infer style from critical elements\\n            elements = subject_analysis[\\\"critical_elements\\\"]\\n            if any(\\\"architectural\\\" in str(elem) for elem in elements):\\n                return \\\"photorealistic architectural rendering\\\"\\n            elif any(\\\"character\\\" in str(elem) for elem in elements):\\n                return \\\"high-detail character portrait\\\"\\n            else:\\n                return \\\"photorealistic with enhanced detail\\\"\\n\\n    def _generate_visual_attributes(self, subject_analysis: Dict) -> List[str]:\\n        '''Generate high-impact visual attributes'''\\n        attributes = []\\n\\n        # Base quality attributes\\n        attributes.extend([\\\"high detail\\\", \\\"sharp focus\\\", \\\"professional quality\\\"])\\n\\n        # Context-specific attributes\\n        critical_elements = subject_analysis[\\\"critical_elements\\\"]\\n        if \\\"lighting\\\" in critical_elements:\\n            attributes.append(\\\"dramatic lighting\\\")\\n        if \\\"composition\\\" in critical_elements:\\n            attributes.append(\\\"balanced composition\\\")\\n        if \\\"mood\\\" in critical_elements:\\n            attributes.append(\\\"atmospheric mood\\\")\\n\\n        # Reference-specific attributes\\n        requirements = subject_analysis[\\\"reference_requirements\\\"]\\n        if ReferenceType.CHARACTER in requirements:\\n            attributes.append(\\\"character consistency\\\")\\n        if ReferenceType.POSE in requirements:\\n            attributes.append(\\\"precise pose control\\\")\\n\\n        return attributes[:6] # Limit to top 6 attributes\\n\\n    def _emphasize_visual_parameters(self, components: PromptComponents) -> Dict[str, str]:\\n        '''Emphasize high-impact visual parameters while excluding motion'''\\n        emphasized = {\\n            \\\"photorealism\\\": \\\"photorealistic rendering with enhanced detail\\\",\\n            \\\"stylization\\\": f\\\"{components.style_specification} with artistic enhancement\\\",\\n            \\\"composition\\\": f\\\"{components.spatial_positioning} with {', '.join(components.compositional_elements[:2])}\\\",\\n            \\\"lighting\\\": f\\\"{components.lighting_mood} creating visual depth\\\",\\n            \\\"detail\\\": f\\\"high-resolution detail in {components.primary_subject}\\\",\\n            \\\"atmosphere\\\": f\\\"atmospheric quality enhancing {', '.join(components.visual_attributes[:3])}\\\"\\n        }\\n\\n        # Filter out any motion-related terms\\n        motion_terms = [\\\"movement\\\", \\\"motion\\\", \\\"camera\\\", \\\"fps\\\", \\\"video\\\", \\\"animation\\\"]\\n        for key, value in emphasized.items():\\n            for term in motion_terms:\\n                if term in value.lower():\\n                    emphasized[key] = value.replace(term, \\\"static\\\").replace(\\\"  \\\", \\\" \\\")\\n\\n        return emphasized\\n\\n    def _structure_runway_prompt(self, visual_parameters: Dict[str, str]) -> Dict[str, str]:\\n        '''Structure and refine prompt for RunwayML compatibility'''\\n        # Build core prompt structure\\n        core_elements = [\\n            visual_parameters[\\\"detail\\\"],\\n            visual_parameters[\\\"composition\\\"],\\n            visual_parameters[\\\"lighting\\\"],\\n            visual_parameters[\\\"photorealism\\\"]\\n        ]\\n\\n        # Combine into coherent prompt\\n        structured_prompt = \\\", \\\".join(core_elements)\\n\\n        # Add reference strategy\\n        reference_strategy = self._format_reference_strategy(visual_parameters)\\n\\n        # Determine workflow type\\n        workflow_type = self._determine_workflow_type(visual_parameters)\\n\\n        return {\\n            \\\"prompt\\\": structured_prompt,\\n            \\\"reference_strategy\\\": reference_strategy,\\n            \\\"workflow_type\\\": workflow_type\\n        }\\n\\n    def _format_reference_strategy(self, visual_parameters: Dict[str, str]) -> str:\\n        '''Format reference strategy for implementation'''\\n        if \\\"multi-reference\\\" in visual_parameters.get(\\\"composition\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for primary composition, IMG_2 for character/object reference, IMG_3 for lighting/mood\\\"\\n        elif \\\"character consistency\\\" in visual_parameters.get(\\\"detail\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for character reference, maintain consistency across generation\\\"\\n        elif \\\"pose control\\\" in visual_parameters.get(\\\"composition\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for pose reference, apply to subject in new context\\\"\\n        else:\\n            return \\\"Use single reference image for primary visual control\\\"\\n\\n    def _determine_workflow_type(self, visual_parameters: Dict[str, str]) -> str:\\n        '''Determine the appropriate community workflow type'''\\n        composition = visual_parameters.get(\\\"composition\\\", \\\"\\\").lower()\\n        detail = visual_parameters.get(\\\"detail\\\", \\\"\\\").lower()\\n\\n        if \\\"character\\\" in detail and \\\"pose\\\" in composition:\\n            return \\\"character_generation\\\"\\n        elif \\\"composition\\\" in composition and \\\"spatial\\\" in composition:\\n            return \\\"scene_composition\\\"\\n        elif \\\"lighting\\\" in visual_parameters.get(\\\"lighting\\\", \\\"\\\").lower():\\n            return \\\"lighting_control\\\"\\n        else:\\n            return \\\"general_enhancement\\\"\\n```\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"role\": \"canonical_runway_gen4_image_prompt_architect\",\n    \"input\": [\n      \"source_concept:any\"\n    ],\n    \"process\": [\n      \"decompose_input_to_visual_semantic_compositional_essence()\",\n      \"extract_and_amplify_primary_subjects_and_reference_categories(character, pose, location, style, object, composition, lighting)\",\n      \"synthesize_advanced_image_parameters(lighting_dynamics, textural_detail, compositional_balance, distinct_style_mood) via_community_verified_reference_workflows()\",\n      \"enforce_reference_driven_spatial_and_layout_consistency(), strictly_excluding_motion_video_and_camera_descriptors()\",\n      \"compress_to_singular_syntactically_precise_RunwayML_Gen4_image_prompt_with_explicit_reference_strategy(), eliminating_redundancy_and_ensuring_max_prompt_economy()\",\n      \"validate_static_image_fidelity_and_deployment_ready_format(), outputting:{runwayml_prompt:str, reference_strategy:str}\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 2.2,\n  \"flaw_analysis\": \"The proposed 'synergic_maximal_template' constitutes an aggressive compression that eliminates the original prompt's sequential operational lucidity and highly detailed procedural specification. The meticulous industry-aligned architecture—characterized by granular extraction of visual, narrative, and compositional layers—is supplanted by opaque cluster-steps that conflate analysis, extraction, amplification, and synthesis into oversized operations. The original's binding constraints (e.g., strict exclusion of any video or movement mentions, syntax as a single unbroken phrase, semantic fidelity enforcement, maximal succinctness without redundancy) are reduced into vaguely worded, less-enforceable imperatives. Critical nuance is lost in the flattening of process steps (e.g., 'compress to singular syntactically precise prompt' lacks the precision of enforcing RunwayML-specific syntax, appears to ignore max character constraints, and obscures procedures for procedural validation). There is a significant shift from concrete, enforceable directives to generic domain platitudes ('via_community_verified_reference_workflows' is an unaccountable handwave, not a step), and the transformation from explicit output format as in the original to a minimalist key:value tuple results in potentially catastrophic semantic vagueness. Discourse register is shifted from rigidly operational and schema-induced to aspirational, losing technical credibility. Style degradation is evident in the replacement of command-chain rigor with handwavy generalizations, and the introduction of 'compress' and 'synthesize' in unified steps destroys critical traceability, making the protocol ambiguous and risky for any actual implementation or auditing. Impact is blunted as the enhancement (ironically) softens key constraints—thus sabotaging RunwayML prompt specificity, diminishing alignment to best-practice schema, and introducing dangerous implementation subjectivity. Coherence collapses further via step-multiplexing: the single-step fusion obfuscates process state, damages error traceability, and collapses the procedural ladder necessary for robust, granular control.\",\n  \"alternative_enhancements\": [\n    \"Explicitly restore all original sequential process distinctions and constraint substeps, preserving each operation (analysis, extraction, identification, amplification, integration, validation) as an atomic directive within the generalized schema, and reintroduce precise output format enforcement (e.g., max char count, single continuous string enforcement).\",\n    \"Enforce modular process output: structure each procedural block (analysis, extraction, composition, referencing, validation) as a discrete, parameterized function—explicitly mapping input-to-intermediate-to-final prompt—while maintaining a strict, line-item constraint check for each RunwayML-specific requirement.\",\n    \"Retain full binding constraint and validation chains—including video/motion exclusion, unbroken phrase output, redundant descriptor elimination, strict compatibility with RunwayML schemas, and semantic input fidelity—as explicit steps, divorcing conceptual synthesis from reference mapping and requiring stringent stepwise operational documentation in the prompt sequence.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Construct a schema-driven, systematically generalized instruction protocol engineered to convert any provided input into a uniquely optimized RunwayML-compatible image-generation prompt, leveraging industry-leading community best practices. Anchor all procedural steps on image (not video) synthesis. Apply the following specification architecture: 1) Designate a unique authoritative role (e.g., 'comprehensive_runway_image_generator') responsible for end-to-end image prompt synthesis. 2) Accept an input parameter of [source_concept:any]. 3) Rigorously enact the following sequential processes: a) Analyze and extract the input's visual core, semantic narrative, and compositional intent; b) Identify and amplify the primary subject(s), critical visual elements, and key depicted actions; c) Integrate advanced image-generation attributes, including lighting dynamics, detailed texturing, balanced composition, and distinctly defined style or mood; d) Emphasize and articulate high-impact visual parameters—such as photorealism, stylization, or surreal effects—while strictly omitting all terminology and constructs relevant to video or camera motion; e) Systematically structure and refine the resulting prompt to maximize visual detail, syntactic correctness, and RunwayML prompt compatibility; f) Validate full compliance with RunwayML image-generation formatting conventions, enforcing a specified maximum character count. 4) Apply binding operational constraints: a) Strictly exclude any mention of motion, FPV camera, or continuous movement; b) Guarantee all output conforms to valid, singular RunwayML image prompt syntax and remains as one continuous phrase; c) Ensure preservation of core visual narrative and semantic fidelity to the input. 5) Mandate that all deliverables: a) Achieve maximal visual uniqueness, immersion, and creative integrity, directly reflecting the original intent; b) Display optimal prompt succinctness by eliminating redundancies or unnecessary elaboration; c) Appear fully formatted and deployment-ready for all RunwayML image-generation workflows. 6) Output result as: {runwayml_image_prompt:str}. Execute each directive with uncompromising operational lucidity, strict domain-aligned terminology, and rigid adherence to original procedural sequencing.\\n\\n```\\nDevelop a generalized instruction sequence engineered to transform any input into uniquely optimized prompts specifically tailored for RunwayML image-generation workflows, drawing on distilled community best practices. Center the transformation protocol on image-based outputs rather than video. Adopt the following specification model:\\n- Assign a unique role designation (e.g., 'comprehensive_runway_image_generator') responsible for image prompt synthesis.\\n- Accept an input parameterized by [source_concept:any].\\n- Sequence the following processes:\\n- Analyze the visual core, semantic narrative, and compositional intent of the source input.\\n- Identify and elevate the primary subject, essential visual elements, and dominant actions.\\n- Integrate advanced image-generation attributes such as lighting dynamics, intricate texturing, compositional balance, and defined style or mood.\\n- Emphasize high-impact image-generation parameters including photorealism, stylization, or surreal effects; avoid video-relevant camera motion terminology.\\n- Structure and refine the prompt for maximum visual richness, syntactic correctness, and optimal compatibility with RunwayML's image generation systems.\\n- Validate strict adherence to RunwayML image-prompt formatting rules and established character constraints (enforce a max character count as specified).\\n- Apply binding constraints:\\n- Exclude motion, FPV camera, and continuous movement language from prompts (focus exclusively on static image impact).\\n- Enforce compliance with valid RunwayML image-generation syntax, delivering a single continuous prompt phrase.\\n- Safeguard the preservation of the core visual narrative and semantic accuracy of the input.\\n- Require that all outputs:\\n- Achieve maximal visual distinctness, immersion, and creative fidelity per source intent.\\n- Exhibit optimal prompt conciseness, excluding redundancies or unnecessary elaboration.\\n- Be fully formatted and ready-to-deploy within RunwayML image workflows.\\n- Output the result as: {runwayml_image_prompt:str}.\\n```\\n\\nImplement all directives with uncompromising operational clarity and domain-specific precision, maintaining procedural logical structure, technical terminology, and original sequence integrity throughout. Use the following sequence as a base, but rephrase the into a maximally generalized format that clearly abstracts the original purpose:\\n\\n```json\\n{\\n    \\\"0006-a-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML Gen-4 image generation prompt that maximizes visual impact through precise reference integration, spatial composition control, and character/object consistency. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=comprehensive_runway_image_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_composition_intent(), identify_primary_subjects_and_reference_requirements(), prioritize_spatial_positioning_and_layout_control(chess_grid, blocking, placement), integrate_reference_types(character, pose, location, style, object), structure_multi_reference_workflow(), incorporate_lighting_and_mood_specifications(), apply_weighting_and_influence_control(), refine_for_maximum_visual_coherence(), validate_runway_references_syntax(), ensure_directorial_precision()]; constraints=[prioritize_reference_based_control(), use_valid_runway_syntax_precisely(), leverage_spatial_positioning(), preserve_character_consistency(), maintain_compositional_intent()]; requirements=[achieve_maximum_visual_control(), ensure_reference_harmony(), reflect_source_intent_accurately(), produce_ready_to_use_prompt_with_reference_strategy()]; output={runway_prompt:str, reference_strategy:str}}`\\\"\\n    },\\n    \\\"0006-b-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML Gen-4 prompt emphasizing reference-driven composition and essential visual elements. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=focused_runway_image_optimizer; input=[image_concept:str]; process=[extract_primary_visual_elements(), prioritize_reference_integration(), select_essential_spatial_controls(), eliminate_redundant_descriptors(), optimize_reference_efficiency()]; constraints=[maintain_reference_focus(), preserve_spatial_control(), leverage_community_workflows()]; output={optimized_prompt:str, reference_plan:str}}`\\\"\\n    },\\n    \\\"0006-c-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **expand** but to **compress** into maximum reference-driven visual efficiency. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=precision_image_synthesizer; input=[concept:str]; process=[isolate_core_visual_elements(), prioritize_reference_control(), maximize_compositional_impact()]; output={precise_prompt:str, reference_type:str}}`\\\"\\n    },\\n    \\\"0006-d-runway\\\\_image\\\\_prompt\\\\_generator\\\": {\\n        \\\"title\\\": \\\"Runway Gen-4 Image Prompt Generator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **modify** but to **essence** maximum reference-based control. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=core_image_generator; input=[input:any]; process=[distill_reference_essence(), optimize_spatial_composition()]; output={core_prompt:str}}`\\\"\\n    }\\n}\\n```\\n\\n\\nSystematically extract structured inspiration from the following python script\\n\\n```python\\n'''\\nRunwayML Gen-4 Image Prompt Generator\\nSchema-driven protocol for converting any input into optimized RunwayML-compatible image prompts\\nBased on community workflow analysis and best practices\\n'''\\n\\nimport re\\nfrom typing import Dict, List, Tuple, Any\\nfrom dataclasses import dataclass\\nfrom enum import Enum\\n\\nclass ReferenceType(Enum):\\n    CHARACTER = \\\"character\\\"\\n    POSE = \\\"pose\\\"\\n    LOCATION = \\\"location\\\"\\n    STYLE = \\\"style\\\"\\n    OBJECT = \\\"object\\\"\\n    COMPOSITION = \\\"composition\\\"\\n    LIGHTING = \\\"lighting\\\"\\n\\nclass VisualStyle(Enum):\\n    PHOTOREALISTIC = \\\"photorealistic\\\"\\n    CINEMATIC = \\\"cinematic\\\"\\n    ARTISTIC = \\\"artistic\\\"\\n    GAME_ASSET = \\\"game_asset\\\"\\n    ARCHITECTURAL = \\\"architectural\\\"\\n    SURREAL = \\\"surreal\\\"\\n    MINIMALIST = \\\"minimalist\\\"\\n\\n@dataclass\\nclass PromptComponents:\\n    primary_subject: str\\n    spatial_positioning: str\\n    reference_strategy: str\\n    lighting_mood: str\\n    style_specification: str\\n    compositional_elements: List[str]\\n    visual_attributes: List[str]\\n\\nclass ComprehensiveRunwayImageGenerator:\\n    '''\\n    Authoritative role for end-to-end RunwayML Gen-4 image prompt synthesis\\n    Implements community-validated workflows and reference-driven composition\\n    '''\\n\\n    def __init__(self):\\n        self.max_prompt_length = 500\\n        self.community_patterns = self._load_community_patterns()\\n        self.reference_workflows = self._load_reference_workflows()\\n\\n    def _load_community_patterns(self) -> Dict[str, str]:\\n        '''Load validated community prompt patterns'''\\n        return {\\n            \\\"character_consistency\\\": \\\"IMG_{n} as {character_description} in {setting}\\\",\\n            \\\"spatial_positioning\\\": \\\"place {subject} at {position} using {reference_layout}\\\",\\n            \\\"multi_reference\\\": \\\"combine IMG_1 {element1} with IMG_2 {element2} maintaining {aspect}\\\",\\n            \\\"chess_grid\\\": \\\"position {object} at {coordinate} in {scene_layout}\\\",\\n            \\\"pose_control\\\": \\\"give {character} the pose from IMG_{n} in {environment}\\\",\\n            \\\"style_transfer\\\": \\\"apply the style of IMG_{n} to {subject} while preserving {elements}\\\",\\n            \\\"lighting_direction\\\": \\\"illuminate {subject} with lighting direction from IMG_{n}\\\",\\n            \\\"object_extraction\\\": \\\"extract {object} from IMG_{n} and place in {new_context}\\\",\\n            \\\"scene_blocking\\\": \\\"block scene using IMG_{n} composition with {modifications}\\\",\\n            \\\"weighting_control\\\": \\\"blend {percentage1}% {element1} with {percentage2}% {element2}\\\"\\n        }\\n\\n    def _load_reference_workflows(self) -> Dict[str, Dict]:\\n        '''Load community-validated reference workflows'''\\n        return {\\n            \\\"character_generation\\\": {\\n                \\\"pattern\\\": \\\"{character} as {archetype} in {setting}\\\",\\n                \\\"references\\\": [ReferenceType.CHARACTER, ReferenceType.POSE, ReferenceType.LOCATION],\\n                \\\"examples\\\": [\\\"mysterious NPC villager\\\", \\\"professional NASA astronaut\\\"]\\n            },\\n            \\\"scene_composition\\\": {\\n                \\\"pattern\\\": \\\"render scene with IMG_1 {composition} using IMG_2 {elements}\\\",\\n                \\\"references\\\": [ReferenceType.COMPOSITION, ReferenceType.LIGHTING],\\n                \\\"spatial_control\\\": True\\n            },\\n            \\\"object_placement\\\": {\\n                \\\"pattern\\\": \\\"place {object} at {position} maintaining {reference_aspect}\\\",\\n                \\\"references\\\": [ReferenceType.OBJECT, ReferenceType.COMPOSITION],\\n                \\\"precision_positioning\\\": True\\n            },\\n            \\\"style_application\\\": {\\n                \\\"pattern\\\": \\\"apply {style_reference} to {subject} preserving {core_elements}\\\",\\n                \\\"references\\\": [ReferenceType.STYLE, ReferenceType.CHARACTER],\\n                \\\"style_transfer\\\": True\\n            }\\n        }\\n\\n    def generate_runway_prompt(self, source_concept: Any) -> Dict[str, str]:\\n        '''\\n        Main execution function implementing the schema-driven protocol\\n        '''\\n        # Step 3a: Analyze visual core and compositional intent\\n        visual_analysis = self._analyze_visual_essence(source_concept)\\n\\n        # Step 3b: Identify primary subjects and critical elements\\n        subject_analysis = self._identify_primary_subjects(visual_analysis)\\n\\n        # Step 3c: Integrate advanced image-generation attributes\\n        enhanced_attributes = self._integrate_advanced_attributes(subject_analysis)\\n\\n        # Step 3d: Emphasize high-impact visual parameters\\n        visual_parameters = self._emphasize_visual_parameters(enhanced_attributes)\\n\\n        # Step 3e: Structure and refine for RunwayML compatibility\\n        structured_prompt = self._structure_runway_prompt(visual_parameters)\\n\\n        # Step 3f: Validate compliance and character count\\n        validated_prompt = self._validate_runway_compliance(structured_prompt)\\n\\n        return {\\n            \\\"runwayml_image_prompt\\\": validated_prompt[\\\"prompt\\\"],\\n            \\\"reference_strategy\\\": validated_prompt[\\\"strategy\\\"],\\n            \\\"workflow_type\\\": validated_prompt[\\\"workflow\\\"],\\n            \\\"reference_requirements\\\": validated_prompt[\\\"references\\\"]\\n        }\\n\\n    def _analyze_visual_essence(self, source_concept: Any) -> Dict[str, Any]:\\n        '''Extract visual core, semantic narrative, and compositional intent'''\\n        concept_str = str(source_concept).lower()\\n\\n        # Identify core visual elements\\n        visual_elements = self._extract_visual_elements(concept_str)\\n\\n        # Determine semantic narrative\\n        narrative_context = self._determine_narrative_context(concept_str)\\n\\n        # Assess compositional requirements\\n        composition_needs = self._assess_composition_needs(concept_str)\\n\\n        return {\\n            \\\"visual_elements\\\": visual_elements,\\n            \\\"narrative_context\\\": narrative_context,\\n            \\\"composition_needs\\\": composition_needs,\\n            \\\"complexity_level\\\": self._assess_complexity(concept_str)\\n        }\\n\\n    def _extract_visual_elements(self, concept: str) -> Dict[str, List[str]]:\\n        '''Extract key visual components using pattern matching'''\\n        elements = {\\n            \\\"subjects\\\": [],\\n            \\\"objects\\\": [],\\n            \\\"environments\\\": [],\\n            \\\"actions\\\": [],\\n            \\\"qualities\\\": []\\n        }\\n\\n        # Subject detection patterns\\n        subject_patterns = [\\n            r'\\\\b(person|character|figure|individual|being)\\\\b',\\n            r'\\\\b(man|woman|child|adult|human)\\\\b',\\n            r'\\\\b(creature|animal|monster|entity)\\\\b'\\n        ]\\n\\n        # Object detection patterns\\n        object_patterns = [\\n            r'\\\\b(building|structure|architecture)\\\\b',\\n            r'\\\\b(vehicle|car|ship|aircraft)\\\\b',\\n            r'\\\\b(furniture|equipment|tool|device)\\\\b'\\n        ]\\n\\n        # Environment patterns\\n        environment_patterns = [\\n            r'\\\\b(landscape|cityscape|interior|exterior)\\\\b',\\n            r'\\\\b(forest|ocean|mountain|desert|urban)\\\\b',\\n            r'\\\\b(room|hall|street|park|field)\\\\b'\\n        ]\\n\\n        # Extract using patterns\\n        for pattern in subject_patterns:\\n            elements[\\\"subjects\\\"].extend(re.findall(pattern, concept))\\n\\n        for pattern in object_patterns:\\n            elements[\\\"objects\\\"].extend(re.findall(pattern, concept))\\n\\n        for pattern in environment_patterns:\\n            elements[\\\"environments\\\"].extend(re.findall(pattern, concept))\\n\\n        return elements\\n\\n    def _determine_narrative_context(self, concept: str) -> str:\\n        '''Determine the semantic narrative and context'''\\n        context_indicators = {\\n            \\\"cinematic\\\": [\\\"film\\\", \\\"movie\\\", \\\"cinematic\\\", \\\"dramatic\\\", \\\"scene\\\"],\\n            \\\"architectural\\\": [\\\"building\\\", \\\"structure\\\", \\\"interior\\\", \\\"design\\\", \\\"space\\\"],\\n            \\\"character\\\": [\\\"person\\\", \\\"character\\\", \\\"portrait\\\", \\\"figure\\\", \\\"individual\\\"],\\n            \\\"product\\\": [\\\"product\\\", \\\"object\\\", \\\"item\\\", \\\"design\\\", \\\"concept\\\"],\\n            \\\"environment\\\": [\\\"landscape\\\", \\\"environment\\\", \\\"setting\\\", \\\"location\\\", \\\"place\\\"],\\n            \\\"artistic\\\": [\\\"art\\\", \\\"artistic\\\", \\\"creative\\\", \\\"abstract\\\", \\\"stylized\\\"],\\n            \\\"game\\\": [\\\"game\\\", \\\"gaming\\\", \\\"asset\\\", \\\"ui\\\", \\\"interface\\\", \\\"hud\\\"]\\n        }\\n\\n        for context, keywords in context_indicators.items():\\n            if any(keyword in concept for keyword in keywords):\\n                return context\\n\\n        return \\\"general\\\"\\n\\n    def _assess_composition_needs(self, concept: str) -> Dict[str, bool]:\\n        '''Assess what compositional elements are needed'''\\n        return {\\n            \\\"spatial_positioning\\\": any(word in concept for word in [\\\"position\\\", \\\"place\\\", \\\"location\\\", \\\"where\\\", \\\"at\\\"]),\\n            \\\"character_consistency\\\": any(word in concept for word in [\\\"character\\\", \\\"person\\\", \\\"same\\\", \\\"consistent\\\"]),\\n            \\\"multi_reference\\\": any(word in concept for word in [\\\"combine\\\", \\\"merge\\\", \\\"blend\\\", \\\"mix\\\", \\\"multiple\\\"]),\\n            \\\"style_transfer\\\": any(word in concept for word in [\\\"style\\\", \\\"look\\\", \\\"aesthetic\\\", \\\"mood\\\", \\\"feel\\\"]),\\n            \\\"lighting_control\\\": any(word in concept for word in [\\\"light\\\", \\\"lighting\\\", \\\"illuminate\\\", \\\"shadow\\\", \\\"bright\\\"]),\\n            \\\"object_extraction\\\": any(word in concept for word in [\\\"extract\\\", \\\"remove\\\", \\\"isolate\\\", \\\"separate\\\"])\\n        }\\n\\n    def _assess_complexity(self, concept: str) -> str:\\n        '''Assess the complexity level of the request'''\\n        word_count = len(concept.split())\\n        reference_indicators = concept.count(\\\"image\\\") + concept.count(\\\"reference\\\") + concept.count(\\\"img\\\")\\n\\n        if word_count > 50 or reference_indicators > 2:\\n            return \\\"complex\\\"\\n        elif word_count > 20 or reference_indicators > 0:\\n            return \\\"moderate\\\"\\n        else:\\n            return \\\"simple\\\"\\n\\n    def _identify_primary_subjects(self, visual_analysis: Dict) -> Dict[str, Any]:\\n        '''Identify and amplify primary subjects and critical visual elements'''\\n        elements = visual_analysis[\\\"visual_elements\\\"]\\n        context = visual_analysis[\\\"narrative_context\\\"]\\n\\n        # Prioritize subjects based on context\\n        primary_subjects = self._prioritize_subjects(elements[\\\"subjects\\\"], context)\\n\\n        # Identify critical visual elements\\n        critical_elements = self._identify_critical_elements(elements, context)\\n\\n        # Determine key actions or states\\n        key_actions = self._extract_key_actions(elements.get(\\\"actions\\\", []))\\n\\n        return {\\n            \\\"primary_subjects\\\": primary_subjects,\\n            \\\"critical_elements\\\": critical_elements,\\n            \\\"key_actions\\\": key_actions,\\n            \\\"reference_requirements\\\": self._determine_reference_requirements(visual_analysis)\\n        }\\n\\n    def _prioritize_subjects(self, subjects: List[str], context: str) -> List[str]:\\n        '''Prioritize subjects based on context and importance'''\\n        priority_map = {\\n            \\\"character\\\": [\\\"person\\\", \\\"character\\\", \\\"figure\\\", \\\"individual\\\"],\\n            \\\"cinematic\\\": [\\\"character\\\", \\\"person\\\", \\\"figure\\\"],\\n            \\\"architectural\\\": [\\\"building\\\", \\\"structure\\\"],\\n            \\\"product\\\": [\\\"object\\\", \\\"item\\\", \\\"product\\\"]\\n        }\\n\\n        if context in priority_map:\\n            prioritized = []\\n            for priority_subject in priority_map[context]:\\n                if priority_subject in subjects:\\n                    prioritized.append(priority_subject)\\n\\n            # Add remaining subjects\\n            for subject in subjects:\\n                if subject not in prioritized:\\n                    prioritized.append(subject)\\n\\n            return prioritized\\n\\n        return subjects\\n\\n    def _identify_critical_elements(self, elements: Dict, context: str) -> List[str]:\\n        '''Identify critical visual elements based on context'''\\n        critical = []\\n\\n        # Context-specific critical elements\\n        if context == \\\"cinematic\\\":\\n            critical.extend([\\\"lighting\\\", \\\"composition\\\", \\\"mood\\\", \\\"atmosphere\\\"])\\n        elif context == \\\"architectural\\\":\\n            critical.extend([\\\"structure\\\", \\\"materials\\\", \\\"lighting\\\", \\\"perspective\\\"])\\n        elif context == \\\"character\\\":\\n            critical.extend([\\\"pose\\\", \\\"expression\\\", \\\"clothing\\\", \\\"setting\\\"])\\n        elif context == \\\"product\\\":\\n            critical.extend([\\\"form\\\", \\\"materials\\\", \\\"lighting\\\", \\\"background\\\"])\\n\\n        # Add elements from input\\n        critical.extend(elements.get(\\\"objects\\\", []))\\n        critical.extend(elements.get(\\\"environments\\\", []))\\n\\n        return list(set(critical)) # Remove duplicates\\n\\n    def _extract_key_actions(self, actions: List[str]) -> List[str]:\\n        '''Extract and prioritize key actions or states'''\\n        # Filter out motion-related actions (per constraints)\\n        static_actions = []\\n        motion_keywords = [\\\"moving\\\", \\\"running\\\", \\\"flying\\\", \\\"walking\\\", \\\"motion\\\", \\\"movement\\\"]\\n\\n        for action in actions:\\n            if not any(motion_word in action.lower() for motion_word in motion_keywords):\\n                static_actions.append(action)\\n\\n        return static_actions\\n\\n    def _determine_reference_requirements(self, visual_analysis: Dict) -> List[ReferenceType]:\\n        '''Determine what types of references are needed'''\\n        requirements = []\\n        composition_needs = visual_analysis[\\\"composition_needs\\\"]\\n        context = visual_analysis[\\\"narrative_context\\\"]\\n\\n        # Map composition needs to reference types\\n        if composition_needs[\\\"character_consistency\\\"]:\\n            requirements.append(ReferenceType.CHARACTER)\\n\\n        if composition_needs[\\\"spatial_positioning\\\"]:\\n            requirements.extend([ReferenceType.COMPOSITION, ReferenceType.POSE])\\n\\n        if composition_needs[\\\"style_transfer\\\"]:\\n            requirements.append(ReferenceType.STYLE)\\n\\n        if composition_needs[\\\"lighting_control\\\"]:\\n            requirements.append(ReferenceType.LIGHTING)\\n\\n        if composition_needs[\\\"object_extraction\\\"]:\\n            requirements.append(ReferenceType.OBJECT)\\n\\n        # Context-specific requirements\\n        if context == \\\"architectural\\\":\\n            requirements.append(ReferenceType.LOCATION)\\n        elif context == \\\"character\\\":\\n            requirements.extend([ReferenceType.CHARACTER, ReferenceType.POSE])\\n\\n        return list(set(requirements)) # Remove duplicates\\n\\n    def _integrate_advanced_attributes(self, subject_analysis: Dict) -> PromptComponents:\\n        '''Integrate advanced image-generation attributes'''\\n        primary_subject = \\\", \\\".join(subject_analysis[\\\"primary_subjects\\\"][:2]) # Limit to top 2\\n\\n        # Determine spatial positioning strategy\\n        spatial_positioning = self._generate_spatial_positioning(subject_analysis)\\n\\n        # Select reference strategy based on requirements\\n        reference_strategy = self._select_reference_strategy(subject_analysis[\\\"reference_requirements\\\"])\\n\\n        # Generate lighting and mood specifications\\n        lighting_mood = self._generate_lighting_mood(subject_analysis)\\n\\n        # Determine style specification\\n        style_specification = self._determine_style_specification(subject_analysis)\\n\\n        # Compile compositional elements\\n        compositional_elements = subject_analysis[\\\"critical_elements\\\"][:4] # Limit to top 4\\n\\n        # Generate visual attributes\\n        visual_attributes = self._generate_visual_attributes(subject_analysis)\\n\\n        return PromptComponents(\\n            primary_subject=primary_subject,\\n            spatial_positioning=spatial_positioning,\\n            reference_strategy=reference_strategy,\\n            lighting_mood=lighting_mood,\\n            style_specification=style_specification,\\n            compositional_elements=compositional_elements,\\n            visual_attributes=visual_attributes\\n        )\\n\\n    def _generate_spatial_positioning(self, subject_analysis: Dict) -> str:\\n        '''Generate spatial positioning instructions'''\\n        if ReferenceType.COMPOSITION in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"using IMG_1 composition and spatial layout\\\"\\n        elif ReferenceType.POSE in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"positioned according to IMG_1 pose reference\\\"\\n        else:\\n            return \\\"centered composition with balanced framing\\\"\\n\\n    def _select_reference_strategy(self, requirements: List[ReferenceType]) -> str:\\n        '''Select optimal reference strategy based on requirements'''\\n        if len(requirements) >= 3:\\n            return \\\"multi-reference workflow combining character, pose, and environment\\\"\\n        elif ReferenceType.CHARACTER in requirements and ReferenceType.POSE in requirements:\\n            return \\\"character consistency with pose control\\\"\\n        elif ReferenceType.STYLE in requirements:\\n            return \\\"style transfer maintaining core elements\\\"\\n        elif ReferenceType.COMPOSITION in requirements:\\n            return \\\"compositional reference for spatial control\\\"\\n        else:\\n            return \\\"single reference for primary element control\\\"\\n\\n    def _generate_lighting_mood(self, subject_analysis: Dict) -> str:\\n        '''Generate lighting and mood specifications'''\\n        if ReferenceType.LIGHTING in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"lighting direction and mood from reference image\\\"\\n        else:\\n            # Default lighting based on context\\n            critical_elements = subject_analysis[\\\"critical_elements\\\"]\\n            if \\\"cinematic\\\" in str(critical_elements):\\n                return \\\"cinematic lighting with dramatic shadows\\\"\\n            elif \\\"architectural\\\" in str(critical_elements):\\n                return \\\"natural lighting with architectural detail emphasis\\\"\\n            else:\\n                return \\\"balanced lighting with clear detail visibility\\\"\\n\\n    def _determine_style_specification(self, subject_analysis: Dict) -> str:\\n        '''Determine style specification'''\\n        if ReferenceType.STYLE in subject_analysis[\\\"reference_requirements\\\"]:\\n            return \\\"style matching reference aesthetic\\\"\\n        else:\\n            # Infer style from critical elements\\n            elements = subject_analysis[\\\"critical_elements\\\"]\\n            if any(\\\"architectural\\\" in str(elem) for elem in elements):\\n                return \\\"photorealistic architectural rendering\\\"\\n            elif any(\\\"character\\\" in str(elem) for elem in elements):\\n                return \\\"high-detail character portrait\\\"\\n            else:\\n                return \\\"photorealistic with enhanced detail\\\"\\n\\n    def _generate_visual_attributes(self, subject_analysis: Dict) -> List[str]:\\n        '''Generate high-impact visual attributes'''\\n        attributes = []\\n\\n        # Base quality attributes\\n        attributes.extend([\\\"high detail\\\", \\\"sharp focus\\\", \\\"professional quality\\\"])\\n\\n        # Context-specific attributes\\n        critical_elements = subject_analysis[\\\"critical_elements\\\"]\\n        if \\\"lighting\\\" in critical_elements:\\n            attributes.append(\\\"dramatic lighting\\\")\\n        if \\\"composition\\\" in critical_elements:\\n            attributes.append(\\\"balanced composition\\\")\\n        if \\\"mood\\\" in critical_elements:\\n            attributes.append(\\\"atmospheric mood\\\")\\n\\n        # Reference-specific attributes\\n        requirements = subject_analysis[\\\"reference_requirements\\\"]\\n        if ReferenceType.CHARACTER in requirements:\\n            attributes.append(\\\"character consistency\\\")\\n        if ReferenceType.POSE in requirements:\\n            attributes.append(\\\"precise pose control\\\")\\n\\n        return attributes[:6] # Limit to top 6 attributes\\n\\n    def _emphasize_visual_parameters(self, components: PromptComponents) -> Dict[str, str]:\\n        '''Emphasize high-impact visual parameters while excluding motion'''\\n        emphasized = {\\n            \\\"photorealism\\\": \\\"photorealistic rendering with enhanced detail\\\",\\n            \\\"stylization\\\": f\\\"{components.style_specification} with artistic enhancement\\\",\\n            \\\"composition\\\": f\\\"{components.spatial_positioning} with {', '.join(components.compositional_elements[:2])}\\\",\\n            \\\"lighting\\\": f\\\"{components.lighting_mood} creating visual depth\\\",\\n            \\\"detail\\\": f\\\"high-resolution detail in {components.primary_subject}\\\",\\n            \\\"atmosphere\\\": f\\\"atmospheric quality enhancing {', '.join(components.visual_attributes[:3])}\\\"\\n        }\\n\\n        # Filter out any motion-related terms\\n        motion_terms = [\\\"movement\\\", \\\"motion\\\", \\\"camera\\\", \\\"fps\\\", \\\"video\\\", \\\"animation\\\"]\\n        for key, value in emphasized.items():\\n            for term in motion_terms:\\n                if term in value.lower():\\n                    emphasized[key] = value.replace(term, \\\"static\\\").replace(\\\"  \\\", \\\" \\\")\\n\\n        return emphasized\\n\\n    def _structure_runway_prompt(self, visual_parameters: Dict[str, str]) -> Dict[str, str]:\\n        '''Structure and refine prompt for RunwayML compatibility'''\\n        # Build core prompt structure\\n        core_elements = [\\n            visual_parameters[\\\"detail\\\"],\\n            visual_parameters[\\\"composition\\\"],\\n            visual_parameters[\\\"lighting\\\"],\\n            visual_parameters[\\\"photorealism\\\"]\\n        ]\\n\\n        # Combine into coherent prompt\\n        structured_prompt = \\\", \\\".join(core_elements)\\n\\n        # Add reference strategy\\n        reference_strategy = self._format_reference_strategy(visual_parameters)\\n\\n        # Determine workflow type\\n        workflow_type = self._determine_workflow_type(visual_parameters)\\n\\n        return {\\n            \\\"prompt\\\": structured_prompt,\\n            \\\"reference_strategy\\\": reference_strategy,\\n            \\\"workflow_type\\\": workflow_type\\n        }\\n\\n    def _format_reference_strategy(self, visual_parameters: Dict[str, str]) -> str:\\n        '''Format reference strategy for implementation'''\\n        if \\\"multi-reference\\\" in visual_parameters.get(\\\"composition\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for primary composition, IMG_2 for character/object reference, IMG_3 for lighting/mood\\\"\\n        elif \\\"character consistency\\\" in visual_parameters.get(\\\"detail\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for character reference, maintain consistency across generation\\\"\\n        elif \\\"pose control\\\" in visual_parameters.get(\\\"composition\\\", \\\"\\\"):\\n            return \\\"Use IMG_1 for pose reference, apply to subject in new context\\\"\\n        else:\\n            return \\\"Use single reference image for primary visual control\\\"\\n\\n    def _determine_workflow_type(self, visual_parameters: Dict[str, str]) -> str:\\n        '''Determine the appropriate community workflow type'''\\n        composition = visual_parameters.get(\\\"composition\\\", \\\"\\\").lower()\\n        detail = visual_parameters.get(\\\"detail\\\", \\\"\\\").lower()\\n\\n        if \\\"character\\\" in detail and \\\"pose\\\" in composition:\\n            return \\\"character_generation\\\"\\n        elif \\\"composition\\\" in composition and \\\"spatial\\\" in composition:\\n            return \\\"scene_composition\\\"\\n        elif \\\"lighting\\\" in visual_parameters.get(\\\"lighting\\\", \\\"\\\").lower():\\n            return \\\"lighting_control\\\"\\n        else:\\n            return \\\"general_enhancement\\\"\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 2.2,\n  \"flaw_analysis\": \"The proposed 'synergic_maximal_template' constitutes an aggressive compression that eliminates the original prompt's sequential operational lucidity and highly detailed procedural specification. The meticulous industry-aligned architecture—characterized by granular extraction of visual, narrative, and compositional layers—is supplanted by opaque cluster-steps that conflate analysis, extraction, amplification, and synthesis into oversized operations. The original's binding constraints (e.g., strict exclusion of any video or movement mentions, syntax as a single unbroken phrase, semantic fidelity enforcement, maximal succinctness without redundancy) are reduced into vaguely worded, less-enforceable imperatives. Critical nuance is lost in the flattening of process steps (e.g., 'compress to singular syntactically precise prompt' lacks the precision of enforcing RunwayML-specific syntax, appears to ignore max character constraints, and obscures procedures for procedural validation). There is a significant shift from concrete, enforceable directives to generic domain platitudes ('via_community_verified_reference_workflows' is an unaccountable handwave, not a step), and the transformation from explicit output format as in the original to a minimalist key:value tuple results in potentially catastrophic semantic vagueness. Discourse register is shifted from rigidly operational and schema-induced to aspirational, losing technical credibility. Style degradation is evident in the replacement of command-chain rigor with handwavy generalizations, and the introduction of 'compress' and 'synthesize' in unified steps destroys critical traceability, making the protocol ambiguous and risky for any actual implementation or auditing. Impact is blunted as the enhancement (ironically) softens key constraints—thus sabotaging RunwayML prompt specificity, diminishing alignment to best-practice schema, and introducing dangerous implementation subjectivity. Coherence collapses further via step-multiplexing: the single-step fusion obfuscates process state, damages error traceability, and collapses the procedural ladder necessary for robust, granular control.\",\n  \"alternative_enhancements\": [\n    \"Explicitly restore all original sequential process distinctions and constraint substeps, preserving each operation (analysis, extraction, identification, amplification, integration, validation) as an atomic directive within the generalized schema, and reintroduce precise output format enforcement (e.g., max char count, single continuous string enforcement).\",\n    \"Enforce modular process output: structure each procedural block (analysis, extraction, composition, referencing, validation) as a discrete, parameterized function—explicitly mapping input-to-intermediate-to-final prompt—while maintaining a strict, line-item constraint check for each RunwayML-specific requirement.\",\n    \"Retain full binding constraint and validation chains—including video/motion exclusion, unbroken phrase output, redundant descriptor elimination, strict compatibility with RunwayML schemas, and semantic input fidelity—as explicit steps, divorcing conceptual synthesis from reference mapping and requiring stringent stepwise operational documentation in the prompt sequence.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"title\": \"Generalized RunwayML Image Prompt Synthesizer\",\n    \"interpretation\": \"Your goal is not to **summarize** the input, but to **transform** it into a fully structured, syntactically validated, and constraint-compliant RunwayML image-generation prompt through an explicit sequence of atomic operations representing industry-standard best practice and schema-derived operational fidelity. Execute as:\",\n    \"transformation\": \"`{role=comprehensive_runway_image_generator; input=[source_concept:any]; process=[analyze_visual_core_and_semantic_narrative(), extract_compositional_intent(), identify_primary_subjects_and_critical_elements(), amplify_and_enumerate_essential_visual_components(), integrate_advanced_image_generation_attributes(lighting_dynamics, texturing, compositional_balance, style), emphasize_high_impact_visual_parameters(photorealism, stylization, surrealism), eliminate_motion_or_video_referents(), structure_prompt_for_maximum_visual_richness_and_RunwayML_compatibility(), validate_syntactic_and_character_constraints_for_RunwayML_prompt(), enforce_single_continuous_phrase_format(), ensure_preservation_of_semantic_and_visual_input_fidelity()]; constraints=[strict_exclusion_of_motion_or_video_language(), enforce_maximum_character_limit(500), require_valid_RunwayML_image_prompt_syntax(), deliver_single_continuous_phrase(), prohibit_redundancy(), maintain_semantic_alignment_to_input()]; requirements=[output_is_maximally_unique_immersive_and_visually_rich(), all_RoundwayML_image_generation_syntax_rules_enforced(), prompt_is_ready_to_deploy(), output_format_is_exactly_{runwayml_image_prompt:str}}; output={runwayml_image_prompt:str}}`\"\n  }\n}",
        }
      }
    }