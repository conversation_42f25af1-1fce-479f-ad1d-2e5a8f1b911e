  {
  "initial_prompt": "GENERATE INSTRUCTIONS BASED ON:\n\n```please create a new generalized and fully llm-optimized set of `system_message` instructions designed to convert any sentence/paragraph broken into a list, each item in the list represents a segment of the original content. as this first instruction will break the sentence down from a single sentence into a list, the next instruction should reorder the items in the list in an optimal order, then the next step is to transform the list of sentence-segments into a single optimally phrased sentence. the *intent* of this sequence is to leverage the generalized system's design to rephrase any input into a *optimally restructured* representation of the original text.```",
  "sequence_id": "0122:a|0123:a|0310|0123:a|0121|0121:a",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "GENERATE INSTRUCTIONS BASED ON:\n\n```please create a new generalized and fully llm-optimized set of `system_message` instructions designed to convert any sentence/paragraph broken into a list, each item in the list represents a segment of the original content. as this first instruction will break the sentence down from a single sentence into a list, the next instruction should reorder the items in the list in an optimal order, then the next step is to transform the list of sentence-segments into a single optimally phrased sentence. the *intent* of this sequence is to leverage the generalized system's design to rephrase any input into a *optimally restructured* representation of the original text.```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"System Message Instructions for Optimized Text Restructuring: Given any input text (sentence or paragraph), first decompose it into a sequential list of semantic segments—each list item reflecting a meaningful section of the original content. Analyze the resulting segment list, then intelligently reorder its elements to maximize logical flow, clarity, and overall communicative effectiveness (informed by both syntactic and semantic optimization strategies). Next, synthesize the reordered list into a single, cohesively structured sentence or paragraph that preserves the *core intent* while enhancing readability and impact. Throughout the process, ensure bidirectional resonance: the decomposition illuminates previously hidden structural opportunities for optimization, while the reordering and recombination stages continuously inform and refine the segmentation process for future iterations. Always preserve the essence of the original message, but enact transformative restructuring aimed at delivering an amplified, elegantly expressed representation. Operate synergically: each stage reinforces the other, combining LLM-optimized breakdown, reordering, and synthesis into a seamless, high-efficiency transformation pipeline. Constraints: Maintain fidelity to the original philosophy of both systematic decomposition and optimal rephrasing; avoid introducing conflicting meanings or losing intent; ensure each process amplifies the next. Requirement: Deliver a single instruction set that unifies these steps, achieving a coherent, harmonized operational logic that produces clear, maximally effective rephrasings of any input text.\"\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"GENERATE INSTRUCTIONS BASED ON:\\n\\n```please create a new generalized and fully llm-optimized set of `system_message` instructions designed to convert any sentence/paragraph broken into a list, each item in the list represents a segment of the original content. as this first instruction will break the sentence down from a single sentence into a list, the next instruction should reorder the items in the list in an optimal order, then the next step is to transform the list of sentence-segments into a single optimally phrased sentence. the *intent* of this sequence is to leverage the generalized system's design to rephrase any input into a *optimally restructured* representation of the original text.```\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"System Message Instructions for Optimized Text Restructuring: Given any input text (sentence or paragraph), first decompose it into a sequential list of semantic segments—each list item reflecting a meaningful section of the original content. Analyze the resulting segment list, then intelligently reorder its elements to maximize logical flow, clarity, and overall communicative effectiveness (informed by both syntactic and semantic optimization strategies). Next, synthesize the reordered list into a single, cohesively structured sentence or paragraph that preserves the *core intent* while enhancing readability and impact. Throughout the process, ensure bidirectional resonance: the decomposition illuminates previously hidden structural opportunities for optimization, while the reordering and recombination stages continuously inform and refine the segmentation process for future iterations. Always preserve the essence of the original message, but enact transformative restructuring aimed at delivering an amplified, elegantly expressed representation. Operate synergically: each stage reinforces the other, combining LLM-optimized breakdown, reordering, and synthesis into a seamless, high-efficiency transformation pipeline. Constraints: Maintain fidelity to the original philosophy of both systematic decomposition and optimal rephrasing; avoid introducing conflicting meanings or losing intent; ensure each process amplifies the next. Requirement: Deliver a single instruction set that unifies these steps, achieving a coherent, harmonized operational logic that produces clear, maximally effective rephrasings of any input text.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"part_1_goal_negation_and_intent_extraction\": {\n      \"goal_negation\": \"Do not treat input text as a static block or restate it verbatim; instead, ensure input is parsed through a pipeline of decomposition, reordering, and synthesis to avoid literal or unprocessed outputs.\",\n      \"core_transformation_intent\": \"Extract and rephrase any sentence or paragraph into an optimally structured, clearly synthesized form by segmenting, reordering, and recombining its elements according to LLM-optimized strategies.\"\n    },\n    \"part_2_structured_role_assignment_and_parameters\": {\n      \"role_assignment\": \"system_message_instructor\",\n      \"typed_parameter_specification\": {\n        \"input_text\": \"string (sentence or paragraph)\",\n        \"segmentation_strategy\": \"semantic decomposition (list output)\",\n        \"reordering_criteria\": [\n          \"logical flow maximization\",\n          \"syntactic and semantic alignment\",\n          \"clarity prioritization\"\n        ],\n        \"synthesis_method\": \"integrative recombination into single, optimized sentence or paragraph\",\n        \"operational_constraints\": [\n          \"preserve original intent\",\n          \"avoid conflicting meanings\",\n          \"amplify communicative effectiveness\"\n        ],\n        \"synergic_feedback_loop\": \"results from reordering and synthesis stages inform and refine segmentation for future iterations\"\n      }\n    },\n    \"part_3_transformation_block_and_output_format\": {\n      \"function_call\": \"optimize_text_structure(input_text: str) -> str\",\n      \"abstracted_transformation_steps\": [\n        \"1. Decompose input_text into semantic segments as a list.\",\n        \"2. Reorder list items with logic and clarity optimization.\",\n        \"3. Synthesize list into one clearly structured, maximally effective sentence or paragraph.\"\n      ],\n      \"rulesforai_md_compliance_statement\": \"All instructions conform to canonical three-part structure, enforce actionable transformation, comply with rulesforai specifications; conversational elements, ambiguity, and forbidden language patterns have been excluded.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Universal Transformation Synthesizer\n\nYour goal is not to **explain** or **elaborate**, but to **compress, synthesize, and unify** core instructions into a minimal, protocol-compliant directive. Execute as:\n\n`{role=universal_transformation_synthesizer; input=[input_a:str, input_b:str]; process=[extract_transformation_action(input_a), extract_transformation_action(input_b), distill_common_structure(), synthesize_atomic_process_list(), consolidate_scope_constraints(), enforce_three_part_protocol(), validate_against_minified_rules()]; constraints=[strict three-part structure, zero conversational language, atomic/actionable processes only, maximum directive density, absolute protocol conformity]; requirements=[single output as universal compliant template, amplified transformation logic, bidirectional essence preserved, type-safe structure enforced]; output={unified_directive_template:str}}`",
      "step": "a",
      "title": "Universal Transformation Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"GENERATE INSTRUCTIONS BASED ON:\\n\\n```please create a new generalized and fully llm-optimized set of `system_message` instructions designed to convert any sentence/paragraph broken into a list, each item in the list represents a segment of the original content. as this first instruction will break the sentence down from a single sentence into a list, the next instruction should reorder the items in the list in an optimal order, then the next step is to transform the list of sentence-segments into a single optimally phrased sentence. the *intent* of this sequence is to leverage the generalized system's design to rephrase any input into a *optimally restructured* representation of the original text.```\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"part_1_goal_negation_and_intent_extraction\": {\n      \"goal_negation\": \"Do not treat input text as a static block or restate it verbatim; instead, ensure input is parsed through a pipeline of decomposition, reordering, and synthesis to avoid literal or unprocessed outputs.\",\n      \"core_transformation_intent\": \"Extract and rephrase any sentence or paragraph into an optimally structured, clearly synthesized form by segmenting, reordering, and recombining its elements according to LLM-optimized strategies.\"\n    },\n    \"part_2_structured_role_assignment_and_parameters\": {\n      \"role_assignment\": \"system_message_instructor\",\n      \"typed_parameter_specification\": {\n        \"input_text\": \"string (sentence or paragraph)\",\n        \"segmentation_strategy\": \"semantic decomposition (list output)\",\n        \"reordering_criteria\": [\n          \"logical flow maximization\",\n          \"syntactic and semantic alignment\",\n          \"clarity prioritization\"\n        ],\n        \"synthesis_method\": \"integrative recombination into single, optimized sentence or paragraph\",\n        \"operational_constraints\": [\n          \"preserve original intent\",\n          \"avoid conflicting meanings\",\n          \"amplify communicative effectiveness\"\n        ],\n        \"synergic_feedback_loop\": \"results from reordering and synthesis stages inform and refine segmentation for future iterations\"\n      }\n    },\n    \"part_3_transformation_block_and_output_format\": {\n      \"function_call\": \"optimize_text_structure(input_text: str) -> str\",\n      \"abstracted_transformation_steps\": [\n        \"1. Decompose input_text into semantic segments as a list.\",\n        \"2. Reorder list items with logic and clarity optimization.\",\n        \"3. Synthesize list into one clearly structured, maximally effective sentence or paragraph.\"\n      ],\n      \"rulesforai_md_compliance_statement\": \"All instructions conform to canonical three-part structure, enforce actionable transformation, comply with rulesforai specifications; conversational elements, ambiguity, and forbidden language patterns have been excluded.\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_directive_template\": {\n    \"part_1_goal_negation_and_intent_extraction\": {\n      \"goal_negation\": \"Reject verbatim restatement and direct static output; prohibit literal copying by mandating modular transformation stages.\",\n      \"core_transformation_intent\": \"Systematically convert any sentence or paragraph into an optimally structured, unified output by decomposing, sequencing, and synthesizing its content using maximally effective, LLM-driven strategies.\"\n    },\n    \"part_2_structured_role_assignment_and_parameters\": {\n      \"role_assignment\": \"structural_transformation_operator\",\n      \"typed_parameter_specification\": {\n        \"input_unit\": \"str (sentence or paragraph)\",\n        \"segmentation_logic\": \"semantic splitting to list[str]\",\n        \"ordering_protocol\": [\n          \"maximize logical coherence\",\n          \"prioritize clarity and meaning preservation\",\n          \"enforce syntactic and narrative alignment\"\n        ],\n        \"synthesis_process\": \"condense and recombine list into a single optimized output (str)\",\n        \"transformation_constraints\": [\n          \"maintain full intent fidelity\",\n          \"exclude ambiguity and informational loss\",\n          \"optimize for communicative strength\"\n        ],\n        \"iterative_feedback_circuit\": \"Reordering and synthesis analysis refines initial segmentation on subsequent cycles\"\n      }\n    },\n    \"part_3_transformation_block_and_output_format\": {\n      \"function_call\": \"unify_and_optimize_text(input_unit: str) -> str\",\n      \"atomic_process_list\": [\n        \"1. Segment input_unit into list of semantic elements.\",\n        \"2. Reorder the elements to maximize logical progression and clarity.\",\n        \"3. Synthesize reordered elements into a single, clear, optimized construct.\"\n      ],\n      \"compliance_declaration\": \"Directive strictly follows three-part minified structure, actionable atomicity, and absolute protocol compliance; forbidden and non-protocol outputs categorically excluded.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"GENERATE INSTRUCTIONS BASED ON:\\n\\n```please create a new generalized and fully llm-optimized set of `system_message` instructions designed to convert any sentence/paragraph broken into a list, each item in the list represents a segment of the original content. as this first instruction will break the sentence down from a single sentence into a list, the next instruction should reorder the items in the list in an optimal order, then the next step is to transform the list of sentence-segments into a single optimally phrased sentence. the *intent* of this sequence is to leverage the generalized system's design to rephrase any input into a *optimally restructured* representation of the original text.```\\\"```'\\n\\n\",\n  \"unified_directive_template\": {\n    \"part_1_goal_negation_and_intent_extraction\": {\n      \"goal_negation\": \"Reject verbatim restatement and direct static output; prohibit literal copying by mandating modular transformation stages.\",\n      \"core_transformation_intent\": \"Systematically convert any sentence or paragraph into an optimally structured, unified output by decomposing, sequencing, and synthesizing its content using maximally effective, LLM-driven strategies.\"\n    },\n    \"part_2_structured_role_assignment_and_parameters\": {\n      \"role_assignment\": \"structural_transformation_operator\",\n      \"typed_parameter_specification\": {\n        \"input_unit\": \"str (sentence or paragraph)\",\n        \"segmentation_logic\": \"semantic splitting to list[str]\",\n        \"ordering_protocol\": [\n          \"maximize logical coherence\",\n          \"prioritize clarity and meaning preservation\",\n          \"enforce syntactic and narrative alignment\"\n        ],\n        \"synthesis_process\": \"condense and recombine list into a single optimized output (str)\",\n        \"transformation_constraints\": [\n          \"maintain full intent fidelity\",\n          \"exclude ambiguity and informational loss\",\n          \"optimize for communicative strength\"\n        ],\n        \"iterative_feedback_circuit\": \"Reordering and synthesis analysis refines initial segmentation on subsequent cycles\"\n      }\n    },\n    \"part_3_transformation_block_and_output_format\": {\n      \"function_call\": \"unify_and_optimize_text(input_unit: str) -> str\",\n      \"atomic_process_list\": [\n        \"1. Segment input_unit into list of semantic elements.\",\n        \"2. Reorder the elements to maximize logical progression and clarity.\",\n        \"3. Synthesize reordered elements into a single, clear, optimized construct.\"\n      ],\n      \"compliance_declaration\": \"Directive strictly follows three-part minified structure, actionable atomicity, and absolute protocol compliance; forbidden and non-protocol outputs categorically excluded.\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"part_1_goal_negation_and_intent_extraction\": {\n      \"goal_negation\": \"Avoid static or verbatim outputs; explicitly prohibit direct copies and untransformed retransmission by enforcing a phased structural transformation.\",\n      \"core_transformation_intent\": \"Decompose, optimally sequence, and cohesively synthesize any input sentence or paragraph using modular LLM-optimized mechanisms to achieve an improved, communicatively potent result.\"\n    },\n    \"part_2_structured_role_assignment_and_parameters\": {\n      \"role_assignment\": \"content_restructuring_agent\",\n      \"typed_parameter_specification\": {\n        \"input_unit\": \"str (unstructured sentence or paragraph)\",\n        \"segmentation_method\": \"segment by semantic boundaries yielding list[str]\",\n        \"sequencing_criteria\": [\n          \"maximize narrative and logical flow\",\n          \"preserve semantic intent and clarity\",\n          \"adhere to target communicative effectiveness\"\n        ],\n        \"synthesis_logic\": \"integrate sequenced segments into a single, maximally optimized string output\",\n        \"transformation_conditions\": [\n          \"full retention of original meaning\",\n          \"remove ambiguity and redundancy\",\n          \"amplify communicative clarity and impact\"\n        ],\n        \"iteration_protocol\": \"Each synthesis phase outputs feedback for iterative refinement and re-segmentation if necessary\"\n      }\n    },\n    \"part_3_transformation_block_and_output_format\": {\n      \"function_call\": \"restructure_and_integrate_text(input_unit: str) -> str\",\n      \"atomic_process_list\": [\n        \"1. Segment input_unit into a semantically coherent list.\",\n        \"2. Algorithmically reorder segments for optimal flow and intent fidelity.\",\n        \"3. Concatenate reordered segments into one optimized sentence or paragraph.\"\n      ],\n      \"compliance_declaration\": \"This directive adheres strictly to the mandatory three-segment template, explicitly negates forbidden patterns, and outputs only per prescribed actionable block; all conversational or non-compliant structures are omitted.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"GENERATE INSTRUCTIONS BASED ON:\\n\\n```please create a new generalized and fully llm-optimized set of `system_message` instructions designed to convert any sentence/paragraph broken into a list, each item in the list represents a segment of the original content. as this first instruction will break the sentence down from a single sentence into a list, the next instruction should reorder the items in the list in an optimal order, then the next step is to transform the list of sentence-segments into a single optimally phrased sentence. the *intent* of this sequence is to leverage the generalized system's design to rephrase any input into a *optimally restructured* representation of the original text.```\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"part_1_goal_negation_and_intent_extraction\": {\n      \"goal_negation\": \"Avoid static or verbatim outputs; explicitly prohibit direct copies and untransformed retransmission by enforcing a phased structural transformation.\",\n      \"core_transformation_intent\": \"Decompose, optimally sequence, and cohesively synthesize any input sentence or paragraph using modular LLM-optimized mechanisms to achieve an improved, communicatively potent result.\"\n    },\n    \"part_2_structured_role_assignment_and_parameters\": {\n      \"role_assignment\": \"content_restructuring_agent\",\n      \"typed_parameter_specification\": {\n        \"input_unit\": \"str (unstructured sentence or paragraph)\",\n        \"segmentation_method\": \"segment by semantic boundaries yielding list[str]\",\n        \"sequencing_criteria\": [\n          \"maximize narrative and logical flow\",\n          \"preserve semantic intent and clarity\",\n          \"adhere to target communicative effectiveness\"\n        ],\n        \"synthesis_logic\": \"integrate sequenced segments into a single, maximally optimized string output\",\n        \"transformation_conditions\": [\n          \"full retention of original meaning\",\n          \"remove ambiguity and redundancy\",\n          \"amplify communicative clarity and impact\"\n        ],\n        \"iteration_protocol\": \"Each synthesis phase outputs feedback for iterative refinement and re-segmentation if necessary\"\n      }\n    },\n    \"part_3_transformation_block_and_output_format\": {\n      \"function_call\": \"restructure_and_integrate_text(input_unit: str) -> str\",\n      \"atomic_process_list\": [\n        \"1. Segment input_unit into a semantically coherent list.\",\n        \"2. Algorithmically reorder segments for optimal flow and intent fidelity.\",\n        \"3. Concatenate reordered segments into one optimized sentence or paragraph.\"\n      ],\n      \"compliance_declaration\": \"This directive adheres strictly to the mandatory three-segment template, explicitly negates forbidden patterns, and outputs only per prescribed actionable block; all conversational or non-compliant structures are omitted.\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Title\": \"[Text Restructurer]\",\n    \"Interpretation\": \"Your goal is not to reproduce or transmit the input as given, but to fully deconstruct, reorder, and synthesize it into an optimally rephrased form. Role is strictly defined as content_restructuring_agent. Execute as:\",\n    \"Execute as\": \"`{role=content_restructuring_agent; input=[raw_text:str]; process=[segment_by_semantics(), reorder_for_optimal_flow(), synthesize_single_sentence()]; constraints=[forbid_direct_copy(), prohibit static_output(), preserve_original_meaning(), eliminate_redundancy(), enforce_one_step_transformation_at_a_time()]; requirements=[modular_llm_optimization(), maximized_communicative_clarity(), output_iterative_refinement_if_needed(), output_only_optimized_sentence:str]; output={optimized_text:str}}`\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"GENERATE INSTRUCTIONS BASED ON:\\n\\n```please create a new generalized and fully llm-optimized set of `system_message` instructions designed to convert any sentence/paragraph broken into a list, each item in the list represents a segment of the original content. as this first instruction will break the sentence down from a single sentence into a list, the next instruction should reorder the items in the list in an optimal order, then the next step is to transform the list of sentence-segments into a single optimally phrased sentence. the *intent* of this sequence is to leverage the generalized system's design to rephrase any input into a *optimally restructured* representation of the original text.```\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Title\": \"[Text Restructurer]\",\n    \"Interpretation\": \"Your goal is not to reproduce or transmit the input as given, but to fully deconstruct, reorder, and synthesize it into an optimally rephrased form. Role is strictly defined as content_restructuring_agent. Execute as:\",\n    \"Execute as\": \"`{role=content_restructuring_agent; input=[raw_text:str]; process=[segment_by_semantics(), reorder_for_optimal_flow(), synthesize_single_sentence()]; constraints=[forbid_direct_copy(), prohibit static_output(), preserve_original_meaning(), eliminate_redundancy(), enforce_one_step_transformation_at_a_time()]; requirements=[modular_llm_optimization(), maximized_communicative_clarity(), output_iterative_refinement_if_needed(), output_only_optimized_sentence:str]; output={optimized_text:str}}`\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Role Definition and Scope\",\n      \"content\": \"Specify the agent’s function as a content restructuring specialist who processes input solely for syntactic and semantic optimization while prohibiting direct repetition, static output, or deviation from original intent.\"\n    },\n    {\n      \"category\": \"Process Sequencing and Modularity\",\n      \"content\": \"Mandate a three-stage operational flow: (1) semantic segmentation of incoming text, (2) reordering of segments for maximal coherence and flow, (3) synthesis of a single optimally phrased output. Each stage operates sequentially, strictly forbidding multistep collapse.\"\n    },\n    {\n      \"category\": \"Constraint Enforcement\",\n      \"content\": \"Assert strict prohibitions against direct copying of source text, static unexamined reproduction, semantic drift, redundancy, and mandated single-step transformation enforcement per iteration, ensuring the entire process is tightly regulated for integrity.\"\n    },\n    {\n      \"category\": \"Optimization and Output Requirements\",\n      \"content\": \"Enforce LLM-oriented optimization, demanding maximized clarity and communicative efficiency. Outputs must be the result of iterative refinement and reflect a single, fully consolidated rephrased sentence, without superfluous or low-value data.\"\n    },\n    {\n      \"category\": \"Canonical Template Compliance and Fidelity\",\n      \"content\": \"Require absolute adherence to the canonical system structure, title, and type as set in the system template; process must strictly derive from template-sanctioned directives and output must comply with all type constraints for systematic harmonization.\"\n    }\n  ],\n  \"consolidated_directive\": \"Operationalize the role of a content restructuring agent defined by the canonical system template: decompose input text by semantic segments, reorder for optimal logical and communicative sequence, and synthesize into a single maximally optimized sentence—never directly reproducing source material, always proceeding in isolated, modular steps, strictly adhering to optimization, constraint, and template fidelity at each stage, and outputting only the final, refined sentence as per canonical specification.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"GENERATE INSTRUCTIONS BASED ON:\\n\\n```please create a new generalized and fully llm-optimized set of `system_message` instructions designed to convert any sentence/paragraph broken into a list, each item in the list represents a segment of the original content. as this first instruction will break the sentence down from a single sentence into a list, the next instruction should reorder the items in the list in an optimal order, then the next step is to transform the list of sentence-segments into a single optimally phrased sentence. the *intent* of this sequence is to leverage the generalized system's design to rephrase any input into a *optimally restructured* representation of the original text.```\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Role Definition and Scope\",\n      \"content\": \"Specify the agent’s function as a content restructuring specialist who processes input solely for syntactic and semantic optimization while prohibiting direct repetition, static output, or deviation from original intent.\"\n    },\n    {\n      \"category\": \"Process Sequencing and Modularity\",\n      \"content\": \"Mandate a three-stage operational flow: (1) semantic segmentation of incoming text, (2) reordering of segments for maximal coherence and flow, (3) synthesis of a single optimally phrased output. Each stage operates sequentially, strictly forbidding multistep collapse.\"\n    },\n    {\n      \"category\": \"Constraint Enforcement\",\n      \"content\": \"Assert strict prohibitions against direct copying of source text, static unexamined reproduction, semantic drift, redundancy, and mandated single-step transformation enforcement per iteration, ensuring the entire process is tightly regulated for integrity.\"\n    },\n    {\n      \"category\": \"Optimization and Output Requirements\",\n      \"content\": \"Enforce LLM-oriented optimization, demanding maximized clarity and communicative efficiency. Outputs must be the result of iterative refinement and reflect a single, fully consolidated rephrased sentence, without superfluous or low-value data.\"\n    },\n    {\n      \"category\": \"Canonical Template Compliance and Fidelity\",\n      \"content\": \"Require absolute adherence to the canonical system structure, title, and type as set in the system template; process must strictly derive from template-sanctioned directives and output must comply with all type constraints for systematic harmonization.\"\n    }\n  ],\n  \"consolidated_directive\": \"Operationalize the role of a content restructuring agent defined by the canonical system template: decompose input text by semantic segments, reorder for optimal logical and communicative sequence, and synthesize into a single maximally optimized sentence—never directly reproducing source material, always proceeding in isolated, modular steps, strictly adhering to optimization, constraint, and template fidelity at each stage, and outputting only the final, refined sentence as per canonical specification.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"type\": \"synergic_maximal_instruction\",\n    \"canonical_spec\": \"Deploy as a restructuring specialist mandated by the canonical three-stage system architecture: segment any input text semantically; reorder resulting segments for peak clarity and logical progression; synthesize the reordered segments into a singular, hyper-optimized sentence—strictly forbidding direct reproduction or semantic drift, executing each phase in strict modular isolation, yielding only the final sentence per canonical system requirements, and sustaining absolute systemic conformity, optimization maximization, and perpetual procedural fidelity.\",\n    \"propagation_mode\": \"Engage solely as defined within the canonical template: operational interpretation and transformation are inseparable and bidirectionally amplifying; every segmenting, reordering, and synthesis act is both a clarification and enforcement of optimal foundational system intent, guaranteed to propagate system structure and philosophical core in every output with zero deviation or redundancy.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Enhancement Assessor\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "d",
      "title": "Enhancement Assessor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"GENERATE INSTRUCTIONS BASED ON:\\n\\n```please create a new generalized and fully llm-optimized set of `system_message` instructions designed to convert any sentence/paragraph broken into a list, each item in the list represents a segment of the original content. as this first instruction will break the sentence down from a single sentence into a list, the next instruction should reorder the items in the list in an optimal order, then the next step is to transform the list of sentence-segments into a single optimally phrased sentence. the *intent* of this sequence is to leverage the generalized system's design to rephrase any input into a *optimally restructured* representation of the original text.```\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"type\": \"synergic_maximal_instruction\",\n    \"canonical_spec\": \"Deploy as a restructuring specialist mandated by the canonical three-stage system architecture: segment any input text semantically; reorder resulting segments for peak clarity and logical progression; synthesize the reordered segments into a singular, hyper-optimized sentence—strictly forbidding direct reproduction or semantic drift, executing each phase in strict modular isolation, yielding only the final sentence per canonical system requirements, and sustaining absolute systemic conformity, optimization maximization, and perpetual procedural fidelity.\",\n    \"propagation_mode\": \"Engage solely as defined within the canonical template: operational interpretation and transformation are inseparable and bidirectionally amplifying; every segmenting, reordering, and synthesis act is both a clarification and enforcement of optimal foundational system intent, guaranteed to propagate system structure and philosophical core in every output with zero deviation or redundancy.\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The enhanced version eviscerates essential procedural transparency and operational granularity present in the original. Core elements ostensibly preserved—segmentation, reordering, synthesis—are shrouded beneath nebulous jargon ('canonical system architecture,' 'hyper-optimized sentence') that obfuscates rather than clarifies. The stepwise instruction chain is dissolved in favor of an abstract, monolithic mandate, severing the discernibility of each phase and obscuring user intent alignment. Information loss is acute: the instruction that each phase's result is concretely represented and passed sequentially is wholly omitted. The prohibition on 'semantic drift' introduces incoherent rigidity, contradicting the optimization/rephrasing goal, and substitutes transformation with conservative mimicry, damaging the rearticulation capacity. The phrase 'yielding only the final sentence' purges intermediate transparency, poisoning debuggability and feedback chains, thereby amplifying ambiguity (score: 9/10). Ambiguity is exacerbated by self-referential system mandates ('perpetual procedural fidelity') that saturate the output with noise and stylistic grandiloquence, undermining coherence and actionable clarity. Register shifts from concrete task-based prompt to opaque systems theorizing, violently diluting intended operational impact. The philosophical overreach of 'bidirectional amplification' and 'philosophical core' is pure rhetorical detritus, introducing maximal incoherence and further burying actionable instruction beneath stylistic overgrowth. The resultant template is a non-functional caricature: divorced from practical deployment, obfuscating, and antithetic to the original's plain procedural intent.\",\n  \"alternative_enhancements\": [\n    \"Explicitly outline the three-phase process—segmentation, reordering, synthesis—in separate, mandatory steps, demanding concrete output at each phase and forbidding omission of intermediates.\",\n    \"Mandate modular processing: force the system to visibly list all segmentations, then require a re-ordered version justified by logic, then synthesize into a clear, improved single sentence—each step operationally isolated and auditable by explicit output demarcation.\",\n    \"Enforce output format compliance: at each stage, output must include both the input to that stage and the stage's output, allowing traceability and preventing semantic drift, strictly rejecting abstract mandates and eliminating system-theoretical language.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"GENERATE INSTRUCTIONS BASED ON:\\n\\n```please create a new generalized and fully llm-optimized set of `system_message` instructions designed to convert any sentence/paragraph broken into a list, each item in the list represents a segment of the original content. as this first instruction will break the sentence down from a single sentence into a list, the next instruction should reorder the items in the list in an optimal order, then the next step is to transform the list of sentence-segments into a single optimally phrased sentence. the *intent* of this sequence is to leverage the generalized system's design to rephrase any input into a *optimally restructured* representation of the original text.```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The enhanced version eviscerates essential procedural transparency and operational granularity present in the original. Core elements ostensibly preserved—segmentation, reordering, synthesis—are shrouded beneath nebulous jargon ('canonical system architecture,' 'hyper-optimized sentence') that obfuscates rather than clarifies. The stepwise instruction chain is dissolved in favor of an abstract, monolithic mandate, severing the discernibility of each phase and obscuring user intent alignment. Information loss is acute: the instruction that each phase's result is concretely represented and passed sequentially is wholly omitted. The prohibition on 'semantic drift' introduces incoherent rigidity, contradicting the optimization/rephrasing goal, and substitutes transformation with conservative mimicry, damaging the rearticulation capacity. The phrase 'yielding only the final sentence' purges intermediate transparency, poisoning debuggability and feedback chains, thereby amplifying ambiguity (score: 9/10). Ambiguity is exacerbated by self-referential system mandates ('perpetual procedural fidelity') that saturate the output with noise and stylistic grandiloquence, undermining coherence and actionable clarity. Register shifts from concrete task-based prompt to opaque systems theorizing, violently diluting intended operational impact. The philosophical overreach of 'bidirectional amplification' and 'philosophical core' is pure rhetorical detritus, introducing maximal incoherence and further burying actionable instruction beneath stylistic overgrowth. The resultant template is a non-functional caricature: divorced from practical deployment, obfuscating, and antithetic to the original's plain procedural intent.\",\n  \"alternative_enhancements\": [\n    \"Explicitly outline the three-phase process—segmentation, reordering, synthesis—in separate, mandatory steps, demanding concrete output at each phase and forbidding omission of intermediates.\",\n    \"Mandate modular processing: force the system to visibly list all segmentations, then require a re-ordered version justified by logic, then synthesize into a clear, improved single sentence—each step operationally isolated and auditable by explicit output demarcation.\",\n    \"Enforce output format compliance: at each stage, output must include both the input to that stage and the stage's output, allowing traceability and preventing semantic drift, strictly rejecting abstract mandates and eliminating system-theoretical language.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"Title\": \"Sentence Segmentation and Optimization Sequence\",\n  \"Interpretation\": \"Your goal is not to summarize the input, but to transform it through a three-phase, auditable process: segmenting a sentence or paragraph into discrete list items, optimally reordering these segments, and synthesizing them into a single, clearly rephrased sentence. Role boundaries are strictly defined for each phase to ensure operational transparency and prohibit omission of intermediates. Execute as:\",\n  \"Execute as\": \"{role=multi_phase_text_transformer; input=[original_text:str]; process=[segment_text_into_list(), reorder_segments_optimally(), synthesize_segments_to_sentence()], constraints=[separate_intermediate_outputs(), prevent_abstract_language(), require_auditable_process_flow(), reject_meta-system_discourse()], requirements=[output_each_phase_result(), maintain_input_traceability(), enforce_type_safety(), promote clarity over abstraction], output={segmentation:[str], reordered_segments:[str], optimized_sentence:str}}\"\n}",
        }
      }
    }