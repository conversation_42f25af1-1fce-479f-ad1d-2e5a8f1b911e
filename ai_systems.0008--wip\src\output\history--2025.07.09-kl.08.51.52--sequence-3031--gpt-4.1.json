  {
  "initial_prompt": "Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.",
  "sequence_id": "3031",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    \"identify what constitutes the current @codebase—locate or clarify the scope of @codebase\",\n    \"gain access to the full codebase (repositories, dependencies, documentation, etc.)\",\n    \"review and read all source code files in the codebase\",\n    \"interpret overall software architecture—understand high-level design, major modules/components, and their relationships\",\n    \"analyze the purpose and functionality of each major module/component in the codebase\",\n    \"understand key data structures and algorithms used throughout the codebase\",\n    \"familiarize with codebase conventions (coding style, design patterns, naming, documentation, testing approach)\",\n    \"map all external dependencies, libraries, and services used by the codebase\",\n    \"investigate build, deployment, and configuration procedures\",\n    \"study historical codebase evolution via commit history or changelogs to get context for past decisions\",\n    \"review accompanying documentation—in-code docs, README files, architectural decision records, etc.\",\n    \"discover the codebase's domain, requirements, and intended behaviors\",\n    \"identify non-obvious or complex parts of implementation\",\n    \"uncover implicit assumptions or tribal knowledge not encoded in code or docs\",\n    \"formulate a mental model of the codebase to guide future modifications\",\n    \"self-assess understanding: ensure confidence that any codebase change would be contextually correct and maintain design integrity\",\n    \"surfaces hidden assumptions: that the codebase is accessible, understandable, sufficiently documented, and not so large or complex as to render deep understanding unfeasible\",\n    \"enumerate blockers: lack of codebase access, incomplete or missing documentation, obfuscated or poorly structured code, unavailable or outdated dependencies, insufficient domain knowledge\"\n  ]\n}",
        }
      }
    }