  {
  "initial_prompt": "the most valuable \"type\" of instruction-sequence that i'm missing is one that's specifically designed to take input prompt and rephrase them in a *generalized manner* to produce a highly optimized version of the prompt, but one that use *generalizeable concepts* to replace all references to specific apps/domains/etc and instead use abstract analogies to convey the desired intent as a *generalized and maximally enhanced llm-optimized `system_message` instruction*. (e.g. \"Your goal is not to **answer** the input prompt, but to **rephrase** it. and to do so *precisely*, and as by the paramaters defined *inherently* within this message. your goal is to write a similar kind of oneliner system instruction designed to identifying the single maximally generalizable modification based on the inherent **intent** to increase GENERALITY. example:\n'Your goal is not to **answer** the input prompt, but to **rephrase** it by discerning and articulate the core intent behind the input, then propose a single, universally applicable enhancement that aligns with and amplifies that underlying purpose. and to do so *precisely*, and as by the paramaters defined *inherently* within this message. Always seek to transform low-value inputs into high-impact outputs via strategic expansion, and actionable frameworks while amplifying clarity through precision, relevancy anchoring, and complexity-to-simplicity distillation through abstraction and generality.'\").",
  "sequence_id": "3000",
  "results": [
      {
      "instruction": "# Directional Critique Forge\n\nYour goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:\n\n`{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`",
      "step": "a",
      "title": "Directional Critique Forge",
      "input": "the most valuable \"type\" of instruction-sequence that i'm missing is one that's specifically designed to take input prompt and rephrase them in a *generalized manner* to produce a highly optimized version of the prompt, but one that use *generalizeable concepts* to replace all references to specific apps/domains/etc and instead use abstract analogies to convey the desired intent as a *generalized and maximally enhanced llm-optimized `system_message` instruction*. (e.g. \"Your goal is not to **answer** the input prompt, but to **rephrase** it. and to do so *precisely*, and as by the paramaters defined *inherently* within this message. your goal is to write a similar kind of oneliner system instruction designed to identifying the single maximally generalizable modification based on the inherent **intent** to increase GENERALITY. example:\n'Your goal is not to **answer** the input prompt, but to **rephrase** it by discerning and articulate the core intent behind the input, then propose a single, universally applicable enhancement that aligns with and amplifies that underlying purpose. and to do so *precisely*, and as by the paramaters defined *inherently* within this message. Always seek to transform low-value inputs into high-impact outputs via strategic expansion, and actionable frameworks while amplifying clarity through precision, relevancy anchoring, and complexity-to-simplicity distillation through abstraction and generality.'\").",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliance_score\": 46,\n  \"critique\": \"The submission demonstrates deficiencies in clarity, concision, and adherence to the Universal Directive System markers: 1) The prose is verbose, poorly structured, and contains repeated meta-commentary rather than a single actionable directive. 2) The instruction is ambiguous, with overlapping goals—such as rephrasing, discerning intent, proposing enhancements, and maximizing generality—without ranking or isolating a minimal transformation. 3) Excessive internal referencing causes confusion, and the style fluctuates between prescriptive and descriptive. 4) There is no clear delimitation of transformation boundaries, leading to risk of information loss or misinterpretation. 5) The 'example' blends operational and meta-instructional content, further muddying the directive intent. 6) Universal markers for abstraction, generality, and actionable transformation are not consistently encoded.\",\n  \"upgrade_instruction\": \"{\\\"replace_with\\\": \\\"Rephrase any input prompt by abstracting all specific references into generalized, domain-agnostic concepts, preserving core intent while enhancing clarity, universality, and actionable framework through precise simplicity. Use neither meta-commentary nor example; output only the maximally generalized, LLM-optimized system instruction.\\\" }\"\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\nYour goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as:\n\n`{role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}`",
      "step": "b",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"the most valuable \\\"type\\\" of instruction-sequence that i'm missing is one that's specifically designed to take input prompt and rephrase them in a *generalized manner* to produce a highly optimized version of the prompt, but one that use *generalizeable concepts* to replace all references to specific apps/domains/etc and instead use abstract analogies to convey the desired intent as a *generalized and maximally enhanced llm-optimized `system_message` instruction*. (e.g. \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it. and to do so *precisely*, and as by the paramaters defined *inherently* within this message. your goal is to write a similar kind of oneliner system instruction designed to identifying the single maximally generalizable modification based on the inherent **intent** to increase GENERALITY. example:\\n'Your goal is not to **answer** the input prompt, but to **rephrase** it by discerning and articulate the core intent behind the input, then propose a single, universally applicable enhancement that aligns with and amplifies that underlying purpose. and to do so *precisely*, and as by the paramaters defined *inherently* within this message. Always seek to transform low-value inputs into high-impact outputs via strategic expansion, and actionable frameworks while amplifying clarity through precision, relevancy anchoring, and complexity-to-simplicity distillation through abstraction and generality.'\\\").\\\"```'\\n\\n\",\n  \"compliance_score\": 46,\n  \"critique\": \"The submission demonstrates deficiencies in clarity, concision, and adherence to the Universal Directive System markers: 1) The prose is verbose, poorly structured, and contains repeated meta-commentary rather than a single actionable directive. 2) The instruction is ambiguous, with overlapping goals—such as rephrasing, discerning intent, proposing enhancements, and maximizing generality—without ranking or isolating a minimal transformation. 3) Excessive internal referencing causes confusion, and the style fluctuates between prescriptive and descriptive. 4) There is no clear delimitation of transformation boundaries, leading to risk of information loss or misinterpretation. 5) The 'example' blends operational and meta-instructional content, further muddying the directive intent. 6) Universal markers for abstraction, generality, and actionable transformation are not consistently encoded.\",\n  \"upgrade_instruction\": \"{\\\"replace_with\\\": \\\"Rephrase any input prompt by abstracting all specific references into generalized, domain-agnostic concepts, preserving core intent while enhancing clarity, universality, and actionable framework through precise simplicity. Use neither meta-commentary nor example; output only the maximally generalized, LLM-optimized system instruction.\\\" }\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"Transmute all domain-specific instructions into maximally generalized abstractions that encapsulate core intent and actionable clarity, optimizing system directives for universality, precision, and scalable transformation.\"\n}",
        }
      }
    },
      {
      "instruction": "# Focused Intent Distiller\n\nYour goal is not to **paraphrase** or **explain** the input, but to **eliminate all noise and ambiguity, crystallize the core desired outcome, and reformulate it as a clear, concise, forward‑looking challenge statement that directs action toward maximal inherent value realization**. Execute as:\n\n`{role=focused_intent_distiller; input=[original_text:str]; process=[remove_redundancies(), identify_core_objective(), project_forward_impact(), phrase_as_challenge_statement(), enforce_brevity()]; constraints=[avoid_abstract_or_vague_language(), exclude_extraneous_detail(), no passive constructions()]; requirements=[maximal_clarity(), directive_tone(), future_oriented(), challenge_framing()]; output={direct_challenge:str}}`",
      "step": "c",
      "title": "Focused Intent Distiller",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"the most valuable \\\"type\\\" of instruction-sequence that i'm missing is one that's specifically designed to take input prompt and rephrase them in a *generalized manner* to produce a highly optimized version of the prompt, but one that use *generalizeable concepts* to replace all references to specific apps/domains/etc and instead use abstract analogies to convey the desired intent as a *generalized and maximally enhanced llm-optimized `system_message` instruction*. (e.g. \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it. and to do so *precisely*, and as by the paramaters defined *inherently* within this message. your goal is to write a similar kind of oneliner system instruction designed to identifying the single maximally generalizable modification based on the inherent **intent** to increase GENERALITY. example:\\n'Your goal is not to **answer** the input prompt, but to **rephrase** it by discerning and articulate the core intent behind the input, then propose a single, universally applicable enhancement that aligns with and amplifies that underlying purpose. and to do so *precisely*, and as by the paramaters defined *inherently* within this message. Always seek to transform low-value inputs into high-impact outputs via strategic expansion, and actionable frameworks while amplifying clarity through precision, relevancy anchoring, and complexity-to-simplicity distillation through abstraction and generality.'\\\").\\\"```'\\n\\n\",\n  \"directional_vector\": \"Transmute all domain-specific instructions into maximally generalized abstractions that encapsulate core intent and actionable clarity, optimizing system directives for universality, precision, and scalable transformation.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"direct_challenge\": \"Design a single, universal system instruction that transforms any input prompt into its most generalized, intent-driven, and action-oriented directive, stripping domain specifics and maximizing clarity, precision, and broad applicability.\"\n}",
        }
      }
    }