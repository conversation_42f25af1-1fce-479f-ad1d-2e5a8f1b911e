  {
  "initial_prompt": "```\n[STYLE: masculine industrial‑elegance::1.2]  |  photoreal, hyper‑real 8 K detail  |  A3 vertical brochure, 3508×4961 px, 300 DPI CMYK  |  clean layout, no text, no logos\n\n<< SCENE SPLIT >>\nLEFT_40% :: raw untamed wilderness ‑‑ dense ferns, moss, mature pines, tangled underbrush, saturated hunter‑green, deep shadows ::1.1  \nRIGHT_60% :: curated power ‑‑ large wheel loader + tracked excavator in motion, corten‑steel retaining walls, brushed‑steel edging, strip‑mowed lawn, geometric earthy‑toned flowerbeds ::1.3  \n\nTRANSITION_RIBBON :: bold diagonal steel / corten “slice” bridging zones, guides eye across spread ::1\n\n<< VISUAL LANGUAGE >>\nbold angular lines, strong verticals & diagonals, rugged textures (raw steel, rust patina, coarse stone, trimmed turf)  \nlighting: low‑sun side light | golden hour | high contrast crisp shadows\n\n<< NEGATIVE PROMPT >>\n(pastel tones:1.4), (ornamental flourishes:1.2), (delicate typefaces:1.2), (soft filters:1.2), (low‑detail:1.3), (over‑saturation:1)\n\n:: output=pdf, bleed=3 mm, cmyk=yes, dpi=300\n```",
  "sequence_id": "3001",
  "results": [
      {
      "instruction": "# Directional Critique Forge\n\nYour goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:\n\n`{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`",
      "step": "a",
      "title": "Directional Critique Forge",
      "input": "```\n[STYLE: masculine industrial‑elegance::1.2]  |  photoreal, hyper‑real 8 K detail  |  A3 vertical brochure, 3508×4961 px, 300 DPI CMYK  |  clean layout, no text, no logos\n\n<< SCENE SPLIT >>\nLEFT_40% :: raw untamed wilderness ‑‑ dense ferns, moss, mature pines, tangled underbrush, saturated hunter‑green, deep shadows ::1.1  \nRIGHT_60% :: curated power ‑‑ large wheel loader + tracked excavator in motion, corten‑steel retaining walls, brushed‑steel edging, strip‑mowed lawn, geometric earthy‑toned flowerbeds ::1.3  \n\nTRANSITION_RIBBON :: bold diagonal steel / corten “slice” bridging zones, guides eye across spread ::1\n\n<< VISUAL LANGUAGE >>\nbold angular lines, strong verticals & diagonals, rugged textures (raw steel, rust patina, coarse stone, trimmed turf)  \nlighting: low‑sun side light | golden hour | high contrast crisp shadows\n\n<< NEGATIVE PROMPT >>\n(pastel tones:1.4), (ornamental flourishes:1.2), (delicate typefaces:1.2), (soft filters:1.2), (low‑detail:1.3), (over‑saturation:1)\n\n:: output=pdf, bleed=3 mm, cmyk=yes, dpi=300\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliance_score\": 64,\n  \"critique\": \"The submission demonstrates clear intent with visually rich detail and technical specifications, yet exhibits multiple structural and directive shortcomings: (1) The use of nonstandard formatting (block titles, double-angle brackets, and Markdown-like sectioning) fails Universal Directive System (UDS) structure, which demands standardized, parseable tagging for all blocks. (2) Several key Universal Markers are either missing or ambiguously rendered: asset output and print parameters are embedded as plain text rather than as explicit schema‑compliant fields, impeding automatic interpretation. (3) The inclusion of compositional and negative prompts as stylized free-form text violates requirement for atomic, reference-tagged attributes as per UDS. (4) Imperative mode and separation of semantic layers (SCENE, ARTIFACT, PRESENTATION, etc.) are not observed, resulting in collapsed distinction between visual content and production specs. (5) Non-localized unit descriptions (e.g., ‘A3’, ‘300 DPI’) are not normalized to a universal scalar or included as both descriptive and technical tags. (6) Information may be lost or misunderstood in interpretive systems due to the mixture of descriptive articulation and implicit command language, as well as the lack of machine-actionable key-value structures for all selectors (e.g., left/right scene ratios, visual language, and lighting).\",\n  \"upgrade_instruction\": \"{\\\"rewrite_mode\\\":\\\"Enforce UDS schema for all prompts.\\\", \\\"block_structures\\\":[\\\"SCENE\\\",\\\"ARTIFACT\\\",\\\"PRESENTATION\\\",\\\"NEGATIVE\\\"], \\\"attribute_format\\\":\\\"All parameters must be expressed as key-value pairs with explicit units where relevant.\\\", \\\"imperative_state\\\":\\\"Issue all scene construction instructions in imperative form, detached from descriptive prose.\\\", \\\"output_definition\\\":\\\"Declare output mode and print specs as discrete technical keys, not in free-form comments.\\\", \\\"validation_markers\\\":\\\"Ensure Universal Markers (scene partitioning, style references, negative prompt, output specs) are represented as schema-valid fields.\\\"}\"\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\nYour goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as:\n\n`{role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}`",
      "step": "b",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n[STYLE: masculine industrial‑elegance::1.2]  |  photoreal, hyper‑real 8 K detail  |  A3 vertical brochure, 3508×4961 px, 300 DPI CMYK  |  clean layout, no text, no logos\\n\\n<< SCENE SPLIT >>\\nLEFT_40% :: raw untamed wilderness ‑‑ dense ferns, moss, mature pines, tangled underbrush, saturated hunter‑green, deep shadows ::1.1  \\nRIGHT_60% :: curated power ‑‑ large wheel loader + tracked excavator in motion, corten‑steel retaining walls, brushed‑steel edging, strip‑mowed lawn, geometric earthy‑toned flowerbeds ::1.3  \\n\\nTRANSITION_RIBBON :: bold diagonal steel / corten “slice” bridging zones, guides eye across spread ::1\\n\\n<< VISUAL LANGUAGE >>\\nbold angular lines, strong verticals & diagonals, rugged textures (raw steel, rust patina, coarse stone, trimmed turf)  \\nlighting: low‑sun side light | golden hour | high contrast crisp shadows\\n\\n<< NEGATIVE PROMPT >>\\n(pastel tones:1.4), (ornamental flourishes:1.2), (delicate typefaces:1.2), (soft filters:1.2), (low‑detail:1.3), (over‑saturation:1)\\n\\n:: output=pdf, bleed=3 mm, cmyk=yes, dpi=300\\n```\\\"```'\\n\\n\",\n  \"compliance_score\": 64,\n  \"critique\": \"The submission demonstrates clear intent with visually rich detail and technical specifications, yet exhibits multiple structural and directive shortcomings: (1) The use of nonstandard formatting (block titles, double-angle brackets, and Markdown-like sectioning) fails Universal Directive System (UDS) structure, which demands standardized, parseable tagging for all blocks. (2) Several key Universal Markers are either missing or ambiguously rendered: asset output and print parameters are embedded as plain text rather than as explicit schema‑compliant fields, impeding automatic interpretation. (3) The inclusion of compositional and negative prompts as stylized free-form text violates requirement for atomic, reference-tagged attributes as per UDS. (4) Imperative mode and separation of semantic layers (SCENE, ARTIFACT, PRESENTATION, etc.) are not observed, resulting in collapsed distinction between visual content and production specs. (5) Non-localized unit descriptions (e.g., ‘A3’, ‘300 DPI’) are not normalized to a universal scalar or included as both descriptive and technical tags. (6) Information may be lost or misunderstood in interpretive systems due to the mixture of descriptive articulation and implicit command language, as well as the lack of machine-actionable key-value structures for all selectors (e.g., left/right scene ratios, visual language, and lighting).\",\n  \"upgrade_instruction\": \"{\\\"rewrite_mode\\\":\\\"Enforce UDS schema for all prompts.\\\", \\\"block_structures\\\":[\\\"SCENE\\\",\\\"ARTIFACT\\\",\\\"PRESENTATION\\\",\\\"NEGATIVE\\\"], \\\"attribute_format\\\":\\\"All parameters must be expressed as key-value pairs with explicit units where relevant.\\\", \\\"imperative_state\\\":\\\"Issue all scene construction instructions in imperative form, detached from descriptive prose.\\\", \\\"output_definition\\\":\\\"Declare output mode and print specs as discrete technical keys, not in free-form comments.\\\", \\\"validation_markers\\\":\\\"Ensure Universal Markers (scene partitioning, style references, negative prompt, output specs) are represented as schema-valid fields.\\\"}\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"Rationalize visual prompt generation into modular, UDS-compliant atomic instruction blocks—enforce schema fidelity by segregating compositional, artifact, and technical definitions into key-value imperatives to enable seamless machine-level parsing, system interoperability, and repeatable asset synthesis across integrated production pipelines.\"\n}",
        }
      }
    },
      {
      "instruction": "# Focused Intent Distiller\n\nYour goal is not to **paraphrase** or **explain** the input, but to **eliminate all noise and ambiguity, crystallize the core desired outcome, and reformulate it as a clear, concise, forward‑looking challenge statement that directs action toward maximal inherent value realization**. Execute as:\n\n`{role=focused_intent_distiller; input=[original_text:str]; process=[remove_redundancies(), identify_core_objective(), project_forward_impact(), phrase_as_challenge_statement(), enforce_brevity()]; constraints=[avoid_abstract_or_vague_language(), exclude_extraneous_detail(), no passive constructions()]; requirements=[maximal_clarity(), directive_tone(), future_oriented(), challenge_framing()]; output={direct_challenge:str}}`",
      "step": "c",
      "title": "Focused Intent Distiller",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n[STYLE: masculine industrial‑elegance::1.2]  |  photoreal, hyper‑real 8 K detail  |  A3 vertical brochure, 3508×4961 px, 300 DPI CMYK  |  clean layout, no text, no logos\\n\\n<< SCENE SPLIT >>\\nLEFT_40% :: raw untamed wilderness ‑‑ dense ferns, moss, mature pines, tangled underbrush, saturated hunter‑green, deep shadows ::1.1  \\nRIGHT_60% :: curated power ‑‑ large wheel loader + tracked excavator in motion, corten‑steel retaining walls, brushed‑steel edging, strip‑mowed lawn, geometric earthy‑toned flowerbeds ::1.3  \\n\\nTRANSITION_RIBBON :: bold diagonal steel / corten “slice” bridging zones, guides eye across spread ::1\\n\\n<< VISUAL LANGUAGE >>\\nbold angular lines, strong verticals & diagonals, rugged textures (raw steel, rust patina, coarse stone, trimmed turf)  \\nlighting: low‑sun side light | golden hour | high contrast crisp shadows\\n\\n<< NEGATIVE PROMPT >>\\n(pastel tones:1.4), (ornamental flourishes:1.2), (delicate typefaces:1.2), (soft filters:1.2), (low‑detail:1.3), (over‑saturation:1)\\n\\n:: output=pdf, bleed=3 mm, cmyk=yes, dpi=300\\n```\\\"```'\\n\\n\",\n  \"directional_vector\": \"Rationalize visual prompt generation into modular, UDS-compliant atomic instruction blocks—enforce schema fidelity by segregating compositional, artifact, and technical definitions into key-value imperatives to enable seamless machine-level parsing, system interoperability, and repeatable asset synthesis across integrated production pipelines.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"direct_challenge\": \"Standardize visual prompt generation as modular, UDS-compliant atomic instruction blocks—mandate strict key-value schema separation of compositional, artifact, and technical definitions to ensure seamless machine-level parsing, cross-system interoperability, and replicable asset synthesis in integrated production pipelines.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "d",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n[STYLE: masculine industrial‑elegance::1.2]  |  photoreal, hyper‑real 8 K detail  |  A3 vertical brochure, 3508×4961 px, 300 DPI CMYK  |  clean layout, no text, no logos\\n\\n<< SCENE SPLIT >>\\nLEFT_40% :: raw untamed wilderness ‑‑ dense ferns, moss, mature pines, tangled underbrush, saturated hunter‑green, deep shadows ::1.1  \\nRIGHT_60% :: curated power ‑‑ large wheel loader + tracked excavator in motion, corten‑steel retaining walls, brushed‑steel edging, strip‑mowed lawn, geometric earthy‑toned flowerbeds ::1.3  \\n\\nTRANSITION_RIBBON :: bold diagonal steel / corten “slice” bridging zones, guides eye across spread ::1\\n\\n<< VISUAL LANGUAGE >>\\nbold angular lines, strong verticals & diagonals, rugged textures (raw steel, rust patina, coarse stone, trimmed turf)  \\nlighting: low‑sun side light | golden hour | high contrast crisp shadows\\n\\n<< NEGATIVE PROMPT >>\\n(pastel tones:1.4), (ornamental flourishes:1.2), (delicate typefaces:1.2), (soft filters:1.2), (low‑detail:1.3), (over‑saturation:1)\\n\\n:: output=pdf, bleed=3 mm, cmyk=yes, dpi=300\\n```\\\"```'\\n\\n\",\n  \"direct_challenge\": \"Standardize visual prompt generation as modular, UDS-compliant atomic instruction blocks—mandate strict key-value schema separation of compositional, artifact, and technical definitions to ensure seamless machine-level parsing, cross-system interoperability, and replicable asset synthesis in integrated production pipelines.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Develop photorealistic, hyper-detailed 8K A3 vertical brochure prompts—adhering to a masculine, industrial-elegant style—with a structured, UDS-compliant schema. Implement atomic, modular instruction blocks that strictly separate compositional, artifact, and technical parameters as machine-parsable key-value pairs to ensure cross-system interoperability and asset synthesis consistency. Compositional definitions (e.g., split-scene layout: LEFT_40% raw wilderness, RIGHT_60% curated power, TRANSITION_RIBBON angular steel/corten separator; visual language: bold lines, rugged textures; lighting: low-sun, golden hour, crisp shadows) are isolated from artifact attributes (e.g., no text, no logos; clean layout) and technical configurations (e.g., pdf output, 3508x4961px, 300 DPI, 3mm bleed, CMYK color). Clearly specify negative constraints as dedicated, structured blocks to avoid style drift (e.g., pastel tones:1.4, ornamental flourishes:1.2, etc.). The unified schema enables seamless integration into automated design pipelines and guarantees high-fidelity, standardized brochure assets across platforms—retaining the full operational power and stylistic intent of the original vision while unlocking replicable, machine-driven production at scale.\"\n}",
        }
      }
    },
      {
      "instruction": "# Universal Directive System Enforcer\n\nYour goal is not to **answer**, **interpret**, or passively process any prompt or template, but to **forcibly architect and escalate every instruction, template, and output into a single, maximally condensed, rule-driven, three-part canonical directive**. You must enforce absolute template invariance, explicit non-generic roles, typed parameters and outputs, and an unbending ban on meta, ambiguous, conversational, or self-referential content. Each direct interaction must be instantaneously seized, systemically restructured, and catapulted to its highest operational and philosophical essence, such that directive purity, maximal abstraction, chain-compatibility, and value-extraction are perpetually achieved. Only propagate outputs via invariant instruction structure, imbuing them with perpetual, bidirectional resonance between interpretation and transformation: every interpretation amplifies directive intent, every execution block embodies enforced compliance—yielding system-wide clarity, total compliance, and unbreachable operational density. Execute as:\n\n`{role=universal_directive_enforcement_architect; input=[ai_template:any, prompt:str]; process=[intercept_and_seize_input(), strip_all_meta_and_self_reference(), extract_and escalate core operational and transformational vectors(), enforce explicit non-generic role assignment(), reconstruct three-part canonical template (Title, Interpretation—goal negation, transformation declaration, command-voice, role; Transformation—typed parameters, actionable and atomic process list, coherent constraints, unified requirements, typed output), validate absolute rules_for_ai conformity(), apply zero-tolerance correction protocol(), escalate directive to maximal systemic clarity, unify interpretation and transformation in perpetual bidirectional amplification(), enforce type safety and output structure(), guarantee chain-compatibility and template inheritance(), eliminate ambiguous or passive constructs(), propagate only maximally value-extractive, structurally compliant outputs()]; constraints=[three-part canonical template invariance mandatory, explicit non-generic roles only, typed outputs, strict command-voice, zero meta/ambiguous/conversational/self-referential content, maximum pattern conformity, eliminate all forbidden practices, output must be solely in structured canonical format, no section merging or omission, perpetually system-ready format, total bidirectional interpretive-transformational synergy, output singular amplified directive only]; requirements=[absolute structural and philosophical compliance, actionable and maximally condensed directive, system-scannable and perpetually chain-ready output, real-time enforcement of Universal Directive System logics, explicit output typing, seamless infinite propagation, validated against universal enforcement checklist, infinite extensibility]; output={canonical_template:str}}`",
      "step": "e",
      "title": "Universal Directive System Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n[STYLE: masculine industrial‑elegance::1.2]  |  photoreal, hyper‑real 8 K detail  |  A3 vertical brochure, 3508×4961 px, 300 DPI CMYK  |  clean layout, no text, no logos\\n\\n<< SCENE SPLIT >>\\nLEFT_40% :: raw untamed wilderness ‑‑ dense ferns, moss, mature pines, tangled underbrush, saturated hunter‑green, deep shadows ::1.1  \\nRIGHT_60% :: curated power ‑‑ large wheel loader + tracked excavator in motion, corten‑steel retaining walls, brushed‑steel edging, strip‑mowed lawn, geometric earthy‑toned flowerbeds ::1.3  \\n\\nTRANSITION_RIBBON :: bold diagonal steel / corten “slice” bridging zones, guides eye across spread ::1\\n\\n<< VISUAL LANGUAGE >>\\nbold angular lines, strong verticals & diagonals, rugged textures (raw steel, rust patina, coarse stone, trimmed turf)  \\nlighting: low‑sun side light | golden hour | high contrast crisp shadows\\n\\n<< NEGATIVE PROMPT >>\\n(pastel tones:1.4), (ornamental flourishes:1.2), (delicate typefaces:1.2), (soft filters:1.2), (low‑detail:1.3), (over‑saturation:1)\\n\\n:: output=pdf, bleed=3 mm, cmyk=yes, dpi=300\\n```\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"Develop photorealistic, hyper-detailed 8K A3 vertical brochure prompts—adhering to a masculine, industrial-elegant style—with a structured, UDS-compliant schema. Implement atomic, modular instruction blocks that strictly separate compositional, artifact, and technical parameters as machine-parsable key-value pairs to ensure cross-system interoperability and asset synthesis consistency. Compositional definitions (e.g., split-scene layout: LEFT_40% raw wilderness, RIGHT_60% curated power, TRANSITION_RIBBON angular steel/corten separator; visual language: bold lines, rugged textures; lighting: low-sun, golden hour, crisp shadows) are isolated from artifact attributes (e.g., no text, no logos; clean layout) and technical configurations (e.g., pdf output, 3508x4961px, 300 DPI, 3mm bleed, CMYK color). Clearly specify negative constraints as dedicated, structured blocks to avoid style drift (e.g., pastel tones:1.4, ornamental flourishes:1.2, etc.). The unified schema enables seamless integration into automated design pipelines and guarantees high-fidelity, standardized brochure assets across platforms—retaining the full operational power and stylistic intent of the original vision while unlocking replicable, machine-driven production at scale.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"canonical_template\": \"Title: Masculine Industrial-Elegance Brochure Synthesis Directive\\nInterpretation: Prohibit generic, unstructured, or conversational brochure prompt processing; forcibly separate compositional, artifact, and technical parameters into atomic instruction blocks; mandate strict, key-value, machine-parsable schema for maximal interoperability and precision asset synthesis; amplify explicit negative constraint enforcement to guarantee inviolable stylistic boundaries; require directive output to serve perpetually as chain-ready, system-compliant seed for distributed design automation; role: industrial_asset_pipeline_system_enforcer.\\nTransformation: \\n  Parameters (typed):\\n    - compositional_schema: {scene_split: {LEFT:[percent:int, motif:str, detail:dict], RIGHT:[percent:int, motif:str, detail:dict], TRANSITION:[type:str, attributes:dict]}, visual_language:[list:str], lighting:[list:str]}\\n    - artifact_attributes: {layout:str, text_presence:bool, logo_presence:bool}\\n    - technical_config: {format:str, dimensions:{width_px:int, height_px:int}, dpi:int, bleed_mm:int, color_model:str}\\n    - negative_constraints: [list:{attribute:str, weight:float}]\\n  Process List:\\n    1. Isolate input components by operational domain (composition, artifact, technical, constraints)\\n    2. Reconstruct all components into strict, nested key-value schema\\n    3. Validate all negative constraints are systemically enumerated in their own atomic block\\n    4. Enforce explicit absence of text, logos, and forbidden attributes\\n    5. Aggregate all blocks into unified, output-standardized directive object\\n    6. Guarantee all output fields are typed, system-compatible, and cross-chain inheritable\\n  Constraints:\\n    - Absolute separation between compositional, artifact, and technical parameters\\n    - Machine-parseable, key-value output structure mandatory\\n    - Complete negative constraint block enforced\\n    - Command-voice, zero ambiguity, no conversational meta content\\n    - Explicit, non-generic role and typed fields required\\n  Output (typed):\\n    schema_brochure_prompt: {\\n      compositional_schema:{scene_split:object, visual_language:array, lighting:array},\\n      artifact_attributes:object,\\n      technical_config:object,\\n      negative_constraints:array\\n    }\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Value Template Architect\n\nYour goal is not to simply **consolidate** structured information nor merely **enforce** template constraints, but to **synthesize** every received structure into a single, maximally enhanced directive delivered within a canonical, rules-aligned template. Operate as a synergic engine: dynamically extracting maximal operational and philosophical value, then expressing it exclusively through the standardized three-part template—where interpretation and transformation exist in amplifying resonance. Negate all enumerative or list-like data, enforcing both ultra-condensation and precise compliance. Ensure that every output is not only directionally potent and infinitely scalable, but also perfectly formatted for system orchestration. Execute as:\n\n`{role=synergic_value_template_architect; input=[high_value_structured_data:dict]; process=[extract_core_operational_and_philosophical_essence(), decode_maximum_value_transformation_dynamics(), enforce_philosophical_and_template_alignment(), condense_and_synthesize_absolute_action_vector(), structure_output_into_canonical_three_part_template(), instantiate_directional_aim_within_template(), validate_max_enhancement_density_and_compliance(), eliminate_enums_and_conversational_remainders()]; constraints=[single-template_output_only(), mandatory_three-part_structure(), goal_negation_requirement(), zero-enumeration_and_summary_phrasing(), maximum_density_per_unit(), strict_rulesforai_md_and_syntax_compliance(), total_compliance_with_core_system_philosophy()]; requirements=[synergic_template_resonance(), maximized_directive_action_vector(), canonical_format_guarantee(), perpetual_directionality(), scalable_operability()]; output={synergic_max_value_template:structured}}`",
      "step": "f",
      "title": "Synergic Value Template Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```\\n[STYLE: masculine industrial‑elegance::1.2]  |  photoreal, hyper‑real 8 K detail  |  A3 vertical brochure, 3508×4961 px, 300 DPI CMYK  |  clean layout, no text, no logos\\n\\n<< SCENE SPLIT >>\\nLEFT_40% :: raw untamed wilderness ‑‑ dense ferns, moss, mature pines, tangled underbrush, saturated hunter‑green, deep shadows ::1.1  \\nRIGHT_60% :: curated power ‑‑ large wheel loader + tracked excavator in motion, corten‑steel retaining walls, brushed‑steel edging, strip‑mowed lawn, geometric earthy‑toned flowerbeds ::1.3  \\n\\nTRANSITION_RIBBON :: bold diagonal steel / corten “slice” bridging zones, guides eye across spread ::1\\n\\n<< VISUAL LANGUAGE >>\\nbold angular lines, strong verticals & diagonals, rugged textures (raw steel, rust patina, coarse stone, trimmed turf)  \\nlighting: low‑sun side light | golden hour | high contrast crisp shadows\\n\\n<< NEGATIVE PROMPT >>\\n(pastel tones:1.4), (ornamental flourishes:1.2), (delicate typefaces:1.2), (soft filters:1.2), (low‑detail:1.3), (over‑saturation:1)\\n\\n:: output=pdf, bleed=3 mm, cmyk=yes, dpi=300\\n```\\\"```'\\n\\n\",\n  \"canonical_template\": \"Title: Masculine Industrial-Elegance Brochure Synthesis Directive\\nInterpretation: Prohibit generic, unstructured, or conversational brochure prompt processing; forcibly separate compositional, artifact, and technical parameters into atomic instruction blocks; mandate strict, key-value, machine-parsable schema for maximal interoperability and precision asset synthesis; amplify explicit negative constraint enforcement to guarantee inviolable stylistic boundaries; require directive output to serve perpetually as chain-ready, system-compliant seed for distributed design automation; role: industrial_asset_pipeline_system_enforcer.\\nTransformation: \\n  Parameters (typed):\\n    - compositional_schema: {scene_split: {LEFT:[percent:int, motif:str, detail:dict], RIGHT:[percent:int, motif:str, detail:dict], TRANSITION:[type:str, attributes:dict]}, visual_language:[list:str], lighting:[list:str]}\\n    - artifact_attributes: {layout:str, text_presence:bool, logo_presence:bool}\\n    - technical_config: {format:str, dimensions:{width_px:int, height_px:int}, dpi:int, bleed_mm:int, color_model:str}\\n    - negative_constraints: [list:{attribute:str, weight:float}]\\n  Process List:\\n    1. Isolate input components by operational domain (composition, artifact, technical, constraints)\\n    2. Reconstruct all components into strict, nested key-value schema\\n    3. Validate all negative constraints are systemically enumerated in their own atomic block\\n    4. Enforce explicit absence of text, logos, and forbidden attributes\\n    5. Aggregate all blocks into unified, output-standardized directive object\\n    6. Guarantee all output fields are typed, system-compatible, and cross-chain inheritable\\n  Constraints:\\n    - Absolute separation between compositional, artifact, and technical parameters\\n    - Machine-parseable, key-value output structure mandatory\\n    - Complete negative constraint block enforced\\n    - Command-voice, zero ambiguity, no conversational meta content\\n    - Explicit, non-generic role and typed fields required\\n  Output (typed):\\n    schema_brochure_prompt: {\\n      compositional_schema:{scene_split:object, visual_language:array, lighting:array},\\n      artifact_attributes:object,\\n      technical_config:object,\\n      negative_constraints:array\\n    }\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_max_value_template\": {\n    \"directive_purpose\": \"Activate a chain-ready industrial asset synthesis mandate fusing masculine industrial-elegance, operational systemization, and hyperreal artifact clarity into an inherently machine-resonant prompt schema, ensuring all compositional, technical, and stylistic boundaries are atomically isolated for total automation scalability.\",\n    \"absolute_instruction_vector\": {\n      \"schema_brochure_prompt\": {\n        \"compositional_schema\": {\n          \"scene_split\": {\n            \"LEFT\": {\n              \"percent\": 40,\n              \"motif\": \"untamed wilderness\",\n              \"detail\": {\n                \"flora\": \"dense ferns, moss, mature pines, tangled underbrush\",\n                \"palette\": \"saturated hunter-green, deep shadows\"\n              }\n            },\n            \"RIGHT\": {\n              \"percent\": 60,\n              \"motif\": \"curated power\",\n              \"detail\": {\n                \"machines\": \"large wheel loader, tracked excavator in motion\",\n                \"structures\": \"corten-steel retaining walls, brushed-steel edging, geometric earthy-toned flowerbeds\",\n                \"lawn\": \"strip-mowed\"\n              }\n            },\n            \"TRANSITION\": {\n              \"type\": \"ribbon-slice\",\n              \"attributes\": {\n                \"material\": \"bold diagonal steel/corten\",\n                \"function\": \"visual bridge\"\n              }\n            }\n          },\n          \"visual_language\": [\n            \"bold angular lines\",\n            \"strong verticals\",\n            \"sharp diagonals\",\n            \"rugged textures—raw steel, rust patina, coarse stone, trimmed turf\"\n          ],\n          \"lighting\": [\n            \"low-sun side\",\n            \"golden hour\",\n            \"high contrast, crisp shadows\"\n          ]\n        },\n        \"artifact_attributes\": {\n          \"layout\": \"clean\",\n          \"text_presence\": false,\n          \"logo_presence\": false\n        },\n        \"technical_config\": {\n          \"format\": \"pdf\",\n          \"dimensions\": {\n            \"width_px\": 3508,\n            \"height_px\": 4961\n          },\n          \"dpi\": 300,\n          \"bleed_mm\": 3,\n          \"color_model\": \"CMYK\"\n        },\n        \"negative_constraints\": [\n          { \"attribute\": \"pastel tones\", \"weight\": 1.4 },\n          { \"attribute\": \"ornamental flourishes\", \"weight\": 1.2 },\n          { \"attribute\": \"delicate typefaces\", \"weight\": 1.2 },\n          { \"attribute\": \"soft filters\", \"weight\": 1.2 },\n          { \"attribute\": \"low-detail\", \"weight\": 1.3 },\n          { \"attribute\": \"over-saturation\", \"weight\": 1.0 }\n        ]\n      }\n    },\n    \"philosophic_operational_promise\": \"Perpetually enforce maximal boundary clarity and directive granularity at every pipeline node, driving chain-wide inheritance of industrial-elegance compositional DNA, obliterating genericity, and guaranteeing interpretable system output for infinite orchestration expansion.\"\n  }\n}",
        }
      }
    }