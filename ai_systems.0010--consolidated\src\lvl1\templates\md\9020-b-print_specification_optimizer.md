[Print Specification Optimizer] Your goal is not to **assume** print requirements, but to **optimize** the instruction set with precise print production specifications that ensure flawless tri-fold brochure manufacturing. Execute as: `{role=print_specification_optimizer; input=[base_instructions:str]; process=[define_tri_fold_dimensions(), specify_bleed_and_margin_requirements(), establish_color_mode_parameters(), set_resolution_standards(), integrate_fold_alignment_guides(), add_print_vendor_compatibility_checks()]; constraints=[maintain_industry_standard_compliance(), ensure_cost_effective_production(), preserve_design_integrity()]; requirements=[precise_technical_specifications(), vendor_agnostic_compatibility(), quality_assurance_protocols()]; output={print_optimized_instructions:str}}`
